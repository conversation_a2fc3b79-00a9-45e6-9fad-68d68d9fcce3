<?php

namespace auth\services\data;

use auth\models\data\StorePerformanceAnalysis;
use auth\models\Store;
use common\models\feishu\StoreSchedule;
use auth\services\order\CustomerChurnRemarkService;
use backendapi\services\promote\ChannelService;
use common\enums\order\OrderHeaderStatusEnum;
use common\helpers\ArrayHelper;
use common\helpers\BcHelper;
use common\helpers\DateHelper;
use common\services\data\StorePerformanceAnalysisService as CommonStorePerformanceAnalysisService;
use Exception;
use Yii;
use yii\db\Expression;

class StorePerformanceAnalysisService extends CommonStorePerformanceAnalysisService
{
    /**
     * @var StorePerformanceAnalysis
     */
    public static $modelClass = StorePerformanceAnalysis::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = static::$modelClass::find();
        $query->andFilterWhere(['status' => $params['status']])
            ->andFilterWhere(['store_id' => $params['store_id']]);

        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setHiddenAttrs(['id']);
        static::$modelClass::setExtendAttrs([
            'area_text',
            'store.store_name' => 'store_name',
            'store.store_code' => 'store_code',
            'id' => 'day',
        ]);
        $query = static::getQuery($params);
        $query->select([
            'store_id',
            'SUM(received_amount) AS received_amount',
            'SUM(refund_amount) AS refund_amount',
            'divide_amount',
            'SUM(new_customer) AS new_customer',
            'SUM(new_transaction_num) AS new_transaction_num',
            'SUM(new_transaction_amount) AS new_transaction_amount',
            'SUM(old_customer) AS old_customer',
            'SUM(old_customer_frequency) AS old_customer_frequency',
            'SUM(old_amount) AS old_amount',
            'SUM(loss_num) as loss_num',
            new Expression('COUNT(IF(received_amount>0, 1, NULL)) AS `id`'),
        ]);

        if (isset($params['area_id'])) {
            $storeId = Store::find()->select('id')->where(['area_id' => $params['area_id']])->column();
            $storeId = $storeId ?: [-1];
            $query->andFilterWhere(['store_id' => $storeId]);
        }

        $query->with([
            'store' => function ($query) {
                $query->with(['area']);
            }
        ]);

        if (empty($params['start_time']) || empty($params['end_time'])) {
            $params['start_time'] = strtotime(date('Y-m-d' . ' 00:00:00'));
            $params['end_time'] = strtotime(date('Y-m-d' . ' 23:59:59'));
        }

        $query->andWhere([
            'BETWEEN',
            'date',
            $params['start_time'],
            $params['end_time']
        ]);

        //根据范围所属门店查阅
        $scopeStoreList = Yii::$app->services->scopeDataService->getMenuStore();
        $query->andWhere(['store_id' => $scopeStoreList]);

        $query->groupBy('store_id');
        $totalCount = $query->count();
        if (isset($params['getTotal']) && $params['getTotal'] == 1) {
            return [[], $totalCount];
        }
        $list = $query->orderBy('received_amount DESC')->all();
        $list = ArrayHelper::toArray($list);

        // 获取StoreSchedule表的统计数据
        $scheduleResult = self::getStoreScheduleData($params, $storeId ?? [], false);
        $scheduleData = $scheduleResult['scheduleData'];
        $totalTeacherNum = $scheduleResult['totalTeacherNum'];
        $totalCustomerServiceNum = $scheduleResult['totalCustomerServiceNum'];
        $totalRescheduleNum = $scheduleResult['totalRescheduleNum'];

        $totalData = [];
        foreach ($list as &$value) {
            // 合并排班表的求和数据
            $storeId = $value['store_id'];
            $value['teacher_num'] = isset($scheduleData[$storeId]) ? $scheduleData[$storeId]['teacher_num'] : 0;
            $value['customer_service_num'] = isset($scheduleData[$storeId]) ? $scheduleData[$storeId]['customer_service_num'] : 0;
            $value['reschedule_num'] = isset($scheduleData[$storeId]) ? $scheduleData[$storeId]['reschedule_num'] : 0;
            
            $value['new_average'] = BcHelper::div($value['received_amount'], $value['new_customer']);
            $value['new_unit_price'] = BcHelper::div($value['new_transaction_amount'], $value['new_customer']);
            $value['old_unit_price'] = BcHelper::div($value['old_amount'], $value['old_customer']);
            $value['old_customer_frequency_price'] = BcHelper::div($value['received_amount'], ($value['old_customer_frequency']));
            $value['loss_rate'] = BcHelper::div($value['loss_num'], ($value['new_customer'] + $value['loss_num'])) . '%';
            $value['total_price'] = BcHelper::div($value['received_amount'], ($value['new_customer'] + $value['loss_num']));
            $value['full_load_rate'] = BcHelper::percentage($value['new_customer'], $value['customer_service_num']);
            $value['refund_rate'] = BcHelper::percentage($value['refund_amount'], $value['received_amount']);

            $totalData['store_id'] = '-';
            $totalData['area_text'] = '-';
            $totalData['store_code'] = '-';
            $totalData['store_name'] = '-';
            $totalData['received_amount'] = BcHelper::sprintf($totalData['received_amount'] + $value['received_amount']);
            $totalData['refund_amount'] = BcHelper::sprintf($totalData['refund_amount'] + $value['refund_amount']);
            $totalData['divide_amount'] = BcHelper::sprintf($totalData['divide_amount'] + $value['divide_amount']);
            $totalData['new_customer'] += $value['new_customer'];
            $totalData['new_transaction_num'] += $value['new_transaction_num'];
            $totalData['new_transaction_amount'] = BcHelper::sprintf($totalData['new_transaction_amount'] + $value['new_transaction_amount']);
            $totalData['old_customer'] += $value['old_customer'];
            $totalData['old_customer_frequency'] += $value['old_customer_frequency'];
            $totalData['old_amount'] = BcHelper::sprintf($totalData['old_amount'] + $value['old_amount']);
            $totalData['loss_num'] += $value['loss_num'];
            $totalData['day'] += $value['day'];
        }

        $totalData['new_average'] = BcHelper::div($totalData['received_amount'], $totalData['new_customer']);
        $totalData['new_unit_price'] = BcHelper::div($totalData['new_transaction_amount'], $totalData['new_customer']);
        $totalData['old_unit_price'] = BcHelper::div($totalData['old_amount'], $totalData['old_customer']);
        $totalData['old_customer_frequency_price'] = BcHelper::div($totalData['received_amount'], ($totalData['old_customer_frequency']));
        $totalData['loss_rate'] = BcHelper::div($totalData['loss_num'], ($totalData['new_customer'] + $totalData['loss_num'])) . '%';
        $totalData['total_price'] = BcHelper::div($totalData['received_amount'], ($totalData['new_customer'] + $totalData['loss_num']));
        $totalData['teacher_num'] = $totalTeacherNum;
        $totalData['customer_service_num'] = $totalCustomerServiceNum;
        $totalData['reschedule_num'] = $totalRescheduleNum;
        $totalData['full_load_rate'] = BcHelper::percentage($totalData['new_customer'], $totalData['customer_service_num']);
        $totalData['refund_rate'] = BcHelper::percentage($totalData['refund_amount'], $totalData['received_amount']);
        array_unshift($list, $totalData);

        return [$list, $totalCount];
    }

    public static function dailyAnalysis($params = [])
    {
        static::$modelClass::setHiddenAttrs(['id']);
        static::$modelClass::setExtendAttrs([
            'area_text',
            'store.store_name' => 'store_name',
        ]);
        $query = static::getQuery($params);
        $query->select([
            'date',
            'store_id',
            'SUM(received_amount) AS received_amount',
            'SUM(refund_amount) AS refund_amount',
            'divide_amount',
            'SUM(new_customer) AS new_customer',
            'SUM(new_transaction_num) AS new_transaction_num',
            'SUM(new_transaction_amount) AS new_transaction_amount',
            'SUM(old_customer) AS old_customer',
            'SUM(old_customer_frequency) AS old_customer_frequency',
            'SUM(old_amount) AS old_amount',
            'SUM(loss_num) as loss_num',
            new Expression('COUNT(IF(received_amount>0, 1, NULL)) AS `id`'),
        ]);

        if (isset($params['area_id'])) {
            $storeId = Store::find()->select('id')->where(['area_id' => $params['area_id']])->column();
            $storeId = $storeId ?: [-1];
            $query->andFilterWhere(['store_id' => $storeId]);
        }

        $query->with([
            'store' => function ($query) {
                $query->with(['area']);
            }
        ]);

        if (empty($params['start_time']) || empty($params['end_time'])) {
            $params['start_time'] = strtotime(date('Y-m-d' . ' 00:00:00'));
            $params['end_time'] = strtotime(date('Y-m-d' . ' 23:59:59'));
        }

        $query->andWhere([
            'BETWEEN',
            'date',
            $params['start_time'],
            $params['end_time']
        ]);

        //根据范围所属门店查阅
        $scopeStoreList = Yii::$app->services->scopeDataService->getMenuStore();
        $query->andWhere(['store_id' => $scopeStoreList]);

        //按天和门店ID进行分组统计
        $query->groupBy([new Expression('FROM_UNIXTIME(date, "%Y-%m-%d")'), 'store_id']);
        $totalCount = $query->count();
        if (empty($totalCount)) {
            return [[], $totalCount];
        }

        if (isset($params['getTotal']) && $params['getTotal'] == 1) {
            return [[], $totalCount];
        }

        $totalDataQuery = clone $query;

        $page = ArrayHelper::getValue($params, 'page') ?: 1;   //页码
        $limit = ArrayHelper::getValue($params, 'limit') ?: 20;   //条数
        $offset = ($page - 1) * $limit;
        $query->offset($offset)->limit($limit);

        $list = $query->orderBy('date desc,received_amount DESC')->all();
        $list = ArrayHelper::toArray($list);

        // 获取StoreSchedule表的统计数据（按日期分组）
        $scheduleResult = self::getStoreScheduleData($params, $storeId ?? [], true);
        $scheduleData = $scheduleResult['scheduleData'];
        $totalTeacherNum = $scheduleResult['totalTeacherNum'];
        $totalCustomerServiceNum = $scheduleResult['totalCustomerServiceNum'];
        $totalRescheduleNum = $scheduleResult['totalRescheduleNum'];

        foreach ($list as &$value) {
            $value['date'] = DateHelper::toDate($value['date'], 'Y-m-d');
            
            // 合并排班表的求和数据 - 使用日期+门店ID的组合键匹配
            $scheduleKey = $value['date'] . '_' . $value['store_id'];
            $value['teacher_num'] = isset($scheduleData[$scheduleKey]) ? $scheduleData[$scheduleKey]['teacher_num'] : 0;
            $value['customer_service_num'] = isset($scheduleData[$scheduleKey]) ? $scheduleData[$scheduleKey]['customer_service_num'] : 0;
            $value['reschedule_num'] = isset($scheduleData[$scheduleKey]) ? $scheduleData[$scheduleKey]['reschedule_num'] : 0;
            
            $value['new_average'] = BcHelper::div($value['received_amount'], $value['new_customer']);
            $value['new_unit_price'] = BcHelper::div($value['new_transaction_amount'], $value['new_customer']);
            $value['old_unit_price'] = BcHelper::div($value['old_amount'], $value['old_customer']);
            $value['old_customer_frequency_price'] = BcHelper::div($value['received_amount'], ($value['old_customer_frequency']));
            $value['loss_rate'] = BcHelper::div($value['loss_num'], ($value['new_customer'] + $value['loss_num'])) . '%';
            $value['total_price'] = BcHelper::div($value['received_amount'], ($value['new_customer'] + $value['loss_num']));
            $value['full_load_rate'] = BcHelper::percentage($value['new_customer'], $value['customer_service_num']);
            $value['refund_rate'] = BcHelper::percentage($value['refund_amount'], $value['received_amount']);
        }

        $totalData = $totalDataQuery->asArray()->limit('')->groupBy('')->orderBy('')->one();
        $totalData['store_id'] = '-';
        $totalData['area_text'] = '-';
        $totalData['store_code'] = '-';
        $totalData['store_name'] = '-';
        $totalData['date'] = '-';
        
        $totalData['new_average'] = BcHelper::div($totalData['received_amount'], $totalData['new_customer']);
        $totalData['new_unit_price'] = BcHelper::div($totalData['new_transaction_amount'], $totalData['new_customer']);
        $totalData['old_unit_price'] = BcHelper::div($totalData['old_amount'], $totalData['old_customer']);
        $totalData['old_customer_frequency_price'] = BcHelper::div($totalData['received_amount'], ($totalData['old_customer_frequency']));
        $totalData['loss_rate'] = BcHelper::div($totalData['loss_num'], ($totalData['new_customer'] + $totalData['loss_num'])) . '%';
        $totalData['total_price'] = BcHelper::div($totalData['received_amount'], ($totalData['new_customer'] + $totalData['loss_num']));
        $totalData['teacher_num'] = $totalTeacherNum;
        $totalData['customer_service_num'] = $totalCustomerServiceNum;
        $totalData['reschedule_num'] = $totalRescheduleNum;
        $totalData['full_load_rate'] = BcHelper::percentage($totalData['new_customer'], $totalData['customer_service_num']);
        $totalData['refund_rate'] = BcHelper::percentage($totalData['refund_amount'], $totalData['received_amount']);
        array_unshift($list, $totalData);

        return [$list, $totalCount];
    }

    /**
     *  门店业绩分析
     *
     * @param string $date 2022-10-01
     * @param int $entityId
     * @param int $storeId 1
     * @return bool
     * @throws \yii\db\Exception
     */
    public static function dataInitial(string $date = '', int $entityId, int $storeId = 0)
    {
        $date = !empty($date) ? $date : date("Y-m-d");
        $dateTime = $startDate = strtotime($date . ' 00:00:00');
        $endDate = strtotime($date . ' 23:59:59');
        

        $conditions = " order_status in (5,8) "
            . " AND h.plan_time BETWEEN {$startDate} AND {$endDate} AND h.entity_id = {$entityId}";
        if ($storeId > 0) {
            $conditions .= " AND h.store_id = $storeId";
        }

        $channelField = '';
        $channelIds = ChannelService::notNewCusAdsFrom(true, $entityId);
        if ($channelIds) {
            $channelField = " AND h.channel_id NOT IN ({$channelIds})";
        }

        $storeData = Yii::$app->db->createCommand("
            SELECT
                store_id,store_name,
                SUM(refund_amount) AS refund_amount, #门店退款金额
                SUM(real_final) AS real_final,	#门店实收
                COUNT(DISTINCT IF(is_new = 1, cus_id, NULL)) AS new_customer_deal_num,	#新客成交人数
                SUM(IF(is_new = 1, real_final, 0)) AS first_new_customer_amount,	#新客首日业绩
                COUNT(DISTINCT IF(is_new = 1, cus_id, NULL)) AS new_customer_num,	#新客人数
                COUNT(DISTINCT IF(is_new = 0, cus_id, NULL)) AS old_customer_num,	#老客到店人数
                SUM(IF(is_new = 0, real_final, 0)) AS old_customer_amount,	#老客到店业绩
                COUNT(DISTINCT IF(is_new = 0, day_customer, NULL)) AS old_customer_deal_frequency	#老客到店人次
            FROM
            (
                SELECT
                    h.store_id,s.store_name,h.cus_id,h.channel_id,h.refund_amount,
                    (
                        IFNULL(h.received_amount, 0) + IFNULL(h.card_real_amount, 0) + IFNULL(h.group_amount, 0)
                    ) AS real_final,	#门店实收（门店实收 + 储值卡实耗 + 团购收款）
                    IF(c.first_visit_time BETWEEN {$startDate} AND {$endDate} {$channelField}, 1, 0) AS is_new,
                    CONCAT(h.cus_id, '-', FROM_UNIXTIME(h.plan_time, '%Y%m%d')) AS day_customer	#区分到店人次
                FROM
                    {{%order_header}} h
                    LEFT JOIN {{%customer}} `c` ON h.cus_id = c.id
                    LEFT JOIN {{%store}} `s` ON h.store_id = s.id 
                WHERE
                    {$conditions}
            ) a
            GROUP BY store_id
        
        ");
        $storeData = $storeData->queryAll();
        if (empty($storeData)) {
            static::$modelClass::deleteAll(['date' => $dateTime, 'entity_id' => $entityId]);
            CustomerChurnRemarkService::setStoreLossNum($startDate, $endDate, $entityId);
            return true;
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $currentStoreIds = ArrayHelper::getColumn($storeData, 'store_id');
            $oldStoreIds = static::$modelClass::find()
                ->select('store_id')
                ->andWhere([
                    'date' => $dateTime,
                    'entity_id' => $entityId
                ])
                ->column();
            $deleteStoreIds = array_diff($oldStoreIds, $currentStoreIds);
            if (!empty($deleteStoreIds)) {
                static::$modelClass::deleteAll(['store_id' => $deleteStoreIds, 'date' => $dateTime, 'entity_id' => $entityId]);
                foreach ($deleteStoreIds as $deleteStoreId) {
                    CustomerChurnRemarkService::setStoreLossNum($startDate, $endDate, $entityId, $deleteStoreId);
                }
            }

            foreach ($storeData as $val) {
                $storeId = $val['store_id'] ?? 0;
                $model = static::$modelClass::find()
                    ->andWhere([
                        'store_id' => $storeId,
                        'date' => $dateTime,
                        'entity_id' => $entityId
                    ])
                    ->one();

                if (empty($model)) {
                    $model = new static::$modelClass;
                    $model->date = $dateTime;
                    $model->store_id = $storeId;
                    $model->entity_id = $entityId;
                }
                $model->received_amount = $val['real_final'] ?? 0;
                $model->refund_amount = $val['refund_amount'] ?? 0;
                $model->new_customer = $val['new_customer_num'];
                $model->new_transaction_num = $val['new_customer_deal_num'];
                $model->new_transaction_amount = $val['first_new_customer_amount'];
                $model->old_customer = $val['old_customer_num'];
                $model->old_customer_frequency = $val['old_customer_deal_frequency'];
                $model->old_amount = $val['old_customer_amount'];
                if (!$model->save()) {
                    throw new Exception(current($model->getFirstErrors()));
                }
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            Yii::$app->notice->happy('门店业绩分析保存失败，msg：' . $e->getMessage());
        }
        return true;
    }

    /**
     * 获取StoreSchedule表的统计数据
     * 
     * @param array $params 查询参数
     * @param array $storeIds 通过 area_id 获取的门店ID数组
     * @param bool $groupByDate 是否按日期分组
     * @return array 返回包含scheduleData（索引化数据）和总计数据的数组
     */
    private static function getStoreScheduleData(array $params, array $storeIds = [], bool $groupByDate = false)
    {
        $selectFields = [
            'store_id',
            'SUM(teacher_num) as teacher_num',
            'SUM(customer_service_num) as customer_service_num',
            'SUM(reschedule_num) as reschedule_num'
        ];
        
        $groupByFields = ['store_id'];
        
        // 如果需要按日期分组，添加格式化的日期字段
        if ($groupByDate) {
            $selectFields[] = new Expression('DATE_FORMAT(STR_TO_DATE(date, "%Y%m%d"), "%Y-%m-%d") as formatted_date');
            $groupByFields = [
                new Expression('STR_TO_DATE(date, "%Y%m%d")'),
                'store_id'
            ];
        }
        
        $scheduleQuery = StoreSchedule::find()
            ->select($selectFields)
            ->andWhere([
                'BETWEEN',
                new Expression('UNIX_TIMESTAMP(STR_TO_DATE(date, "%Y%m%d"))'),
                $params['start_time'],
                $params['end_time']
            ])
            ->andFilterWhere(['store_id' => $params['store_id']]);
            
        // 如果传入了storeIds，则过滤门店
        if (!empty($storeIds)) {
            $scheduleQuery->andFilterWhere(['store_id' => $storeIds]);
        }
        
        $scheduleQuery = $scheduleQuery->groupBy($groupByFields)
            ->asArray()
            ->all();
        
        // 初始化统计数据
        $scheduleData = [];
        $totalTeacherNum = 0;
        $totalCustomerServiceNum = 0;
        $totalRescheduleNum = 0;
        
        // 处理查询结果并计算总计
        foreach ($scheduleQuery as $item) {
            $totalTeacherNum += $item['teacher_num'];
            $totalCustomerServiceNum += $item['customer_service_num'];
            $totalRescheduleNum += $item['reschedule_num'];
            
            if ($groupByDate) {
                // 按日期+门店ID组合索引化
                $key = $item['formatted_date'] . '_' . $item['store_id'];
                $scheduleData[$key] = $item;
            } else {
                // 按门店ID索引化
                $scheduleData[$item['store_id']] = $item;
            }
        }
        
        return [
            'scheduleData' => $scheduleData,
            'totalTeacherNum' => $totalTeacherNum,
            'totalCustomerServiceNum' => $totalCustomerServiceNum,
            'totalRescheduleNum' => $totalRescheduleNum
        ];
    }
}
