<?php

namespace auth\services\feishu;

use common\components\Feishu;
use Exception;
use Yii;

class ToolService
{
    /**
     * 上传图片到多维表格
     * @param array $arrImageUrl 图片地址数组,且图片是URL
     * @param string $table_file_token 多维表格文件token
     * @return array
     * @throws Exception
     */
    public static function uploadImageUrlToMultidimensionalTable($arrImageUrl, $table_file_token ,$imgDir)
    {
        $data = [];
        $feishu = new Feishu();
        foreach ($arrImageUrl as $i => $imageUrl) {
            $fileName = $i . '_multidimensionalTable_' . time() . '_' . round(microtime(true) * 1000) . '.jpg';

            if (!is_dir($imgDir)) {
                mkdir($imgDir, 0777, true);
            }

            $tmpFile = $imgDir . '/' . $fileName;
            file_put_contents($tmpFile, file_get_contents($imageUrl));

            $uploadRes = $feishu->uploadMedia($tmpFile, 'bitable_image', $table_file_token, $fileName);
            @unlink($tmpFile);

            if ($uploadRes['code'] != 0) {
                throw new Exception('上传图片失败：' . $feishu->error_msg);
            }

            $data[] = $uploadRes;
        }

        return $data;
    }

    /**
     * 将图片地址数组转换为多维表格图片字段数组
     * @param array $data 图片地址数组
     * @return array
     */
    public static function realToMultidimensionalTableImage($data)
    {
        if (empty($data)) {
            return [];
        }

        $reslut = [];
        foreach ($data as $i) {
            $reslut[]['file_token'] = $i['data']['file_token'];
        }

        return $reslut;
    }
}
