<?php

namespace auth\services;

use auth\models\Customer;
use auth\models\order\CustomerChurnRemark;
use backendapi\models\CustomerLog;
use backendapi\models\wxcom\CusTag;
use common\enums\order\OrderHeaderStatusEnum;
use common\helpers\ArrayHelper;
use common\helpers\DateHelper;
use common\helpers\ResultHelper;
use common\models\backend\order\OrderHeader;
use common\models\wxcom\CusCustomer;
use common\models\wxcom\CusCustomerUser;
use common\models\wxcom\User;
use common\services\CustomerService as CommonCustomerService;
use console\models\WxcomCusCustomer;
use console\models\WxcomCusCustomerUser;
use Exception;
use Yii;
use yii\db\Query;

class CustomerService extends CommonCustomerService
{
    /**
     * @var Customer
     */
    public static $modelClass = Customer::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        if ($params['search_customer']) {
            $query->andWhere(['or', ['like', 'name', $params['search_customer']], ['like', 'mobile', $params['search_customer']]]);
        }
        $query->andFilterWhere(['store_id' => $params['store_id']]);
        if ($params['created_start_time'] && $params['created_end_time']) {
            $query->andFilterWhere(['BETWEEN', 'created_at', strtotime($params['created_start_time']), strtotime($params['created_end_time'] . ' 23:59:59')]);
        }
        $query->with(['store']);
        return $query;
    }

    public static function getInfoById($id)
    {
        static::$modelClass::setExtendAttrs([
            'store.store_name' => 'store_name',
            'channel.name' => 'channel_name',
        ]);
        return parent::getInfoById($id);
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andFilterWhere(['like', 'id', $params['keyword']]);
            // $query->andWhere(['or', ['like', 'id', $params['keyword']], ['like', 'wxcom_user_id', $params['keyword']]]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    /**
     * 获取客户详情
     *
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function getView($id)
    {
        $wxcom_user_id = Yii::$app->request->get('wxcom_user_id', 0);
        $customer = static::$modelClass::find()
            ->select('c.id,c.wxcom_cus_id,c.name,c.gender,c.age_bracket,c.birthday,c.avatar,c.nick_name,c.mobile,c.address,c.remark as family_info,c.first_store_time,s.store_name,pc.name AS channel_name,ce.is_focus,ce.add_time AS add_time,ce.first_pay_time,ce.last_store_time,ccu.tag_ids')
            ->alias('c')
            ->leftJoin('{{%customer_extends}} ce', 'ce.cus_id = c.id')
            ->leftJoin('{{%store}} s', 's.id = c.store_id')
            ->leftJoin('{{%promote_channel}} pc', 'pc.id = c.channel_id')
            ->leftJoin('{{%wxcom_cus_customer_user}} ccu', 'ccu.cus_id = c.id')
            ->where(['c.id' => $id])
            ->andWhere(['c.entity_id' => Yii::$app->user->identity->current_entity_id])
            ->asArray()->one();
        if (!$customer) throw new Exception('用户不存在，请认真核对！');

        $customer['add_time'] = DateHelper::toDate($customer['add_time']);
        $customer['tag_ids'] = $customer['tag_ids'] ?? '';
        $customer['is_focus'] = $customer['is_focus'] ?? '';
        $customer['store_name'] = $customer['store_name'] ?? '';
        $customer['first_pay_time'] = $customer['first_pay_time'] ?? '';
        $customer['last_store_time'] = $customer['last_store_time'] ?? '';
        $customer['first_store_time_text'] = DateHelper::toDate($customer['first_store_time']);

        $customer_log = CustomerLog::find()->select('id,cus_id,user_id,type,remark,extends,created_at')->where(['cus_id' => $customer['id']])->asArray()->all();
        foreach ($customer_log as &$v) {
            $v['extends'] = $v['extends'] ? json_decode($v['extends'], true) : [];
            $v['time'] = DateHelper::toDate($v['created_at'], 'H:i:s');
            $v['years'] = DateHelper::toDate($v['created_at'], 'Y-m-d');
            $v['created_at'] = DateHelper::toDate($v['created_at']);
        }
        $customer['customer_log'] = $customer_log;

        $customer['tag_name_array'] = $customer['tag_ids'] ? CusTag::find()->select('name')->where(['id' => explode(',', $customer['tag_ids'])])->asArray()->column() : [];
        $customer['mobile'] = ResultHelper::mobileEncryption($customer['mobile']);
        $customer['age'] = Customer::getAge($customer['birthday']);
        $customer['is_add_wecom'] = 0;
        if ($wxcom_user_id && $customer['wxcom_cus_id']) {
            $str_wxcom_user_id = User::find()->select('wxcom_user_id')->where(['id' => $wxcom_user_id])->scalar();
            if ($str_wxcom_user_id) {
                $isExists = CusCustomerUser::find()->where(['cus_id' => $customer['wxcom_cus_id'], 'wxcom_user_id' => $str_wxcom_user_id])->exists();
                if ($isExists) {
                    $customer['is_add_wecom'] = 1;
                }
            }
        }
        return $customer;
    }

    /**
     * 获取客户订单信息
     *
     * @param array $params
     * @return array
     * @throws Exception
     */
    public static function getCustomerOrder($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query = OrderHeader::find()->select(['h.id,h.order_no,h.deposit,s.store_name,GROUP_CONCAT(DISTINCT IF(op.package_id>0,op.package_name,op.goods_name) SEPARATOR "，") AS project,h.order_status,h.pay_amount,m.username AS plan_name,h.plan_by,h.plan_time,h.created_by,cm.username AS created_by_text,h.created_at,h.pre_pay_status'])
            ->alias('h')
            ->leftJoin('{{%store}} s', 's.id = h.store_id')
            ->leftJoin('{{%order_project}} op', 'op.order_id = h.id')
            ->leftJoin('{{%backend_member}} m', 'm.id = h.plan_by')
            ->leftJoin('{{%backend_member}} cm', 'cm.id = h.created_by')
            ->where(['cus_id' => $params['user_id']])
            ->andFilterWhere(['h.entity_id' => Yii::$app->user->identity->current_entity_id])
            ->groupBy('h.id')
            ->orderBy('h.plan_time DESC');

        $totalCount = $query->count();
        $orders = $query->offset($offset)->limit($limit)->asArray()->all();
        foreach ($orders as &$v) {
            $v['order_status'] = OrderHeaderStatusEnum::getValue($v['order_status']);
            $v['plan_time'] = DateHelper::toDate($v['plan_time']);
            $v['created_at'] = DateHelper::toDate($v['created_at']);
        }

        return [$orders, $totalCount];
    }

    /**
     * 获取列表
     *
     * @param array $params
     * @return array
     * @throws Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'store.store_name' => 'store_name',
            'created_at_text',
            'mobile_text' => 'mobile',
        ]);
        return parent::search($params);
    }

    /**
     * 获取用户详情用于订单新增的
     *
     * @param $params
     * @return array|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function getInfoByOrder($params)
    {
        $info = static::$modelClass::find()
            ->select('id as customer_id,wxcom_cus_id,channel_id,name,nick_name,avatar,gender,code,unionid')
            ->where(['mobile_code' => $params['mobile_code']])
            ->andWhere(['mobile' => $params['mobile']])
            ->andFilterWhere(['entity_id' => Yii::$app->user->identity->current_entity_id])
            ->asArray()
            ->one();
        if (!$info) return [];

        $wxComUnion = CusCustomer::find()
            ->select('unionid')
            ->andWhere([
                'id' => $params['wxcom_cus_id'],
                'entity_id' => Yii::$app->user->identity->current_entity_id
            ])
            ->scalar();
        if ($info['wxcom_cus_id'] && $info['unionid'] != $wxComUnion) {
            throw new Exception('客户微信信息与当前微信信息不符！');
        }

        //客户企微备注
        $wxcomCusCustomerUserInfo = $info['wxcom_cus_id'] ? WxcomCusCustomerUser::find()
            ->select('id,remark')
            ->where(['cus_id' => $info['wxcom_cus_id']])
            ->andFilterWhere(['entity_id' => Yii::$app->user->identity->current_entity_id])
            ->asArray()
            ->one() : '';
        $info['wxcom_cus_remark'] = isset($wxcomCusCustomerUserInfo['remark']) ? $wxcomCusCustomerUserInfo['remark'] : '';

        return $info;
    }

    /**
     * 获取用户列表用于订单新增的
     *
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getListByOrder()
    {
        $list = WxcomCusCustomerUser::find()
            ->select('cus_id as wxcom_cus_id,remark as wxcom_cus_remark')
            ->where(['is_deleted' => 0])
            ->andFilterWhere(['entity_id' => Yii::$app->user->identity->current_entity_id])
            ->asArray()
            ->all();
        if ($list) {
            $cusIdArr = array_unique(array_column($list, 'wxcom_cus_id'));
            $cusList = WxcomCusCustomer::find()
                ->select('id,name as nick_name,avatar,gender')
                ->where(['id' => $cusIdArr])
                ->andFilterWhere(['entity_id' => Yii::$app->user->identity->current_entity_id])
                ->asArray()
                ->all();
            $cusData = [];
            foreach ($cusList as $value) {
                $cusData[$value['id']] = $value;
            }

            foreach ($list as &$item) {
                $item['customer_id'] = 0;
                $item['nick_name'] = $item['avatar'] = $item['gender'] = '';
                if (isset($cusData[$item['wxcom_cus_id']])) {
                    $item['nick_name'] = $cusData[$item['wxcom_cus_id']]['nick_name'];
                    $item['avatar'] = $cusData[$item['wxcom_cus_id']]['avatar'];
                    $item['gender'] = $cusData[$item['wxcom_cus_id']]['gender'];
                }
            }
        }

        return $list;
    }

    /**
     * 获取客户详情及企微账号信息 by 企微UserID
     *
     * @param $wxcomUserId
     * @return array
     * @throws Exception
     */
    public static function getInfoByWxcomUserId($wxcomUserId)
    {
        $wxcomCustomer = CusCustomer::find()->where(['external_user_id' => $wxcomUserId])->one();
        if (!$wxcomCustomer) {
            throw new Exception('找不到对应企微客户信息');
        }

        $customer = static::$modelClass::find()->andWhere(['unionid' => $wxcomCustomer->unionid])->one();
        return [
            'customer' => $customer,
            'wxcom_customer' => $wxcomCustomer,
            'code' => $customer ? $customer->code : '',
        ];
    }

    //门店添加客户
    public static function storeCreate($params)
    {
        $customer = static::$modelClass::find()->where(['mobile_code' => $params['mobile_code'], 'mobile' => $params['mobile']])->one();
        if ($customer) {
            throw new Exception('客户信息已存在');
        }
        $model = new static::$modelClass();
        $model->mobile = $params['mobile'];
        $model->mobile_code = $params['mobile_code'];
        $model->channel_id = $params['channel_id'];
        $model->name = $params['name'];
        $model->store_id = $params['store_id'];
        if (!$model->save(false)) throw new Exception(current($model->getFirstErrors()));
        $model->avatar = '';
        $model->mobile = ResultHelper::mobileEncryption($model->mobile);
        return $model;
    }

    //门店搜索客户列表
    public static function storeSearchList($params)
    {
        static::$modelClass::setExtendAttrs([
            'mobileStar' => 'mobile',
            'customerProductNum' => 'product_num',
        ]);
        $query = static::$modelClass::find();
        $query->select('id,name,mobile_code,mobile,avatar,store_id,channel_id');
        if (!$params['keyword']) {
            return [[], 0];
        }
        $query->andWhere([
            'OR',
            ['=', 'name', $params['keyword']],
            ['=', 'mobile', $params['keyword']],
        ]);
        $totalCount = $query->limit(10)->count();
        $orders = $query->all();
        return [$orders, $totalCount];
    }

    /**
     * 设置客户首次订单完成时间
     *
     * @param $cusId
     * @return bool
     * @throws Exception
     */
    public static function updateCustomerFirstStoreTime($cusId)
    {
        $customer = static::$modelClass::find()
            ->andWhere(['id' => $cusId])
            ->one();

        if (empty($customer)) {
            throw new Exception('更新客户首次订单完成时间时间：客户不存在');
        }

        $first_store_time = OrderHeader::find()
            ->select('plan_time')
            ->andWhere([
                'cus_id' => $cusId,
                'order_status' => OrderHeaderStatusEnum::STATUS_COMPLETED
            ])
            ->orderBy('plan_time ASC')
            ->scalar();

        $first_store_time = $first_store_time ?: 0;
        //当前订单预约时间小于客户首次到店时间
        if ($customer->first_store_time == $first_store_time) {
            return true;
        }

        $customer->first_store_time = $first_store_time;
        if (!$customer->save()) {
            throw new Exception('更新客户首次订单完成时间失败：' . current($customer->getFirstErrors()));
        }

        return true;
    }

    /**
     * 设置客户首次到店时间
     *
     * @param $cusId
     * @return bool
     * @throws Exception
     */
    public static function setCustomerFirstVisitTime($cusId)
    {
        $customer = static::$modelClass::find()->andWhere(['id' => $cusId])->one();

        if (empty($customer)) {
            throw new Exception('更新客户首次到店时间：客户不存在');
        }

        Yii::info('设置客户首次到店时间', 'setCustomerFirstVisitTime');
        // 子查询 1：从订单表中获取已完成订单的预约时间
        $subQuery1 = OrderHeader::find()
            ->select(['cus_id AS customer_id', 'plan_time AS first_plan_time'])
            ->where(['order_status' => OrderHeaderStatusEnum::STATUS_COMPLETED])
            ->andWhere(['cus_id' => $cusId]);

        // 子查询 2：从流失表关联订单表获取客户 ID 和预约时间
        $subQuery2 = CustomerChurnRemark::find()
            ->alias('c')
            ->select(['o.cus_id AS customer_id', 'c.plan_time AS first_plan_time'])
            ->innerJoin(['o' => OrderHeader::tableName()], 'c.order_id = o.id')
            ->where(['c.reach_status' => 2])
            ->andWhere(['o.cus_id' => $cusId]);

        // 合并两个子查询
        $combinedQuery = (new Query())
            ->from(['combined_data' => $subQuery1->union($subQuery2)])
            ->where(['customer_id' => $cusId])
            ->select(['MIN(first_plan_time) AS first_visit_time']);

        // 执行查询并获取结果
        $first_visit_time = $combinedQuery->scalar();
        $first_visit_time = $first_visit_time ? $first_visit_time : 0;

        if ($customer->first_visit_time == $first_visit_time) {
            return true;
        }

        $customer->first_visit_time = $first_visit_time;
        if (!$customer->save()) {
            throw new Exception('更新客户首次到店时间：' . current($customer->getFirstErrors()));
        }

        return true;
    }
}
