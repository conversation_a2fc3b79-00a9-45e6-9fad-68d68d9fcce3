<?php

namespace auth\services;

use auth\models\pay\StoreUnionPay;
use auth\models\PayRecord;
use auth\models\Store;
use Exception;
use auth\models\UnionPay;
use common\components\PayFactory;
use common\enums\pay\UnionPayBillNoTitleEnum;
use common\enums\StatusEnum;
use common\helpers\ArrayHelper;
use common\helpers\BcHelper;
use common\helpers\DateHelper;
use common\helpers\Tool;
use common\models\StorePayRecord;
use common\services\UnionPayService as CommonUnionPayService;
use Yii;
use yii\helpers\Url;

class UnionPayService extends CommonUnionPayService
{
    /**
     * @var UnionPay
     */
    public static $modelClass = UnionPay::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        $query->andFilterWhere(['like', 'name', trim($params['name'])]);
        $query->andFilterWhere(['like', 'licence', trim($params['licence'])]);
        $query->andFilterWhere(['status' => $params['status']]);
        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        UnionPay::setExtendAttrs([
            'tag_name_array',
        ]);

        UnionPay::setShowAttrs([
            "id",
            "name",
            "licence",
            "status",
            "type"
        ], true);

        $query = static::getQuery($params);

        $totalCount = $query->count();
        $list = $query->orderBy('status desc, sort desc, id desc')->all();

        $list = ArrayHelper::toArray($list);
        if (empty($params['startTime']) || empty($params['endTime'])) {
            $thisMonth = DateHelper::thisMonth();
            $params['startTime'] = $thisMonth['start'];
            $params['endTime'] = $thisMonth['end'];
        }

        foreach ($list as &$item) {
            $item['amount'] = static::getUnionPayAmount($item['id'], $item['type'], $params['startTime'], $params['endTime']);
        }

        return [$list, $totalCount];
    }

    public static function getUnionPayAmount($id, $type, $startTime, $endTime)
    {
        $amountService = 0;
        if ($type == 1) { //客服收款金额
            $amountService = static::getServiceUnionPayAmount($id, $startTime, $endTime);
        }

        //门店收款金额
        $amountStore = static::getStoreUnionPayAmount($id, null, $startTime, $endTime);

        $total_fee = BcHelper::add($amountService, $amountStore);
        return $total_fee;
    }

    /**
     * 获取客服收款金额
     */
    public static function getServiceUnionPayAmount($id, $startTime, $endTime)
    {
        $amountService = PayRecord::find()
            ->select('sum(total_fee) as total_fee')
            ->where(['mch_id' => $id])
            ->andFilterWhere(['between', 'pay_time', $startTime, $endTime])
            ->scalar();

        return $amountService ?: 0;
    }

    /**
     * 获取门店收款金额
     */
    public static function getStoreUnionPayAmount($id, $storeId, $startTime, $endTime)
    {
        //门店收款金额
        $amountStore = StorePayRecord::find()
            ->select('sum(amount) as total_fee')
            ->where(['union_pay_id' => $id])
            ->andFilterWhere(['store_id' => $storeId])
            ->andFilterWhere(['between', 'pay_time', $startTime, $endTime])
            ->scalar();

        return $amountStore ?: 0;
    }

    /**
     * 获取用户收款金额
     */
    public static function getUserUnionPayAmount($user_id, $startTime, $endTime)
    {
        $amount = StorePayRecord::find()
            ->select('sum(amount) as total_fee')
            ->where(['user_id' => $user_id])
            ->andFilterWhere(['between', 'pay_time', $startTime, $endTime])
            ->scalar();

        return $amount ?: 0;
    }

    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andFilterWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        $info->pay_tag_ids = Tool::getStringToArray($info->pay_tag_ids);
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name');
        $query->andFilterWhere(['status' => $params['status']]);
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']]
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    public static function getStoreListForSelect($params)
    {
        $store_id = ArrayHelper::getValue($params, 'store_id', -1);
        $list = static::$modelClass::find()
            ->alias('up')
            ->select('up.id,up.name')
            ->leftJoin(['sup' => StoreUnionPay::tableName()], 'sup.union_pay_id = up.id')
            ->where(['sup.store_id' => $store_id])
            ->andWhere(['up.status' => StatusEnum::ENABLED])
            ->asArray()
            ->all();

        return $list;
    }

    public static function getUserQrcode()
    {
        $storeId = Yii::$app->request->post('store_id', 0);
        $user_id = Yii::$app->request->post('user_id', 0);
        $money = Yii::$app->request->post('money', 0);
        $remark = Yii::$app->request->post('remark', '');
        $union_pay_id = Yii::$app->request->post('union_pay_id', null);

        if ($money <= 0) {
            throw new Exception('收款金额必须大于0');
        }

        $arrConfig = Store::find()->alias('s')
            ->select('s.store_name,up.id as union_pay_id,up.bill_no_title,up.app_id,up.app_key,up.mid,up.tid,up.merchantCode,up.terminalCode,su.rate')
            ->leftJoin(['su' => StoreUnionPay::tableName()], 's.id = su.store_id')
            ->leftJoin(['up' => UnionPay::tableName()], 'su.union_pay_id = up.id')
            ->where(['s.id' => $storeId])
            ->andFilterWhere(['up.id' => $union_pay_id])
            ->asArray()
            ->all();

        if (empty($arrConfig)) {
            throw new Exception('该门店未设置收款配置，请联系管理人员');
        }

        $store_name = $arrConfig[0]['store_name'];
        $count = count($arrConfig);
        if ($count > 1) {
            $thisMonth = DateHelper::thisMonth();

            $arrMonthAmount = [];
            //取收款金额最小的收款配置
            foreach ($arrConfig as $k => $v) {
                $monthAmount = static::getStoreUnionPayAmount($v['union_pay_id'], $storeId, $thisMonth['start'], $thisMonth['end']);
                $arrMonthAmount[$k] = [
                    'amount' => $monthAmount,
                    'rate' => $v['rate'] / 100,
                    'key' => $k
                ];
            }
            $minKey = static::getBestKey($arrMonthAmount, $money);
        } else {
            $minKey = 0;
        }
        $config = $arrConfig[$minKey];

        $srcReserve = [
            'store_id' => $storeId,
            'user_id' => $user_id,
            'member_id' => Yii::$app->user->identity->id,
            'content' => $remark
        ];

        $callbackUrl = Url::to(['apis/pay-callback/union'], true);
        $callbackUrl = str_replace('/mobileapi', '', $callbackUrl);
        $data = ['notify_url' => $callbackUrl, 'billDesc' => $store_name, 'srcReserve' => json_encode($srcReserve, 256), 'type' => 'getQrcode'];
        $model = PayFactory::getInstance()->getPayment('getQrcode', $config, $data);
        $model->content['totalAmount'] = BcHelper::mul($money, 100, 0);
        $expireTime = time() + 60 * 60 * 24 * 30; //有效时长30天
        $model->content['expireTime'] = date('Y-m-d H:i:s', $expireTime);

        $billNo = ($config['bill_no_title'] ?: UnionPayBillNoTitleEnum::TITLE_14JK) . date('YmdHis', time()) . $storeId . $user_id;

        $billNo = substr($billNo, 0, 28);
        $model->content['billNo'] = $billNo;
        $model->content['billDate'] = date('Y-m-d', time());

        $res = $model->getQrcode();
        return ['userQrcodeUrl' => $res['billQRCode'], 'union_pay_id' => $config['union_pay_id']];
    }

    public static function getBestKey($arrMonthAmount, $recharge)
    {
        $minTotalDiff = PHP_FLOAT_MAX;
        $targetKey = 0;

        foreach ($arrMonthAmount as $index => $item) {
            // 克隆数组，模拟本次充值加在当前项
            $simulated = $arrMonthAmount;
            $simulated[$index]['amount'] += $recharge;

            // 计算新的总金额
            $newTotal = array_sum(array_column($simulated, 'amount'));

            // 计算所有项的新占比和目标占比的差值总和
            $totalDiff = 0;
            foreach ($simulated as $i) {
                $newRate = $i['amount'] / $newTotal;
                $totalDiff += abs($newRate - $i['rate']);
            }

            if ($totalDiff < $minTotalDiff) {
                $minTotalDiff = $totalDiff;
                $targetKey = $item['key'];
            }
        }

        return $targetKey;
    }
}
