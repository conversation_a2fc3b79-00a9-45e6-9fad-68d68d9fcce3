<?php

namespace auth\services\wxcom;

use auth\models\Customer;
use auth\models\Store;
use auth\models\wxcom\CustomerServiceLog;
use common\enums\order\OrderHeaderStatusEnum;
use common\helpers\ArrayHelper;
use common\helpers\DateHelper;
use common\models\backend\order\OrderHeader;
use common\models\backend\order\OrderProject;
use common\services\wxcom\CustomerServiceLogService as CommonCustomerServiceLogService;
use console\models\WxcomUser;
use DateTime;
use Exception;
use mobileapi\models\order\CustomerChurnRemark;
use Yii;
use yii\db\Query;

class CustomerServiceLogService extends CommonCustomerServiceLogService
{
    /**
     * @var CustomerServiceLog
     */
    public static $modelClass = CustomerServiceLog::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        $query->andFilterWhere(['status' => $params['status']]);
        if ($params['keyword']) {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        $cus_id = ArrayHelper::getValue($params, 'cus_id', 0);
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
            'service_at_text',
            'responsible_text',
            'content_text',
            'wxcom_user.avatar' => 'avatar',
        ]);

        $query = static::getQuery($params);
        $query->alias('l');
        $query->leftJoin('{{%department_assignment}} ds', 'ds.user_id = l.responsible_id');
        $query->with(['createdPerson', 'updatedPerson']);
        $query->where(['l.entity_Id' => \Yii::$app->user->identity->current_entity_id]);
        $query->andWhere(['l.cus_id' => $cus_id]);

        $totalCount = $query->count();
        $list = $query->orderBy('l.service_at DESC,l.id DESC')->all();

        return [$list, $totalCount];
    }

    public static function getInfoById($id)
    {
        static::$modelClass::setExtendAttrs([
            'customer.mobile' => 'mobile',
            'customer.name' => 'name',
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
            'service_at_text',
            'responsible_text',
            'content_text',
            'accessory',
        ]);
        $query = static::$modelClass::find();
        $query->with(['createdPerson', 'updatedPerson', 'customer']);
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    public static function changerResponsible($wxcom_cus_id, $responsible_id)
    {
        if (empty($wxcom_cus_id) || empty($responsible_id)) {
            return false;
        }

        $ids = Customer::find()->select('id')->where(['wxcom_cus_id' => $wxcom_cus_id])->column();
        if (empty($ids)) return true;

        CustomerServiceLog::updateAll(['responsible_id' => $responsible_id], ['cus_id' => $ids]);
        return true;
    }

    /**
     * 获取客户服务日志列表
     */
    public function list($params = [])
    {
        $page = $params['page'] - 0 ?: 1;
        $limit = $params['limit'] - 0 ?: 10;
        $offset = ($page - 1) * $limit;

        $unionQuery = $this->listQuery($params);

        $totalCount = $unionQuery->count();
        if (!$totalCount) {
            return [[], $totalCount];
        }

        // 执行查询
        $unionQuery->offset($offset)->limit($limit)->orderBy('u.cus_id ASC');
        $list = $unionQuery->all();

        $startTime = strtotime($list[0]['plan_time']);
        $endTime = $startTime + 86400 - 1;

        foreach ($list as &$item) {
            $item['id'] = 0;
            $log_id = static::$modelClass::find()
                ->select('id')
                ->where([
                    'cus_id' => $item['cus_id'],
                    'responsible_id' => Yii::$app->user->identity->id,
                    'order_id' => $item['order_id'],
                    'entity_id' => Yii::$app->user->identity->current_entity_id,
                ])
                ->andWhere(['between', 'service_at', $startTime, $endTime])
                ->scalar();
            if ($log_id) {
                $item['id'] = $log_id;
            }
        }

        return [$list, $totalCount];
    }

    /**
     * 获取日期数据列表
     */
    public function dateList($params = [])
    {
        $dates = $this->getDates($params);
        unset($params['start_time'], $params['end_time']);
        $list = [];
        //返回$status 0 代表没有数据 1代表未完成 2代表已完成
        foreach ($dates as $date) {
            $params['time'] = strtotime($date);
            $query = $this->listQuery($params);
            $totalCount = $query->count();
            $completedCount = 1;

            $status = 0;
            if ($totalCount > 0) {
                $status = ($totalCount - $completedCount) > 0 ? 1 : 2;
            }
            $list[] = [
                'date' => $date,
                'status' => $status
            ];
        }
        return $list;
    }

    public function getDates($params)
    {
        $startTime = $params['start_time'] ?? '';
        $endTime = $params['end_time'] ?? '';

        if (empty($startTime) || empty($endTime)) {
            $currentDate = new DateTime();
            $startDate = (clone $currentDate)->modify('-3 days'); // 前3天
            $endDate = (clone $currentDate)->modify('+3 days');   // 后3天

            // 生成日期范围数组
            $dates = [];
            while ($startDate <= $endDate) {
                $dates[] = $startDate->format('Y-m-d'); // 格式化日期为 YYYY-MM-DD
                $startDate->modify('+1 day');           // 日期加1天
            }
        } else {
            $dates = DateHelper::createDateRange(date('Y-m-d', $startTime), date('Y-m-d', $endTime));
        }

        return $dates;
    }

    public function listQuery($params)
    {
        $date = $params['time'] ? date('Y-m-d', $params['time']) : date('Y-m-d', time());
        $startTime = strtotime($date);
        $endTime = $startTime + 86400 - 1;
        //流失客户数据
        $query1 = (new Query())
            ->select([
                'cus_id' => 'oh.cus_id',
                'order_id' => 'r.order_id',
                'store_id' => 'oh.store_id',
                'project_name' => 'r.project_name',
                'amount' => new \yii\db\Expression('0'),
            ])
            ->from(['r' => CustomerChurnRemark::tableName()])
            ->leftJoin(['oh' => OrderHeader::tableName()], 'r.order_id = oh.id')
            ->where([
                'r.entity_id' => 1,
                'r.reach_status' => 2,
            ])
            // ->andWhere(['r.plan_teacher_id' => Yii::$app->user->identity->id])
            ->andWhere(['between', 'r.plan_time', $startTime, $endTime]);

        //状态为：已到店、待结算、已完成的订单客户数据
        $orderStatus = [OrderHeaderStatusEnum::STATUS_ARRIVED_STORE, OrderHeaderStatusEnum::STATUS_SETTLEMENT, OrderHeaderStatusEnum::STATUS_COMPLETED];
        $query2 = (new Query())
            ->select([
                'cus_id' => 'oh.cus_id',
                'order_id' => 'oh.id',
                'store_id' => 'oh.store_id',
                'project_name' => new \yii\db\Expression("GROUP_CONCAT(DISTINCT CASE WHEN op.package_id > 0 THEN op.package_name ELSE op.goods_name END)"),
                'amount' => new \yii\db\Expression("SUM(if(oh.order_status = 5,(op.received_amount + op.group_amount),0))"),
            ])
            ->from(['oh' => OrderHeader::tableName()])
            ->leftJoin(['op' => OrderProject::tableName()], 'op.order_id = oh.id')
            ->where([
                'oh.order_status' => $orderStatus,
            ])
            // ->andWhere(['oh.plan_user_id' => Yii::$app->user->identity->id])
            ->andWhere(['between', 'oh.plan_time', $startTime, $endTime])
            ->groupBy('oh.id');

        // 合并查询
        $unionQuery = (new Query())
            ->select([
                'u.cus_id',
                'cus.name as cus_name',
                'u.order_id',
                'cus.avatar',
                's.store_name',
                'plan_time' => new \yii\db\Expression("'" . $date . "'"),
                'is_new_cus' => new \yii\db\Expression("IF(cus.first_store_time > 0 AND '" . $date . "' > FROM_UNIXTIME(cus.first_store_time,'%Y-%m-%d'),0,1)"),
                'amount' => new \yii\db\Expression("SUM(u.amount)"),
                'project_name' => new \yii\db\Expression("GROUP_CONCAT(DISTINCT(u.project_name))")
            ])
            ->from(['u' => $query1->union($query2)])
            ->leftJoin(['s' => Store::tableName()], 's.id = u.store_id')
            ->leftJoin(['cus' => Customer::tableName()], 'cus.id = u.cus_id')
            ->groupBy('u.cus_id');

        return $unionQuery;
    }

    public static function getInfoByIdTwo($id)
    {
        static::$modelClass::setExtendAttrs([
            'content_text',
            'accessory',
            'payment_screenshot',
            'remind_data'
        ]);

        static::$modelClass::setHiddenAttrs([
            'wxcom_user_id',
            'entity_id',
            'created_by',
            'updated_by',
            'created_at',
            'updated_at'
        ]);

        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }
}
