<?php

namespace auth\services\material;

use Exception;
use common\helpers\ArrayHelper;
use common\models\backend\material\Material;
use auth\models\material\ProcurementPay;
use backendapi\services\EntityService;
use common\components\Feishu;
use common\components\feishu\process\ProcurementPayProcess;
use common\enums\ProcessStatusEnum;
use common\enums\ProcurementPayTypeEnum;
use common\enums\ProcurementStatusEnum;
use common\helpers\BcHelper;
use common\helpers\DateHelper;
use common\helpers\Tool;
use common\models\backend\ProcurementDetails;
use common\models\common\Department;
use common\services\material\ProcurementPayService as CommonProcurementPayService;
use services\common\FeishuExamineService;
use Yii;

class ProcurementPayService extends CommonProcurementPayService
{
    /**
     * @var ProcurementPay
     */
    public static $modelClass = ProcurementPay::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        $query->andFilterWhere(['status' => $params['status']]);
        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
            'approval_time_text',
        ]);

        $query = static::getQuery($params);
        $query->with(['createdPerson', 'updatedPerson']);
        $query->andFilterWhere(['list_no' => trim($params['list_no'])]);
        $query->andFilterWhere(['in', 'serial_number', $params['serial_number']]);

        if ($params['approval_time_start'] && $params['approval_time_end']) { //搜索审批完成时间
            $query->andFilterWhere(['BETWEEN', 'approval_time', $params['approval_time_start'], $params['approval_time_end']]);
        }

        $totalCount = $query->count();
        $list = $query->all();

        return [$list, $totalCount];
    }

    public static function create($params)
    {
        $model = new static::$modelClass();
        if (isset($model->scenarios()['create'])) {
            $model->scenario = 'create';
        }

        $params['detail_ids'] = ArrayHelper::getColumn($params['detail_list'], 'id') ?: [];

        $feishu_attachment_code = [];
        if ($params['attachment']) {
            $attachment = [];
            foreach ($params['attachment'] as $item) {
                $attachment[] = $item['url'];
                $feishu_attachment_code[] = $item['file']['feishu_data']['code'];
            }
            $params['attachment'] = json_encode(['attachment' => $attachment, 'feishu_attachment_code' => $feishu_attachment_code], 256);
        }

        $model->attributes = $params;
        $model->status = ProcessStatusEnum::IN_REVIEW;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrMsg());
        }

        foreach ($params['detail_list'] as $item) {
            //修改采购详情
            /**@var  ProcurementDetails $detailModel */
            $detailModel = ProcurementDetails::findOne($item['id']);
            if (empty($detailModel)) {
                throw new Exception('选择的采购商品不存在，请刷新页面重试');
            }
            $detailModel->status = ProcurementStatusEnum::PAY_REVIEW;
            $detailModel->receipt_id = $model->id;
            $detailModel->total_price = $item['price'];
            $detailModel->purchasing_cost = BcHelper::div($detailModel->total_price, $detailModel->procurement_num);
            $detailModel->discount_price = $detailModel->purchasing_cost;
            if(!$detailModel->save()){
                throw new Exception($detailModel->getFirstErrMsg());
            }
        }

        //创建审批单
        static::createApproval($model, $feishu_attachment_code);

        return $model;
    }

    /**
     * 创建审批单
     */
    public static function createApproval($model, $feishu_attachment_code = [])
    {
        $reason = $model->reason_title . PHP_EOL;
        $detail = ProcurementDetails::find()->alias('pd')
            ->leftJoin('{{%procurement}} p', 'p.id = pd.pt_id')
            ->select('pd.id,pd.material_id,pd.material_name,pd.unit,pd.procurement_num,p.process_instance_id,pd.dept_id,pd.total_price')
            ->andWhere(['pd.id' => Tool::getStringToArray($model->detail_ids)])->asArray()->all();
        if (empty($detail)) {
            return true;
        }

        $materialIds = ArrayHelper::getColumn($detail, 'material_id');
        $materialSpec = Material::find()->select('id,spec')->andWhere(['id' => $materialIds])->indexBy('id')->asArray()->all();

        $connect = [];
        $arrDetail = ArrayHelper::index($detail, null, 'dept_id');

        foreach ($arrDetail as $dept_id => $item) {
            $storeName = Department::getName($dept_id);
            $count = 1;
            $total_price = 0;
            $storeInfo = '';
            foreach ($item as $v) {
                if ($v['process_instance_id']) {
                    $connect[] = $v['process_instance_id'];
                }

                $storeInfo .= $count . '、' . $v['material_name'] . '    ' . (isset($materialSpec[$v['material_id']]) ? $materialSpec[$v['material_id']]['spec'] : '') . '    ' . $v['procurement_num'] . $v['unit'] . PHP_EOL;
                $count++;
                $total_price += $v['total_price'];
            }

            $reason .= $storeName . ':（' . $total_price . '元）' . PHP_EOL;
            $reason .= $storeInfo;
        }
        $bank_info = json_decode($model->bank_info, true);
        $account = '';
        if ($bank_info) {
            foreach ($bank_info as $fields => $info) {
                $account .= $fields . ' : ' . $info . PHP_EOL;
            }
        }

        $data = [
            'feishu_userid' => Yii::$app->user->identity->feishu_userid,
            'list_no' => $model->list_no,
            'reason' => $reason,
            'amount' => $model->amount,
            'type' => ProcurementPayTypeEnum::getValue($model->pay_type),
            'date' => DateHelper::toDate($model->pay_time, 'Y-m-d'),
            'account' => $account,
            'connect' => array_values(array_unique($connect)),
            'attachment' => $feishu_attachment_code,
            'remark' => $model->remark
        ];

        $entity_id = Yii::$app->user->identity->current_entity_id;
        $feishu = ProcurementPayProcess::create($data, $entity_id);
        $model->scenario = 'approval';
        if ($feishu['code'] != 0) {
            Yii::error('新增采购付款单报错,付款单ID:' . $model->id . ',创建飞书审批回调信息:' . json_encode($feishu, 256));
            throw new Exception('创建飞书审批失败,请联系信息部处理:');
        } else {
            $model->process_instance_id = $feishu['data']['instance_code'];

            $entity_id = Yii::$app->user->identity->current_entity_id;
            $entityCode = EntityService::find()->select('code')->where(['id' => $entity_id])->cache(60)->scalar();
            $feishu = new Feishu($entityCode);
            $result = $feishu->getSingleApproval($model->process_instance_id);
            if ($result['code'] == 0) {
                $model->serial_number = $result['data']['serial_number'];
            }
        }

        $model->save();
    }

    public static function getInfoById($id)
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
            'pay_time_text',
        ]);
        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }

        $data = $info->toArray();
        $data['bank_info'] = $data['bank_info'] ? json_decode($data['bank_info'], true) : [];
        $data['pay_type'] = ProcurementPayTypeEnum::getValue($data['pay_type']);
        $data['detailList'] = [];
        $detail = ProcurementDetails::find()->alias('pd')
            ->leftJoin('{{%procurement}} p', 'p.id = pd.pt_id')
            ->select('pd.id,pd.material_name,pd.bar_code,pd.unit,pd.apply_num,pd.procurement_num,pd.dept_id,pd.total_price')
            ->andWhere(['pd.id' => Tool::getStringToArray($info['detail_ids'])])->asArray()->all();

        if ($detail) {
            foreach ($detail as &$item) {
                $item['store_name'] = Department::getName($item['dept_id']);
                unset($item['dept_id']);
                $data['detailList'][] = $item;
            }
        }

        //审批流程
        if (empty($data['process_detail']) && $data['process_instance_id'] && $data['status'] == ProcessStatusEnum::IN_REVIEW) { //实时获取飞书审批信息
            $processInfo = FeishuExamineService::getSingleApproval($data['process_instance_id']);
        } else {
            $processInfo = $data['process_detail'] ? json_decode($data['process_detail'], true) : [];
        }
        $data['process_detail'] = $processInfo;
        $data['attachment'] = $data['attachment'] ? json_decode($data['attachment'], true) : [];

        return $data;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    /**
     * 再次发起审批
     */
    public static function againApprove($id)
    {
        $procurementPay = ProcurementPay::find()->where(['id' => $id])->one();
        if (empty($procurementPay)) {
            throw new Exception('数据不存在');
        }

        if ($procurementPay->status != ProcessStatusEnum::IN_REVIEW) {
            throw new Exception('该付款单不是审批中状态，不能重新发起审批');
        }

        if ($procurementPay->process_instance_id) {
            throw new Exception('该付款单已发起过飞书审批了，请刷新重试');
        }

        $feishu_attachment_code = [];
        if ($procurementPay->attachment) {
            $attachment = json_decode($procurementPay->attachment, true);
            if ($attachment) {
                $feishu_attachment_code = $attachment['feishu_attachment_code'];
            }
        }

        return ProcurementPayService::createApproval($procurementPay, $feishu_attachment_code);
    }
}
