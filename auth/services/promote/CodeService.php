<?php

namespace auth\services\promote;

use Exception;
use common\helpers\ArrayHelper;
use auth\models\promote\Code;
use common\enums\StatusEnum;
use common\services\promote\CodeService as CommonCodeService;
use Yii;

class CodeService extends CommonCodeService
{
    /**
     * @var Code
     */
    public static $modelClass = Code::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        $query->andFilterWhere(['like', 'code', $params['code']])
            ->andFilterWhere(['like', 'code', $params['keyword']]);

        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        $query = static::getQuery($params);
        if ($params['is_bound']) {
            $query->andWhere([">", 'user_id', 0]);
        }

        $totalCount = $query->count();
        $list = $query->all();

        return [$list, $totalCount];
    }


    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andFilterWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,id as name');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andFilterWhere(['like', 'id', $params['keyword']]);
            // $query->andWhere(['or', ['like', 'id', $params['keyword']], ['like', 'wxcom_user_id', $params['keyword']]]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    /**
     * 选择窗口列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getListForChoose($params = [])
    {
        Code::setExtendAttrs([
            'user.realname' => 'user_name',
            'is_disabled',
        ]);
        Code::setShowAttrs([
            "id",
            "user_id",
            "code",
        ], true);

        $query = static::getQuery($params);
        $query->joinWith('user user')
            ->andWhere(['user.status' => StatusEnum::ENABLED]);

        $totalCount = $query->count();
        $list = $query
            ->offset(0)
            ->limit(null)
            ->all();

        return [$list, $totalCount];
    }
}
