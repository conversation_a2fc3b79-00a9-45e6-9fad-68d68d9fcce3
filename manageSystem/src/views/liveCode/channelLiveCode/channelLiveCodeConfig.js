export default {
    pc: [
        {
            title: "ID",
            align: "left",
            width: 70,
            dataIndex: "id"
        },
        {
            title: "名称",
            align: "left",
            width: 180,
            dataIndex: "name",
            scopedSlots: {
                customRender: "name"
            }
        },
        {
            title: "分组",
            align: "left",
            dataIndex: "group_text",
            width: 70,
            scopedSlots: {
                customRender: "group_text"
            }
        },
        {
            title: "今日加粉数",
            align: "left",
            width: 120,
            dataIndex: "today_add_num"
        },
        {
            title: "使用成员",
            align: "left",
            width: 180,
            dataIndex: "user_list",
            scopedSlots: {
                customRender: "user_list"
            }
        },
        {
            title: "备用成员",
            align: "left",
            width: 180,
            dataIndex: "spare_user_list",
            scopedSlots: {
                customRender: "spare_user_list"
            }
        },
        {
            title: "推广渠道",
            align: "center",
            width: 120,
            dataIndex: "channel_text"
        },
        {
            title: "推广代号",
            align: "center",
            width: 100,
            dataIndex: "code"
        },
        {
            title: "推广项目",
            align: "left",
            width: 100,
            dataIndex: "project_name"
        },
        {
            title: "创建人",
            align: "left",
            width: 100,
            dataIndex: "created_by_text"
        },
        {
            title: "企微",
            align: "left",
            width: 100,
            dataIndex: "com_text"
        },
        {
            title: "状态",
            align: "center",
            width: 100,
            dataIndex: "status",
            scopedSlots: {
                customRender: "status"
            }
        },
        {
            title: "操作",
            align: "left",
            width: 300,
            fixed: 'right',
            scopedSlots: {
                customRender: "action"
            }
        }
    ],
    mobile: [
        {
            title: "ID",
            align: "center",
            width: 70,
            dataIndex: "id"
        },
        {
            title: "名称",
            align: "center",
            width: 200,
            dataIndex: "name",
            scopedSlots: {
                customRender: "name"
            }
        },
        {
            title: "分组",
            align: "center",
            width: 100,
            dataIndex: "group_text",
            scopedSlots: {
                customRender: "group_text"
            }
        },
        {
            title: "今日加粉数",
            align: "center",
            width: 150,
            dataIndex: "today_add_num"
        },
        {
            title: "使用成员",
            align: "left",
            width: 180,
            dataIndex: "user_list",
            scopedSlots: {
                customRender: "user_list"
            }
        },
        {
            title: "备用成员",
            align: "left",
            width: 180,
            dataIndex: "spare_user_list",
            scopedSlots: {
                customRender: "spare_user_list"
            }
        },
        {
            title: "推广渠道",
            align: "center",
            width: 150,
            dataIndex: "channel_text"
        },
        {
            title: "创建人",
            align: "center",
            width: 100,
            dataIndex: "created_by_text"
        },
        {
            title: "企微",
            align: "center",
            width: 100,
            dataIndex: "com_text"
        },
        {
            title: "操作",
            align: "right",
            width: 200,
            scopedSlots: {
                customRender: "action"
            }
        }
    ]
};
