<template>
  <div>
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 顶部按钮 -->
      <template slot="top">
        <self-col-btn v-has="'cus-qrcode:create'">
          <a-button type="primary" icon="plus" @click="handleAdd({})"
            >新增活码</a-button
          >
        </self-col-btn>
        <!--  项目管理 -->
        <self-col-btn v-has="'promote-project:index'">
          <self-project></self-project>
        </self-col-btn>
        <self-col-btn v-has="'cus-qrgroup:index'">
          <a-button type="primary" @click="showGroup">分组管理</a-button>
        </self-col-btn>
        <self-col-btn v-has="'cus-qrcode:update-group-batch'">
          <a-button type="primary" @click="setGroup({ title: '' })"
            >批量分组</a-button
          >
        </self-col-btn>
        <self-col-btn  v-has="'promote-data-source:index'">
          <data-source></data-source>
        </self-col-btn>
      </template>
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="渠道活码">
          <a-input
            placeholder="请输入活码名称或ID搜索"
            v-model="queryParam.name"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="分组名称">
          <a-select
            @search="(e) => handleSearch(e, 'SelectGroup', 'getSelectGroup')"
            show-search
            v-model="queryParam.group_id"
            placeholder="请选择分组名称"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            @change="(e) => handleSearch('', 'SelectGroup', 'getSelectGroup')"
          >
            <a-select-option value>全部</a-select-option>
            <a-select-option
              v-for="d in SelectGroup"
              :value="d.id"
              :key="d.id"
              >{{ d.name }}</a-select-option
            >
          </a-select>
        </self-col>
        <self-col label="创建人">
          <commone-self-principal
            searchKey="username"
            :requestFun="getPromotePerson"
            placeholder="请选择创建人"
            value_key="username"
            v-model="queryParam.created_by"
            :isRequest="true"
          />
        </self-col>
        <self-col label="状态">
          <commone-self-principal
            :selectData="selectData"
            placeholder="请选择状态"
            value_key="name"
            :isSearchrRequest="false"
            v-model="queryParam.status"
          />
        </self-col>
      </template>
    </self-page-header>
    <div v-if="SummaryShow" class="progress-box">
      <a-progress :percent="CitySummary"></a-progress>
    </div>
    <a-table
      ref="table"
      :pagination="ipagination"
      @change="handleTableChange"
      size="middle"
      :dataSource="dataSourceFormat"
      rowKey="id"
      :columns="columns"
      :loading="loading"
      :scroll="scroll"
      :row-selection="{
        onChange: onChange,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        selectedRowKeys: selectedRowKeys,
      }"
    >
      <span slot="name" slot-scope="text">{{ text }}</span>
      <span slot="title1" slot-scope="text">{{ text }}</span>
      <span slot="status" slot-scope="text">
        <span
          :style="{
            color: text === 1 ? 'green' : text === 0 ? 'red' : '',
          }"
          >{{ text === 1 ? "启用" : text === 0 ? "禁用" : "-" }}</span
        >
      </span>
      <div slot="user_list" slot-scope="text">
        <self-list-drop-down
          :data="text"
          nameKey="name"
          icon="enterpriseMicro"
        />
      </div>
      <div slot="spare_user_list" slot-scope="text">
        <self-list-drop-down
          :data="text"
          nameKey="name"
          icon="enterpriseMicro"
        />
      </div>
      <span slot="action" slot-scope="text, record" v-divider>
        <a
          href="javascript:;"
          @click="getviewConfig(record)"
          v-has="'cus-qrcode:view'"
          >详情</a
        >
        <a
          href="javascript:;"
          @click="dataStatistics(record)"
          v-has="'cus-qrcode:statistics'"
          >统计</a
        >
        <a
          href="javascript:;"
          @click="uploadQrcode(record)"
          v-has="'cus-qrcode:img'"
          >下载</a
        >
        <a
          href="javascript:;"
          @click="copyQrcodeUrl(record)"
          v-has="'cus-qrcode:img'"
          >复制链接</a
        >
        <a
          href="javascript:;"
          @click="handleEdit({ record })"
          v-has="'cus-qrcode:update'"
          >编辑</a
        >
        <a
          href="javascript:;"
          v-has="'cus-qrcode:update-status'"
          @click="setStatus(record)"
          >{{ record.status == 1 ? "禁用" : "启用" }}</a
        >
      </span>
    </a-table>
    <!--活码详情-->
    <j-modal
      width="600px"
      :visible.sync="viewConfig.visible"
      :title="viewConfig.title"
      centered
    >
      <div class="viewBody">
        <div ref="viewBody">
          <h4>基础信息</h4>
          <a-form :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }">
            <a-form-item label="创建时间">{{
              viewConfig.info.created_at_text
            }}</a-form-item>
            <a-form-item label="客服成员">
              <div class="tabsbox tabsbox-self">
                <template v-for="(item, index) in viewConfig.info.user_list">
                  <div class="userTabs btn" :key="'btn' + index">
                    <img class="userImg" :src="item.avatar" />
                    {{ item.user_name }}
                  </div>
                </template>
              </div>
            </a-form-item>
            <a-form-item label="员工添加上限">
              <div>
                {{ viewConfig.info.has_limit == 0 ? "已关闭" : "已开启" }}
              </div>
              <div
                class="staffConfig centont-self tabsbox-self"
                v-if="viewConfig.info.has_limit == 1"
              >
                <div class="title">
                  <div>客服名称</div>
                  <div>今日已添加</div>
                  <div>每日添加上限</div>
                </div>
                <div
                  class="centont"
                  v-for="(item, index) in viewConfig.info.user_list"
                  :key="'centont' + index"
                >
                  <div>{{ item.user_name }}</div>
                  <div>{{ item.today_num }}</div>
                  <div>{{ item.max_num }}</div>
                </div>
              </div>
            </a-form-item>
            <a-form-item label="备用成员">
              <div class="tabsbox tabsbox-self">
                <template
                  v-for="(item, index) in viewConfig.info.spare_user_list"
                >
                  <div class="userTabs btn" :key="'btn' + index">
                    <img class="userImg" :src="item.avatar" />
                    {{ item.user_name }}
                  </div>
                </template>
              </div>
            </a-form-item>
          </a-form>
        </div>
      </div>
      <div slot="footer" class="selfBottom">
        <a-button type="primary" @click="handleViewSubmit">确定</a-button>
      </div>
    </j-modal>

    <!--分组-->
    <j-modal
      :visible.sync="groupConfig.visible"
      :title="groupConfig.title"
      @ok="handleGroupSubmit"
      @cancel="handleGroupCancel"
      :loading="groupConfig.loading"
      centered
      :bodyStyle="{
        padding: 0,
      }"
    >
      <div class="groupConfig">
        <!-- 弹窗 -->
        <a-popover
          title
          trigger="click"
          placement="bottom"
          :visible="groupConfig.clicked"
        >
          <div slot="content">
            <a-input
              v-model="groupConfig.value"
              placeholder="请输入分组名称，不可重复"
            />
            <div class="popover">
              <a-button
                @click="
                  () => {
                    groupConfig.clicked = false;
                  }
                "
                >取消</a-button
              >
              <a-button type="primary" @click="createItem">确定</a-button>
            </div>
          </div>
          <a-button
            type="primary"
            @click="groupEreatConfig"
            v-has="'cus-qrgroup:create'"
            >新增分组</a-button
          >
        </a-popover>
        <!-- 表格 -->
        <a-table
          :columns="groupConfig.columns"
          rowKey="id"
          :data-source="groupConfig.data"
          :pagination="groupConfig.pagination"
          :loading="groupConfig.loading"
          @change="groupConfigChange"
          style="margin-top: 10px"
        >
          <div slot="action" slot-scope="text, record, index">
            <a-popover
              title
              trigger="click"
              placement="bottom"
              :visible="groupConfig.data[index].clicked"
            >
              <div slot="content">
                <a-input
                  v-model="groupConfig.value"
                  placeholder="请输入分组名称，不可重复"
                />
                <div class="popover">
                  <a-button @click="showEditaction(false, index)"
                    >取消</a-button
                  >
                  <a-button
                    type="primary"
                    @click="groupConfigEdit(record, index)"
                    >确定</a-button
                  >
                </div>
              </div>
              <a
                @click="showEditaction(true, index)"
                v-has="'cus-qrgroup:update'"
                >修改名称</a
              >
            </a-popover>
            <a-divider type="vertical" />
            <a @click="groupConfigDelete(record)" v-has="'cus-qrgroup:delete'"
              >删除</a
            >
          </div>
        </a-table>
      </div>
    </j-modal>
    <!--批量设置分组 -->
    <a-modal
      :title="groupSelect.title"
      :visible="groupSelect.visible"
      @ok="GroupSubmit"
      :switchFullscreen="false"
      :fullscreen="false"
      width="350px"
      centered
      @cancel="
        () => {
          groupSelect.visible = false;
        }
      "
    >
      <a-form :form="form">
        <a-form-item label="分组" style="display: flex; margin-bottom: 0">
          <a-select
            show-search
            @search="(e) => selecthandleSearch(e)"
            placeholder="选择分组"
            ref="store"
            style="width: 200px"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-decorator="['group_id', changeRules.group_id]"
            @change="groupSelectChange"
          >
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in groupSelect.data"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script>
import { cusQrgroup, cusQrcode, promoteAccount } from "@/api/api";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import commentMixin from "@/mixins/commentMixin";
import pick from "lodash.pick";
import channelLiveCodeConfig from "./channelLiveCodeConfig.js";
import DataSource from "@/views/promoteManage/dataSource/dataSource.vue";
export default {
  name: "channelLiveCode",
  mixins: [JeecgListMixin, commentMixin],
  components: {
    DataSource
  },
  data() {
    return {
      cusQrcode,
      getPromotePerson: promoteAccount.getPromotePerson,
      form: this.$form.createForm(this),
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        selectedRowKeys: [],
        showTotal: function (total, range) {
          let page = "20/页 共" + total + "条";
          return page;
        },
      },
      scroll: {
        x: 1200,
      },
      selectData: [
        {
          id: 1,
          name: "启用",
        },
        {
          id: 0,
          name: "禁用",
        },
      ],
      columns: this.$actionForAuth(channelLiveCodeConfig.pc, [
        "cus-qrcode:view",
        "cus-qrcode:statistics",
        "cus-qrcode:img",
        "cus-qrcode:update",
        "cus-qrcode:update-status",
      ]),
      queryParam: {
        status: "",
        group_id: "",
        created_by: "",
      },
      modalTitle: "",
      // disableMixinCreated: true,
      viewBodyHeight: 212,
      SelectGroup: [],
      viewConfig: {
        visible: false,
        loading: false,
        info: {},
        title: "渠道活码详情",
      },
      groupConfig: {
        visible: false,
        value: "",
        title: "分组管理",
        columns: [
          {
            dataIndex: "name",
            width: 100,
          },
          {
            align: "right",
            width: 100,
            scopedSlots: { customRender: "action" },
          },
        ],
        data: [],
        loading: false,
        clicked: false,
        hovered: false,
        pagination: {
          current: 1,
          total: 0,
          pageSize: 10,
          showSizeChanger: false,
          selectedRowKeys: [],
          size: "small",
          limit: 10,
          page: 0,
          showTotal: function (total, range) {
            let page = "10/页 共" + total + "条";
            return page;
          },
        },
      },
      url: {
        list: "/wxcom/cus-qrcode/index",
      },
      groupSelect: {
        visible: false,
        group_id: "123123",
        title: "123123",
        data: [],
      },
      timeid: null,
      time: null,
      showTag: true,
    };
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      d.map((element) => {
        if (!element.id) element.id = "-";
        if (!element.name) element.name = "-";
        if (!element.group_text) element.group_text = "-";
        if (!element.today_add_num) element.today_add_num = "-";
        if (!element.spare_user_list) element.spare_user_list = "-";
        if (!element.channel_text) element.channel_text = "-";
        if (!element.created_by_text) element.created_by_text = "-";
        if (!element.com_text) element.com_text = "-";
        if (!element.created_at_text) element.created_at_text = "-";
        if (!element.project_name) element.project_name = "-";
        return element;
      });
      return d;
    },
    changeRules: function () {
      return {
        group_id: { rules: [{ required: true, message: "请选择分组!" }] }, // 部门ID
      };
    },
  },
  async mounted() {
    this.changeColumns();
    this.promotePerson();
    this.getSelectGroup();
  },
  methods: {
    changeColumns() {
      const clientWidth = document.body.clientWidth;
      if (clientWidth <= 414) {
        this.scroll = {};
        this.columns = channelLiveCodeConfig.mobile;
      }
    },
    dataStatistics(record) {
      this.$store.commit("SET_PERMISSIONLISTPASSROUTER", ["dataStatistics"]);
      console.log(this.$store.state.user.permissionListPassRouter);
      this.$nextTick(() => {
        this.$router.push({
          name: "数据统计",
          params: {
            record,
          },
        });
      });
    },
    searchReset() {
      this.ipagination.current = 1;
      this.queryParam = {
        group_id: "",
        created_by: "",
        status: "",
      };
      this.loadData(1);
    },
    //  获取活码分组
    showGroup() {
      this.groupConfig.visible = true;
      this.getCusQrgroup();
    },
    getCusQrgroup(page = 0) {
      this.groupConfig.loading = true;
      const { limit } = this.groupConfig.pagination;
      cusQrgroup
        .index({ limit, page })
        .then((res) => {
          this.groupConfig.loading = false;
          this.groupConfig.data = res.data.list.map((item) => {
            item.clicked = false;
            return item;
          });
          this.groupConfig.pagination.total = parseInt(res.data.totalCount);
          this.groupConfig.pagination.page = page;
        })
        .catch(() => {
          this.groupConfig.loading = false;
        });
    },
    showEditaction(e, index) {
      if (this.groupConfig.data.length == 0) return;
      this.groupConfig.clicked = false;
      this.groupConfig.value = "";
      this.groupConfig.data.map((item) => (item.clicked = false));
      this.groupConfig.data[index].clicked = e;
    },
    groupConfigEdit(record, index) {
      const { value } = this.groupConfig;
      cusQrgroup.update({ name: value, id: record.id }).then((res) => {
        console.log(res);
        if (res.code == 200) {
          this.getCusQrgroup(this.groupConfig.pagination.page);
          this.$message.success(res.message);
        } else {
          this.$message.warning(res.message);
        }
      });
      this.showEditaction(false, index);
    },
    groupConfigDelete({ id }) {
      const _this = this;
      this.$confirm({
        title: "提示",
        content: "请确认删除此条活码分组",
        onOk() {
          cusQrgroup.delete({ id }).then((res) => {
            if (res.code == 200) {
              _this.$message.success(res.message);
              _this.getCusQrgroup();
            } else {
              _this.$message.warning(res.message);
            }
          });
        },
      });
    },
    groupConfigChange({ current }) {
      this.groupConfig.pagination.current = current;
      this.getCusQrgroup(current);
    },
    groupEreatConfig() {
      this.showEditaction(false, 0);
      this.groupConfig.clicked = true;
    },
    createItem() {
      cusQrgroup.create({ name: this.groupConfig.value }).then((res) => {
        if (res.code == 200) {
          this.getCusQrgroup();
          this.groupConfig.clicked = false;
          this.$message.success(res.message);
        } else {
          this.$message.warning(res.message);
        }
      });
    },
    handleGroupSubmit() {
      this.showEditaction(false, 0);
      this.groupConfig.clicked = false;
    },
    handleGroupCancel() {
      this.showEditaction(false, 0);
      this.groupConfig.clicked = false;
    },
    // 获取查询分组
    getSelectGroup(keyword = "") {
      return new Promise((resolve, reject) => {
        cusQrgroup
          .select({ keyword })
          .then((res) => {
            console.log("===>");
            this.SelectGroup = res.data;
          })
          .catch(() => {
            this.SelectGroup = [];
          });
      });
    },
    // 下载活码二维码
    uploadQrcode({ qrcode_url, name }) {
      if (!qrcode_url) return this.$message.warning("没有二维码可供下载！");
      let image = new Image();
      image.setAttribute("crossOrigin", "anonymous");
      image.onload = function () {
        let canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        let context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        let url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        let a = document.createElement("a"); // 生成一个a元素
        let event = new MouseEvent("click"); // 创建一个单击事件
        a.download = name || "photo"; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = qrcode_url;
    },
    // 复制活码链接
    copyQrcodeUrl({ link_url, id }) {
      if (!link_url) {
        return this.$message.warning("该链接为空");
      }
      
      // 构造带有customer_channel参数的链接
      let finalUrl = link_url;
      const customerChannelParam = `customer_channel=landpage:${id}`;
      
      // 检查链接是否已经包含查询参数
      if (finalUrl.includes('?')) {
        // 已有查询参数，用&连接
        finalUrl += `&${customerChannelParam}`;
      } else {
        // 没有查询参数，用?连接
        finalUrl += `?${customerChannelParam}`;
      }
      
      // 创建一个临时的textarea元素用于复制
      const textarea = document.createElement('textarea');
      textarea.value = finalUrl;
      document.body.appendChild(textarea);
      textarea.select();
      
      try {
        // 执行复制操作
        document.execCommand('copy');
        this.$message.success('链接已复制到剪贴板');
      } catch (err) {
        // 如果复制失败，尝试使用现代API
        if (navigator.clipboard) {
          navigator.clipboard.writeText(finalUrl).then(() => {
            this.$message.success('链接已复制到剪贴板');
          }).catch(() => {
            this.$message.error('复制失败，请手动复制链接');
          });
        } else {
          this.$message.error('复制失败，请手动复制链接');
        }
      } finally {
        // 清理临时元素
        document.body.removeChild(textarea);
      }
    },
    // 分组详情
    getviewConfig({ id }) {
      cusQrcode
        .view({ id })
        .then((res) => {
          this.viewConfig.info = res.data.info;
          this.$nextTick(() => {
            this.viewConfig.visible = true;
          });
          console.log(res.data.info);
        })
        .catch(() => {
          this.viewConfig.loading = false;
        });
    },
    handleViewSubmit() {
      this.viewConfig.visible = false;
      this.viewConfig.info = {};
    },
    handleViewCancel() {
      this.viewConfig.info = {};
      this.viewBodyHeight = 212;
    },
    //  批量设置分组
    setGroup() {
      if (this.selectedRowKeys.length == 0)
        return this.$message.warning("您还未选择数据！");
      this.groupSelect.title =
        "批量分组" + this.selectedRowKeys.length + "条数据";
      this.groupSelect.visible = true;
      console.log(this.selectedRowKeys);
      this.selecthandleSearch("");
    },
    groupSelectChange(e) {
      this.groupSelect.group_id = e;
      this.selecthandleSearch("");
    },
    selecthandleSearch(keyword) {
      if (this.timeid) {
        clearTimeout(this.timeid);
      }
      this.timeid = setTimeout(async () => {
        let { data } = await cusQrgroup.select({ keyword });
        this.groupSelect.data = await data;
      }, 500);
    },
    GroupSubmit() {
      if (this.time) clearTimeout(this.time);
      this.time = setTimeout(() => {
        this.form.validateFields(async (err, values) => {
          if (!err) {
            let { group_id } = values;
            let ids = JSON.parse(JSON.stringify(this.selectedRowKeys));
            cusQrcode.updateGroupBatch({ group_id, ids }).then((res) => {
              if (res.code == 200) {
                this.loadData();
                this.groupSelect.visible = false;
                this.$message.success(res.message);
              } else {
                this.$message.warning(res.message);
              }
            });
          }
        });
      }, 500);
    },
    /**
     * 跳转 新增或编辑页面
     */
    async handleAdd({ record = {} }) {
      this.$store.commit("SET_PERMISSIONLISTPASSROUTER", ["newlivecode"]);
      await this.$asyncNextTick();
      this.$router.push({
        name: "新增活码",
        params: {
          record,
        },
      });
    },
    async handleEdit({ record = {} }) {
      this.$store.commit("SET_PERMISSIONLISTPASSROUTER", ["editlivecode"]);
      await this.$asyncNextTick();
      this.$router.push({
        name: "编辑活码",
        params: {
          record,
        },
      });
    },
    //  列表复选框
    onChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      // this.queryParam.ids = selectedRowKeys
    },
    onSelect(record, selected, selectedRows) {},
    onSelectAll(selected, selectedRows, changeRows) {},
    /**
     * 列表修改上报渠道名称、备注
     */
    getpopoverData(e, index) {
      const { dataSourceFormat } = this;
      let value = document.getElementById("popover_" + e).value;
      if (dataSourceFormat[index][e] != value && value) {
        this.submitLineData(e, value, dataSourceFormat[index], index);
      } else {
        !value && this.$message.error("值不能为空！");
        !value || this.$message.error("值不能相等！");
        console.log("相等或空值");
      }
    },
    submitLineData(e, value, record, index) {
      let formData = record;
      formData[e] = value;
    },
    // popover
    hide() {
      this.clicked = false;
      this.hovered = false;
    },
    handleHoverChange(visible) {
      this.clicked = false;
      this.hovered = visible;
    },
    handleClickChange(visible) {
      this.clicked = visible;
      this.hovered = false;
    },
    // 启禁用
    setStatus({ id, status }) {
      console.log(status);
      const _this = this;
      status = status === 1 ? 0 : 1;
      cusQrcode.updateStatus({ id, status }).then((res) => {
        if (res.code == 200) {
          _this.$message.success(res.message);
          _this.loadData();
        } else {
          _this.$message.error(res.message);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "./commone.less";
.paneBox {
  background: #fff;
}
/deep/ .ant-modal {
  top: 0 !important;
  padding-bottom: 0 !important;
}
.popoverbox /deep/.ant-input-group-addon {
  padding: 0 !important;
}
.popover_box {
  position: relative;
  height: 100%;
  width: 100%;
  .popover_text {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}
.popover_text {
  cursor: pointer;
}
.popover {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}
.groupConfig {
  padding: 12px 12px 0;
  /deep/ .ant-table-thead {
    display: none;
  }
}
/deep/ .ant-modal-footer {
  border-top: none !important;
}
.ant-popover-placement-top {
  z-index: 999999;
}
.viewBody {
  h4::before {
    content: "";
    display: inline-block;
    height: 15px;
    width: 2px;
    margin-right: 2px;
    background: rgba(24, 144, 255, 1);
    position: relative;
    top: 2px;
  }
  /deep/ .ant-form-item {
    margin-bottom: 6px !important;
  }
}
.tTop {
  position: relative;
  top: -1px;
}
.tabsbox-self {
  width: 420px;
}
.tagbody {
  width: 60px;
  display: flex;
  > .imgboxy {
    margin-right: 5px;
  }
  > .centent {
    flex: 1;
    width: 0px;
    text-align: left;
  }
}
.UpDown {
  font-size: 12px;
  cursor: pointer;
}
</style>
