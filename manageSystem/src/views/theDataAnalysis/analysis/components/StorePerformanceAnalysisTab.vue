<!--
 * @Date: 2022-07-08 10:23:27
 * @Author: cd
 * @LastEditTime: 2023-04-20 15:59:48
 * @Descripttion: 门店业绩分析
-->
<template>
  <div>
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="时间搜索">
          <self-time
            timeRange="month"
            v-model="selfDefaultTime.promote"
            :reset="timeReset"
            :InitializationTime="true"
            :timeKey="{
              start: 'start_time',
              end: 'end_time',
            }"
          />
        </self-col>
        <self-col label="门店名称">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="searchStoreByEntity"
            placeholder="请选择预约门店"
            value_key="store_name"
            id_key="store_id"
            :query="{}"
            v-model="queryParam.store_id"
          />
        </self-col>
        <self-col label="门店区域">
          <a-tree-select
            style="width: 100%"
            :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
            :treeData="treeSearch"
            v-model="queryParam.area_id"
            placeholder="请选择门店区域"
          ></a-tree-select>
        </self-col>
      </template>
      <template slot="export">
        <export-to-csv
          :query="{
            ...queryParam,
            isorter,
            field: getQueryField(),
            column: isorter.column,
            order: isorter.order,
          }"
          fileName="城市门店业绩分析"
          btnName="导出"
          :limit="1000"
          v-has="'data-store-performance-analysis:export'"
          :queryParam="{
            ...queryParam,
            ...isorter,
            column: isorter.column,
            field: getQueryField(),
            order: isorter.order,
          }"
          :CommentApi="storePerformanceAnalysis.export"
          :header="header"
          :csvKey="csvKey"
        ></export-to-csv>
      </template>
    </self-page-header>
    <a-table
      class="totalTable"
      :scroll="{ x: 1200 }"
      :pagination="false"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :loading="loading"
      @change="onChange"
      :row-key="(l, index) => index"
    >
      <template v-for="item in slotAnalyServiceColumns">
        <div
          v-if="item.hasOwnProperty('slots')"
          :slot="item.slots.title"
          :key="item.slots.title"
        >
          <span>
            {{ item.slots.slotName }}
          </span>
          <a-tooltip placement="top">
            <template slot="title">
              <span>{{ item.slots.slotText }}</span>
            </template>
            <a class="slotColor">
              <a-icon
                type="question-circle"
                class="question-circle_poust ml5"
                theme="filled"
              />
            </a>
          </a-tooltip>
        </div>
      </template>
      <span slot="city_name" slot-scope="text">
        {{ text }}
      </span>
    </a-table>
  </div>
</template>
<script>
import { analyServiceColumns, exportKey, header } from "./StoreColumns";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import commentMixin from "@/mixins/commentMixin";
import {
  inventoryManage,
  territorialSort,
  storePerformanceAnalysis,
} from "@/api/api";
export default {
  name: "StorePerformanceAnalysisTab",
  mixins: [JeecgListMixin, commentMixin],
  data() {
    return {
      storePerformanceAnalysis,
      header: header(),
      csvKey: exportKey(),
      searchStoreByEntity: inventoryManage.searchStoreByEntity,
      treeSearch: [],
      selfDefaultTime: {
        promote: {},
      },
      slotAnalyServiceColumns: JSON.parse(
        JSON.stringify(
          analyServiceColumns.filter((l) => l.dataIndex != "dept_name")
        )
      ),
      columns: JSON.parse(
        JSON.stringify(
          analyServiceColumns.filter((l) => l.dataIndex != "dept_name")
        )
      ),

      defaultTime: [],
      url: {
        list: "/data/store-performance-analysis/index",
      },
    };
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      // 针对指定字段进行三位一个逗号切割
      d = this.$utils.batchCutForArray(
        d,
        false,
        ...exportKey("store_name", "area_text", "loss_rate", "full_load_rate", "refund_rate")
      );
      d = this.$utils.TablefieldCompletion({ list: d });
      return d;
    },
  },
  created() {
    this.loadTree();
  },
  methods: {
    async loadTree() {
      await territorialSort
        .selectCategory({
          id: 0,
          type: 1,
          is_top: 0,
        })
        .then((res) => {
          if (res.code == 200) {
            this.treeSearch = this.dataformat(res.data);
          }
        });
    },
    dataformat(treeList) {
      let arr = [];
      let that = this;
      for (let a = 0; a < treeList.length; a++) {
        let temp = treeList[a];
        let obj = {
          label: temp.name,
          parentId: temp.parent_id || "",
          key: (temp.parent_id || "") + "" + temp.id,
          value: temp.id,
        };

        if (temp.child && temp.child.length) {
          obj.children = that.dataformat(temp.child);
        }
        arr.push(obj);
      }
      return arr;
    },
    onChange(pagination, filters, sorter) {
      // 排序
      if (sorter.order) {
        this.queryParam.order_field = sorter.field;
        this.queryParam.order_type =
          sorter.order === "descend" ? "desc" : "asc";
      } else {
        this.queryParam.order_field = "";
        this.queryParam.order_type = "";
      }
      this.ipagination.current = 1;
      //  end
      this.handleTableChange();
    },
  },
};
</script>
<style scoped lang="less">
.slotColor {
  color: #d4d6d9;
}
.selectDep {
  max-width: 280px;
}
.employmentStatus {
  border-color: rgba(0, 0, 0, 0);
  transform: scale(0.8);
  position: relative;
  top: -1px;
}
/deep/
  .ant-table-thead
  > tr
  > th
  .ant-table-column-sorter
  .ant-table-column-sorter-inner {
  margin-left: 0.3em !important;
}
/deep/ .ant-table-column-title {
  > div {
    display: flex;
    align-items: center;
    text-align: center;
  }
}

.cityMultiple {
  /deep/ div {
    height: 30px;
    overflow-y: auto;
  }
}
/deep/ .ant-table-row-level-1 {
  background: #fcfdfd;
}
</style> 