export const analyServiceColumns = [
    {
        dataIndex: "teacher_name",
        title: "老师姓名",
        key: "teacher_name",
        align: "left",
        width: 200,
        scopedSlots: { customRender: 'teacher_name' },
    },
    {
        dataIndex: "status",
        title: "状态",
        key: "status",
        align: "left",
        width: 100,
        scopedSlots: { customRender: 'status' },
    },
    {
        dataIndex: "dept_name",
        title: "所属门店",
        key: "dept_name",
        align: "left",
        width: 140,
        scopedSlots: { customRender: 'dept_name' },
    },
    {
        dataIndex: "date",
        title: "出勤天数",
        key: "date",
        align: "left",
        width: 100,
        scopedSlots: { customRender: "date" },
    },
    {
        dataIndex: "total_amount",
        title: "总业绩",
        key: "total_amount",
        align: "left",
        width: 120,
        scopedSlots: { customRender: "total_amount" },
    },
    {
        dataIndex: "total_refund_amount",
        title: "退款金额",
        key: "total_refund_amount",
        align: "left",
        width: 120,
        scopedSlots: { customRender: "total_refund_amount" },
    },
    {
        dataIndex: "refund_rate",
        key: "refund_rate",
        align: "left",
        width: 120,
        slots: {
            title: "refund_rate",
            slotName: "退款率",
            slotText: "退款率=退款金额/总业绩" 
        }
    },
    {
        dataIndex: "new_amount",
        title: "新客业绩",
        key: "new_amount",
        align: "left",
        width: 120,
        scopedSlots: { customRender: "new_amount" },
    },
    {
        dataIndex: "new_cus_count",
        title: "新客到店人数",
        key: "new_cus_count",
        align: "left",
        width: 120,
        scopedSlots: { customRender: "new_cus_count" },
    },
    {
        dataIndex: "new_price",
        key: "new_price",
        align: "left",
        width: 130,
        slots: {
            title: "new_price",
            slotName: "新客客单价",
            slotText: "新客客单价=新客业绩/新客到店人数" 
        }
    },
    {
        dataIndex: "old_amount",
        title: "老客业绩",
        key: "old_amount",
        align: "left",
        width: 120,
        scopedSlots: { customRender: "old_amount" },
    },
    {
        dataIndex: "old_cus_count",
        title: "老客到店人数",
        key: "old_cus_count",
        align: "left",
        width: 120,
        scopedSlots: { customRender: "old_cus_count" },
    },
    {
        dataIndex: "old_price",
        key: "old_price",
        align: "left",
        width: 130,
        slots: {
            title: "old_price",
            slotName: "老客客单价",
            slotText: "老客客单价=老客业绩/老客到店人数" 
        }
    },
    {
        dataIndex: "loss_num",
        title: "流失人数",
        key: "loss_num",
        align: "left",
        width: 100,
        scopedSlots: { customRender: "loss_num" },
    },
    {
        dataIndex: "total_price",
        key: "total_price",
        align: "left",
        width: 160,
        slots: {
            title: "total_price",
            slotName: "客单价(包含流失)",
            slotText: "客单价=总业绩/(新客到店人数+流失人数)" 
        }
    },
]

export const header = (key = '') => analyServiceColumns.reduce((l, j) => {
    l.push(j);
    return l
}, []).filter(item => item.title != key).map(item => item.title || item.slots.slotName)

export const exportKey = (...keys) => analyServiceColumns.reduce((l, j) => {
    l.push(j);
    return l
}, []).filter(item => {
    return !keys.includes(item.dataIndex)
}).map(item => item.dataIndex)