<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2022-09-17 17:10:00
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-04-28 14:58:17
 * @FilePath: \manageSystem\src\views\storeWorkbench\components\orderLayout\remarks\storesTheTeacher.vue
 * @Description: 
-->
<template>
  <self-detail-modal
    title="选择老师"
    :show-confirm="false"
    :destroyOnClose="false"
    placement="center"
    v-model="propvisible"
    :confirmLoading="loading"
    :drawerWidth="'1000px'"
    :confirmPromise="handleOk"
    closeText="取消"
    confirmText="确认"
    footerPosition="center"
  >
    <div slot="centent" class="teacherCentent">
      <tag
        :propCurrent="storeViewForSubmitData.order_type_id"
        :tagList="orderType"
        @confirm="changeTag"
      />
      <div class="Box">
        <div
          class="cell top"
          :style="{ width: maxWidth + 'px', minWidth: '100%' }"
        >
          <div>老师设置</div>
          <div v-for="(item, index) in teacherList" :key="index">
            <a href="javascript:;">
              <popover
                class="teacherPopover"
                :value="'批量设置' + item.name || ''"
                msg="请选择老师"
                placement="bottom"
                :judgeValue="sale_teacher_id && sale_teacher_id.user_id"
                @confirm="saleConfirm"
                @close="popoverClose"
                ref="popover"
                :index="item.id"
              >
                <selectForDropdown
                  :teacherList="item.teacherList"
                  type="allSetTeacher"
                  v-if="showSelectForDropdown"
                  style="width: 240px"
                  :type_id="item.id"
                  @search="e => $emit('search', e)"
                  @change="
                    (e) =>
                      allSetTeacher({
                        value: e,
                        key: 'sale_teacher_id',
                      })
                  "
                />
              </popover>
            </a>
          </div>
        </div>
        <div
          class="cell title bold f14 color333"
          :style="{ width: maxWidth + 'px', minWidth: '100%' }"
        >
          <div>商品名称</div>
          <div v-for="(item, index) in teacherList" :key="index">
            {{ item.name }}
          </div>
        </div>
        <a-form
          layout="inline"
          :form="form"
          :style="{ width: maxWidth + 'px', minWidth: '100%' }"
        >
          <listTree
            v-for="(item, index) in storeViewForSubmitData.projects"
            :key="index"
            :index="index"
            :item="item"
            :form="form"
            :teacherList="teacherList"
            :salesOfTheTeacher="salesOfTheTeacher"
            :operatingTheTeacher="operatingTheTeacher"
            :teacher_selact_key="teacher_selact_key"
            @search="e => $emit('search', e)"
            @setTeacher="setTeacher"
          />
        </a-form>
      </div>
    </div>
  </self-detail-modal>
</template>

<script>
import tag from "../../tagBox.vue";
import teacherList from "./teacherList.vue";
import card from "./card.vue";
import popover from "../../../billingCashier/popover.vue";
import listTree from "./listTree.vue";
import selectForDropdown from "../../../billingCashier/selectForDropdown";
export default {
  components: {
    tag,
    teacherList,
    card,
    popover,
    listTree,
    selectForDropdown,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    storeViewForSubmitData: {
      type: Object,
      default: () => {},
    },
    salesOfTheTeacher: {
      type: Array,
      default: () => [],
    },
    operatingTheTeacher: {
      type: Array,
      default: () => [],
    },
    orderType: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
    },
    teacherList_: {
      type: Array,
      default: () => [],
    },
    teacher_selact_key: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showSelectForDropdown: true,
      value: void 0,
      sale_teacher_id: void 0,
      operation_teacher_id: void 0,
      form: this.$form.createForm(this),
      tagList: [
        {
          name: "普通订单",
        },
        {
          name: "光电专场",
        },
        {
          name: "半永久专场",
        },
      ],
    };
  },
  computed: {
    propvisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit("close");
        }
      },
    },
    teacherList: function () {
      let teacherList = this.teacherList_;
      return teacherList;
    },
    maxWidth: function () {
      return (this.teacherList.length + 1) * 310;
    },
  },
  watch: {
    propvisible: function (newVal) {
      if (!newVal) {
        this.value = void 0;
        this.sale_teacher_id = void 0;
        this.operation_teacher_id = void 0;
      }
    },
  },
  methods: {
    popoverClose() {
      this.showSelectForDropdown = false;
      this.sale_teacher_id = void 0;
      this.$nextTick(() => {
        this.showSelectForDropdown = true;
      });
    },
    setTeacher(e) {
      console.log(e);
      try {
        this.$emit("setTeacher", e);
      } catch (error) {
        console.log(error);
      }
    },
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.$emit("teacherConfirm", (e) => {
            if (e == 1) {
              this.$emit("close");
            }
          });
        } else {
          this.$message.error("请完善信息");
          this.$scrollIntoView();
        }
      });
    },
    changeTag(e) {
      this.$emit("teacherOrderChange", e);
      this.form.resetFields();
    },
    saleConfirm(e) {
      this.$emit("allSetTeacher", {
        value: this.sale_teacher_id,
        key: this.teacher_selact_key + e,
      });
    },
    allChange(e, key) {
      this.$emit("allChange", {
        value: e,
      });
    },
    allSetTeacher({ value, key }) {
      console.log(value, key);
      this[key] = value;
    },
  },
};
</script>

<style lang="less" scoped>
@import url(./style.less);
.spin {
  position: absolute;
  top: 48px;
  left: 0;
  right: 0;
  bottom: 55px;
  display: flex;
  align-items: top;
  padding-top: 100px;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9;
}
/deep/ .ant-form-item-with-help {
  margin: 0;
  font-weight: 400;
}
/deep/ .ant-form-item {
  margin: 0;
}
</style>