<!--
    * @Author: superLjj <EMAIL>
    * @Date: 2022-09-17 14:07:06
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-03-22 16:11:42
 * @FilePath: \manageSystem\src\views\storeWorkbench\components\orderLayout\dropDownSearch.vue
    * @Description: 
   -->
<template>
  <!-- 设置用户信息弹窗 -->
  <div>
    <div class="theCustomerInformation customerInformationSelect">
      <div class="detailBox">
        <img :src="selectItem.avatar" v-imgdef="$headPortrait" />
        <div class="detail">
          <div>
            {{ selectItem.name || "-" }}
            <a-tag color="orange" class="detail-tag">VIP</a-tag>
          </div>
          <div>{{ selectItem.mobile || "-" }}</div>
        </div>
      </div>
      <span
        class="order"
        :data-orderNum="orderNum < 100 ? orderNum : 99"
        @click="handleClick"
        v-if="showOrderNum"
      >
        <a-icon class="icon" type="file-text" />
      </span>
      <span class="delete" @click="deleteItem">
        <a-button type="link">变更客户</a-button>
      </span>
    </div>
    <self-detail-modal
      title="选择客户"
      :confirm="false"
      placement="center"
      v-model="propVisible"
      :confirmLoading="loading"
      :drawerWidth="460"
      :drawerHeight="300"
      padding="20px 24px"
      :close="false"
      @ok="handleOk"
      confirmText="确定"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
      :maskStyle="{
        left: '138px',
      }"
      centered
      ref="detailmodal"
    >
      <div slot="centent" class="dropDownSearch" ref="dropDownSearch">
        <a-input-search
          size="large"
          :placeholder="placeholder"
          enter-button
          @search="search"
        />
        <div class="customerList">
          <a-empty class="empty" v-if="list.length == 0">
            <div slot="description" class="notFoundContent">
              暂无客户信息，点击
              <a href="javascript:;" @click="addCustomer">新增客户</a>
            </div>
          </a-empty>
          <div
            class="theCustomerInformation mb10"
            v-for="(item, index) in list"
            :key="index"
            @click="change(item)"
          >
            <div class="detailBox">
              <img :src="item.avatar" v-imgdef="$headPortrait" />
              <div class="detail">
                <div class="bold">{{ item.name }}</div>
                <div>{{ item.mobile }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </self-detail-modal>
    <a-modal v-model="visible" :width="1000" @ok="handleOk">
      <h3>该用户存在的可用订单</h3>
      <a-table
        :pagination="false"
        :columns="columns"
        :data-source="dataSource"
        :rowKey="(e, index) => index"
        :scroll="{ y: 500 }"
      >
        <span slot="order_no" class="order_no" slot-scope="text, tableRecord">
          {{ text }}
          <a-tag
            class="orderTag"
            color="blue"
            v-if="record ? tableRecord.id == record.order_id : false"
            >当前选中
          </a-tag>
        </span>
        <span slot="action" slot-scope="text, record" class="action">
          <a href="javascript:;" @click="settlement(record)"> 结算 </a>
        </span>
        <span slot="order_status" slot-scope="text, record" class="action">
          {{ statusList[text].name }}
        </span>
      </a-table>
      <template slot="footer">
        <a-button key="back" @click="handleCancel"> 取消 </a-button>
      </template>
    </a-modal>
    <addCustomer ref="addCustomer" @setCusInformation="setCusInformation" />
  </div>
</template>

<script>
const defaultValur = () => new Object();
const defaultUnValur = () => void 0;
import addCustomer from "./addCustomer.vue";
import { cardsSales, orderStore, customer } from "@/api/api";
export default {
  components: { addCustomer },
  props: {
    showOrderNum: {
      type: Boolean,
      default: true,
    },
    record: {
      type: Object,
    },
    order_id: {
      type: [Number, String],
    },
  },
  data() {
    return {
      outOfFocus: false,
      selectText: false,
      loading: false,
      value: "",
      placeholder: "请输入客户信息查询",
      visible: false,
      open: false,
      itemKey: defaultUnValur(),
      selectItem: defaultValur(),
      orderNum: 0,
      //   list: [],
      list: [],
      columns: [
        {
          title: "订单编号",
          dataIndex: "order_no",
          width: 265,
          scopedSlots: { customRender: "order_no" },
        },
        {
          title: "预约门店",
          dataIndex: "store_name",
          scopedSlots: { customRender: "store_name" },
        },
        {
          title: "订金金额",
          dataIndex: "deposit",
          scopedSlots: { customRender: "deposit" },
        },
        {
          title: "订单实收",
          dataIndex: "received_amount",
          scopedSlots: { customRender: "received_amount" },
        },
        {
          title: "订单状态",
          dataIndex: "order_status",
          scopedSlots: { customRender: "order_status" },
        },
        {
          title: "预约时间",
          dataIndex: "plan_date",
          scopedSlots: { customRender: "plan_date" },
        },
        {
          title: "操作",
          dataIndex: "action",
          scopedSlots: { customRender: "action" },
        },
      ],
      dataSource: [],
      propVisible: false,
    };
  },
  watch: {
    propVisible: function (newval) {
      newval && this.changModalStyle("138px");
      newval || this.changModalStyle("0");
    },
  },
  computed: {
    statusList: function () {
      let data = this.$store.getters.statusList;
      let obj = {};
      data.forEach((element) => {
        obj[element.id] = element;
      });
      return obj;
    },
  },
  mounted() {
    if (
      !this.selectItem ||
      (this.selectItem && JSON.stringify(this.selectItem) == "{}")
    ) {
      this.propVisible = true;
    }
    this.propVisible && this.changModalStyle("138px");
    this.propVisible || this.changModalStyle("0");
  },
  created() {
    if (this.record) {
      this.selectItem = this.record;
      this.storeList(this.selectItem);
    }
  },
  methods: {
    async changModalStyle(e) {
      await this.$asyncNextTick();
      setTimeout(async () => {
        if (!this.propVisible) return;
        let dom =
          this.$refs.detailmodal.$children[0].$children[0].$children[0].$el
            .children[1];
        if (dom) {
          if (e == "0") {
            await this.$asyncNextTick(200);
            dom.style.left = e;
          } else {
            dom.style.left = e;
          }
        }
      });
    },
    async setCusInformation(e) {
      e.cus_id = e.id;
      this.selectItem = e;
      // 先获取完整的客户信息（包括年龄段和性别），然后再发送事件
      await this.getCustomerCompleteInfo(this.selectItem);
      this.$emit("exportData", this.selectItem);
      this.storeList(this.selectItem);
      this.open = false;
      this.propVisible = false;
    },
    addCustomer() {
      this.$refs.addCustomer.getChannel();
      this.$refs.addCustomer.visible = true;
    },
    deleteItem() {
      if (this.selectItem) {
        this.list = [this.selectItem];
      } else {
        this.list = [];
      }
      this.selectItem = defaultValur();
      this.itemKey = defaultUnValur();
      this.propVisible = true;
      this.orderNum = 0;
      this.dataSource = [];
      this.itemKey = defaultUnValur();
      this.$emit("exportData", defaultUnValur());
    },
    async change(e) {
      this.selectItem = e;
      // 先获取完整的客户信息（包括年龄段和性别），然后再发送事件
      await this.getCustomerCompleteInfo(e);
      this.$emit("exportData", e);
      this.storeList(e);
      this.open = false;
      this.propVisible = false;
    },
    storeList(e) {
      let { id } = e;
      orderStore
        .storeCusList({
          cus_id: id,
          store_id: this.$store.getters.store.id,
          order_id: this.order_id || "",
        })
        .then((res) => {
          if (res.code == 200) {
            this.dataSource = res.data.list.filter(
              (item) => item.order_status == 4 || item.order_status == 3
            );
            this.orderNum = res.data.totalCount;
          }
        });
    },
    search(e) {
      this.$newDebounce(() => {
        this.loading = true;
        cardsSales.storeSearchList({ keyword: e }).then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.list = res.data.list;
          } else {
            this.$message.warning(res.message);
          }
        });
      }, 0);
    },
    openDrow() {
      this.$nextTick(() => {
        if (!this.selectText) {
          this.open = true;
          this.loading = false;
        }
      });
    },
    handleClick() {
      this.visible = true;
    },
    settlement(e) {
      this.$emit("settlement", e);
      this.visible = false;
    },
    handleOk() {},
    handleCancel() {
      this.visible = false;
    },

    // 获取完整的客户信息（包括年龄段和性别）
    async getCustomerCompleteInfo(customerInfo) {
      try {
        const res = await customer.view({
          id: customerInfo.id,
        });
        
        if (res.code === 200 && res.data && res.data.info) {
          // 将年龄段和性别信息添加到客户信息中
          // 注意：customer.view接口返回的是字符串，需要转换为数字
          if (res.data.info.age_bracket && res.data.info.age_bracket !== "0") {
            customerInfo.cus_age_bracket = parseInt(res.data.info.age_bracket, 10);
          }
          if (res.data.info.gender && res.data.info.gender !== "0") {
            customerInfo.cus_gender = parseInt(res.data.info.gender, 10);
          }
          
          console.log('dropDownSearch - 获取到的客户完整信息:', {
            age_bracket: res.data.info.age_bracket,
            gender: res.data.info.gender,
            converted_age_bracket: customerInfo.cus_age_bracket,
            converted_gender: customerInfo.cus_gender
          });
        }
      } catch (error) {
        console.error('获取客户完整信息失败:', error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.dropDownSearch {
  display: inline-flex;
  flex-flow: column;
  width: 100%;
  height: 100%;
  .dropBox {
    width: 100%;
    display: inline-block;
    .select,
    .input {
      width: 100%;
    }
  }
  .customerList {
    position: relative;
    flex: 1;
    height: 0;
    width: 100%;
    margin-top: 10px;
    overflow-y: scroll;
    padding-right: 10px;
    .empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .btn {
    position: relative;
    height: 40px;
    font-size: 16px;
    margin: 0;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  /deep/ .ant-select-selection,
  .ant-input,
  .ant-tag {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
}
.dropBox input {
  height: 40px;
}
.theCustomerInformation {
  background: @bg-color-page;
  padding: 0;
  padding: 15px 0 15px 24px;
  font-size: 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  .detailBox {
    display: flex;
    align-items: center;
    flex: 1;
    .detail-tag {
      height: 15px;
      line-height: 6px;
      transform: scale(0.8);
    }
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 10px;
    }
  }
}
.customerInformationSelect {
  width: 400px;
  border-radius: 3px;
  background: @bg-color-component;
  img {
    width: 48px !important;
    height: 48px !important;
  }
  .icon {
    font-size: 18px;
  }
  .delete {
    display: inline-block;
    line-height: 20px;
    text-align: center;
    color: @font-color-gray;
    margin: 0 10px;
    cursor: pointer;
    font-size: 12px;
  }
  .order {
    position: relative;
    cursor: pointer;
    &::after {
      position: absolute;
      display: inline-block;
      border-radius: 50%;
      font-size: 12px;
      color: #fff;
      content: attr(data-orderNum);
      background: @brand-color;
      top: 0;
      right: 0;
      width: 20px;
      line-height: 20px;
      text-align: center;
      transform: translate(50%, -50%) scale(0.7);
    }
  }
}
.notFoundContent {
  text-align: center;
  cursor: pointer;
  font-size: 13px;
}
.order_no {
  white-space: nowrap;
}
.orderTag {
  transform: scale(0.8);
  transform-origin: left;
}
</style>
