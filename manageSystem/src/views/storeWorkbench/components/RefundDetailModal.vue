<template>
  <a-modal
    v-model="visible"
    title="退款申请详情"
    :width="1000"
    :footer="null"
    @cancel="handleCancel"
    :destroyOnClose="true"
    class="refund-detail-modal"
  >
    <div class="refund-detail-content" v-if="detailData">
      <!-- 基本信息区域 -->
      <div class="info-group">
        <div class="info-item">
          <label class="info-label">申请单号：</label>
          <span class="info-value">{{ detailData.application_no }}</span>
        </div>
        <div class="info-item">
          <label class="info-label">客户姓名：</label>
          <span class="info-value">{{ detailData.customer_name }}</span>
        </div>
        <div class="info-item">
          <label class="info-label">客户电话：</label>
          <span class="info-value">{{ detailData.customer_phone }}</span>
        </div>
        <div class="info-item">
          <label class="info-label">门店名称：</label>
          <span class="info-value">{{ detailData.store_name }}</span>
        </div>
        <div class="info-item">
          <label class="info-label">实付金额：</label>
          <span class="info-value">¥{{ detailData.total_order_amount }}</span>
        </div>
        <div class="info-item">
          <label class="info-label">服务老师：</label>
          <span class="info-value">{{ detailData.teacher_name }}</span>
        </div>
        <div class="info-item">
          <label class="info-label">应退金额：</label>
          <span class="info-value">¥{{ detailData.total_refund_amount }}</span>
        </div>
        <div class="info-item">
          <label class="info-label">状态：</label>
          <span class="info-value">
            <a-tag :color="getApprovalStatusColor(detailData.approval_status)">
              {{ detailData.approval_status_text }}
            </a-tag>
          </span>
        </div>
        <div class="info-item">
          <label class="info-label">退款原因：</label>
          <span class="info-value">{{ detailData.refund_reason }}</span>
        </div>
        <div class="info-item">
          <label class="info-label">创建时间：</label>
          <span class="info-value">{{ detailData.created_at_text }}</span>
        </div>
        <div class="info-item">
          <label class="info-label">出纳确认：</label>
          <span class="info-value">{{ detailData.cashier_confirm }}</span>
        </div>
      </div>

      <!-- 订单明细 -->
      <div class="order-table-section" v-if="detailData.details && detailData.details.length">
        <h4 class="table-title">关联订单</h4>
        <a-table 
          :dataSource="detailData.details" 
          :columns="orderColumns"
          :pagination="false"
          size="small"
          rowKey="order_no"
          class="order-table"
        >
          <template slot="order_amount" slot-scope="text">
            ¥{{ parseFloat(text).toFixed(2) }}
          </template>
          <template slot="refund_amount" slot-scope="text">
            <span style="color: #f5222d; font-weight: 600;">¥{{ parseFloat(text).toFixed(2) }}</span>
          </template>
        </a-table>
      </div>

      <!-- 收款账户信息 -->
      <div class="bank-info-section">
        <h4 class="bank-title">收款账户信息</h4>
        <div class="bank-info-content">
          <div class="bank-info-item">
            <label class="bank-label">账户类型：</label>
            <span class="bank-value">{{ getBankAccountInfo('account_type') }}</span>
          </div>
          <div class="bank-info-item">
            <label class="bank-label">户名：</label>
            <span class="bank-value">{{ getBankAccountInfo('account_name') }}</span>
          </div>
          <div class="bank-info-item">
            <label class="bank-label">账号：</label>
            <span class="bank-value">{{ getBankAccountInfo('account_no') }}</span>
          </div>
          <div class="bank-info-item">
            <label class="bank-label">银行：</label>
            <span class="bank-value">{{ getBankAccountInfo('bank_name') }}</span>
          </div>
          <div class="bank-info-item" v-if="getBankAccountInfo('bank_location')">
            <label class="bank-label">银行所在地：</label>
            <span class="bank-value">{{ getBankAccountInfo('bank_location') }}</span>
          </div>
          <div class="bank-info-item" v-if="getBankAccountInfo('bank_branch')">
            <label class="bank-label">支行名称：</label>
            <span class="bank-value">{{ getBankAccountInfo('bank_branch') }}</span>
          </div>
        </div>
      </div>

      <!-- 附件 -->
      <div class="detail-section" v-if="detailData.attachment_urls_array && detailData.attachment_urls_array.length">
        <h4 class="section-title">付款截图及退款协议照片</h4>
        <div class="upload-area">
          <div class="attachment-list">
            <div 
              v-for="(item, index) in detailData.attachment_urls_array" 
              :key="index"
              class="attachment-item"
            >
              <img 
                :src="getAttachmentUrl(item)" 
                :alt="`附件${index + 1}`"
                @click="handlePreview(getAttachmentUrl(item))"
                class="attachment-image"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 审批流程 -->
      <div class="detail-section" v-if="detailData.ding_data && detailData.ding_data.data && detailData.ding_data.data.length">
        <h4 class="section-title">审批流程</h4>
        <a-timeline>
          <a-timeline-item 
            color="rgba(138, 180, 248,1)" 
            v-for="(item, index) in detailData.ding_data.data" 
            :key="index"
          >
            <div class="approval-step">
              <div class="step-header">
                <b>{{ item.type }}</b>
                <span>{{ index === 0 ? item.create_time : item.finish_time }}</span>
              </div>
              <span class="step-operator">{{ item.name_cn }}</span>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>

      <!-- 审批结果 -->
      <div class="detail-section" v-if="detailData.approval_result">
        <h4 class="section-title">审批结果</h4>
        <div class="approval-result">
          {{ detailData.approval_result }}
        </div>
      </div>
    </div>

    <!-- 图片预览 -->
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancelPreview">
      <img alt="preview" style="width: 100%" :src="previewImage" />
    </a-modal>
  </a-modal>
</template>

<script>

export default {
  name: 'RefundDetailModal',
  data() {
    return {
      visible: false,
      detailData: null,
      previewVisible: false,
      previewImage: '',
      
      // 订单明细表格列
      orderColumns: [
        {
          title: '订单编号',
          dataIndex: 'order_no',
          align: 'center'
        },
        {
          title: '订单金额',
          dataIndex: 'order_amount',
          align: 'center',
          scopedSlots: { customRender: 'order_amount' }
        },
        {
          title: '退款金额',
          dataIndex: 'refund_amount',
          align: 'center',
          scopedSlots: { customRender: 'refund_amount' }
        }
      ]
    }
  },
  
  methods: {
    // 打开详情弹窗（接收数据参数）
    open(data) {
      this.detailData = data
      this.visible = true
    },
    
    // 关闭弹窗
    handleCancel() {
      this.visible = false
      this.detailData = null
    },
    
    // 获取申请状态颜色
    getStatusColor(status) {
      const colors = {
        1: 'blue',    // 待审批
        2: 'green',   // 审批通过  
        3: 'red',     // 审批拒绝
        4: 'orange',  // 已撤销
        9: 'gray'     // 已删除
      }
      return colors[status] || 'default'
    },
    
    // 获取审批状态颜色
    getApprovalStatusColor(status) {
      const colors = {
        0: 'blue',    // 草稿
        1: 'blue',    // 审核中
        2: 'red',     // 审核未通过
        3: 'gray',    // 审核已撤销
        4: 'gray',    // 审核已撤销
        5: 'green'    // 审核通过
      }
      return colors[status] || 'default'
    },
    
    // 预览图片
    handlePreview(url) {
      this.previewImage = url
      this.previewVisible = true
    },
    
    // 关闭图片预览
    handleCancelPreview() {
      this.previewVisible = false
    },
    
    // 获取银行账户信息
    getBankAccountInfo(field) {
      if (!this.detailData) return ''
      
      // 优先使用新的bank_account_info字段
      let bankAccountData = null
      
      if (this.detailData.bank_account_info) {
        bankAccountData = this.detailData.bank_account_info
      }
      
      if (!bankAccountData) {
        // 如果没有找到JSON数据，回退到原有字段
        const fieldMap = {
          'account_type': 'bank_account_type_text',
          'account_no': 'bank_account_no',
          'account_name': 'bank_account_name',
          'bank_name': 'bank_name',
          'bank_branch': 'bank_branch',
          'bank_location': 'bank_location'
        }
        return this.detailData[fieldMap[field]] || ''
      }
      
      // 处理账户类型显示
      if (field === 'account_type') {
        return this.detailData.bank_account_type_text || bankAccountData[field] || ''
      }
      
      return bankAccountData[field] || ''
    },
    
    // 获取附件URL
    getAttachmentUrl(item) {
      return (item && item.url) || ''
    }
  }
}
</script>

<style lang="less" scoped>
.refund-detail-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .ant-modal-body {
    background-color: #f5f5f5;
    border-radius: 10px;
    padding: 20px;
  }
}

.refund-detail-content {
  padding: 0 16px;
  
  // 基本信息区域
  .info-group {
    padding: 0 20px 10px;
    background: #fff;
    border-radius: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 0px 24px;
    
    .info-item {
      flex: 0 0 calc(50% - 12px);
      display: flex;
      padding: 8px 0;
      position: relative;
      
      .info-label {
        min-width: 85px;
        font-size: 14px;
        color: #666;
        font-weight: 500;
        margin-right: 8px;
        text-align: right;
      }
      
      .info-value {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }
    }
  }
  
  // 通用区域样式
  .detail-section {
    padding: 20px;
    background: #fff;
    border-radius: 10px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      position: relative;
      padding-left: 12px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: linear-gradient(45deg, #9373ee, #b794f6);
        border-radius: 2px;
      }
    }
    
    .reason-content, .approval-result {
      padding: 16px;
      background: #fafafa;
      border-radius: 8px;
      min-height: 60px;
      line-height: 1.6;
      color: #333;
      font-size: 14px;
    }
    
    .upload-area {
      .attachment-list {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        
        .attachment-item {
          .attachment-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            
            &:hover {
              border-color: #9373ee;
              transform: scale(1.02);
            }
          }
        }
      }
    }
  }
  
  // 订单表格区域
  .order-table-section {
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    
    .table-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      position: relative;
      padding-left: 12px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: linear-gradient(45deg, #9373ee, #b794f6);
        border-radius: 2px;
      }
    }
    
    .order-table {
      /deep/ .ant-table {
        border-radius: 8px 8px 0 0;
        overflow: hidden;
      }
      
      /deep/ .ant-table-small > .ant-table-content > .ant-table-body {
        margin: 0;
      }
      
      /deep/ .ant-table-thead > tr > th {
        background: #f8f9fa;
        border-bottom: 2px solid #e8e8e8;
        font-weight: 600;
        color: #333;
        padding: 12px 16px;
        
        &:first-child {
          border-left: none;
        }
        
        &:last-child {
          border-right: none;
        }
      }
      
      /deep/ .ant-table-container {
        border-left: none;
        border-right: none;
      }
      
      /deep/ .ant-table-tbody > tr {
        transition: all 0.3s;
        
        &:hover {
          background: #f6f8fa;
        }
      }
      
      /deep/ .ant-table-tbody > tr > td {
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 16px;
        
        &:first-child {
          border-left: none;
        }
        
        &:last-child {
          border-right: none;
        }
      }
      
      /deep/ .ant-table-tbody > tr:last-child > td {
        border-bottom: none;
      }
    }
  }
  
  // 银行信息区域
  .bank-info-section {
    padding: 24px;
    background: #fff;
    border-radius: 12px;
    
    .bank-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
      position: relative;
      padding-left: 12px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: linear-gradient(45deg, #9373ee, #b794f6);
        border-radius: 2px;
      }
    }
    
    .bank-info-content {
      .bank-info-item {
        display: flex;
        padding: 8px 0;
        position: relative;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .bank-label {
          min-width: 85px;
          font-size: 14px;
          color: #666;
          margin-right: 10px;
          font-weight: 500;
          text-align: right;
        }
        
        .bank-value {
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
  
  // 审批流程区域
  .approval-step {
    .step-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      
      b {
        color: #333;
        font-size: 14px;
        font-weight: 600;
      }
      
      span {
        color: #666;
        font-size: 12px;
      }
    }
    
    .step-operator {
      color: #666;
      font-size: 13px;
    }
  }
}
</style>