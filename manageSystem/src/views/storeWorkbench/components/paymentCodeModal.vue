<template>
  <a-modal :visible="paymentCodeVisible" title="录入流水号" width="1000" @cancel="handleClose" centered>
    <a-alert type="warning" show-icon>
      <p slot="message" class="mb0">多笔流水，请一笔一笔录入。</p>
    </a-alert>
    <div class="mt15" style="width:470px">
      <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol" ref="paymentForm">
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" :colon="false" label="流水号">
          <a-input 
            style="width: 350px" 
            placeholder="请输入流水号" 
            size="large"
            v-decorator="['bar_code', validatorRules.bar_code]"
          ></a-input>
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" :colon="false" label="金额">
          <a-input-number
            style="width: 200px"
            placeholder="请输入金额"
            :min="0"
            size="large"
            v-decorator="['amount', validatorRules.amount]"
          ></a-input-number>
        </a-form-item>
      </a-form>
    </div>
    <template slot="footer">
      <div style="text-align: center">
        <a-button key="back" @click="handleClose">取消</a-button>
        <a-button key="submit" type="primary" @click="confirmWater">确认</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script>
import moment from "moment";
export default {
  name: "paymentCodeModal",
  props: {
    paymentCodeVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      moment,
      loading: false,
      labelCol: {
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 16 }
      }
    };
  },
  computed: {
    validatorRules: function() {
      return {
        bar_code: { rules: [{ required: true, message: "请输入流水号!" }] },
        amount: { rules: [{ required: true, message: "请输入金额!" }] },
      };
    }
  },
  methods: {
    handleClose() {
      this.form.resetFields();
      this.$emit("handlePaymentCodeClose");
    },
    confirmWater() {
      if(this.loading){
        return;
      }
      this.loading = true;
      this.form.validateFields((err, values) => {
        if (!err) {
          const { bar_code, amount } = values;
          this.$emit("confirmPaymentCode", { bar_code, amount });
        }else{
          this.loading = false;
        }
      });
    }
  }
};
</script>
