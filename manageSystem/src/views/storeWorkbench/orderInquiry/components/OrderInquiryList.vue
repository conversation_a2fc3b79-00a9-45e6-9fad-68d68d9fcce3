<template>
  <div>
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col-max label="时间搜索">
          <self-picker
            :disabledDate="(e) => false"
            :selectData="selectData"
            :selectValue="selcetId"
            timeRange="month"
            v-model="selfDefaultTime.promote"
            :reset="timeReset"
            :InitializationTime="false"
            :timeKey="{
              start: 'created_start_time',
              end: 'created_end_time',
            }"
          />
        </self-col-max>
        <self-col label="客户信息">
          <a-input
            placeholder="请输入姓名或手机号"
            v-model="queryParam.search_customer"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="订单编号">
          <a-input
            placeholder="请输入订单编号"
            v-model="queryParam.order_no"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="订单状态">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'channelList')"
            placeholder="请选择订单状态"
            v-model="queryParam.order_status"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option value>全部</a-select-option>
            <a-select-option
              :key="index"
              :value="item.id"
              v-for="(item, index) in statusList"
              >{{ item.name }}</a-select-option
            >
          </a-select>
        </self-col>
        <self-col label="下单人员">
          <a-input
            placeholder="请输入员工姓名"
            v-model="queryParam.created_by"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="预约人员">
          <a-input
            placeholder="请输入员工姓名"
            v-model="queryParam.plan_by"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="支付流水号">
          <a-input
            placeholder="请输入支付流水号"
            v-model="queryParam.out_trade_no"
            allowClear
          ></a-input>
        </self-col>
      </template>
    </self-page-header>
    <div v-if="SummaryShow" class="progress-box">
      <a-progress :percent="CitySummary"></a-progress>
    </div>
    <list-table
      class="mt16"
      :dataSource="dataSourceFormat"
      :columns="columns"
      :loading="loading"
      :pagination="ipagination"
      v-if="render"
    >
      <template slot="title" slot-scope="recored">
        <div class="mb5">{{ recored.data.cus_name || "-" }}</div>
        <div>
          {{ recored.data.cus_mobile }}
          <a
            @click="handleShow(recored.data)"
            v-has="'order-order-header:mobile-check'"
          >
            显示
          </a>
        </div>
      </template>
      <template slot="order_status_name" slot-scope="data">
        <span class="status">
          <a-tag :color="tagColor['status_' + data.data.order_status]">{{
            data.data.order_status_name
          }}</a-tag>
        </span>
      </template>
      <template slot="pre_pay_status" slot-scope="data">{{
        data.data.pre_pay_status == "0" ? "未支付" : "已支付"
      }}</template>
      <span slot="action" slot-scope="data" v-divider>
        <a
          v-has="'order-order-header:view'"
          @click="editor(data)"
          v-if="
            [0, 1, 3].some((l) => l == data.data.order_status) &&
            data.data.source_type == 2
          "
        >
          预约修改
        </a>
        <a v-has="'order-order-header:view'" @click="handleEdit(data)">
          详情
        </a>
        <a
          v-has="'order-order-header:store-retract'"
          v-if="data.data.order_status === 3 || data.data.order_status === 4"
          href="javascript:;"
          @click="order_retract(data.data)"
        >
          撤回已预约
        </a>
        <a
          @click="cancelAfterVerification(data)"
          v-has="'order-order-header:store-view-for-edit'"
          v-if="data.data.order_status === 3 || data.data.order_status === 4"
        >
          结算
        </a>
       
        <a
          @click="toOrderAgain(data)"
          v-has="'order-order-header:other-create'"
          v-if="data.data.order_status === 5"
        >
          新增预约
        </a>
        <self-dropdown
          v-if="data.data.order_status === 5 || data.data.order_status === 6"
        >
          <a
            v-has="'order-order-header:store-view-for-print'"
            href="javascript:;"
            @click="printText(data.data)"
          >
            打印小票
          </a>
          <a
            v-has="'order-order-header:store-cancel'"
            v-if="
              [0, 1, 4].some((l) => l == data.data.order_status) &&
              data.data.received_amount == 0
            "
            href="javascript:;"
            @click="order_cancel(data.data)"
          >
            取消订单
          </a>
          <a
            @click="handleRefundApply(data)"
            v-if="data.data.order_status === 5"
            v-has="'order-refund:create'"
          >
            申请退款
          </a>
        </self-dropdown>
      </span>
    </list-table>
    <self-detail-info ref="self-detail-info" />
    
    <button
      v-selfPrint="'#printContent'"
      ref="printContent"
      style="display: none"
    ></button>
    <self-printText
      :info="printTextInfo"
      :showPagePring="false"
      id="printContent"
    />
    <div class="loading" v-if="printLoading">
      <a-spin tip="正在加载..."> </a-spin>
    </div>
    <a-modal
      v-model="phoneLook"
      title=""
      centered
      :width="320"
      destroyOnClose
      :footer="false"
      @ok="handleOk"
    >
      <div class="phone-look">{{ phone }}</div>
      <div class="phone-info">客户手机号是公司重要信息，请勿分享给他人</div>
      <div class="btn-modal">
        <a-button key="submit" class="btn" type="primary" @click="handleOk">
          确定
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import {
  inventoryManage,
  theOrderManagementByorder,
  payCode,
  storeManage,
} from "@/api/api.js";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import commentMixin from "@/mixins/commentMixin";
import listTable from "../listTable";
import pick from "lodash.pick";
import moment from "moment";
import pageTitle from "../../components/pageTitle.vue";

export default {
  name: "OrderInquiryList",
  mixins: [JeecgListMixin, commentMixin],
  components: { listTable, pageTitle },
  data() {
    return {
      moment,
      loading: false,
      confirmLoading: false,
      propvisible: false,
      phoneLook: false,
      showTable: true,
      phone: "",
      selfDefaultTime: {
        promote: {},
      },
      printTextInfo: {},
      printLoading: false,
      info: {
        goods_list: [],
      },
      tagColor: {
        status_0: "blue",
        status_1: "blue",
        status_2: "red",
        status_3: "orange",
        status_4: "orange",
        status_5: "green",
        status_6: "green",
        status_7: "red",
        status_8: "red",
        status_9: "red",
      },
      timeValue: [],
      selcetId: 1,
      searchStoreByEntity: inventoryManage.storeSelect,
      getAllList: theOrderManagementByorder.getAllList,
      getListByOrder: theOrderManagementByorder.getListByOrder,
      exportApi: theOrderManagementByorder.export,
      header: [
        "客户名称",
        "客户电话",
        "渠道来源",
        "下单人员",
        "预约人员",
        "订单来源",
        "定金金额",
        "订单状态",
        "预约时间",
      ],
      selectData: [
        {
          id: 1,
          lable: "预约时间",
          timeKey: {
            start: "plan_start_time",
            end: "plan_end_time",
          },
        },
        {
          id: 2,
          lable: "创建时间",
          timeKey: {
            start: "created_start_time",
            end: "created_end_time",
          },
        },
        {
          id: 3,
          lable: "预收金时间",
          timeKey: {
            start: "pre_begin_time",
            end: "pre_end_time",
          },
          disabledDate: (current) => current && current > moment().endOf("day"),
        },
        {
          id: 4,
          lable: "结算时间",
          timeKey: {
            start: "settlement_begin_time",
            end: "settlement_end_time",
          },
        },
      ],
      export_: {},
      statusTheme: {},
      statusList: [
        {
          id: 0,
          name: "待预约",
        },
        {
          id: 1,
          name: "已预约",
        },
        {
          id: 2,
          name: "已取消",
        },
        {
          id: 3,
          name: "已到店",
        },
        {
          id: 4,
          name: "待结算",
        },
        {
          id: 5,
          name: "已完成",
        },
        {
          id: 6,
          name: "第三方结算",
        },
        {
          id: 7,
          name: "申请退订",
        },
        {
          id: 8,
          name: "售后服务",
        },
        {
          id: 9,
          name: "已放弃",
        },
        {
          id: 10,
          name: "作废",
        },
      ],
      time: {
        start: "",
        end: "",
      },
      queryParam: {
        order_status: "",
        store_id: this.$store.getters.store.id || "-1",
        source_type: "",
      },
      columns: [
        {
          title: "客户信息",
          width: 260,
          align: "left",
          slot: "title",
        },
        {
          title: "渠道来源",
          dataIndex: "channel_name",
          align: "left",
        },
        {
          title: "下单人员",
          dataIndex: "created_by_text",
          align: "left",
        },
        {
          title: "预约人员",
          dataIndex: "plan_name",
          align: "left",
        },
        {
          title: "订单来源",
          dataIndex: "source_type_name",
          align: "left",
        },
        {
          title: "订金金额",
          dataIndex: "deposit",
          align: "left",
        },
        {
          title: "实收金额",
          dataIndex: "received_amount",
          align: "left",
        },
        {
          title: "订单状态",
          dataIndex: "order_status_name",
          align: "center",
          slot: "order_status_name",
        },
        {
          title: "预约时间",
          width: 180,
          dataIndex: "plan_time_text",
          align: "left",
        },
        {
          title: "操作",
          align: "center",
          dataIndex: "channel_name",
          width: 270,
          slot: "action",
        },
      ],
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        pageSizeOptions: ["20", "50", "100", "200"],
        showSizeChanger: true,
        change: (e) => {
          this.loadData();
        },
        showTotal: function (total, range) {
          let page = range[1] + "/页 共" + total + "条";
          return page;
        },
      },
      md: 12,
      url: {
        list: "/order/order-header/store-order-list",
      },
      render: true,
    };
  },
  created() {
    const clientWidth = document.body.clientWidth;
    if (clientWidth < 1500) {
      this.md = 16;
    }
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      d.forEach((item) => {
        item.phoneShow = true;
      });
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    },
  },
  mounted() {
    window.addEventListener("resize", () => {
      this.$newDebounce(async () => {
        this.render = false;
        await this.$asyncNextTick();
        this.render = true;
      }, 100);
    });
  },
  destroyed() {
    window.removeEventListener("resize", () => {
      this.$newDebounce(async () => {
        this.render = false;
        await this.$asyncNextTick();
        this.render = true;
      }, 100);
    });
  },
  methods: {
    order_cancel({ id }) {
      this.$confirm({
        title: "提示",
        content: "是否取消当前订单",
        onOk: () => {
          return new Promise((resolve) => {
            storeManage
              .storeCancel({ id })
              .then((res) => {
                if (res.code == "200") {
                  this.$message.success(res.message);
                  this.loadData();
                }
              })
              .finally(() => resolve());
          });
        },
      });
    },
    order_retract({ id }) {
      this.$confirm({
        title: "提示",
        content: "订单是否确定撤回已预约",
        onOk: () => {
          return new Promise((resolve) => {
            storeManage
              .storeRetract({ id })
              .then((res) => {
                if (res.code == "200") {
                  this.$message.success(res.message);
                  this.loadData();
                }
              })
              .finally(() => resolve());
          });
        },
      });
    },
    editor({ data }) {
      this.$router.push({
        name: "新增预约",
        params: {
          record: data,
          type: 1,
        },
      });
    },
    printText({ id }) {
      this.printLoading = true;
      payCode.storeViewForPrint({ id }).then((res) => {
        if (res.code === 200) {
          this.info = this.printTextInfo = res.data;
          this.$nextTick(() => {
            this.$refs.printContent.click();
          });
        } else {
          this.$message.error(res.message);
        }
        this.printLoading = false;
      });
    },
    newBooking() {
      this.$router.push({
        name: "新增预约",
      });
    },
    //导出-数据格式
    dataFormat({ list }) {
      let arr = [];
      for (let i = 0; i < list.length; i++) {
        let b = list[i];
        let key = [
          "cus_name",
          "cus_mobile",
          "channel_name",
          "created_name",
          "plan_name",
          "source_type_name",
          "deposit",
          "order_status_name",
          "plan_date",
        ];
        b = this.$utils.fieldCompletion(key, b);
        let nb = pick(b, ...key);
        arr.push(nb);
      }
      return arr;
    },
    // handleEdit
    handleEdit(e) {
      const id = e.data.id;
      this.$refs["self-detail-info"].handleView(id);
    },
    cancelAfterVerification(e) {
      const record = e.data;
      // 设置缓存
      this.$store.commit("SET_PERMISSIONLISTPASSROUTER", ["toPayADeposit"]);
      this.$router.push({
        name: "开单收银",
        params: {
          settlementType: "settlementOrder",
          record,
        },
      });
    },
    toOrderAgain(e) {
      const record = e.data;
      // 设置缓存
      this.$store.commit("SET_PERMISSIONLISTPASSROUTER", ["toPayADeposit"]);
      this.$router.push({
        name: "新增预约",
        params: {
          record,
          type: 2,
        },
      });
    },
    handleShow(data) {
      // data.phoneShow = !data.phoneShow;
      const { cus_id } = data;
      if (!data.phoneShow) data.mobile = data.cus_mobile;
      theOrderManagementByorder.mobileCheck({ cus_id }).then((res) => {
        if (res.code == 200) {
          data.mobile = res.data.mobile;
          this.phone = res.data.mobile;
          this.phoneLook = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    handleOk() {
      this.phoneLook = false;
    },
    
    // 申请退款 - 通过事件通知父组件
    handleRefundApply(data) {
      console.log('OrderInquiryList: handleRefundApply called with data:', data);
      
      // 通过 emit 事件通知父组件打开退款申请弹窗
      this.$emit('refund-apply', data);
    },
  },
};
</script>

<style lang="less" scoped>
.top-title {
  font-size: 24px;
  font-weight: bold;
}
.aircraftAction {
  color: #52c41a;
}
.status_1,
.status_2 {
  color: green;
}
.status_3,
.status_4,
.status_5 {
  color: orange;
}
.status_6,
.status_7,
.status_8 {
  color: red;
}

.name .ant-tag {
  position: relative;
  left: -2px;
  padding: 0 7px !important;
  transform: scale(0.8);
  margin-left: 10px;
}
/deep/ .ant-modal-body {
  padding: 45px 0 40px;
  .phone-look {
    text-align: center;
    font-family: MiSans-Medium;
    font-size: 34px;
    letter-spacing: -1px;
    color: #333;
  }
  .phone-info {
    font-size: 10px;
    color: #999;
    padding: 3px 30px 15px;
    text-align: center;
  }
  .btn-modal {
    text-align: center;
    margin-top: 10px;
    .btn {
      width: 260px;
      border-radius: 21px !important;
      height: 42px;
      line-height: 42px;
    }
  }
}

.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.ant-modal-close-x {
  display: none;
}
</style>