<template>
  <div>
    <a-card :bordered="false">
      <!-- Tab切换 -->
      <a-tabs :default-active-key="activeTab" @change="callback">
        <a-tab-pane key="orders" tab="订单查询">
          <order-inquiry-list ref="orderInquiryList" @refund-apply="handleRefundApply" />
        </a-tab-pane>
        <a-tab-pane key="refunds" tab="退款申请列表" v-if="hasRefundPermission">
          <refund-application-list-component ref="refundApplicationList" />
        </a-tab-pane>
      </a-tabs>
      
      <!-- 退款申请弹窗 -->
      <refund-application-modal 
        ref="refundModal" 
        @success="handleRefundSuccess"
      />
    </a-card>
  </div>
</template>

<script>
import RefundApplicationModal from "../components/RefundApplicationModal.vue";
import RefundApplicationListComponent from "../refundApplication/refundApplicationList.vue";
import OrderInquiryList from "./components/OrderInquiryList.vue";
import { USER_AUTH } from "@/store/mutation-types";

export default {
  name: "orderInquiry",
  components: { 
    RefundApplicationModal, 
    RefundApplicationListComponent,
    OrderInquiryList
  },
  data() {
    return {
      activeTab: "orders",
    };
  },
  computed: {
    // 检查是否有退款申请列表权限
    hasRefundPermission() {
      try {
        const authList = JSON.parse(sessionStorage.getItem(USER_AUTH) || "[]");
        const permissions = authList
          .filter(auth => auth.type != '2') // 过滤掉type为2的权限
          .map(auth => auth.action);
        // 使用正确的权限标识
        return permissions.includes('order-refund:index');
      } catch (error) {
        console.error('获取权限数据失败:', error);
        return false;
      }
    }
  },
  methods: {
    callback(e) {
      this.activeTab = e;
      
      // 切换到退款申请列表时，自动刷新数据
      if (e === 'refunds') {
        this.$nextTick(() => {
          const refundListComponent = this.$refs.refundApplicationList;
          if (refundListComponent && typeof refundListComponent.loadData === 'function') {
            console.log('切换到退款申请列表，刷新数据...');
            refundListComponent.loadData();
          } else {
            console.warn('退款申请列表组件未找到或loadData方法不存在', refundListComponent);
          }
        });
      }
    },
    
    // 申请退款 - 从子组件接收事件
    handleRefundApply(data) {
      console.log('orderInquiry: handleRefundApply called with data:', data);
      console.log('refundModal ref:', this.$refs.refundModal);
      
      // 打开退款申请弹窗，传入客户ID、客户信息和当前订单ID
      const customerInfo = {
        name: data.data.cus_name,
        phone: data.data.cus_mobile,
        storeName: data.data.store_name
      }
      
      console.log('customerInfo:', customerInfo);
      console.log('customerId:', data.data.cus_id);
      console.log('currentOrderId:', data.data.id);
      
      // 获取当前门店ID
      const storeId = this.$store.getters.store.id;
      console.log('storeId:', storeId);
      
      if (this.$refs.refundModal) {
        this.$refs.refundModal.open(data.data.cus_id, customerInfo, data.data.id, storeId);
      } else {
        console.error('refundModal ref not found!');
        this.$message.error('退款申请组件未找到，请刷新页面重试');
      }
    },
    
    // 退款申请成功回调
    handleRefundSuccess() {
      // 刷新订单列表数据
      this.$nextTick(() => {
        const orderListComponent = this.$refs.orderInquiryList;
        if (orderListComponent && typeof orderListComponent.loadData === 'function') {
          console.log('刷新订单列表数据...');
          orderListComponent.loadData();
        } else {
          console.warn('订单列表组件未找到或loadData方法不存在', orderListComponent);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ant-tabs-nav .ant-tabs-tab {
  padding: 0 16px 12px 16px !important;
  font-size: 16px;
}
/deep/ .ant-tabs-bar {
  margin: 0 !important;
}
</style>