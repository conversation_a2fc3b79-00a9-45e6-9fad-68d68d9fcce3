<template>
  <a-card :bordered="false">
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="审批时间">
          <self-time
            timeRange="month"
            v-model="selfDefaultTime.promote"
            :reset="timeReset"
            :InitializationTime="false"
            :timeKey="{
              start: 'approval_time_start',
              end: 'approval_time_end',
            }"
            :disabledDate="() => false"
          />
        </self-col>
        <self-col label="付款单号">
          <a-input placeholder="请输入付款单号" v-model="queryParam.list_no" allowClear></a-input>
        </self-col>
        <self-col label="订单状态">
          <commone-self-principal
            searchKey="keyword"
            placeholder="请选择状态"
            :selectData="approvalStatus"
            value_key="name"
            midwayChange
            v-model="queryParam.status"
          />
        </self-col>
        <self-col label="飞书审批号">
          <a-input
            @click.native="handleClick"
            readonly
            :placeholder="
              queryParam.serial_number.length === 0
                ? `输入飞书审批号`
                : `${queryParam.serial_number.length}条`
            "
          ></a-input>
          <a-modal title="飞书审批号" :visible="visible" @ok="handleOk" @cancel="handleCancel">
            <a-form :form="form" style="width: 100%">
              <a-form-item>
                <div>
                  <span style="color: 20px; font-size: 20px; font-weight: bold">飞书审批号</span>
                  <span class="labelTips">已输入{{ wechatList.length }}条</span>
                </div>
                <a-textarea
                  type="text"
                  :rows="6"
                  ref="textarea"
                  placeholder="请输入飞书审批号,多个飞书审批号换行分隔"
                  @change="cutting"
                />
              </a-form-item>
            </a-form>
            <template slot="footer">
              <a-button @click="handleOk">确认</a-button>
            </template>
          </a-modal>
        </self-col>
      </template>
      <!-- 导出 -->
      <!-- <template slot="export">
        <export-to-csv  
          :dataFormat="dataFormat"
          :query="queryParam"
          fileName="付款列表"
          :limit="1000"
          class="ml0 mr10"
          :total="ipagination.total"
          :queryParam="queryParam"
          :CommentApi="inventoryManage.exportList"
          :header="[
            '采购单号',
            '采购单名称',
          ]"
        ></export-to-csv>
      </template>-->
    </self-page-header>
    <!--付款单列表-->
    <a-table
      ref="table"
      bordered
      size="middle"
      rowKey="id"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :pagination="ipagination"
      :loading="loading"
      :scroll="{ x: 1200 }"
      @change="handleTableChange"
    >
      <span
        slot="status"
        slot-scope="text"
      >{{ (approvalStatus.find(item => item.id == text)||{}).name || '未知状态'}}</span>
      <span slot="list_no" slot-scope="list_no">
        <j-ellipsis :value="list_no" :length="25" />
      </span>
      <span slot="approval_time_text" slot-scope="approval_time_text">
        <template v-if="!approval_time_text">-</template>
        <template v-else>{{ approval_time_text }}</template>
      </span>
      <span slot="action" slot-scope="text, record" v-divider>
        <a href="javascript:;" @click="showDetail(record)" v-has="'procurement-pay:view'">查看详情</a>
        <a-popconfirm
          v-if="isAgainPay(record)" 
          title="是否确定重新发起？"
          ok-text="确定"
          cancel-text="取消"
          @confirm="againPay(record)"
        >
          <a v-has="'procurement-pay:approve'" type="primary" :loading="approveLoading">发起审批</a>
        </a-popconfirm>
      </span>

    </a-table>

    <!--订单详情-->
    <j-modal
      :visible.sync="modal2.visible"
      :width="1400"
      :title="modal2.title"
      :fullscreen.sync="modal2.fullscreen"
      :switchFullscreen="modal2.switchFullscreen"
      :confirmLoading="modal2.confirmLoading"
    >
      <template>
        <div v-if="modal2.data">
          <p class="tit f16 mb10">{{ modal2.data.info.reason_title }}</p>
          <a-table
            :columns="columns2"
            :data-source="listdata.info.detailList"
            :loading="modalTablloading"
            :pagination="{ current: moadlTableCurrent }"
            @change="
              (e) => {
                moadlTableCurrent = e.current;
              }
            "
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            bordered
          >
            <template slot="name" slot-scope="name">
              <j-ellipsis :value="name" :length="10" />
            </template>
          </a-table>
          <p class="f14 mb10">付款金额：{{ modal2.data.info.amount }}</p>
          <p class="f14 mb10">付款方式：{{ modal2.data.info.pay_type }}</p>
          <p class="f14 mb10">付款日期：{{ modal2.data.info.pay_time_text }}</p>
          <p class="f14 mb10">银行账户：</p>
          <p
            class="ml60 mb10"
            :right="20"
            v-for="(bank_info_fields,bank_info_value) in modal2.data.info.bank_info"
            :key="bank_info_value"
          >{{bank_info_value}}：{{bank_info_fields}}</p>
          <p class="f14 mb10">备注：{{ modal2.data.info.remark }}</p>
          
          <a-form-item label="图片" class="images">
            <img
              v-for="(item, index) in modal2.data.info.attachment.attachment"
              :key="index"
              :preview="index"
              :src="item"
              v-imgdef
              class="avtarImg upload ml60"
              alt
            />
          </a-form-item>

          <p class="tit mt20">审批流程</p>
          <a-timeline>
            <a-timeline-item
              color="rgba(138, 180, 248,1)"
              v-for="(m, index) in listdata.info.process_detail.data"
              :key="index"
            >
              <div class="ltk">
                <b>{{ m.type }}</b>
                <span v-if="index == 0">{{ m.create_time }}</span>
                <span v-else>{{ m.finish_time }}</span>
              </div>
              <span>{{ m.name_cn }}</span>
            </a-timeline-item>
          </a-timeline>
        </div>
      </template>
    </j-modal>
  </a-card>
</template>
  
  <script>
import { inventoryManage, common as commonApi } from "@/api/api";
import JEllipsis from "@/components/jeecg/JEllipsis";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import JInput from "@/components/jeecg/JInput";
import pick from "lodash.pick";
import moment from "moment";
const MODALQUERYPARAM = {
  name: "",
  id: ""
};
export default {
  name: "PurchasePayIndex",
  mixins: [JeecgListMixin],
  components: {
    JInput,
    JEllipsis
  },
  data() {
    return {
      inventoryManage,
      form: this.$form.createForm(this),
      moadl_queryParam: MODALQUERYPARAM,
      modalTablloading: false,
      approveLoading:false,
      exportTotal: 0,
      moadlTableCurrent: 1,
      selectedRowKeys: [],
      selectedRows: [],
      value: undefined,
      searchtext: "",
      searchSelect: {
        data: [],
        value: undefined
      },
      form: this.$form.createForm(this),
      approvalStatus: [],
      visible: false,
      wechatList: [],
      queryParam: {
        type: "",
        status: "",
        ids: [],
        serial_number: []
      },
      visible: false,
      wechatList: [],
      modal2: {
        title: "付款单详情",
        visible: false,
        fullscreen: false,
        switchFullscreen: true,
        confirmLoading: false,
        data: undefined
      },
      loading: true,
      // toggleSearchStatus: true,
      // formList: [],
      ipagination: {
        total: 0,
        pageSize: 20,
        pageSizeOptions: ["10", "20", "30", "50"],
        showSizeChanger: true
      },
      labelCol: {
        xs: { span: 2 },
        sm: { span: 2 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 6 }
      },
      columns: [
        {
          title: "付款ID",
          align: "center",
          width: 100,
          dataIndex: "id",
          scopedSlots: { customRender: "id" }
        },
        {
          title: "付款单号",
          align: "center",
          width: 220,
          dataIndex: "list_no",
          scopedSlots: { customRender: "list_no" }
        },
        {
          title: "飞书审批号",
          align: "center",
          width: 220,
          dataIndex: "serial_number",
          scopedSlots: { customRender: "serial_number" }
        },
        {
          title: "审批标题",
          align: "center",
          width: 200,
          dataIndex: "reason_title"
        },
        {
          title: "发起人",
          align: "center",
          width: 100,
          dataIndex: "created_by_text"
        },
        {
          title: "状态",
          align: "center",
          width: 150,
          dataIndex: "status",
          scopedSlots: { customRender: "status" }
        },
        {
          title: "创建时间",
          align: "center",
          width: 150,
          dataIndex: "created_at_text"
        },
        {
          title: "审批时间",
          align: "center",
          width: 150,
          dataIndex: "approval_time_text",
          scopedSlots: { customRender: "approval_time_text" }
        },
        {
          title: "操作",
          dataIndex: "action",
          scopedSlots: { customRender: "action" },
          align: "center",
          fixed: "right",
          width: 220
        }
      ],
      columns2: [
        {
          title: "门店名称",
          dataIndex: "store_name",
          width: "20%",
          scopedSlots: { customRender: "store_name" }
        },
        {
          title: "物料名称",
          dataIndex: "material_name",
          width: "20%",
          scopedSlots: { customRender: "material_name" }
        },
        {
          title: "条形码",
          dataIndex: "bar_code",
          width: "20%"
        },
        {
          title: "申请数量",
          dataIndex: "apply_num",
          width: "10%",
          scopedSlots: { customRender: "apply_num" }
        },
        {
          title: "采购数量",
          dataIndex: "procurement_num",
          width: "10%",
          scopedSlots: { customRender: "procurement_num" }
        },
        {
          title: "单位",
          dataIndex: "unit",
          width: "10%"
        },
        {
          title: "价格(元)",
          dataIndex: "total_price",
          width: "10%"
        }
      ],
      listdata: {},
      url: {
        list: "/material/procurement-pay/index"
      }
    };
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    },
    expParams() {
      return Object.assign({ is_export: 1 }, this.queryParam);
    }
  },
  mounted() {
    this.getApprovalStatus();
  },
  methods: {
    moment,
    getApprovalStatus() {
      commonApi.approvalStatus().then(res => {
        if (res.code == 200) {
          this.approvalStatus = res.data;
        }
      });
    },
    modal_searchQuery() {
      const { name, id } = this.moadl_queryParam;
      this.modalTablloading = true;
      setTimeout(() => {
        this.modalTablloading = false;
      }, 100);
      if (!name && !id) {
        this.listdata.info.detailList = this.modal2.data.info.detailList;
      } else {
        const data = JSON.parse(
          JSON.stringify(this.modal2.data.info.detailList)
        );
        this.listdata.info.detailList = data.filter(item => {
          return (
            (item.material_name.indexOf(name.trim()) > -1 && name.trim()) ||
            (item.bar_code.indexOf(id.trim()) > -1 && id.trim())
          );
        });
      }
    },
    modal_searchReset() {
      this.moadl_queryParam = {
        name: "",
        id: ""
      };
      this.moadlTableCurrent = 1;
      this.modal_searchQuery();
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    handleClick() {
      this.visible = true;
    },
    handleOk(e) {
      this.queryParam.serial_number = this.wechatList;
      this.visible = false;
    },
    handleCancel() {
      this.$refs.textarea.stateValue = null;
      this.wechatList = this.queryParam.serial_number;
      this.$refs.textarea.stateValue = this.wechatList.join("\r\n");
      this.visible = false;
    },
    cutting({ target: { value } }) {
      this.wechatList = JSON.parse(
        JSON.stringify(this.queryParam.serial_number)
      );
      if (value) {
        this.wechatList = value.split(/[\s\n]/);
      } else {
        this.wechatList = [];
      }
      //限制不能超过200行
      if (this.wechatList.length > 200) {
        this.wechatList = this.wechatList.slice(0, 200);
        this.$refs.textarea.stateValue = this.wechatList.join("\r\n");
      }
      this.wechatList = this.wechatList.filter(i => i);
    },
    dateChange(date, dateString) {
      this.queryParam.submit_time_start = dateString[0]
        ? parseInt(new Date(dateString[0]).getTime() / 1000)
        : "";
      this.queryParam.submit_time_end = dateString[1]
        ? parseInt(new Date(dateString[1]).getTime() / 1000)
        : "";
    },
    //
    searchDateChange(date, dateString) {
      this.queryParam.approval_time_start = dateString[0]
        ? parseInt(new Date(dateString[0]).getTime() / 1000)
        : "";
      this.queryParam.approval_time_end = dateString[1]
        ? parseInt(new Date(dateString[1]).getTime() / 1000)
        : "";
    },
    //处理树级数据格式
    treeDataformat(treeList) {
      let arr = [];
      let that = this;
      for (let a = 0; a < treeList.length; a++) {
        let temp = treeList[a];
        let obj = {
          label: temp.name,
          parentId: temp.parent_id + "" || "",
          key: (temp.parent_id + "" || "") + "" + temp.id,
          value: temp.id
        };

        if (temp.child && temp.child.length) {
          obj.children = that.treeDataformat(temp.child);
        }
        arr.push(obj);
      }
      return arr;
    },
    //时间格式
    dateParse(date) {
      return moment(new Date(parseInt(date))).format("YYYY-MM-DD HH:mm:ss");
    },
    //加载数据
    reload() {
      this.loadData();
    },
    handleClick() {
      this.visible = true;
    },
    handleOk(e) {
      this.queryParam.serial_number = this.wechatList;
      this.visible = false;
    },
    handleCancel() {
      this.$refs.textarea.stateValue = null;
      this.wechatList = this.queryParam.serial_number;
      this.$refs.textarea.stateValue = this.wechatList.join("\r\n");
      this.visible = false;
    },
    cutting({ target: { value } }) {
      this.wechatList = JSON.parse(
        JSON.stringify(this.queryParam.serial_number)
      );
      if (value) {
        this.wechatList = value.split(/[\s\n]/);
      } else {
        this.wechatList = [];
      }
      //限制不能超过200行
      if (this.wechatList.length > 200) {
        this.wechatList = this.wechatList.slice(0, 200);
        this.$refs.textarea.stateValue = this.wechatList.join("\r\n");
      }
      this.wechatList = this.wechatList.filter(i => i);
    },
    /**
     *  监测当前时间是否是采购时间
     *  ps 当value有值时 表示被编辑时调用
     *  @param value
     * */
    checkDay(value) {
      let date = new Date();
      let day = date.getDate();
      if (day < 4 || (14 < day && day < 18)) {
        !value && this.boundOperation("日常物料采购单", 1);
        return true;
      } else {
        this.dayVisible = true;
        return false;
        // this.boundOperation('日常物料采购单',1)
      }
    },
    initDictConfig() {},
    //导出-数据格式
    dataFormat({ list }) {
      let arr = [];
      for (let i = 0; i < list.length; i++) {
        let b = list[i];
        b.created_at = this.dateParse(parseInt(b.created_at) * 1000);
        let nb = pick(
          b,
          "list_no",
          "name",
          "material_name",
          "bar_code",
          "dept_name",
          "num",
          "apply_num",
          "procurement_num",
          "belong_warehouse",
          "hair_num",
          "collect_num",
          "supplier",
          "brand",
          "cate_name_1",
          "cate_name_2",
          "cate_name_3",
          "cate_name_4",
          "status",
          "shipments",
          "acceptance",
          "type",
          "username",
          "recipient",
          "time",
          "approval_time",
          "address"
        );
        arr.push(nb);
      }
      return arr;
    },
    //导出-数据源
    async getExpData(size) {
      let that = this;
      return new Promise((resolve, reject) => {
        try {
          if (that.queryParam.ids == "") {
            that.$message.warning("请勾选导出条目");
            return false;
          }
          if (that.queryParam.ids.length > 200) {
            that.$message.warning("一次性最多选择200条采购单");
            return false;
          }
          inventoryManage
            .exportList({ ...size, ...that.queryParam })
            .then(res => {
              if (res.code == 200) {
                resolve(res.data.list);
              } else {
                reject(res.message);
              }
            });
        } catch (err) {
          reject(err.data);
        } finally {
        }
      });
    },
    isAgainPay( record ) {
      if(record.status == 1 && record.process_instance_id.length <= 0){
        return true;
      }
      return false;
    },
    againPay(record) {
      this.approveLoading = true;
      inventoryManage.purchasePayFeiShuApprove({ id: record.id }).then(res => {
        if (res.code == 200) {
          this.$message.success(res.message);
          this.approveLoading = false;
          this.reload();
        } 
        this.approveLoading = false;
      });
    },
    //查看详情
    showDetail(record) {
      let that = this;
      that.loading = true;
      inventoryManage.purchasePayDetail({ id: record.id }).then(res => {
        that.loading = false;
        if (res.code == 200) {
          that.modal2.visible = true;
          that.modal2.data = JSON.parse(JSON.stringify(res.data));
          that.listdata = JSON.parse(JSON.stringify(res.data));
        } else {
          that.$message.warning(res.message);
        }
      });
    }
  }
};
</script>
  
  <style lang="less" scoped>
.buttom-bar {
  text-align: right;
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #eee;
  left: 0;
  padding: 10px 0;
  background: #fff;

  button {
    margin: 0 5px;
  }
}

.table-operator {
  display: flex;
  flex-direction: row;

  .operator {
    flex: 1;
    display: flex;
    flex-direction: row;
    // flex-grow: ;
    a {
      white-space: nowrap;
    }
  }

  .btns {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }
}
.images {
  margin-bottom: 30px;

  img {
    width: 80px;
    height: 80px;
    object-fit: cover;
  }
}

.ltk {
  width: 300px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.tit {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
</style>
  