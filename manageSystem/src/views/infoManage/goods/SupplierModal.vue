<template>
  <div>
    <self-detail-modal
      :title="title"
      placement="right"
      v-model="visible"
      :confirmLoading="confirmLoading"
      :confirmPromise="confirmPromise"
      :drawerWidth="700"
      :drawerHeight="'auto'"
      :show-confirm="!disableSubmit"
    >
      <a-form
        :disabled="disableSubmit"
        :form="form"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        slot="centent"
      >
        <a-form-item label="供应商名称" hasFeedback required>
          <a-input
            placeholder="请输入供应商名称"
            v-decorator="['name', validatorRules.name]"
            :readOnly="disableSubmit"
          />
        </a-form-item>

        <a-form-item label="联系人">
          <a-input
            placeholder="请输入联系人"
            v-decorator="['contact_person', validatorRules.contact_person]"
            :readOnly="disableSubmit"
          />
        </a-form-item>

        <a-form-item label="手机号">
          <a-input
            placeholder="请输入手机号"
            v-decorator="['mobile', validatorRules.mobile]"
            :readOnly="disableSubmit"
          />
        </a-form-item>

        <a-form-item label="邮箱">
          <a-input
            placeholder="请输入邮箱"
            v-decorator="['email', validatorRules.email]"
            :readOnly="disableSubmit"
          />
        </a-form-item>

        <a-form-item label="标签">
          <a-input
            placeholder="标签"
            v-decorator="['tag', validatorRules.tag]"
            :readOnly="disableSubmit"
          />
        </a-form-item>

        <a-form-item label="备注">
          <a-input
            placeholder="备注"
            v-decorator="['remark', validatorRules.remark]"
            :readOnly="disableSubmit"
          />
        </a-form-item>

        <a-form-item label="收款账户">
          <div class="AListBox">
            <a-list item-layout="horizontal" style="flex" :data-source="model.account">
              <a-list-item slot="renderItem" slot-scope="item, index">
                <a-list-item-meta>
                  <span slot="title">
                    {{ item.bank_account_name }}
                    <a-tag color="blue">{{ item.bank_account_type == 1 ? "个人" : "对公" }}</a-tag>
                  </span>
                  <span slot="description">
                    <div style="color: blcak">{{ item.bank_account }}</div>
                    <div>{{ item.bank_name }}</div>
                    <div>{{ item.bank_location }}</div>
                    <div>{{ item.bank_branch_name }}</div>
                  </span>
                </a-list-item-meta>
                <a-icon slot="actions" type="form" @click="handleAddorEdit(item, index)" />
                <a-icon slot="actions" type="delete" @click="handleDelete(index)" />
              </a-list-item>
            </a-list>
            <a-button
              :disabled="(model.account && model.account.length > 0) || disableSubmit"
              @click="handleAddorEdit()"
              type="primary"
              class="primary ml10"
              size="small"
              icon="plus"
            >添加收款账户</a-button>
          </div>
        </a-form-item>

        <!-- <a-form-item label="状态 " required>
            <a-radio-group v-decorator="['status', validatorRules.status]">
              <a-radio-button value="1">
                启用
              </a-radio-button>
              <a-radio-button value="0">
                禁用
              </a-radio-button>
            </a-radio-group>
        </a-form-item>-->
      </a-form>
    </self-detail-modal>
    <!--  账户相关弹出层   -->
    <a-modal v-model="account.Visible" title="添加收款账户" @ok="accounthandleAdd">
      <div class="account">
        <a-radio-group class="radioGroup" @change="radioChange" v-model="account.bank_account_type">
          <a-radio-button :value="1">个人账户</a-radio-button>
          <a-radio-button :value="2">对公账户</a-radio-button>
        </a-radio-group>

        <a-form
          :form="form_"
          style="width: 100%"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          v-if="account.Visible"
        >
          <!-- 个人账户 -->
          <a-form-item label="户名" required>
            <a-input
              style="width: 100%"
              placeholder="请输入账户名称"
              v-decorator="['bank_account_name', changeRules.bank_account_name]"
            ></a-input>
          </a-form-item>
          <a-form-item label="卡号" required>
            <a-input
              style="width: 100%"
              placeholder="请输入银行卡号"
              v-decorator="['bank_account', changeRules.bank_account]"
            ></a-input>
          </a-form-item>
          <a-form-item label="银行" required>
            <a-input
              style="width: 100%"
              placeholder="请输入银行名称"
              v-decorator="['bank_name', changeRules.bank_name]"
              :precision="3"
            ></a-input>
          </a-form-item>
          <!-- 对公账户 -->
          <template v-if="account.bank_account_type == 2">
            <a-form-item label="银行所在地" required>
              <a-input
                style="width: 100%"
                placeholder="请输入银行所在地"
                v-decorator="['bank_location', changeRules.bank_location]"
                :precision="3"
              ></a-input>
            </a-form-item>
            <a-form-item label="支行名称" required>
              <a-input
                style="width: 100%"
                placeholder="请输入支行名称"
                v-decorator="['bank_branch_name', changeRules.bank_branch_name]"
                :precision="3"
              ></a-input>
            </a-form-item>
          </template>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { supplierManage } from "@/api/api";
import JImageUpload from "@/components/jeecg/JImageUpload";
import { cloneDeep, isEqual } from "lodash";
const accountKey = [
  ["bank_account_name", "bank_account", "bank_name"],
  [
    "bank_account_name",
    "bank_account",
    "bank_name",
    "bank_location",
    "bank_branch_name"
  ]
];
const defaultUplateAccount = {
  bank_account_type: 1,
  bank_account_name: "",
  bank_account: "",
  bank_name: "",
  bank_location: "",
  bank_branch_name: ""
};
export default {
  name: "MaterialModal",
  components: { JImageUpload },
  data() {
    return {
      title: "",
      visible: false,
      disableSubmit: false,
      handleduise: {
        type: "",
        index: ""
      },
      account: {
        Visible: false,
        bank_account_type: "1"
      },
      model: {
        account: []
      },
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      defaultValue: {},
      confirmLoading: false,
      form: this.$form.createForm(this),
      form_: this.$form.createForm(this, {
        onValuesChange: this.onValuesChange
      })
    };
  },
  computed: {
    validatorRules: function() {
      return {
        name: { rules: [{ required: true, message: "请输入名称!" }] },
        contact_person: {
          rules: [{ required: false, message: "请输入联系人!" }]
        },
        status: { initialValue: "1" },
        mobile: { rules: [{ required: false, message: "请选择手机号!" }] },
        email: { rules: [{ required: false, message: "请输入邮箱!" }] },
        tag: { rules: [{ required: false, message: "请选择标签!" }] },
        remark: { rules: [{ required: false, message: "请输入备注!" }] }
      };
    },
    changeRules: function() {
      return {
        bank_account_name: {
          rules: [{ required: true, message: "请输入账户名称!" }]
        },
        bank_account: {
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (!value) {
                  cbfn("请输入银行卡号！");
                }
                cbfn();
              }
            }
          ]
        },
        bank_name: {
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (!value) {
                  cbfn("请输入银行名称");
                }
                cbfn();
              }
            }
          ]
        },
        bank_location: {
          rules: [{ required: true, message: "请输入银行所在地!" }]
        },
        bank_branch_name: {
          rules: [{ required: true, message: "请输入支行名称!" }]
        }
      };
    }
  },
  watch: {
    visible: function(newvalue) {
      if (!newvalue) {
        this.model.account = [];
      }
    }
  },
  methods: {
    onValuesChange(props) {
      this.defaultValue = props.form.getFieldsValue() || {};
    },
    async radioChange() {
      await this.$asyncNextTick();
      this.handleAddorEdit(this.defaultValue, 0);
    },
    handleDelete(index) {
      this.model.account = [];
      // this.model.account.splice(index, 1);
    },
    add() {
      // 默认值
      this.model.id = 0;
      this.visible = true;
    },
    async edit(record) {
      this.visible = true;
      let qid = record.id || 0;
      this.confirmLoading = true;
      const {
        code,
        data: { info }
      } = await supplierManage.view({ id: qid });
      if (code == "200") {
        const {
          bank_account_type,
          bank_account_name,
          bank_account,
          bank_name,
          bank_location,
          bank_branch_name
        } = info;
        const account = {
          bank_account_type,
          bank_account_name,
          bank_account,
          bank_name,
          bank_location,
          bank_branch_name
        };
        this.model = Object.assign(
          {
            account: isEqual(account, defaultUplateAccount) ? [] : [account]
          },
          info
        );
        let fieldsVal = this.$pick(
          this.model,
          "name",
          "contact_person",
          "mobile",
          "email",
          "tag",
          "remark"
        );
        this.$nextTick(() => {
          this.form.setFieldsValue(fieldsVal);
        });
      } else {
        this.$message.warning(res.message);
      }
      this.confirmLoading = false;
    },
    confirmPromise() {
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          const model = cloneDeep(this.model);
          let formData = Object.assign(
            {
              id: model.id
            },
            values
          );
          formData = {
            ...formData,
            ...(model.account[0] || defaultUplateAccount)
          };
          this.confirmLoading = true;
          let obj;
          if (!model.id) {
            //新增
            delete formData.id;
            obj = supplierManage.create(formData);
          } else {
            //修改
            delete formData.status;
            obj = supplierManage.update(formData);
          }
          obj
            .then(res => {
              if (res.code == "200") {
                this.$message.success(res.message);
                this.$emit("ok");
                this.visible = false;
              } else {
                this.$message.warning(res.message);
              }
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        }
      });
    },
    handleAddorEdit(item = void 0, index) {
      if (!item) this.handleduise.type = "add";
      else {
        this.handleduise.type = "edit";
        this.handleduise.index = index;
      }
      this.account = Object.assign(
        JSON.parse(JSON.stringify(this.account)),
        item
      );
      let fieldsVal = this.$pick(
        this.account,
        ...accountKey[this.account.bank_account_type - 1]
      );
      this.account.Visible = true;
      this.$nextTick(() => {
        this.form_.setFieldsValue(fieldsVal);
      });
    },
    accounthandleAdd() {
      this.form_.validateFields((err, values) => {
        if (!err) {
          try {
            let formData = Object.assign(
              JSON.parse(JSON.stringify(this.account)),
              values
            );
            delete formData.Visible;
            delete formData.id;
            if (this.handleduise.type == "add") {
              this.model.account.push(formData);
            } else {
              this.model.account.splice(this.handleduise.index, 1, formData);
            }
            this.account.Visible = false;
            this.account = {
              Visible: false,
              bank_account_type: "1"
            };
          } catch (error) {
            console.log(error);
          }
        } else {
          console.log(err);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.AListBox {
  position: relative;
  top: -4px;
  left: 8px;
  display: flex;
  .primary {
    position: relative;
    top: 6px;
  }
  /deep/ .ant-list-split {
    flex: 1;
    max-height: 500px;
    overflow-y: scroll;
    position: relative;
  }
}
.account {
  display: flex;
  align-items: center;
  flex-direction: column;
  .radioGroup {
    margin: 0 auto 10px;
  }
}
</style>
