<template>
  <a-card :bordered="false">
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="门店名称">
          <goodStore v-model="queryParam.dept_id" />
        </self-col>
        <self-col label="采购类型">
          <a-select v-model="queryParam.type" placeholder="请选择采购类型">
            <a-select-option key value>全部</a-select-option>
            <a-select-option :key="key" :value="key" v-for="(value, key) in typeList">{{ value }}</a-select-option>
          </a-select>
        </self-col>
        <self-col label="订单状态">
          <a-select v-model="queryParam.status">
            <a-select-option value>全部</a-select-option>
            <a-select-option :key="i" :value="i" v-for="(s, i) in dataSource.status">{{ s }}</a-select-option>
          </a-select>
        </self-col>
        <self-col label="物料名称" v-if="dataSource.isCanShow">
          <a-input
            placeholder="请输入物料名称"
            v-model="queryParam.material_name"
          ></a-input>
        </self-col>
        <self-col label="供应商名称" v-if="dataSource.isCanShow">
          <a-input
            placeholder="请输入供应商名称"
            v-model="queryParam.supplier"
          ></a-input>
        </self-col>
        <self-col label="付款申请时间" v-if="dataSource.isCanShow">
          <self-time
            timeRange="month"
            v-model="selfDefaultTime.purchasePay"
            :reset="timeReset"
            :InitializationTime="false"
            :disabledDate="disabledDate"
            :timeKey="{
              start: 'pay_request_start_time',
              end: 'pay_request_end_time',
            }"
          />
        </self-col>
        <self-col label="发货时间" v-if="dataSource.isCanShow">
          <self-time
            timeRange="month"
            v-model="selfDefaultTime.purchasePay"
            :reset="timeReset"
            :InitializationTime="false"
            :disabledDate="disabledDate"
            :timeKey="{
              start: 'shipments_start_time',
              end: 'shipments_end_time',
            }"
          />
        </self-col>
      </template>
      <template slot="bottom">
        <self-col-btn>
          <a-button @click="payFormUpdata()" v-has="'procurement-pay:create'">申请付款单</a-button>
        </self-col-btn>
        <self-col-btn>
          <a-button @click="batchShipments()" :loading="batchShipmentsLoading" v-has="'procurement:shipment'">批量发货</a-button>
        </self-col-btn>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          v-has="'procurement:detail-export'"
          :dataFormat="dataFormat"
          :query="queryParam"
          fileName="采购详情列表"
          :limit="1000"
          class="ml0 mr10"
          :total="ipagination.total"
          :queryParam="queryParam"
          :CommentApi="inventoryManage.detailExport"
          :header="exportHeader"
        ></export-to-csv>
      </template>
    </self-page-header>
    <!--采购详情列表-->
    <a-table
      ref="table"
      bordered
      size="middle"
      rowKey="id"
      :columns="columns"
      :row-selection="{
        onChange: onChange,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        selectedRowKeys: selectedRowKeys,
      }"
      :dataSource="dataSourceFormat"
      :pagination="ipagination"
      :loading="loading"
      :scroll="{ x: 1200 }"
      @change="handleTableChange"
    >
      <span slot="status" slot-scope="status,record">
        <span :style="{ color: status == 12 ? 'red' : '' }">
          <span v-if="status == 7 && record.shipments == 0">{{ dataSource.status[status] ? dataSource.status[status] : '进行中' }}</span>
          <span v-if="status == 7 && (record.shipments == 1 && record.acceptance == 0)">{{ dataSource.status[status] ? dataSource.status[status] + '(已发货)' : '进行中' }}</span>
          <span v-if="status == 7 && record.acceptance == 1">{{ dataSource.status[status] ? dataSource.status[status] + '(已收货)' : '进行中' }}</span>
          <span v-if="status != 7">{{ dataSource.status[status] ? dataSource.status[status]: '进行中' }} </span>
        </span>
      </span>
      <span slot="image" slot-scope="image, record, index">
            <img 
            :src="record.image || ''" 
            alt   
            class="listimage" 
            :preview="index"
            v-imgdef
            style="cursor: pointer" />
      </span>
      <span slot="list_no" slot-scope="list_no">
        <j-ellipsis :value="list_no" :length="25" />
      </span>
      <span slot="type" slot-scope="text, record">
        <span v-if="record.type == 1">日常物料</span>
        <span v-if="record.type == 2">特殊物料</span>
      </span>
      <!-- 操作 -->
      <span slot="action" slot-scope="text, record" v-divider>
        <span v-has="'procurement:detail-view'">
          <a @click="view(record)">详情</a>
        </span>
        <span v-if="includesStatus(record.status, [6])">
          <a @click="changePurchaseNum(record)" v-has="'procurement:change-purchase-num'">修改采购数量</a>
        </span>
        <span v-if="includesStatus(record.status, [7,8]) &&  record.shipments == 0">
          <a @click="shipments(record)" v-has="'procurement:shipment'">发货</a>
        </span>
        <span v-if="includesStatus(record.status, [9]) || (includesStatus(record.status, [7]) && record.shipments == 1 && record.acceptance == 0)">
          <a @click="acceptance(record)" v-has="'procurement:acceptance'">收货</a>
        </span>
        <span v-if="includesStatus(record.status, [7,8,9]) && record.acceptance == 0">
          <a @click="changePrice(record)" v-has="'procurement:changePrice'">修改价格</a>
        </span>
        <span v-if="includesStatus(record.status, [12])">
          <a
            href="javascript:;"
            @click="checkError(record)"
            v-has="'procurement:handle-exception'"
          >异常处理</a>
        </span>
        <span v-if="includesStatus(record.status, [5,6,8,9,10,11,12])">
          <a
           href="javascript:;"
           @click="cancelPurchase(record)" 
            v-has="'procurement:cancel'"
          >取消采购</a>
        </span>
      </span>
    </a-table>

    <!-- 修改采购数量 -->
    <a-modal
      :title="'修改采购数量：' + changePurchaseModal.store_name"
      :visible="changePurchaseModal.visible"
      :confirm-loading="changePurchaseModal.confirmLoading"
      @ok="handleChangePurchaseCheck"
      @cancel="handleChangePurchaseCancel"
    >
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">物料名称:</span>
          <span>{{changePurchaseModal.data ? changePurchaseModal.data.material_name : ""}}</span>
        </p>
        <p>
          <span :style="{ 'margin-right': '15px' }">条形码:</span>
          <span>{{changePurchaseModal.data ? changePurchaseModal.data.bar_code : "" }}</span>
        </p>
      </div>
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">申请数量:</span>
          <span>{{changePurchaseModal.data ? changePurchaseModal.data.apply_num : 0}}</span>
        </p>
      </div>
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">采购数量:</span>
          <a-input-number v-model="changePurchaseModal.procurement_num"></a-input-number>
        </p>
      </div>
    </a-modal>
    <!-- 修改采购数量 -->

    <!-- 详情  -->
    <a-drawer
    title="详情"
    :dataSource="viewModal.data"
    :confirmLoading="viewModal.confirmLoading"
    @close="handleViewCancel"
    width="700"
    :visible="viewModal.visible"
    >
      <a-spin :spinning="viewModal.confirmLoading">
        <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="门店名称">
          <span>{{viewModal.data ? viewModal.data.store_name : ""}}</span>
        </a-form-item>
        <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="发起人">
          <span>{{viewModal.data ? viewModal.data.username : ""}}</span>
        </a-form-item>
        <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="物料名称">
          <span>{{viewModal.data ? viewModal.data.material_name : ""}}</span>
        </a-form-item>
        <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="条形码">
          <span>{{viewModal.data ? viewModal.data.bar_code : ""}}</span>
        </a-form-item>
        <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="状态">
          <span>{{viewModal.data ? statusData ? statusData[viewModal.data.status] ? statusData[viewModal.data.status] : '进行中' : "-" : "-"}}</span>
        </a-form-item>
        <!-- <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="门店库存">
          <span>{{viewModal.data ? viewModal.data.num : ""}}</span>
        </a-form-item> -->
        <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="申请数量">
          <span>{{viewModal.data ? viewModal.data.apply_num : ""}}</span>
        </a-form-item>
        <!-- <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="采购审批数量">
          <span>{{viewModal.data ? viewModal.data.procurement_num : ""}}</span>
        </a-form-item> -->
        <!-- <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="30天内实发数量">
          <span>{{viewModal.data ? viewModal.data.before_hair_num : ""}}</span>
        </a-form-item> -->
        <!-- <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="实发数量">
          <span>{{viewModal.data ? viewModal.data.hair_num : ""}}</span>
        </a-form-item> -->
        <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="实收数量">
          <span>{{viewModal.data ? viewModal.data.collect_num : ""}}</span>
        </a-form-item>
        <a-form-item v-if="viewModal.data.abnormal_note" class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="异常处理">
          <span>{{viewModal.data ? viewModal.data.abnormal_note : ""}}</span>
        </a-form-item>
        <a-form-item class="formRowHeight" :labelCol="labelCol" :wrapperCol="wrapperCol" label="收货图片">
          <img
              v-for="(item, index) in viewModal.data.acceptance_img"
              :key="index"
              :preview="index"
              :src="item"
              v-imgdef
              class="avtarImg upload"
              alt
            />
        </a-form-item>
      </a-spin>
    </a-drawer>
    <!-- 详情  -->

    <!-- 发货 -->
    <a-modal
      :title="'发货到门店：' + shipmentsModal.store_name"
      :visible="shipmentsModal.visible"
      :confirm-loading="shipmentsModal.confirmLoading"
      @ok="handleShipmentsCheck"
      @cancel="handleShipmentsCancel"
    >
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">物料名称:</span>
          <span>{{shipmentsModal.data ? shipmentsModal.data.material_name : ""}}</span>
        </p>
        <p>
          <span :style="{ 'margin-right': '15px' }">条形码:</span>
          <span>{{shipmentsModal.data ? shipmentsModal.data.bar_code : "" }}</span>
        </p>
      </div>
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">申请数量:</span>
          <span>{{shipmentsModal.data ? shipmentsModal.data.apply_num : 0}}</span>
        </p>
      </div>
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">采购数量:</span>
          <span>{{shipmentsModal.data ? shipmentsModal.data.procurement_num : 0}}</span>
        </p>
      </div>
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">实发数量:</span>
          <a-input-number v-model="shipmentsModal.hair_num"></a-input-number>
        </p>
      </div>
    </a-modal>
    <!-- 发货 -->

    <!-- 收货 -->
    <a-modal
      :title="'收货门店：' + acceptanceModal.store_name"
      :visible="acceptanceModal.visible"
      :confirm-loading="acceptanceModal.confirmLoading"
      @ok="handleAcceptanceCheck"
      @cancel="handleAcceptanceCancel"
    >
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="条形码">
        <span>{{acceptanceModal.data ? acceptanceModal.data.material_name : ""}}</span>
      </a-form-item>
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="条形码">
        <span>{{acceptanceModal.data ? acceptanceModal.data.bar_code : "" }}</span>
      </a-form-item>
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="申请数量">
        <span>{{acceptanceModal.data ? acceptanceModal.data.apply_num : 0}}</span>
      </a-form-item>
      <!-- <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="实发数量">
        <span>{{acceptanceModal.data ? acceptanceModal.data.hair_num : 0}}</span>
      </a-form-item> -->
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="实收数量">
        <a-input-number v-model="acceptanceModal.collect_num"></a-input-number>
      </a-form-item>
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="收货照片">
        <j-image-upload 
            isMultiple
            :max="5"
            :StorageDirectory="acceptancePath"
            v-model="acceptanceModal.acceptance_img"
        />
      </a-form-item>
    </a-modal>
    <!-- 收货 -->

    <!-- 修改价格 -->
    <a-modal
      :title="'修改采购价格'"
      :visible="changePriceModal.visible"
      :confirm-loading="changePriceModal.confirmLoading"
      @ok="handleChangePriceCheck"
      @cancel="handleChangePriceCancel"
    >
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">门店名称:</span>
          <span>{{changePriceModal.store_name ? changePriceModal.data.store_name : ""}}</span>
        </p>
        <p>
          <span :style="{ 'margin-right': '15px' }">物料名称:</span>
          <span>{{changePriceModal.data ? changePriceModal.data.material_name : ""}}</span>
        </p>
        <p>
          <span :style="{ 'margin-right': '15px' }">条形码:</span>
          <span>{{changePriceModal.data ? changePriceModal.data.bar_code : "" }}</span>
        </p>
      </div>
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">申请数量:</span>
          <span>{{changePriceModal.data ? changePriceModal.data.apply_num : 0}}</span>
        </p>
      </div>
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">采购数量:</span>
          <span>{{changePriceModal.data ? changePriceModal.data.procurement_num : 0}}</span>
        </p>
      </div>
      <div>
        <p>
          <span :style="{ 'margin-right': '15px' }">采购价格:</span>
          <a-input-number v-model="changePriceModal.total_price"></a-input-number>
        </p>
      </div>
    </a-modal>
    <!-- 修改价格 -->

    <!--异常-->
    <j-modal
      :visible="errorInfo.visible"
      :title="errorInfo.title"
      @ok="handleErrorOk"
      @cancel="handleErrorCancel"
      :confirmLoading="errorInfo.confirmLoading"
    >
      <a-textarea
        class="pd10"
        style="min-height: 120px"
        placeholder="请输入异常原因及处理措施"
        v-model="errorInfo.note"
      ></a-textarea>
    </j-modal>
    <!--异常-->

    <!--取消采购-->
    <j-modal
      :visible="cancleInfo.visible"
      :title="cancleInfo.title"
      @ok="handleCancleOk"
      @cancel="handleCancleCancel"
      :confirmLoading="cancleInfo.confirmLoading"
    >
      <a-textarea
        class="pd10"
        style="min-height: 120px"
        placeholder="请输入取消原因"
        v-model="cancleInfo.note"
      ></a-textarea>
    </j-modal>
    <!--取消采购-->

    <!-- 付款单申请 -->
    <j-modal
      :title="`付款申请单申请：已选中${selectedRowKeys.length || 0}条`"
      :width="700"
      :visible="payForm.visible"
      @ok="handlePayFormOk"
      @cancel="handlePayFormClose"
      :confirmLoading="confirmPayFormLoading"
    >
      <a-spin :spinning="confirmPayFormLoading">
        <a-form :form="form">
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="付款事由">
            <a-input
              placeholder="请输入付款事由"
              v-decorator="['reason_title', validatorRules.reason_title]"
            />
          </a-form-item>

          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" class="material" label="申请物料">
            <div
              style="margin-bottom: 6px"
              v-for="(item , index) in payForm.data.detail"
              :key="index"
            >
              {{ item.store_name }}:（{{getTotal(item.material)}}元）
              <div
                style="padding-left: 12px;"
                class="mb10"
                v-for="(childItem, childIndex) in item.material"
                :key="childIndex"
              >
                {{ childIndex + 1 }}、
                <span class="mr10">{{ childItem.material_name  }}</span>
                <span class="mr10">{{ childItem.procurement_num  }}{{ childItem.unit  }}</span>
                <a-input-number :precision="2" style="width: 140px;" :min="0" v-model="childItem.total_price" class="mr5" />元
              </div>
            </div>
          </a-form-item>

          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="付款金额">
            <a-input-number
              style="width: 200px"
              placeholder="请输入付款金额"
              :precision="2"
              :disabled="true"
              v-decorator="['amount', validatorRules.amount]"
            />
          </a-form-item>

          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="付款方式">
            <commone-self-principal
              style="width: 100%"
              midwayChange
              :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
              :selectData="payForm.payTypeData"
              :showAll="false"
              :isRequest="false"
              v-decorator="['pay_type', validatorRules.pay_type]"
              placeholder="请选择付款方式"
              :isSearchrRequest="false"
            />
          </a-form-item>

          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="付款时间">
            <a-date-picker
              style="width: 200px"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              v-decorator="['pay_time', validatorRules.pay_time]"
            />
          </a-form-item>

          <a-form-item label="供应商" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <commone-self-principal
              placeholder="请选择供应商"
              :requestFun="supplierSelect"
              :query="{
                name: '',
                is_bank:1
              }"
              searchKey="name"
              value_key="name"
              v-decorator="['supplier_id', validatorRules.supplier_id]"
              :isRequest="false"
              :showAll="false"
              @exportData="(item) => supplierData = item"
            ></commone-self-principal>
            <div slot="extra">
              <div v-if="supplierData">
                <div>账户类型： {{ supplierData.bank_account_type == 1 ? "个人" : "对公" }}</div>
                <div>户名： {{ supplierData.bank_account_name }}</div>
                <div>账号： {{ supplierData.bank_account }}</div>
                <div>银行： {{ supplierData.bank_name }}</div>
                <template v-if=" supplierData.bank_account_type == 2">
                  <div>银行所在地区： {{ supplierData.bank_location }}</div>
                  <div>银行支行： {{ supplierData.bank_branch_name }}</div>
                </template>
              </div>
            </div>
          </a-form-item>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="备注">
            <a-input
              placeholder="请输入备注"
              v-decorator="['remark', validatorRules.remark]"
            />
          </a-form-item>
          <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="图片">
            <JFileUpload
              accept="*"
              isMultiple
              hasInformation
              :StorageDirectory="payPath"
              v-decorator="['attachment']"
            />
          </a-form-item>
        </a-form>
      </a-spin>
    </j-modal>
  </a-card>
</template>
  
<script>
import { inventoryManage, supplierManage } from "@/api/api";
import JEllipsis from "@/components/jeecg/JEllipsis";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import JInput from "@/components/jeecg/JInput";
import pick from "lodash.pick";
import _ from 'lodash'
import moment from "moment";
import JImageUpload from "@/components/jeecg/JImageUpload";
import JFileUpload from "@/components/jeecg/JFileUpload";
import goodStore from "@/views/infoManage/components/goodStore.vue";
const MODALQUERYPARAM = {
  name: "",
  id: ""
};
export default {
  name: "PurchaseDetailList",
  mixins: [JeecgListMixin],
  components: {
    JInput,
    JEllipsis,
    JImageUpload,
    JFileUpload,
    goodStore
  },
  data() {
    return {
      payPath:'purchase/pay',
      acceptancePath:'purchase/acceptance',
      inventoryManage,
      selfDefaultTime: {},
      supplierSelect: supplierManage.search,
      form: this.$form.createForm(this),
      moadl_queryParam: MODALQUERYPARAM,
      modalTablloading: false,
      exportTotal: 0,
      moadlTableCurrent: 1,
      expandedRowKeys:[],
      selectedRowKeys: [],
      selectedRows: [],
      typeList: [],
      value: undefined,
      searchtext: "",
      form: this.$form.createForm(this),
      edit_id: "",
      visible: false,
      confirmPayFormLoading: false,
      batchShipmentsLoading: false,
      wechatList: [],
      supplierData: null,
      statusData:[],
      queryParam: {
        type: "",
        status: "",
        dept_name: "",
        brand: "",
        ids: undefined,
        ding_business_ids: []
      },
      visible: false,
      wechatList: [],
      modal: {
        title: "创建采购单",
        visible: false,
        fullscreen: false,
        switchFullscreen: true,
        confirmLoading: false,
        dept_id: 0,
        thumb: [],
        note: ""
      },
      loading: true,
      toggleSearchStatus: true,
      formList: [],
      ipagination: {
        total: 0,
        pageSize: 20,
        pageSizeOptions: ["10", "20", "30", "50"],
        showSizeChanger: true
      },
      labelCol: {
        xs: { span: 6 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 8 },
        sm: { span: 18 }
      },
      viewModal: {
        visible: false,
        confirmLoading: false,
        data: {}
      },
      changePurchaseModal: {
        visible: false,
        confirmLoading: false,
        store_name: "",
        procurement_num: 0,
        data: undefined
      },
      shipmentsModal: {
        visible: false,
        confirmLoading: false,
        store_name: "",
        hair_num: 0,
        data: undefined
      },
      acceptanceModal: {
        visible: false,
        confirmLoading: false,
        store_name: "",
        collect_num: 0,
        data: undefined,
        acceptance_img: []
      },
      changePriceModal: {
        visible: false,
        confirmLoading: false,
        store_name: "",
        total_price: 0,
        data: undefined
      },
      payForm: {
        visible: false,
        payTypeData: null,
        data: {
          amount: 0,
          detail: ""
        }
      },
      errorInfo: {
        id: "",
        title: "异常处理",
        visible: false,
        note: "",
        confirmLoading:false
      },
      cancleInfo: {
        id: "",
        title: "取消采购",
        visible: false,
        note: "",
        confirmLoading:false
      },
      columns:[],
      url: {
        list: "/procurement/detail-list"
      },
      exportHeader:[],
      exportKey:[],
    };
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      this.typeList = this.dataSource.type;
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      this.statusData = this.dataSource.status;
      return d;
    },
    expParams() {
      return Object.assign({ is_export: 1 }, this.queryParam);
    },
    validatorRules: function() {
      return {
        reason_title: {
          rules: [{ required: true, message: "请输入付款事由" }]
        },
        amount: {
          initialValue: this.payForm.data.amount,
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (value === null) {
                  cbfn("请输入付款金额");
                }
                if (value < 0) {
                  cbfn("付款金额不能小于0");
                }
                cbfn();
              }
            }
          ]
        },
        pay_type: {
          rules: [{ required: true, message: "请选择付款方式" }]
        },
        pay_time: {
          rules: [{ required: true, message: "请选择付款时间" }]
        },
        supplier_id: {
          rules: [{ required: true, message: "请选择供应商" }]
        },
        remark: {
          rules: [{ required: false, message: "请输入备注" }]
        }
      };
    }
  },
  mounted() {
    this.$store.commit("DELETE_PERMISSIONLISTPASSROUTER", [
      "PurchaseDetailList"
    ]);
    this.getListColumns();
  },
  watch: {
    payForm: {
      handler: function () {
        const total = this.getTotal(this.payForm.data.detail, 2)
        this.payForm.visible && this.form.setFieldsValue({amount: total})
      },
      deep: true
    }
  },
  methods: {
    moment,
    getListColumns(){
     inventoryManage.detailListColumns().then(res => {
          if (res.code == "200") {
            this.columns = res.data.columns;
            this.exportHeader = res.data.exportColumns;
            this.exportKey = res.data.exportKey;
          }else{
            this.$message.error('获取列表字段失败,原因:'.res.msg)
          }
        });
    },
    getTotal(list, type = 1) {
      if(!list) return 0
      if(type == 2 ) {
        list = list.reduce((n,l)=> {
          n = n.concat(l.material)
          return n
        }, [])
      }
      //  使用第三方库解决运算精度丢失问题这个没太必要 刚记成有减法了
      const total = list.reduce((n,l)=> {
        n += Number(l.total_price || '0')
        return n
      }, 0)
      return total
    },
    modal_searchReset() {
      this.moadl_queryParam = {
        name: "",
        id: ""
      };
      this.moadlTableCurrent = 1;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    dateChange(date, dateString) {
      this.queryParam.submit_time_start = dateString[0]
        ? parseInt(new Date(dateString[0]).getTime() / 1000)
        : "";
      this.queryParam.submit_time_end = dateString[1]
        ? parseInt(new Date(dateString[1]).getTime() / 1000)
        : "";
    },
    //
    searchDateChange(date, dateString) {
      this.queryParam.approval_time_start = dateString[0]
        ? parseInt(new Date(dateString[0]).getTime() / 1000)
        : "";
      this.queryParam.approval_time_end = dateString[1]
        ? parseInt(new Date(dateString[1]).getTime() / 1000)
        : "";
    },
    //处理树级数据格式
    treeDataformat(treeList) {
      let arr = [];
      let that = this;
      for (let a = 0; a < treeList.length; a++) {
        let temp = treeList[a];
        let obj = {
          label: temp.name,
          parentId: temp.parent_id + "" || "",
          key: (temp.parent_id + "" || "") + "" + temp.id,
          value: temp.id
        };

        if (temp.child && temp.child.length) {
          obj.children = that.treeDataformat(temp.child);
        }
        arr.push(obj);
      }
      return arr;
    },
    //时间格式
    dateParse(date) {
      return moment(new Date(parseInt(date))).format("YYYY-MM-DD HH:mm:ss");
    },
     //设置可选日期
     disabledDate(current) {
      return current && current >= moment().endOf('day')
    },
    //加载数据
    reload() {
      this.loadData();
    },
    handleClick() {
      this.visible = true;
    },
    handleOk(e) {
      this.queryParam.ding_business_ids = this.wechatList;
      this.visible = false;
    },
    handleCancel() {
      this.$refs.textarea.stateValue = null;
      this.wechatList = this.queryParam.ding_business_ids;
      this.$refs.textarea.stateValue = this.wechatList.join("\r\n");
      this.visible = false;
    },
    onChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.queryParam.ids = selectedRowKeys;
  
    },
    onSelect(record, selected, selectedRows) {
      if (selected) {
        this.selectedRows.push(record);
      }

      if (!selected) {
        let delIndex = this.selectedRows.findIndex((val) => {
          return val.id === record.id;
        });
        this.selectedRows.splice(delIndex, 1);
      }

      this.payFormData(this.selectedRows);
    },
    onSelectAll(selected, selectedRows, changeRows) {
      if (selected) {
        this.selectedRows = this.selectedRows.concat(changeRows);
      }
      if (!selected) {
        let selectedRows = JSON.parse(JSON.stringify(this.selectedRows));
        let delIndex = [];
        selectedRows.forEach((item, index) => {
          changeRows.forEach((val, itemIndex) => {
            if (item.id === val.id) {
              delIndex.push(index);
            }
          });
        });
        delIndex.forEach((item) => {
          delete selectedRows[item];
        });

        selectedRows = selectedRows.filter((item) => {
          return item != undefined;
        });
        this.selectedRows = selectedRows;
      }
      this.payFormData(this.selectedRows);
    },
    // getCheckboxProps: record => ({
    //   props: {
    //     disabled: ![6,10].includes(parseInt(record.status))
    //   }
    // }),
    payFormData(list) {
      let amount = 0;
      for (let i = 0; i < list.length; i++) {
        let data = list[i];
        amount += data.total_price;
      }
      const dataList = list.reduce((l, j) => {
        const item = l.find(item => item.dept_id === j.dept_id);
        const materialItem = {
          material_name: j.material_name,
          procurement_num: j.procurement_num,
          unit: j.unit,
          amount:j.purchasing_cost,
          total_price:j.total_price || 0,
          id: j.id
        };

        if (!item) {
          l.push({
            dept_id: j.dept_id,
            store_name: j.store_name,
            material: [materialItem]
          });
        } else {
          item.material.push(materialItem);
        }
        return l;
      }, []);
      this.payForm.data.amount = amount;
      this.payForm.data.detail = dataList;
    },
    //付款申请
    payFormUpdata() {
      if (this.selectedRowKeys.length > 0) {
        let is_can_pay = true;
        this.selectedRows.forEach((value) => {
          if(!this.includesStatus(value.status,[6,10,11])){
            is_can_pay = false;
          }
        });

        if(!is_can_pay){
          this.$message.warning("选择的数据中，有不是待付款的数据，请认真核对");
          return ;
        }

        this.form.resetFields();
        this.supplierData = null;
        this.payForm.visible = true;
        inventoryManage.payTypeSelect().then(res => {
          if (res.code == "200") {
            this.payForm.payTypeData = res.data;
          }
        });
      } else {
        this.$message.warning("至少选择一笔要付款的单子");
      }
    },
    handlePayFormOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmPayFormLoading = true;
          values.detail_list = this.payForm.data.detail.reduce((n,l)=> {
          n = n.concat(l.material)
          return n
        }, []).map(l=> ({id: l.id, price: l.total_price}));
          values.pay_time = moment(values.pay_time).unix();
          this.$newDebounce(() => {
            inventoryManage
              .purchasePayCreate(values)
              .then(res => {
                if (res.code == 200) {
                  this.payForm.visible = false;
                  this.$message.success(res.message);
                  this.reload();
                } else {
                  this.$message.err(res.message);
                }
              })
              .finally(() => {
                this.confirmPayFormLoading = false;
              });
          });
        }
      });
    },
    handlePayFormClose() {
      this.payForm.visible = false;
    },
    initDictConfig() {},
    //取消采购
    cancelPurchase(record) {
      this.cancleInfo.id = record.id;
      this.cancleInfo.visible = true;
    },
    handleCancleOk() {
      let params = {
        id: this.cancleInfo.id,
        abnormal_note: this.cancleInfo.note,
      };
      let that = this;
      that.cancleInfo.confirmLoading = true;
      inventoryManage.canclePurchase(params).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.message);
          that.handleCancleCancel();
          that.reload();
        } else {
          that.cancleInfo.confirmLoading = false;
        }
      });
    },
    handleCancleCancel() {
      this.cancleInfo = {
        id: "",
        title: "异常处理",
        visible: false,
        note: "",
      };
      this.cancleInfo.visible = false;
      this.cancleInfo.confirmLoading = false;
    },
    //详情
    view(record){
      this.viewModal.visible = true;
      this.viewModal.confirmLoading = true;
      inventoryManage.detailView({id:record.id}).then(res => {
        this.viewModal.confirmLoading = false;  
        if (res.code == "200") {
            this.viewModal.data = res.data;
          }
        });
    },
    handleViewCancel(){
      this.viewModal.visible = false;
    },
    //修改采购数量
    changePurchaseNum(record) {
      if (record.id <= 0) {
        this.$message.warning("请选择数据");
        return;
      }
      this.changePurchaseModal.visible = true;
      this.changePurchaseModal.store_name = record.store_name;
      this.changePurchaseModal.procurement_num = record.procurement_num;
      this.changePurchaseModal.data = record;
    },
    handleChangePurchaseCheck() {
      let params = [
        {
          id: this.changePurchaseModal.data.id,
          procurement_num: this.changePurchaseModal.procurement_num
        }
      ];
      let $this = this;
      $this.changePurchaseModal.confirmLoading = true;
      inventoryManage.changePurchaseNum(params).then(res => {
        if (res.code == 200) {
          $this.handleChangePurchaseCancel();
          $this.$message.success(res.message);
          $this.reload();
        } else {
          $this.changePurchaseModal.confirmLoading = false;
        }
      });
    },
    handleChangePurchaseCancel() {
      this.changePurchaseModal.visible = false;
      this.changePurchaseModal.confirmLoading = false;
      this.changePurchaseModal.store_name = "";
      this.changePurchaseModal.procurement_num = 0;
      this.changePurchaseModal.data = undefined;
    },
    //发货
    shipments(record) {
      if (record.id <= 0) {
        this.$message.warning("请选择数据");
        return;
      }
      this.shipmentsModal.visible = true;
      this.shipmentsModal.store_name = record.store_name;
      this.shipmentsModal.hair_num = record.procurement_num;
      this.shipmentsModal.data = record;
    },
    handleShipmentsCheck() {
      let params = [
        {
          id: this.shipmentsModal.data.id,
          hair_num: this.shipmentsModal.hair_num
        }
      ];
      let $this = this;
      $this.shipmentsModal.confirmLoading = true;
      inventoryManage.shipmentGoods(params).then(res => {
        if (res.code == 200) {
          $this.handleShipmentsCancel();
          $this.$message.success(res.message);
          $this.reload();
        } else {
          $this.shipmentsModal.confirmLoading = false;
        }
      });
    },
    handleShipmentsCancel() {
      this.shipmentsModal.visible = false;
      this.shipmentsModal.confirmLoading = false;
      this.shipmentsModal.store_name = "";
      this.shipmentsModal.hair_num = 0;
      this.shipmentsModal.data = undefined;
    },
    batchShipments() {
      if (this.selectedRowKeys.length > 0) {
        let params = [];
        let is_can_shipment = true;
        this.batchShipmentsLoading = true;
        let error = '';
        this.selectedRows.forEach((value) => {
          try{
              if(!this.includesStatus(value.status,[8,7])){
                 throw new Error('只允许付款审核中、待发货状态才能发货操作,物料：' + value.material_name + "，不允许发货");
              }
            
              if(value.shipments == 1){
                throw new Error(value.material_name + ',该物料已经发货,请刷新页面');
              }
            params.push({
              id: value.id,
              hair_num: (value.status == 7) ? value.procurement_num : value.hair_num,
            })
          }catch(e){
            is_can_shipment = false;
            error = e.message;
          }
        });

        if(!is_can_shipment){
          this.batchShipmentsLoading = false;
          this.$message.warning(error);
          return ;
        }
    
        const _this = this;
        this.$confirm({
          title: "批量发货",
          okText: "确认",
          cancelText: "取消",
          content: "是否确认批量发货",
          onOk: () => {
            return new Promise((resolve, reject) => {
              inventoryManage.shipmentGoods(params)
                .then(res => {
                    if (res.code == 200) {
                      _this.$message.success(res.message);
                      resolve();
                      _this.reload();
                    }else{
                      reject();
                    }
                    this.batchShipmentsLoading = false;
                  });
            });
          },
          onCancel: () => {
            this.batchShipmentsLoading = false;
          },
        });
      } else {
        this.$message.warning("请选择要发货的数据");
      }
    },
    //收货
    acceptance(record) {
      if (record.id <= 0) {
        this.$message.warning("请选择数据");
        return;
      }
      this.acceptanceModal.visible = true;
      this.acceptanceModal.store_name = record.store_name;
      this.acceptanceModal.collect_num = record.hair_num;
      this.acceptanceModal.data = record;
      this.acceptanceModal.acceptance_img = [];
    },
    changePrice(record){
      if (record.id <= 0) {
        this.$message.warning("请选择数据");
        return;
      }
      this.changePriceModal.visible = true;
      this.changePriceModal.store_name = record.store_name;
      this.changePriceModal.total_price = record.total_price;
      this.changePriceModal.data = record;
    },
    handleChangePriceCheck(){
      let params =
        {
          id: this.changePriceModal.data.id,
          total_price: this.changePriceModal.total_price
        };
      
      let $this = this;
      $this.changePriceModal.confirmLoading = true;
      inventoryManage.changePrice(params).then(res => {
        if (res.code == 200) {
          $this.handleChangePriceCancel();
          $this.$message.success(res.message);
          $this.reload();
        } else {
          $this.changePriceModal.confirmLoading = false;
        }
      });
    },
    handleChangePriceCancel(){
      this.changePriceModal.visible = false;
      this.changePriceModal.confirmLoading = false;
      this.changePriceModal.store_name = "";
      this.changePriceModal.total_price = 0;
      this.changePriceModal.data = undefined;
    },
    handleAcceptanceCheck() {
      let acceptance_img = [];
      if(this.acceptanceModal.acceptance_img.length > 0){
        acceptance_img = this.acceptanceModal.acceptance_img.split(',')
      }
      let params = [
        {
          id: this.acceptanceModal.data.id,
          collect_num: this.acceptanceModal.collect_num,
          acceptance_img : acceptance_img
        }
      ];
      let $this = this;
      $this.acceptanceModal.confirmLoading = true;
      inventoryManage.acceptanceGoods(params).then(res => {
        if (res.code == 200) {
          $this.handleAcceptanceCancel();
          $this.$message.success(res.message);
          $this.reload();
        } else {
          $this.acceptanceModal.confirmLoading = false;
        }
      });
    },
    handleAcceptanceCancel() {
      this.acceptanceModal.visible = false;
      this.acceptanceModal.confirmLoading = false;
      this.acceptanceModal.store_name = "";
      this.acceptanceModal.collect_num = 0;
      this.acceptanceModal.data = undefined;
    },
    //异常处理
    checkError(record) {
      this.errorInfo.id = record.id;
      this.errorInfo.visible = true;
    },
    handleErrorOk() {
      let params = {
        id: this.errorInfo.id,
        abnormal_note: this.errorInfo.note,
      };
      let that = this;
      that.errorInfo.confirmLoading = true;
      inventoryManage.handleError(params).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.message);
          that.handleErrorCancel();
          that.reload();
        } else {
          that.errorInfo.confirmLoading = false;
        }
      });
    },
    handleErrorCancel() {
      this.errorInfo = {
        id: "",
        title: "异常处理",
        visible: false,
        note: "",
      };
      this.errorInfo.visible = false;
      this.errorInfo.confirmLoading = false;
    },
    //导出-数据格式
    dataFormat({ list }) {
      let arr = [];
      for (let i = 0; i < list.length; i++) {
        let b = list[i];
        b.created_at = this.dateParse(parseInt(b.created_at) * 1000);
        b.amount = b.purchasing_cost * b.procurement_num;
        let nb = pick(
          b,
          this.exportKey
        );
        arr.push(nb);
      }
      return arr;
    },
    includesStatus(status, arr) {
      return arr.includes(parseInt(status));
    },
  }
};
</script>
  
  <style lang="less" scoped>
@import "~@assets/less/common.less";
.buttom-bar {
  text-align: right;
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #eee;
  left: 0;
  padding: 10px 0;
  background: #f47b7b;

  button {
    margin: 0 5px;
  }
}

.table-operator {
  display: flex;
  flex-direction: row;

  .operator {
    flex: 1;
    display: flex;
    flex-direction: row;
    // flex-grow: ;
    a {
      white-space: nowrap;
    }
  }

  .btns {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }
}

.material {
  /deep/ .ant-form-item-children {
    line-height: normal;
    position: relative;
    top: 10px;
  }
}
img {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    padding: 6px;
    margin-right: 10px;
  }
.cell {
  font-size: 16px;
  > span:nth-child(1) {
    display: inline-block;
    width: 130px;
    text-align: right;
    margin-right: 10px;
  }
  > span:nth-child(2) {
    color: rgba(0, 0, 0, 0.85);
  }
}

 .formRowHeight{
  margin-bottom: 0px;
 }
 .listimage {
  width: 50px;
  height: 50px;
  cursor: pointer;
}
</style>
  