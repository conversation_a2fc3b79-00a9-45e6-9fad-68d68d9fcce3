<template>
  <div>
    <a-button type="dashed" icon="plus" @click="showModal">
      添加收款商户
    </a-button>
    <j-modal :visible.sync="modal.visible" :width="800" :title="modal.title" :fullscreen.sync="modal.fullscreen"
      :confirmLoading="modal.confirmLoading" @ok="okClose">
      <a-row :gutter="24">
        <a-col :lg="24" :md="24" :sm="24" style="margin-bottom: 8px">
          <template>
            <a-popover v-model="modalVisible" title="新增" trigger="click" placement="bottomLeft" :overlayStyle="{
              width: '260px',
              height: 'auto',
            }">
              <div slot="content">
                <a-input placeholder="请输入定向名称" v-model="name" class="mb10" @keyup.enter.native="add()"></a-input>
                <a-input :maxLength="64" placeholder="请输入备注" v-model="remark" class="mb10"
                  @keyup.enter.native="add()"></a-input>
                <div class="butBox">
                  <a-button @click="modalHide()">取 消</a-button>
                  <a-button type="primary" :disabled="disabled" @click="add()">确 定</a-button>
                </div>
              </div>
            </a-popover>
          </template>
        </a-col>
      </a-row>

      <template>
        <div>
          <a-table :columns="columns_modal" :data-source="modal.data.list" :pagination="ipagination"
            :loading="modalLoading" @change="handleTableChange" :scroll="{ y: 500 }" :rowKey="(record, index) => {
              return record.id;
            }
              " :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">
            <template slot="name" slot-scope="name">
              <j-ellipsis :value="name" :length="20" />
            </template>
            <span slot="status" slot-scope="status">
              <span :style="{ color: status == 1 ? 'green' : 'red' }">{{
                status == 1 ? "启用" : "禁用"
              }}</span>
            </span>
            <div slot="tag_name_array" slot-scope="text, record, index">
              <self-list-drop-down :data="text" :hiddenLength="12" isUnidimensional icon="a" />
            </div>
            <span slot="action" slot-scope="text, record" v-if="showAction" v-divider>
              <template>
                <a-popover v-model="record.visible" title="修改定向" trigger="click" placement="bottomRight" :overlayStyle="{
                  width: '260px',
                  height: 'auto',
                }">
                  <div slot="content">
                    <a-input placeholder="请输入定向名称" v-model="record.changeName" class="mb10"
                      @keyup.enter.native="change(record)"></a-input>
                    <a-input :maxLength="64" placeholder="请输入备注" v-model="record.changeRemark" class="mb10"
                      @keyup.enter.native="change(record)"></a-input>
                    <div class="butBox">
                      <a-button @click="changeHide(record)">取 消</a-button>
                      <a-button type="primary" :disabled="disabled" @click="change(record)">确 定</a-button>
                    </div>
                  </div>
                  <a type="primary">修改名称</a>
                </a-popover>
              </template>
              <a href="javascript:;" @click="setStatus(record.id, 1)" v-if="record.status == 0">启用</a>
              <a href="javascript:;" @click="setStatus(record.id, 0)" v-if="record.status == 1">禁用</a>
            </span>
          </a-table>
        </div>
      </template>
    </j-modal>
  </div>
</template>
<script>
import { unionPay } from "@/api/api";
import lodash from 'lodash'
export default {
  name: "directional",
  props: {
    unionPayConfig: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedRowKeys: [],
      disabled: false,
      modal: {
        title: "添加收款账户",
        visible: false,
        fullscreen: false,
        switchFullscreen: true,
        confirmLoading: false,
        time: null,
        data: {
          list: [],
        },
      },
      modalVisible: false,
      showAction: true,
      name: "",
      remark: "",
      //定向管理-字段
      columns_modal: [
        {
          title: "分店名称",
          align: "left",
          dataIndex: "name",
        },
        {
          title: "商户名称（开户执照）",
          align: "left",
          dataIndex: "licence",
        },
        {
          title: "标签",
          align: "left",
          dataIndex: "tag_name_array",
          scopedSlots: { customRender: "tag_name_array" },
        },
        {
          title: "状态",
          align: "center",
          dataIndex: "status",
          scopedSlots: { customRender: "status" },
        },
      ],
      modalLoading: false,
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 10,
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
      list1: []
    };
  },
  watch: {
    // 监听弹窗关闭，重置状态以便下次重新初始化
    'modal.visible'(newVal, oldVal) {
      if (oldVal === true && newVal === false) {
        // 弹窗从打开变为关闭，重置相关状态
        this.selectedRowKeys = [];
        this.list1 = [];
        this.ipagination.current = 1;
      }
    }
  },
  methods: {
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    okClose() {
      console.log(this.selectedRowKeys)
      console.log(this.list1)
      
      // 从已加载的数据中筛选出被选中的商户
      const newSelectedList = this.list1.filter((item) => this.selectedRowKeys.some(i => i == item.id))
      
      // 获取原始配置的商户ID列表
      const originalSelectedIds = this.unionPayConfig.map(l => parseInt(l))
      const loadedIds = this.list1.map(item => item.id)
      
      // 识别在已加载页面中被用户取消选择的原始配置商户ID
      const canceledOriginalIds = originalSelectedIds.filter(id => 
        loadedIds.includes(id) && !this.selectedRowKeys.includes(id)
      )
      
      // 识别那些原本已配置但不在当前已加载数据中的商户（未访问页面的原始配置）
      // 需要排除那些被用户在已加载页面中明确取消的商户
      const unloadedOriginalIds = originalSelectedIds.filter(id => 
        !loadedIds.includes(id) && !canceledOriginalIds.includes(id)
      )
      
      // 为未加载的原始配置创建占位对象（保持原有配置不丢失）
      const unloadedPlaceholders = unloadedOriginalIds.map(id => ({
        id: id,
        // 添加占位标识，表示这是未加载的原始配置
        _isUnloadedOriginal: true
      }))
      
      // 合并新选中的和未加载的原始配置
      const finalList = [...newSelectedList, ...unloadedPlaceholders]
      
      this.$emit('okClose', finalList)
    },

    modalHide() {
      let that = this;
      that.modalVisible = false;
      that.name = "";
      that.remark = "";
    },
    //新增链路
    add() {
      if (this.time) clearTimeout(this.time);
      this.time = setTimeout(() => {
        let that = this;
      if (that.name.length === 0) {
          that.$message.warning("标签名称不能为空");
          return false;
        }
        this.disabled = true;
        unionPay
          .index({ name: that.name, remark: that.remark })
          .then((res) => {
            if (res.code == 200) {
              that.$message.success(res.message);
              this.showModal();
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            this.disabled = false;
          });
      }, 200);
    },
    //修改
    change(record) {
      if (this.time) clearTimeout(this.time);
      this.time = setTimeout(() => {
        let that = this;
        if (record.changeName.length === 0) {
          that.$message.warning("定向名称不能为空");
          return false;
        }
        this.disabled = true;
        payCode
          .unionPayTagUpdate({
            id: record.id,
            name: record.changeName,
            remark: record.changeRemark,
          })
          .then((res) => {
            if (res.code == 200) {
              record.visible = false;
              that.$message.success(res.message);
              this.showModal();
            } else {
              that.$message.warning(res.message);
            }
          }).finally(() => {
            this.disabled = false;
          });;
      }, 200);
    },
    //关闭修改弹窗
    changeHide(record) {
      record.visible = false;
      record.changeName = record.name;
      record.changeRemark = record.remark;
    },
    //管理-分页触发事件
    handleTableChange(pagination) {
      //分页、排序、筛选变化时触发
      const pager = { ...this.ipagination };
      pager.current = pagination.current;
      this.ipagination = pager;
      this.tableLoading = true;
      this.showModal();
    },
    showModal() {
      let that = this;
      that.modalLoading = true;
      
      // 只在第一次打开弹窗时初始化选中状态，后续页面切换不重置
      const isFirstOpen = !that.modal.visible;
      that.modal.visible = true;
      
      if (isFirstOpen) {
        this.selectedRowKeys = this.unionPayConfig.map(l=> parseInt(l));
      }
      
      unionPay
        .index({
          status: 1,
          limit: that.ipagination.pageSize,
          page: that.ipagination.current,
        })
        .then((res) => {
          that.modalLoading = false;
          that.showAction = false;
          that.$nextTick(() => {
            that.showAction = true;
          });
          if (res.code === 200) {
            that.modal.data = res.data;
            that.list1 = lodash.uniqBy(that.list1.concat(res.data.list), 'id')
            that.ipagination.total = parseInt(res.data.totalCount);
          } else {
            that.$message.warning(res.message);
          }
        });
    },
    setStatus(id, status) {
      let that = this;
      that.modalLoading = true;
      payCode.unionPayTagSetStatus({ id: id, status: status }).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.message);
          that.showModal();
        } else {
          that.$message.warning(res.message);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.butBox {
  display: flex;

  button {
    flex: 1;
    margin: 0 3px;
  }
}

/deep/ .ant-modal-body {
  padding: 8px 24px 24px 24px;
}
</style>
