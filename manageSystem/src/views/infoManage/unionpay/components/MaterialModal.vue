<template>
  <a-drawer
    :title="title"
    :width="drawerWidth"
    @close="handleCancel"
    :visible="visible"
    :confirmLoading="confirmLoading"
  >
    <div :style="{ width: '100%', background: '#fff' }" class="pb60">
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <a-form-item
            label="分店名称"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input
              placeholder="请输入分店名称"
              v-decorator="['name', validatorRules.name]"
              :readOnly="disableSubmit"
              :maxLength="50"
            />
          </a-form-item>
          <a-form-item
            label="商户名称"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input
              placeholder="请输入商户名称（开户执照）"
              v-decorator="['licence', validatorRules.licence]"
              :readOnly="disableSubmit"
              :maxLength="50"
            />
          </a-form-item>
          <a-form-item
            label="B扫C商户编号"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input
              placeholder="请输入B扫C商户编号"
              v-decorator="['merchantCode', validatorRules.merchantCode]"
              :readOnly="disableSubmit"
              :maxLength="50"
            />
          </a-form-item>
          <a-form-item
            label="B扫C终端编号"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input
              placeholder="请输入B扫C终端编号"
              v-decorator="['terminalCode', validatorRules.terminalCode]"
              :readOnly="disableSubmit"
              :maxLength="50"
            />
          </a-form-item>
          <a-form-item
            label="C扫B商户号"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input
              placeholder="请输入C扫B商户号"
              v-decorator="['mid', validatorRules.mid]"
              :readOnly="disableSubmit"
              :maxLength="50"
            />
          </a-form-item>
          <a-form-item
            label="C扫B终端号"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input
              placeholder="请输入C扫B终端号"
              v-decorator="['tid', validatorRules.tid]"
              :readOnly="disableSubmit"
              :maxLength="50"
            />
          </a-form-item>
          <a-form-item
            label="来源编号"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select
              placeholder="请选择来源编号"
              v-decorator="['bill_no_title', validatorRules.bill_no_title]"
              :readOnly="disableSubmit"
              @change="handleBillNoTitleChange"
              allowClear
            >
              <a-select-option
                v-for="item in billNoTitleOptions"
                :key="item.id"
                :value="item.id"
              >
                <span>{{ item.id }}</span>
                <span style="color: #999; font-size: 12px;">({{ item.name }})</span>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="商户appKey"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input
              placeholder="通过来源编号自动填充"
              v-decorator="['app_key', validatorRules.app_key]"
              :readOnly="true"
              :maxLength="50"
              class="readonly-field"
            />
          </a-form-item>
          <a-form-item
            label="商户appId"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-input
              placeholder="通过来源编号自动填充"
              v-decorator="['app_id', validatorRules.app_id]"
              :readOnly="true"
              :maxLength="50"
              class="readonly-field"
            />
          </a-form-item>
          <a-form-item
            label="通讯密钥"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
          <a-input
              placeholder="通过来源编号自动填充"
              v-decorator="['key', validatorRules.key]"
              :readOnly="true"
              :maxLength="50"
              class="readonly-field"
            />
          </a-form-item>
          <a-form-item
            label="银联收款标签"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <commone-self-principal
              :requestFun="unionPayTagSelect"
              placeholder="请选择银联收款标签"
              value_key="name"
              id_key="id"
              :showAll="false"
              :isRequest="true"
              mode="multiple"
              v-decorator="['pay_tag_ids', validatorRules.pay_tag_ids]"
            />
          </a-form-item>
        </a-form>
      </a-spin>
      <div class="right-side-bottom-bar">
        <a-button size="large" class="pl25 pr25 mr15" @click="handleCancel"
          >关闭</a-button
        >
        <a-button
          size="large"
          class="pl25 pr25"
          :disabled="disableSubmit"
          @click="handleOk"
          type="primary"
          >确定</a-button
        >
      </div>
    </div>
  </a-drawer>
</template>

<script>
import { unionPay, payCode } from "@/api/api";
import pick from "lodash.pick";
import JImageUpload from "@/components/jeecg/JImageUpload";
import commentMixin from "@/mixins/commentMixin";
import mixin from "../mixin";
export default {
  name: "MaterialModal",
  components: { JImageUpload },
  mixins: [commentMixin, mixin],
  data() {
    return {
      unionPayTagSelect: payCode.unionPayTagSelect,
      billNoTitleOptions: [],
      drawerWidth: 700,
      treeData: [],
      title: "操作",
      record: {},
      visible: false,
      redirect: "",
      disableSubmit: false,
      model: {
        // report_array: null,
      },
      request_way: "GET",
      show: true, //根据菜单类型，动态显示隐藏表单元素
      menuLabel: "接口名称",
      isRequrie: true, // 是否需要验证
      rtype: "1", //手动输入/选中已有
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },

      confirmLoading: false,
      form: this.$form.createForm(this),

      iconChooseVisible: false,
      validateStatus: "",
      searchSelect: {
        data: [],
        value: undefined,
      },
      detail: undefined,
      linkList: [], // 链路列表
      channelList: [], // 渠道列表
      projectList: [], // 项目列表
      filterListData: [], // 代理商
      isCreate: false,
      test: ["测试账户1", "测试账户2"],
      timeid: null, // 防抖
      record: {},
      temporary: {},
      getListData: [],
      selectedList: [],
      report_account_list: [],
      report_condition_list: [],
      report_platform_list: [],
      report_condition_list_: [],
      report_event_list: [],
      report_event_list_: [],
      report_show: true,
    };
  },
  computed: {
    validatorRules: function () {
      return {
        name: { rules: [{ required: true, message: "请输入分店名称!" }] },
        licence: { rules: [{ required: true, message: "请输入商户名称!" }] },
        merchantCode: { rules: [{ required: true, message: "请输入B扫C商户编号!" }] },
        terminalCode: { rules: [{ required: true, message: "请输入B扫C终端编号!" }] },
        key: { rules: [{ required: true, message: "请输入通讯密钥!" }] },
        app_id: { rules: [{ required: true, message: "请输入app_id!" }] },
        app_key: { rules: [{ required: true, message: "请输入appsercet!" }] },
        mid: { rules: [{ required: true, message: "请输入C扫B商户编号!" }] },
        tid: { rules: [{ required: true, message: "请输入C扫B终端号!" }] },
        bill_no_title: { rules: [{ required: true, message: "请选择来源编号!" }] },
        indexpath: { rules: [{ required: true, message: "请输入首页路径!" }] },
        remark: { rules: [{ required: true, message: "请输入备注!" }] },
      };
    },
  },
  methods: {
    // 获取来源编号选项
    async loadBillNoTitleOptions() {
      try {
        const res = await unionPay.billNoTitleSelect();
        if (res.code === 200) {
          this.billNoTitleOptions = res.data;
        }
      } catch (error) {
        console.error('加载来源编号选项失败:', error);
      }
    },
    // 处理来源编号变化
    handleBillNoTitleChange(value) {
      if (value) {
        // 找到对应的选项数据
        const selectedOption = this.billNoTitleOptions.find(item => item.id === value);
        if (selectedOption) {
          // 自动填充关联字段
          this.form.setFieldsValue({
            app_id: selectedOption.app_id,
            app_key: selectedOption.app_key,
            key: selectedOption.key
          });
        }
      } else {
        // 清空关联字段
        this.form.setFieldsValue({
          app_id: '',
          app_key: '',
          key: ''
        });
      }
    },
    add() {
      // 默认值
      this.edit({ id: 0 });
      this.isCreate = true;
    },
    async edit(record) {
      this.record = JSON.parse(JSON.stringify(record));
      this.resetScreenSize(); // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
      this.form.resetFields();
      this.model = {};
      
      // 加载来源编号选项
      await this.loadBillNoTitleOptions();
      
      if (record.id != 0) {
        let {
          code,
          data: { info },
        } = await this.getCategory(record.id);
        if (code != "200") return;
        this.model = Object.assign({}, info);
      }
      delete this.model.status;
      this.visible = true;
      // 构造数据
      let fieldsVal = pick(
        this.model,
        "name",
        "licence",
        "app_id",
        "app_key",
        "mid",
        "tid",
        "index_path",
        "remark",
        "terminalCode",
        "merchantCode",
        "key",
        "pay_tag_ids",
        "bill_no_title",
        // 'status',
      );
      this.$nextTick(() => {
        this.form.setFieldsValue(fieldsVal);
      });
    },
    close() {
      this.$emit("close");
      this.disableSubmit = false;
      this.visible = false;
      this.isCreate = false;
      this.searchSelect = {
        data: [],
        value: undefined,
      };
    },
    requestFun() {
      const that = this;
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          let formData = Object.assign(
            JSON.parse(JSON.stringify(this.model)),
            values
          );
          that.confirmLoading = true;
          let obj;
          that.disableSubmit = true;
          if (!this.model.id) {
            //新增
            delete formData.id;
            obj = unionPay.create(formData);
          } else {
            //修改
            obj = unionPay.update(formData);
          }
          obj
            .then((res) => {
              if (res.code == "200") {
                that.$message.success(res.message);
                that.$emit("ok");
                that.close();
              } else {
                that.$message.warning(res.message);
              }
              that.disableSubmit = false;
            })
            .finally(() => {
              that.confirmLoading = false;
              that.disableSubmit = false;
            });
        }
      });
    },
    handleCancel() {
      this.close();
    },
    handleChange(value) {
      this.searchSelect.value = value;
      fetch(
        this.currentEntity,
        value,
        (data) => (this.searchSelect.data = data)
      );
      this.queryParam.dept_id = this.searchSelect.value;
      this.searchQuery();
    },
    /**
     * slesct下拉树 数据格式转换
     *@param  treeList 需转换的数组
     */
    treeDataformat(treeList, hierarchy) {
      let arr = [];
      let that = this;
      for (let a = 0; a < treeList.length; a++) {
        let temp = treeList[a];
        // if(temp.id==0){
        //   continue;
        // }
        let id = null;
        if (hierarchy == 1) {
          id = "h1" + a;
        } else {
          id = temp.id;
        }
        let obj = {
          label: temp.name,
          value: id,
          hierarchy,
        };

        if (temp.child && temp.child.length) {
          obj.children = this.treeDataformat(temp.child, 2);
        }
        arr.push(obj);
      }
      return arr;
    },
    // 根据屏幕变化,设置抽屉尺寸
    resetScreenSize() {
      let screenWidth = document.body.clientWidth;
      if (screenWidth < 500) {
        this.drawerWidth = screenWidth;
      } else {
        this.drawerWidth = 600;
      }
    },
    handleTypeChose(value) {
      this.request_way = value;
    },
    handleParentIdChange(e, label, extra, type) {
      // this.$refs.treeSelect.$vnode.elm.hidden = true
      if (!e) {
        this.validateStatus = "error";
      } else {
        this.validateStatus = "success";
        if (e.indexOf("h1") > -1) {
          let fieldsVal = pick({ promote_id: undefined }, "promote_id");
          this.$nextTick(() => {
            this.form.setFieldsValue(fieldsVal);
          });
        }
      }
    },
    create69() {
      let code = new Date().getTime();
      this.form.setFieldsValue({ bar_code: code });
    },
  },
};
</script>

<style lang="less" scoped>
.reporBlock {
  padding: 15px 0;
  border: dashed 1px #d9d9d9;
  margin-bottom: 24px;
  position: relative;
  .condition {
    position: absolute;
    padding: 5px 10px;
    background: #fff;
    top: -2px;
    left: 20px;
    transform: translate(10%, -50%);
  }
  .delete {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(50%, -50%);
  }
  .a-form-item {
    margin-bottom: 0 !important;
  }
}

// 只读字段样式
/deep/ .readonly-field.ant-input[readonly] {
  background-color: #f5f5f5 !important;
  color: #666 !important;
  cursor: not-allowed !important;
  border-color: #d9d9d9 !important;
}

/deep/ .readonly-field.ant-input[readonly]:hover {
  border-color: #d9d9d9 !important;
}

/deep/ .readonly-field.ant-input[readonly]:focus {
  border-color: #d9d9d9 !important;
  box-shadow: none !important;
}
</style>
