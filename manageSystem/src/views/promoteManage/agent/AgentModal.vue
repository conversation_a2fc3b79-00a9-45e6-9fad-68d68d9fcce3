<template>
  <a-drawer
    :title="title"
    :width="drawerWidth"
    @close="handleCancel"
    :visible="visible"
    :confirmLoading="confirmLoading"
  >
    <div
      :style="{
        width: '100%',
        border: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
      }"
    >
      <a-spin :spinning="confirmLoading">
        <a-form :form="form">
          <!-- end -->
          <a-form-item
            label="代理商名称"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            required
          >
            <a-input
              :disabled="disableSubmit"
              placeholder="请输入代理商名称"
              v-decorator="['name', validatorRules.name]"
              :readOnly="disableSubmit"
            />
          </a-form-item>

          <a-form-item
            label="渠道分类"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            required
          >
            <a-select
              show-search
              @search="(e) => handleSearch(e, 'promote_list')"
              placeholder="请选择渠道"
              :default-active-first-option="false"
              :disabled="disableSubmit"
              :filter-option="false"
              :not-found-content="null"
              v-decorator="['promote_id', validatorRules.promote_id]"
              v-if="rtype == 1"
            >
              <a-select-option
                :key="item.id"
                :value="item.id"
                v-for="item in promote_list"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
            <a-input
              v-decorator="['label', validatorRules.label]"
              v-if="rtype == 2"
            ></a-input>
          </a-form-item>

          <a-form-item
            label="代号"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            required
          >
            <a-input
              :disabled="disableSubmit"
              placeholder="请输入代号"
              v-decorator="['code', validatorRules.code]"
              :readOnly="disableSubmit"
            />
          </a-form-item>

          <a-form-item
            label="返点"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            required
          >
            <a-input-number
              :disabled="disableSubmit"
              style="width: 100%"
              placeholder="请输入返点"
              v-decorator="['rebate', validatorRules.rebate]"
              :readOnly="disableSubmit"
              :precision="3"
            />
          </a-form-item>

          <a-form-item
            label="说明"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-textarea
              :disabled="disableSubmit"
              placeholder="请输入说明"
              v-decorator="['note', validatorRules.note]"
              :readOnly="disableSubmit"
            />
          </a-form-item>

          <a-form-item
            label="状态"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <a-select
              :disabled="disableSubmit"
              v-decorator="['status', validatorRules.status]"
            >
              <a-select-option
                :key="item.id"
                :value="item.id"
                v-for="item in statusList"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </a-form-item>

          <a-form-item
            label="收款账户"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
          >
            <div class="AListBox">
              <a-list
                item-layout="horizontal"
                style="flex"
                :data-source="model.account"
              >
                <a-list-item slot="renderItem" slot-scope="item, index">
                  <a-list-item-meta>
                    <span slot="title">
                      {{ item.name }}
                      <a-tag color="blue">
                        {{ item.type == 1 ? "个人" : "对公" }}
                      </a-tag>
                    </span>
                    <span slot="description">
                      <div style="color: blcak">{{ item.bank_name }}</div>
                      <div>{{ item.card_no }}</div>
                    </span>
                  </a-list-item-meta>
                  <a-icon
                    slot="actions"
                    type="form"
                    @click="handleAddorEdit(item, index)"
                  />
                  <a-icon
                    slot="actions"
                    type="delete"
                    @click="handleDelete(index)"
                  />
                </a-list-item>
              </a-list>
              <a-button
                @click="handleAddorEdit()"
                type="primary"
                class="primary ml10"
                size="small"
                icon="plus"
              >
                添加收款账户
              </a-button>
            </div>
          </a-form-item>

          <template v-if="disableSubmit && false">
            <a-form-item
              label="创建人"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="disableSubmit"
                placeholder="请输入创建人"
                v-decorator="['code', validatorRules.code]"
                :readOnly="disableSubmit"
              />
            </a-form-item>

            <a-form-item
              label="创建时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="disableSubmit"
                placeholder="请输入创建时间"
                v-decorator="['code', validatorRules.code]"
                :readOnly="disableSubmit"
              />
            </a-form-item>

            <a-form-item
              label="修改人"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="disableSubmit"
                placeholder="请输入修改人"
                v-decorator="['code', validatorRules.code]"
                :readOnly="disableSubmit"
              />
            </a-form-item>

            <a-form-item
              label="修改时间"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <a-input
                :disabled="disableSubmit"
                placeholder="请输入修改时间"
                v-decorator="['code', validatorRules.code]"
                :readOnly="disableSubmit"
              />
            </a-form-item>
          </template>
        </a-form>
      </a-spin>
      <a-row :style="{ textAlign: 'right' }">
        <a-button :style="{ marginRight: '8px' }" @click="handleCancel">
          关闭
        </a-button>
        <a-button :disabled="disableSubmit" @click="handleOk" type="primary"
          >确定</a-button
        >
      </a-row>
    </div>
    <!--  账户相关弹出层   -->
    <a-modal
      v-model="account.Visible"
      title="添加收款账户"
      @ok="accounthandleAdd"
    >
      <div class="account">
        <a-radio-group class="radioGroup" v-model="account.type">
          <a-radio-button value="1"> 个人账户 </a-radio-button>
          <a-radio-button value="2"> 对公账户 </a-radio-button>
        </a-radio-group>

        <a-form :form="form_" style="width: 100%" v-if="account.Visible">
          <!-- 个人账户 -->
          <a-form-item
            label="户名"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            required
          >
            <a-input
              style="width: 100%"
              placeholder="请输入账户名称"
              v-decorator="['name', changeRules.name]"
            >
            </a-input>
          </a-form-item>
          <a-form-item
            label="卡号"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            required
          >
            <a-input
              style="width: 100%"
              placeholder="请输入银行卡号"
              v-decorator="['card_no', changeRules.card_no]"
            >
            </a-input>
          </a-form-item>
          <a-form-item
            label="银行"
            :labelCol="labelCol"
            :wrapperCol="wrapperCol"
            required
          >
            <a-input
              style="width: 100%"
              placeholder="请输入银行名称"
              v-decorator="['bank_name', changeRules.bank_name]"
              :precision="3"
            >
            </a-input>
          </a-form-item>
          <!-- 对公账户 -->
          <template v-if="account.type == 2">
            <a-form-item
              label="银行所在地"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              required
            >
              <a-input
                style="width: 100%"
                placeholder="请输入银行所在地"
                v-decorator="['location', changeRules.location]"
                :precision="3"
              >
              </a-input>
            </a-form-item>
            <a-form-item
              label="支行名称"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              required
            >
              <a-input
                style="width: 100%"
                placeholder="请输入支行名称"
                v-decorator="['branch_name', changeRules.branch_name]"
                :precision="3"
              >
              </a-input>
            </a-form-item>
          </template>
        </a-form>
      </div>
    </a-modal>
  </a-drawer>
</template>
  
  <script>
import { promoteAgent } from "@/api/api";
import pick from "lodash.pick";
import moment from "moment";
import commentMixin from "@/mixins/commentMixin";
import { bankCardAttribution } from "@/utils/bank.js";
export default {
  name: "AgentModal",
  mixins: [commentMixin],
  data() {
    return {
      card_no: "",
      handleduise: {
        type: "",
        index: "",
      },
      account: {
        Visible: false,
        type: "1",
      },
      drawerWidth: 700,
      treeData: [],
      title: "操作",
      visible: false,
      redirect: "",
      disableSubmit: false,
      model: {
        account: [],
      },
      request_way: "GET",
      show: true, //根据菜单类型，动态显示隐藏表单元素
      menuLabel: "接口名称",
      isRequrie: true, // 是否需要验证
      rtype: "1", //手动输入/选中已有
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      form_: this.$form.createForm(this),
      iconChooseVisible: false,
      validateStatus: "",
      userSelect: {
        data: [],
        value: undefined,
      },
      detail: undefined,
      promote_list: undefined,
      statusList: {},
    };
  },
  computed: {
    validatorRules: function () {
      return {
        name: { rules: [{ required: true, message: "请输入代理商名称!" }] },
        code: { rules: [{ required: true, message: "请输入代号!" }] },
        promote_id: { rules: [{ required: true, message: "请选择渠道分类!" }] },
        // rebate: {rules: [{required: true, message: '请输入返点!'}]},
        rebate: {
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (!value) {
                  if (value == 0) {
                    cbfn("返点不能小于等于0！");
                  } else {
                    cbfn("请输入返点！");
                  }
                } else if (value <= 0) {
                  cbfn("返点不能小于等于0！");
                }
                cbfn();
              },
            },
          ],
        },
        note: { initialValue: "" },
        status: { initialValue: "1" },
      };
    },
    changeRules: function () {
      return {
        name: { rules: [{ required: true, message: "请输入账户名称!" }] },
        card_no: {
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                console.log(value);
                if (!value) {
                  cbfn("请输入银行卡号！");
                } else if (!/^[1-9]\d{9,29}$/.test(value)) {
                  cbfn("请输入正确的银行卡号！");
                }
                cbfn();
              },
            },
          ],
        },
        bank_name: {
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (!value) {
                  cbfn("请输入银行名称");
                } else {
                  console.log(this.form_.getFieldValue("card_no"));
                  if (this.account) {
                    bankCardAttribution(this.form_.getFieldValue("card_no"))
                      .bankName != value && cbfn("请输入正确的银行名称！");
                  }
                }
                cbfn();
              },
            },
          ],
        },
        location: { rules: [{ required: true, message: "请输入银行所在地!" }] },
        branch_name: {
          rules: [{ required: true, message: "请输入支行名称!" }],
        },
      };
    },
  },
  created() {
    console.log(bankCardAttribution("6217001210024455220"));
    this.initDictConfig();
  },
  methods: {
    setCardNo(e) {
      console.log(e);
    },
    handleDelete(index) {
      const _this = this;
      this.$confirm({
        title: "提示",
        content: "确定删除此条数据！",
        onOk() {
          _this.model.account.splice(index, 1);
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    },
    handleAddorEdit(item = {}, index) {
      console.log(item);
      console.log(index);
      if (JSON.stringify(item) == "{}") this.handleduise.type = "add";
      else {
        this.handleduise.type = "edit";
        this.handleduise.index = index;
      }
      this.account = Object.assign(
        JSON.parse(JSON.stringify(this.account)),
        item
      );
      let fieldsVal = pick(
        this.account,
        "name",
        "card_no",
        "bank_name",
        "location",
        "branch_name"
      );
      this.account.Visible = true;
      this.$nextTick(() => {
        this.form_.setFieldsValue(fieldsVal);
      });
    },
    accounthandleAdd() {
      this.form_.validateFields((err, values) => {
        if (!err) {
          try {
            let formData = Object.assign(
              JSON.parse(JSON.stringify(this.account)),
              values
            );
            delete formData.Visible;
            if (this.handleduise.type == "add") {
              formData.id = "0";
              this.model.account.push(formData);
            } else {
              this.model.account.splice(this.handleduise.index, 1, formData);
            }
            this.account.Visible = false;
            this.account = {
              Visible: false,
              type: "1",
            };
          } catch (error) {
            console.log(error);
          }
        } else {
          console.log(err);
        }
      });
    },
    treeDataformat(treeList) {
      let arr = [];
      let that = this;
      for (let a = 0; a < treeList.length; a++) {
        let temp = treeList[a];
        let obj = {
          label: temp.name,
          value: temp.id,
        };

        if (temp.child && temp.child.length) {
          obj.children = that.treeDataformat(temp.child);
        }
        arr.push(obj);
      }
      return arr;
    },
    radioChange(v) {
      this.form.setFieldsValue({ tag: undefined });
    },
    dataformat(treeList) {
      let arr = [];
      let that = this;
      for (let a = 0; a < treeList.length; a++) {
        let temp = treeList[a];
        // temp.isLeaf = temp.leaf;
        temp.label = temp.title;
        temp.parentId = temp.pid;
        temp.key = temp.pid + temp.id;
        temp.value = temp.id;
        if (temp.child && temp.child.length) {
          temp.children = that.dataformat(temp.child);
        }
        arr.push(temp);
      }
      return arr;
    },
    getDetail(id) {},
    loadTree(id) {},
    add() {
      // 默认值
      this.edit({ id: 0 });
    },
    //时间格式
    dateParse(date) {
      return moment(new Date(parseInt(date))).format("YYYY-MM-DD HH:mm:ss");
    },
    async edit(record) {
      this.resetScreenSize(); // 调用此方法,根据屏幕宽度自适应调整抽屉的宽度
      this.form.resetFields();
      this.visible = true;
      let qid = record.id || 0;
      let $this = this;

      let res = await promoteAgent.view({ id: qid });
      if (res.code == "200") {
        $this.detail = res.data;
        if (res.data.info.bank_account) {
          res.data.info.account = res.data.info.account.map((item) => {
            if (typeof item.type == "number") item.type = item.type.toString();
            return item;
          });
        }
        console.log(res.data.info.account);
        this.model = Object.assign({ account: [] }, res.data.info);
        // this.promote_list = {
        //     promote_list: res.data.promote
        //     // promote_list: this.treeDataformat(res.data.promote)
        // }
        this.promote_list = res.data.promote;
        this.statusList = [
          { id: "0", name: "禁用" },
          { id: "1", name: "启用" },
        ];
        let keyArr = ["promote_id"];
        for (let i = 0; i < keyArr.length; i++) {
          let key = keyArr[i];
          if (this.model[key] == "0") {
            this.model[key] = undefined;
          } else {
            this.model[key] =
              this.model[key] != "" && this.model[key]
                ? String(this.model[key])
                : undefined;
          }
        }
        let fieldsVal = pick(
          this.model,
          "name",
          "code",
          "promote_id",
          "rebate",
          "code",
          "note",
          "status"
        );
        this.$nextTick(() => {
          this.form.setFieldsValue(fieldsVal);
        });
      } else {
        $this.$message.warning(res.message);
      }
    },
    close() {
      this.$emit("close");
      this.disableSubmit = false;
      this.visible = false;
    },
    requestFun() {
      const that = this;
      console.log("---------------->");
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          let formData = Object.assign(this.model, values);
          let params = {
            id: formData.id,
            name: formData.name,
            code: formData.code,
            promote_id: formData.promote_id,
            rebate: formData.rebate,
            note: formData.note,
            status: formData.status,
            account: formData.account,
          };
          that.confirmLoading = true;
          that.disableSubmit = true;
          let obj;
          if (!this.model.id) {
            obj = promoteAgent.create(params);
          } else {
            //修改
            obj = promoteAgent.update(params);
          }
          obj
            .then((res) => {
              if (res.code == "200") {
                that.$message.success(res.message);
                that.$emit("ok");
                that.close();
              } else {
                that.$message.warning(res.message);
              }
              that.disableSubmit = false;
            })
            .finally(() => {
              that.confirmLoading = false;
              that.disableSubmit = false;
            });
        }
      });
    },
    handleCancel() {
      this.close();
    },
    // 根据屏幕变化,设置抽屉尺寸
    resetScreenSize() {
      let screenWidth = document.body.clientWidth;
      if (screenWidth < 500) {
        this.drawerWidth = screenWidth;
      } else {
        this.drawerWidth = 700;
      }
    },
    initDictConfig() {},
    handleTypeChose(value) {
      this.request_way = value;
    },
    handleParentIdChange(value) {
      if (!value) {
        this.validateStatus = "error";
      } else {
        this.validateStatus = "success";
      }
    },
  },
};
</script>
  
  <style lang="less" scoped>
.AListBox {
  position: relative;
  top: -4px;
  left: 8px;
  display: flex;
  .primary {
    position: relative;
    top: 6px;
  }
  /deep/ .ant-list-split {
    flex: 1;
    max-height: 500px;
    overflow-y: scroll;
    position: relative;
  }
}

.account {
  display: flex;
  align-items: center;
  flex-direction: column;
  .radioGroup {
    margin: 0 auto 10px;
  }
}
</style>