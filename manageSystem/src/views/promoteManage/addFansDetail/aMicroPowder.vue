<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2022-07-08 14:24:06
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2022-11-28 11:50:16
 * @FilePath: \manageSystem\src\views\promoteManage\addFansDetail\aMicroPowder.vue
 * @Description: 加粉明细/企微加粉
-->
<template>
  <div>
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="客户名称">
          <a-input
            placeholder="请输入客户昵称或备注"
            v-model="queryParam.cus_name"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="加粉时间">
          <self-time
            timeRange="month"
            v-model="selfDefaultTime.promote"
            :reset="timeReset"
            :timeKey="{
              start: 'start_time',
              end: 'end_time',
            }"
          />
        </self-col>
        <self-col label="所属成员">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="wxcomUserSelcet"
            placeholder="请选择所属成员"
            value_key="name"
            v-model="queryParam.user_id"
            :isRequest="false"
          />
        </self-col>
        <self-col label="活码名称">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="cusQrcoedeselcet"
            @exportData="changeCode"
            placeholder="请选择活码名称"
            value_key="name"
            v-model="queryParam.qrcode_id"
            :isRequest="false"
          />
        </self-col>
        <self-col label="客户来源">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="getAddWaySelect"
            placeholder="请选择客户来源"
            value_key="name"
            v-model="queryParam.add_way"
            :isRequest="true"
          />
        </self-col>
        <self-col label="广告版位">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="getAdPositionSelect"
            placeholder="请选择广告版位"
            value_key="name"
            :isSearchrRequest="true"
            v-model="queryParam.csite"
            :isRequest="true"
          />
        </self-col>
        <self-col label="是否流失">
          <a-select
            show-search
            v-model="queryParam.status"
            placeholder="请选择是否流失"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option value>全部</a-select-option>
            <a-select-option :value="1" :key="1">未流失</a-select-option>
            <a-select-option :value="0" :key="0">已流失</a-select-option>
          </a-select>
        </self-col>
        <self-col label="所属企微">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="selcet"
            placeholder="请选择所属企微"
            value_key="name"
            v-model="queryParam.com_id"
          />
        </self-col>
        <self-col label="客户标签">
          <self-wxcus-tag @handleOk="handleOk" v-model="queryParam.tag_ids" />
        </self-col>
        <self-col label="归属推广">
          <commone-self-principal
            searchKey="username"
            :requestFun="getPromotePerson"
            placeholder="请选择归属推广"
            value_key="username"
            v-model="queryParam.qrcode_user_id"
            :isRequest="false"
          />
        </self-col>
        <self-col label="所属部门">
          <a-tree-select
            show-search
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="dept_list"
            v-model="queryParam.qrcode_dept_id"
            placeholder="请选择所属部门"
          ></a-tree-select>
        </self-col>
        <self-col label="推广账户">
          <a-input
            placeholder="请输入推广账户名称或账户ID"
            v-model="queryParam.promote_account"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="推广渠道">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="channelSelect"
            placeholder="请选择推广渠道"
            value_key="name"
            :isSearchrRequest="true"
            v-model="queryParam.channel_id"
            :isRequest="true"
          />
        </self-col>
        <self-col label="素材ID">
          <a-input
            placeholder="请输入素材ID"
            v-model="queryParam.mid3"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="推广项目">
          <a-tree-select
            show-search
            style="selectDep"
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="Project_tree"
            v-model="queryParam.currentKey"
            @change="treeSelectChange"
            :replaceFields="{
              children: 'child',
              title: 'name',
              value: 'currentKey',
            }"
            placeholder="请选择项目"
          ></a-tree-select>
        </self-col>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          :dataFormat="dataFormat"
          :query="queryParam"
          fileName="企微加粉"
          btnName="导出"
          :limit="1000"
          :queryParam="queryParam"
          :CommentApi="wxcomUser.export"
          :header="header"
          v-has="'cus-customer-user:export'"
        ></export-to-csv>
      </template>
    </self-page-header>
    <!--  导出进度条 -->
    <div v-if="SummaryShow" class="progress-box">
      <a-progress :percent="CitySummary"></a-progress>
    </div>
    <a-table
      ref="table"
      :pagination="ipagination"
      @change="handleTableChange"
      size="middle"
      :scroll="{ x: 2500 }"
      :dataSource="dataSourceFormat"
      :rowKey="(i, index) => index"
      :columns="columns"
      :loading="loading"
    >
      <template slot="customer" slot-scope="text, record, index">
        <user-detail
          :name="text ? (text.name || '-') : '-'"
          :remark="record.remark || '-'"
          :keyValue="index"
          :avatar="text ? text.avatar : ''"
          :mainBodyType="text ? text.type : ''"
          :explainLength="8"
        />
      </template>
      <template slot="user_name" slot-scope="text">
        <self-list-drop-down
          :data="text || {}"
          nameKey="name"
          icon="enterpriseMicro"
        />
      </template>
      <template slot="sub_advertiser_name" slot-scope="text, record">
        <div>{{ record.sub_advertiser_name }}</div>
        <div class="Id-lightGray" v-if="record.sub_advertiser_id">
          账户ID：{{ record.sub_advertiser_id }}
          <div v-if="record.adid">
          计划ID：{{ record.adid }}
        </div>
        </div>
      </template>
      <template slot="mid3" slot-scope="text, record">
        <!-- 有视频缩略图时，整个单元格内容都可以悬停展示缩略图 -->
        <a-popover 
          v-if="record.video_img" 
          placement="right" 
          trigger="hover"
          :getPopupContainer="triggerNode => triggerNode.parentNode"
        >
          <template slot="content">
            <div class="video-thumbnail-container">
              <img 
                :src="record.video_img" 
                alt="视频缩略图" 
                class="video-thumbnail"
                @error="handleImageError"
              />
            </div>
          </template>
          <div>
            <div class="Id-lightGray" v-if="record.mid3">
              <a class="Id-lightGray" @click="materialList(record)">素材ID：{{ record.mid3 }}</a>
            </div>
            <div class="Id-lightGray" v-if="record.csite_text">
              广告版位：
              <span 
                :class="{ 'ad-position-special': record.csite_text === '通投广告位' }"
              >
                {{ record.csite_text }}
              </span>
            </div>
          </div>
        </a-popover>
        <!-- 没有视频缩略图时的正常显示 -->
        <div v-else>
          <div class="Id-lightGray" v-if="record.mid3">
            <a class="Id-lightGray" @click="materialList(record)">素材ID：{{ record.mid3 }}</a>
          </div>
          <div class="Id-lightGray" v-if="record.csite_text">
            广告版位：
            <span 
              :class="{ 'ad-position-special': record.csite_text === '通投广告位' }"
            >
              {{ record.csite_text }}
            </span>
          </div>
        </div>
      </template>
      <template slot="tag_name_array" slot-scope="text">
        <a-tag color="blue" class="tag" v-for="item in (text || [])" :key="item">
          {{ item }}
        </a-tag>
      </template>
    </a-table>
  </div>
</template>
<script>
import { wxcomUser, cusQrcode, com, promoteAccount ,channel } from "@/api/api";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import { mapActions } from "vuex";
import columns from "./columns";
export default {
  name: "aMicroPowder",
  mixins: [JeecgListMixin],
  data() {
    return {
      wxcomUser,
      selcet: com.select,
      channelSelect: channel.select,
      wxcomUserSelcet: wxcomUser.select,
      cusQrcoedeselcet: cusQrcode.select,
      getPromotePerson: promoteAccount.getPromotePerson,
      getAddWaySelect: wxcomUser.getAddWaySelect,
      getAdPositionSelect: wxcomUser.getAdPositionSelect,
      queryParam: {},
      selfDefaultTime: {},
      columns: columns.aMicroPowder,
      header: [
        "ID",
        "客户昵称",
        "账户ID",
        "备注",
        "所属企微",
        "企微标签",
        "加粉时间",
        "推广渠道",
        "推广项目",
        "推广链路",
        "推广人员",
        "推广计划",
        "素材id",
        "广告版位",
        "推广账户",
        "unionid",
        "企微名称",
        "客户来源",
        "归属推广",
        "IP属地",
        "callback",
        "ip",
      ],
      url: {
        list: "/wxcom/cus-customer-user/index",
      },
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        showTotal: function (total, range) {
          let page = "20/页 共" + total + "条";
          return page;
        },
      },
    };
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    },
    //  部门
    dept_list: function () {
      return this.$store.getters.dept_list;
    },
    Project_tree: function () {
      return this.$store.state.organizationalStructure.Project_tree;
    },
  },
  created() {
    //  获取部门
    this.Department();
    this.projectSelectTree();
  },
  methods: {
    ...mapActions("organizationalStructure", ["projectSelectTree","Department"]),
    treeSelectChange(value, label, extra) {
      const selectedNode = extra.triggerNode._props.dataRef;
      this.queryParam.project_type = selectedNode.type;
      this.queryParam.project_id = selectedNode.id;
    },
    handleOk(e) {
      this.$set(this.queryParam, "com_id", e);
    },
    changeCode(e) {
      if (e) {
        let type = e.type || "";
        this.queryParam.type = type;
      } else {
        this.queryParam.type = "";
      }
    },
    materialList({mid3}){
      if(mid3.length > 0){
        this.$store.commit("SET_PERMISSIONLISTPASSROUTER", ["materialData"]);
        this.$router.push({
          name: "素材分析",
          params: {
            mid3
          }
        });
      }
    },
    // 处理图片加载错误
    handleImageError(event) {
      event.target.style.display = 'none';
      console.warn('视频缩略图加载失败');
    },
    //导出-数据格式
    dataFormat({ list }) {
      let arr = [];
      for (let i = 0; i < list.length; i++) {
        let b = list[i];
        // 添加空值检查，防止customer为null时导致的错误
        b.name = b.customer ? b.customer.name : '-'; // 客户名称
        b.unionid = b.customer ? b.customer.unionid : '-'; // uniond
        // b.corp_name = b.customer.corp_name; // corp_name
        try {
          b.tag_name_array = b.tag_name_array.join(","); // 企微标签
        } catch (err) {}
        const key = [
          "id",
          "name", // 客户昵称
          "sub_advertiser_id", // 账户ID
          "remark", // 备注
          "user_name", // 所属企微
          "tag_name_array", // 企微标签
          "add_time_text", // 添加时间
          "channel_name", // 推广渠道   1
          "project_name", // 推广项目 1
          "link_name", // 推广链路
          "responsible_name", // 推广人员
          "adid", // 推广计划
          "mid3", // 素材id
          "csite_text", // 广告版位
          "sub_advertiser_name", // 推广账户
          "unionid", // unionid
          "com_name", // 企微名称
          "add_way_text", // 客户来源
          "qrcode_created_person_name", // 归属推广
          "location", // IP属地
          "callback", // callback
          "ip", // ip
        ];
        for (let i = 0; i < key.length; i++) {
          let exportKey = key[i];
          b[exportKey] = b[exportKey]
            ? b[exportKey]
            : b[exportKey] === 0
            ? b[exportKey]
            : "-";
        }
        console.log(key.length);
        let nb = this.$pick(b, ...key);
        arr.push(nb);
      }
      return arr;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .tag {
  margin-bottom: 5px;
  transform: scale(0.9);
  transform-origin: left;
  &:hover {
    opacity: 1 !important;
  }
}

// 视频缩略图相关样式
.video-thumbnail-container {
  padding: 8px;
  max-width: 600px;
  max-height: 400px;
  
  .video-thumbnail {
    max-width: 100%;
    max-height: 360px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    object-fit: cover;
  }
}

// 通投广告位特殊样式
.ad-position-special {
  color: #ff4d4f !important;
  font-weight: 500;
}
</style>
