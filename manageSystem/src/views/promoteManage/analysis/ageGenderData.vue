<template>
  <a-card>
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="时间搜索">
          <self-time
            timeRange="month"
            v-model="selfDefaultTime.promote"
            :reset="timeReset"
            :InitializationTime="true"
            :disabledDate="disabledDate"
            :timeKey="{
              start: 'start_time',
              end: 'end_time',
            }"
          />
        </self-col>
        <self-col label="年龄">
          <a-select 
            placeholder="请选择年龄段" 
            v-model="queryParam.age" 
            allowClear
            style="width: 100%"
          >
            <a-select-option 
              v-for="item in ageOptions" 
              :key="item.id" 
              :value="item.name"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="账户ID">
          <a-input placeholder="请输入账户ID" v-model="queryParam.sub_advertiser_id" allowClear></a-input>
        </self-col>
        <self-col label="推广项目">
          <a-tree-select
            show-search
            style="selectDep"
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="Project_tree"
            v-model="queryParam.currentKey"
            @change="treeSelectChange"
            :replaceFields="{
              children: 'child',
              title: 'name',
              value: 'currentKey',
            }"
            placeholder="请选择项目"
          ></a-tree-select>
        </self-col>
        <self-col label="推广渠道">
          <a-tree-select
            show-search
            allow-clear
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="channel_tree"
            v-model="queryParam.channelCurrentKey"
            @change="channelTreeSelectChange"
            :replaceFields="{
              children: 'child',
              title: 'name',
              value: 'currentKey',
            }"
            placeholder="请选择渠道"
          ></a-tree-select>
        </self-col>
        <self-col label="负责人">
          <a-select
            show-search
            style="width: 100%;"
            @search="(e) => handleSearch(e, 'personCharge', 'promotePerson')"
            placeholder="请选择负责人"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-model="queryParam.responsible_id"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in personCharge"
            >{{ item.username }}</a-select-option>
          </a-select>
        </self-col>
      </template>
      <template slot="export">
        <export-to-csv
          :query="{
            ...queryParam,
            isorter,
            field: getQueryField(),
            column: isorter.column,
            order: isorter.order,
          }"
          fileName="年龄性别分析"
          btnName="导出"
          :dataFormat="dataFormat"
          :limit="1000"
          :queryParam="{
            ...queryParam,
            ...isorter,
            column: isorter.column,
            field: getQueryField(),
            order: isorter.order,
          }"
          :CommentApi="DataAnalysis.ageGenderDataExport"
          :header="header"
          :csvKey="csvKey"
          v-has="'promote-age-gender-analysis:export'"
        ></export-to-csv>
      </template>
    </self-page-header>
    <a-table
      class="totalTable"
      :scroll="{ x: 1200 }"
      :pagination="false"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :loading="loading"
      @change="onChange"
      :row-key="(l, index) => index"
    >
      <div slot="video_img" slot-scope="video_img, record, index">
        <img :src="video_img" alt class="listimage" :preview="index" />
      </div>
      <div slot="sub_advertiser_id" slot-scope="text, record,index">
        <div v-if="index == 0">-</div>
        <div v-if="index != 0">{{ record.sub_advertiser_id }}</div>
        <div v-if="index != 0">{{ record.sub_advertiser_name }}</div>
      </div>
      <div slot="ctr" slot-scope="text">{{text}}%</div>
      <div slot="conversion_rate" slot-scope="text">{{text}}%</div>
      <div slot="deep_convert_rate" slot-scope="text">{{text}}%</div>
      <template v-for="item in slotAnalyServiceColumns">
        <div v-if="item.hasOwnProperty('slots')" :slot="item.slots.title" :key="item.slots.title">
          <span>{{ item.slots.slotName }}</span>
          <a-tooltip placement="top">
            <template slot="title">
              <span>{{ item.slots.slotText }}</span>
            </template>
            <a class="slotColor">
              <a-icon type="question-circle" class="question-circle_poust ml5" theme="filled" />
            </a>
          </a-tooltip>
        </div>
      </template>
    </a-table>
    <self-pagination :ipagination="ipagination" @change="handleTableChange" />
  </a-card>
</template>
<script>
import { analyServiceColumns, exportKey, header } from "./ageGenderColumns";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import commentMixin from "@/mixins/commentMixin";
import { DataAnalysis, cardsSales } from "@/api/api";
import moment from "moment";
import { mapActions } from "vuex";
export default {
  name: "ageGenderData",
  mixins: [JeecgListMixin, commentMixin],
  data() {
    return {
      DataAnalysis,
      header: header(),
      csvKey: exportKey(),
      selfDefaultTime: {},
      // 年龄段选项数据 - 从API接口获取
      ageOptions: [],
      slotAnalyServiceColumns: JSON.parse(
        JSON.stringify(
          analyServiceColumns.filter(l => l.dataIndex != "dept_name")
        )
      ),
      columns: JSON.parse(
        JSON.stringify(
          analyServiceColumns.filter(l => l.dataIndex != "dept_name")
        )
      ),
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 10,
        pageSizeOptions: ["20", "50", "100", "200"],
        showSizeChanger: true,
        showTotal: function(total, range) {
          let page = 20 + "/页 共" + total + "条";
          return page;
        }
      },
      defaultTime: [],
      url: {
        list: "/promote/analysis/age-gender-analysis"
      }
    };
  },
  created() {
    this.projectSelectTree();
    this.channelSelectTree();
    this.getAgeBracketOptions();
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      d = this.$utils.TablefieldCompletion({ list: d });
      return d;
    },
    Project_tree: function() {
      return this.$store.state.organizationalStructure.Project_tree;
    },
    channel_tree: function() {
      return this.$store.state.organizationalStructure.channel_tree;
    }
  },
  methods: {
    ...mapActions("organizationalStructure", ["projectSelectTree", "channelSelectTree"]),
    /**
     * 获取年龄段选项数据
     */
    async getAgeBracketOptions() {
      try {
        const res = await cardsSales.getAgeBracket();
        if (res.code === 200 && res.data) {
          this.ageOptions = res.data;
        }
      } catch (error) {
        console.error('获取年龄段选项失败:', error);
      }
    },
    treeSelectChange(value, label, extra) {
      const selectedNode = extra.triggerNode._props.dataRef;
      this.queryParam.project_type = selectedNode.type;
      this.queryParam.project_id = selectedNode.id;
    },
    channelTreeSelectChange(value, label, extra) {
      const selectedNode = extra.triggerNode._props.dataRef;
      this.queryParam.channel_id = selectedNode.ids;
    },
    onChange(pagination, filters, sorter) {
      // 排序
      if (sorter.order) {
        this.queryParam.order_field = sorter.field;
        this.queryParam.order_type =
          sorter.order === "descend" ? "desc" : "asc";
      } else {
        this.queryParam.order_field = "";
        this.queryParam.order_type = "";
      }
      this.ipagination.current = 1;
      //  end
      this.handleTableChange();
    },
    disabledDate(current) {
      return current && current >= moment().startOf("day");
    },
    //导出-数据格式
    dataFormat({ list }) {
      list = list.map(item => {
        item.ctr = item.ctr + "%";
        item.conversion_rate = item.conversion_rate + "%";
        item.deep_convert_rate = item.deep_convert_rate + "%";
        return item;
      });
      return list;
    }
  }
};
</script>
  <style scoped lang="less">
.slotColor {
  color: #d4d6d9;
}
.selectDep {
  max-width: 280px;
}
.employmentStatus {
  border-color: rgba(0, 0, 0, 0);
  transform: scale(0.8);
  position: relative;
  top: -1px;
}
/deep/
  .ant-table-thead
  > tr
  > th
  .ant-table-column-sorter
  .ant-table-column-sortr-inner {
  margin-left: 0.3em !important;
}
/deep/ .ant-table-column-title {
  > div {
    display: flex;
    align-items: center;
    text-align: center;
  }
}
</style>        
<style lang="less" scoped>
/deep/ .ant-table-row-level-1 {
  background: #fcfdfd;
}
.listimage {
  width: 40px;
  cursor: pointer;
}
</style>
    