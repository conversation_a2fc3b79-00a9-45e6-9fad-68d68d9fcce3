<template>
  <a-card>
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="素材ID">
          <a-input
            @click.native="materialHandleClick"
            readOnly
            :placeholder="
              queryParam.material_id.length === 0
                ? `输入素材ID`
                : `${queryParam.material_id.length}条`
            "
          ></a-input>
          <a-modal
            title="素材ID"
            :visible="materialVisible"
            @ok="materialHandleOk"
            @cancel="materialHandleCancel"
          >
            <a-form :form="composer_form" style="width: 100%">
              <a-form-item>
                <div>
                  <span style="color: 20px; font-size: 20px; font-weight: bold">
                    素材ID
                  </span>
                  <span class="labelTips">已输入{{ materialIdList.length }}条</span>
                </div>
                <a-textarea
                  type="text"
                  :rows="6"
                  ref="textarea"
                  placeholder="请输入素材ID,多个素材ID换行分隔"
                  @change="materialCutting"
                />
              </a-form-item>
            </a-form>
            <template slot="footer">
              <a-button @click="materialHandleOk">确认</a-button>
            </template>
          </a-modal>
        </self-col>
        <self-col label="素材名称">
          <a-input placeholder="请输入素材名称" v-model="queryParam.material_name" allowClear></a-input>
        </self-col>

        <self-col label="账户ID">
          <a-input placeholder="请输入账户ID" v-model="queryParam.sub_advertiser_id" allowClear></a-input>
        </self-col>
        <self-col label="推广渠道">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'channelList','getchannel')"
            placeholder="请选择渠道"
            v-model="queryParam.promote_id"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in channelList"
            >{{ item.name }}</a-select-option>
          </a-select>
        </self-col>
        <self-col label="推广项目">
          <a-tree-select
            show-search
            style="selectDep"
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="Project_tree"
            v-model="queryParam.currentKey"
            @change="treeSelectChange"
            :replaceFields="{
              children: 'child',
              title: 'name',
              value: 'currentKey',
            }"
            placeholder="请选择项目"
          ></a-tree-select>
        </self-col>
        <self-col label="创作人">
          <commone-self-principal
            searchKey="username"
            :requestFun="getPromotePerson"
            placeholder="请选择创作人"
            value_key="username"
            v-model="queryParam.composer_id"
          />
        </self-col>
        <self-col label="翻新人">
          <commone-self-principal
            searchKey="username"
            :requestFun="getPromotePerson"
            placeholder="请选择翻新人"
            value_key="username"
            v-model="queryParam.refurbisher_id"
          />
        </self-col>
      </template>
      <template slot="bottom">
        <self-col-btn>
          <a-button v-has="'promote-material:change-composer'" @click="batchChangePerson()">批量修改创作人</a-button>
        </self-col-btn>
        <self-col-btn>
          <a-button v-has="'promote-material:change-refurbisher'" @click="batchChangeRefurbisher()">批量修改翻新人</a-button>
        </self-col-btn>
      </template>
      <template slot="export">
        <export-to-csv
          :query="{
            ...queryParam,
            isorter,
            field: getQueryField(),
            column: isorter.column,
            order: isorter.order,
          }"
          fileName="素材列表"
          btnName="导出"
          :dataFormat="dataFormat"
          :limit="1000"
          :queryParam="{
            ...queryParam,
            ...isorter,
            column: isorter.column,
            field: getQueryField(),
            order: isorter.order,
          }"
          :CommentApi="adsMaterial.export"
          :header="header"
          :csvKey="csvKey"
          v-has="'promote-material:export'"
        ></export-to-csv>
      </template>
    </self-page-header>
    <a-table
      class="totalTable"
      :scroll="{ x: 600 }"
      :pagination="false"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :loading="loading"
      :row-key="(l, index) => index"
      rowKey="id"
      :row-selection="{
        onChange: onChange,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        selectedRowKeys: selectedRowKeys,
      }"
    >
      <div slot="video_img" slot-scope="video_img, record, index">
        <img :src="video_img" alt class="listimage" :preview="index" v-imgdef />
      </div>
      <span slot="bundle_id" slot-scope="text, record, index">
        <span>{{
          text > 1 ? "是" : "否"
        }}</span>
      </span>
      <template slot="note" slot-scope="note">
        <j-ellipsis :value="note" :length="20" />
      </template>
      <span slot="action" slot-scope="text, record, index" v-divider>
        <a
          href="javascript:;"
          @click="changePersonInCharge(record)"
          v-has="'promote-material:change-composer'"
        >修改创作人</a>
        <a
          href="javascript:;"
          @click="changeRefurbisherCharge(record)"
          v-has="'promote-material:change-refurbisher'"
        >修改翻新人</a>
        <a
          href="javascript:;"
          @click="changeNote(record)"
          v-has="'promote-material:update-note'"
        >修改备注</a>
        <!-- <a
          v-has="'promote-material:bundleList'"
          href="javascript:;"
          @click="
            handleEdit(record, (title = '捆绑列表'), (config = { type: 2 }))
          "
        >
        捆绑列表
        </a>
        <a
          href="javascript:;"
          @click="unbundling(record)"
          v-if="record.bundle_id > 0"
          v-has="'promote-material:unbundling'"
        >取消绑定</a> -->
      </span>
    </a-table>
    <self-pagination :ipagination="ipagination" @change="handleTableChange" />
    <self-detail-modal
      :title="composerTitle"
      :show-confirm="false"
      placement="center"
      v-model="responsible"
      :confirmLoading="confirmLoading"
      :drawerWidth="400"
      padding="0 24px"
      :drawerHeight="'auto'"
      centered
      :confirmPromise="composer_handleOk"
      :close="true"
      :confirm="true"
    >
      <template slot="centent">
        <div style="padding: 10px 0">
          <a-form :form="composer_form">
            <a-form-item
              label="创作人"
              style="margin-bottom: 0"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <commone-self-principal
                searchKey="username"
                :defaultRendering="additional"
                :requestFun="getPromotePerson"
                placeholder="请选择创作人"
                value_key="username"
                :showAll="false"
                v-decorator="['composer_id', validatorRules.composer_id]"
              />
            </a-form-item>
          </a-form>
        </div>
      </template>
    </self-detail-modal>
    <self-detail-modal
      :title="refurbisherTitle"
      :show-confirm="false"
      placement="center"
      v-model="responsibleResponsible"
      :confirmLoading="confirmLoadingResponsible"
      :drawerWidth="400"
      padding="0 24px"
      :drawerHeight="'auto'"
      centered
      :confirmPromise="refurbisher_handleOk"
      :close="true"
      :confirm="true"
    >
      <template slot="centent">
        <div style="padding: 10px 0">
          <a-form :form="refurbisher_form">
            <a-form-item
              label="翻新人"
              style="margin-bottom: 0"
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
            >
              <commone-self-principal
                searchKey="username"
                :defaultRendering="additionalRefurbisher"
                :requestFun="getPromotePerson"
                placeholder="请选择翻新人"
                value_key="username"
                :showAll="false"
                v-decorator="['refurbisher_id', validatorRules.refurbisher_id]"
              />
            </a-form-item>
          </a-form>
        </div>
      </template>
    </self-detail-modal>
    <self-detail-modal
      :title="noteTitle"
      :show-confirm="false"
      placement="center"
      v-model="noteVisible"
      :confirmLoading="confirmLoadingNote"
      :drawerWidth="400"
      padding="0 24px"
      :drawerHeight="'auto'"
      centered
      :confirmPromise="note_handleOk"
      :close="true"
      :confirm="true"
    >
      <template slot="centent">
        <div style="padding: 10px 0;">
          <a-form :form="note_form">
            <a-form-item label="备注">
              <a-textarea
                placeholder="请输入备注"
                allow-clear
                :maxLength="200"
                v-decorator="['content',validatorRules.content]"
                :autosize="{
                  minRows: 10,
                  maxRows: 10,
                }"
              />
            </a-form-item>
          </a-form>
        </div>
      </template>
    </self-detail-modal>
    <MaterialBundleModal ref="modalForm" />
  </a-card>
</template>
<script>
import { analyServiceColumns, exportKey, header } from "./materialColumns";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import JEllipsis from '@/components/jeecg/JEllipsis'
import MaterialBundleModal from "@/views/promoteManage/analysis/MaterialBundleModal";
import commentMixin from "@/mixins/commentMixin";
import { adsMaterial, promoteAccount } from "@/api/api";
import pick from "lodash.pick";
import { mapActions } from "vuex";
export default {
  name: "materialList",
  mixins: [JeecgListMixin, commentMixin],
  components:{
    MaterialBundleModal,
    JEllipsis
  },
  data() {
    return {
      id: 0,
      adsMaterial,
      confirmLoading: false,
      confirmLoadingResponsible: false,
      confirmLoadingNote: false,
      projectList: [],
      header: header(),
      csvKey: exportKey(),
      responsible: false,
      responsibleResponsible: false,
      noteVisible: false,
      materialIdList:[],
      materialVisible:false,
      queryParam:{
        material_id:[]
      },
      getPromotePerson: promoteAccount.getPromotePerson,
      composer_form: this.$form.createForm(this),
      refurbisher_form: this.$form.createForm(this),
      note_form: this.$form.createForm(this),
      composerTitle: "",
      refurbisherTitle:"",
      noteTitle: "",
      additional: {},
      additionalRefurbisher: {},
      selectedRowKeys: [],
      selectedRows: [],
      selectIds: [],
      labelCol: {
        xs: {
          span: 22
        },
        sm: {
          span: 6
        }
      },
      wrapperCol: {
        xs: {
          span: 22
        },
        sm: {
          span: 15
        }
      },
      columns: JSON.parse(
        JSON.stringify(
          analyServiceColumns.filter(l => l.dataIndex != "dept_name")
        )
      ),
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 50,
        // pageSizeOptions: ["20", "50", "100", "200"],
        showSizeChanger: true,
        showTotal: function(total, range) {
          let page = 50 + "/页 共" + total + "条";
          return page;
        }
      },
      defaultTime: [],
      url: {
        list: "/promote/ads-material/index"
      }
    };
  },
  created() {
    this.projectSelectTree();
    this.getchannel();
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      this.projectList = this.dataSource.projectList || []; // 项目列表
      d = this.$utils.TablefieldCompletion({ list: d });
      return d;
    },
    Project_tree: function () {
      return this.$store.state.organizationalStructure.Project_tree;
    },
    validatorRules: function() {
      return {
        composer_id: {
          rules: [{ required: true, message: "请选择创作人!" }]
        },
        content: {
          rules: [
            { required: true, message: "请输入备注内容!" },
            { max: 200, message: "备注内容不能超过200个字符!" }
          ]
        }
      };
    }
  },
  methods: {
    ...mapActions("organizationalStructure", [
      "projectSelectTree"
    ]),
    treeSelectChange(value, label, extra) {
      const selectedNode = extra.triggerNode._props.dataRef;
      this.queryParam.project_type = selectedNode.type;
      this.queryParam.project_id = selectedNode.id;
    },
    onChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
    },
    onSelect(record, selected, selectedRows) {},
    onSelectAll(selected, selectedRows, changeRows) {},
    materialHandleClick(){
      this.materialIdList = JSON.parse(JSON.stringify(this.queryParam.material_id));
      this.$nextTick(() => {
        if (this.$refs.textarea) {
          this.$refs.textarea.stateValue = this.materialIdList.join("\r\n");
        }
      });
      this.materialVisible = true;
    },
    materialHandleOk(e) {
      this.queryParam.material_id = this.materialIdList;
      this.materialVisible = false;
    },
    materialHandleCancel() {
      this.$refs.textarea.stateValue = null;
      this.materialIdList = this.queryParam.material_id;
      this.$refs.textarea.stateValue = this.materialIdList.join("\r\n");
      this.materialVisible = false;
    },
    materialCutting({ target: { value } }) {
      this.materialIdList = JSON.parse(
        JSON.stringify(this.queryParam.material_id)
      );
      if (value) {
        this.materialIdList = value.split(/[\s\n]/);
      } else {
        this.materialIdList = [];
      }
      //限制不能超过200行
      if (this.materialIdList.length > 200) {
        this.materialIdList = this.materialIdList.slice(0, 200);
        this.$refs.textarea.stateValue = this.materialIdList.join("\r\n");
      }
      this.materialIdList = this.materialIdList.filter((i) => i);
    },
    changePersonInCharge(record) {
      this.responsible = true;
      this.composerTitle = "素材ID:" + record.material_id || "";
      this.additional = {
        id: record.composer_id + "",
        username: record.composer_name
      };
      this.selectIds = [record.id];

      let fieldsVal = pick(
        {
          composer_id: record.composer_id + ""
        },
        "composer_id"
      );

      this.$nextTick(() => {
        this.composer_form.setFieldsValue(fieldsVal);
      });
    },
    batchChangePerson() {
      if(this.selectedRowKeys.length === 0){
        this.$message.error("请选择要修改的数据");
        return;
      }
      this.responsible = true;
      this.additional = {};
      this.selectIds = [];
      this.composerTitle = "批量修改" + this.selectedRowKeys.length + "条数据";
      this.selectedRowKeys.forEach(element => {
        this.selectIds.push(element);
      });
    },
    composer_handleOk() {
      this.composer_form.validateFields(async (err, values) => {
        if (!err) {
          this.confirmLoading = true;
          if (values.composer_id == 0) {
            this.$message.error("请选择创作人");
            this.confirmLoading = false;
            return;
          }
          let parmas = {
            id: this.selectIds,
            composer_id: values.composer_id
          };
          adsMaterial.changeComposer(parmas).then(res => {
            this.confirmLoading = false;
            if (res.code == 200) {
              this.$message.success(res.message);
              this.responsible = false;
              this.loadData();
            } else {
              this.$message.error(res.message);
            }
          });
        }
      });
    },
    changeRefurbisherCharge(record) {
      this.responsibleResponsible = true;
      this.refurbisherTitle = "素材ID:" + record.material_id || "";
      this.additionalRefurbisher = {
        id: record.refurbisher_id + "",
        username: record.refurbisher_name
      };
      this.selectIds = [record.id];

      let fieldsVal = pick(
        {
          refurbisher_id: record.refurbisher_id + ""
        },
        "refurbisher_id"
      );

      this.$nextTick(() => {
        this.refurbisher_form.setFieldsValue(fieldsVal);
      });
    },
    batchChangeRefurbisher() {
      if(this.selectedRowKeys.length === 0){
        this.$message.error("请选择要修改的数据");
        return;
      }
      this.responsibleResponsible = true;
      this.additionalRefurbisher = {};
      this.selectIds = [];
      this.refurbisherTitle = "批量修改" + this.selectedRowKeys.length + "条数据";
      this.selectedRowKeys.forEach(element => {
        this.selectIds.push(element);
      });
    },
    refurbisher_handleOk() {
      this.refurbisher_form.validateFields(async (err, values) => {
        if (!err) {
          this.confirmLoadingResponsible = true;
          if (values.refurbisher_id == 0) {
            this.$message.error("请选择翻新人");
            this.confirmLoadingResponsible = false;
            return;
          }
          let parmas = {
            id: this.selectIds,
            refurbisher_id: values.refurbisher_id
          };
          adsMaterial.changeRefurbisher(parmas).then(res => {
            this.confirmLoadingResponsible = false;
            if (res.code == 200) {
              this.$message.success(res.message);
              this.responsibleResponsible = false;
              this.loadData();
            } else {
              this.$message.error(res.message);
            }
          });
        }
      });
    },
    unbundling(record){
      let that = this;
      this.$confirm({
        title: "提示",
        content: "是否取消捆绑？",
        onOk: () => {
          adsMaterial
            .unbundling({ id: [record.id]})
            .then((res) => {
              if (res.code == 200) {
                that.$message.success(res.message);
                that.loadData();
              } else {
                that.$message.warning(res.message);
              }
            });
        },
      });
    },
    //导出-数据格式
    dataFormat({ list }) {
      return list;
    },
    changeNote(record) {
      this.noteVisible = true;
      this.noteTitle = "素材ID:" + record.material_id || "";
      this.id = record.id;

      let fieldsVal = pick(
        {
          content: (record.note === '-' ? null : record.note) || ""
        },
        "content"
      );

      this.$nextTick(() => {
        this.note_form.setFieldsValue(fieldsVal);
      });
    },
    note_handleOk() {
      this.note_form.validateFields(async (err, values) => {
        if (!err) {
          this.confirmLoadingNote = true;
          let params = {
            id: this.id,
            note: values.content
          };
          
          try {
            const res = await adsMaterial.updateNote(params);
            if (res.code == 200) {
              this.$message.success(res.message);
              this.noteVisible = false;
              this.loadData();
            } else {
              this.$message.error(res.message);
            }
          } catch (error) {
            this.$message.error('修改备注失败');
          } finally {
            this.confirmLoadingNote = false;
          }
        }
      });
    }
  }
};
</script>
  <style scoped lang="less">
.slotColor {
  color: #d4d6d9;
}
.selectDep {
  max-width: 280px;
}
.employmentStatus {
  border-color: rgba(0, 0, 0, 0);
  transform: scale(0.8);
  position: relative;
  top: -1px;
}
/deep/
  .ant-table-thead
  > tr
  > th
  .ant-table-column-sorter
  .ant-table-column-sortr-inner {
  margin-left: 0.3em !important;
}
/deep/ .ant-table-column-title {
  > div {
    display: flex;
    align-items: center;
    text-align: center;
  }
}
</style>        
<style lang="less" scoped>
/deep/ .ant-table-row-level-1 {
  background: #fcfdfd;
}
.listimage {
  width: 40px;
  cursor: pointer;
}
/* 覆盖全局样式，恢复表格首行默认样式 */
.totalTable /deep/ .ant-table-tbody .ant-table-row:first-child {
  color: unset !important;
  background: unset !important;
}
</style>
    