export const analyServiceColumns = [
    {
        dataIndex: "main_body_name",
        title: "账户主体",
        key: "main_body_name",
        align: "left",
        fixed: 'left',
        width: 200,
    },
    {
        dataIndex: "material_id",
        title: "素材ID",
        key: "material_id",
        align: "left",
        fixed: 'left',
        width: 230,
        scopedSlots: { customRender: 'material_id' },
    },
    {
        dataIndex: "ad_platform_material_name",
        title: "素材名称",
        key: "ad_platform_material_name",
        align: "left",
        fixed: 'left',
        width: 230,
        hide: true, // 自定义属性用于隐藏列
        scopedSlots: { customRender: 'ad_platform_material_name' },
    },
    {
        dataIndex: "video_img",
        title: "视频图片",
        key: "video_img",
        align: "left",
        fixed: 'left',
        width: 100,
        scopedSlots: { customRender: "video_img" },
    },
    {
        dataIndex: "stat_cost",
        key: "stat_cost",
        align: "right",
        width: 140,
        sorter: true,
        slots: {
            title: "stat_cost",
            slotName: "账面消耗",
            slotText: "表示广告在投放期内的预估花费金额"
        }
    },
    // {
    //     dataIndex: "real_cost",
    //     title: "实际消耗",
    //     key: "real_cost",
    //     align: "right",
    //     sorter: true,
    //     width: 180
    // },
    {
        dataIndex: "add_fans",
        title: "加粉数",
        key: "add_fans",
        align: "right",
        sorter: true,
        width: 180
    },
    {
        dataIndex: "add_fans_cost",
        key: "add_fans_cost",
        align: "right",
        sorter: false,
        width: 160,
        slots: {
            title: "add_fans_cost",
            slotName: "账面加粉成本",
            slotText: "账面消耗/加粉数"
        }
    },
    // {
    //     dataIndex: "real_add_fans_cost",
    //     key: "real_add_fans_cost",
    //     align: "right",
    //     sorter: false,
    //     width: 160,
    //     slots: {
    //         title: "real_add_fans_cost",
    //         slotName: "实际加粉成本",
    //         slotText: "实际消耗/加粉数"
    //     }
    // },
    {
        dataIndex: "deposit_count",
        title: "订金数",
        key: "deposit_count",
        align: "right",
        sorter: true,
        width: 180
    },
    {
        dataIndex: "deposit_count_cost",
        key: "deposit_count_cost",
        align: "right",
        sorter: false,
        width: 160,
        slots: {
            title: "deposit_count_cost",
            slotName: "账面订金成本",
            slotText: "账面消耗/订金数"
        }
    },
    // {
    //     dataIndex: "real_deposit_count_cost",
    //     key: "real_deposit_count_cost",
    //     align: "right",
    //     sorter: false,
    //     width: 160,
    //     slots: {
    //         title: "real_deposit_count_cost",
    //         slotName: "实际订金成本",
    //         slotText: "实际消耗/订金数"
    //     }
    // },
    {
        dataIndex: "arrival_amount",
        title: "到店数",
        key: "arrival_amount",
        align: "right",
        sorter: false,
        width: 180
    },
    {
        dataIndex: "arrival_amount_cost",
        key: "arrival_amount_cost",
        align: "right",
        sorter: false,
        width: 160,
        slots: {
            title: "arrival_amount_cost",
            slotName: "账面到店成本",
            slotText: "账面消耗/到店数"
        }
    },
    // {
    //     dataIndex: "real_arrival_amount_cost",
    //     key: "real_arrival_amount_cost",
    //     align: "right",
    //     sorter: false,
    //     width: 160,
    //     slots: {
    //         title: "real_arrival_amount_cost",
    //         slotName: "实际到店成本",
    //         slotText: "实际消耗/到店数"
    //     }
    // },
    {
        dataIndex: "real_final",
        title: "实收业绩",
        key: "real_final",
        align: "right",
        sorter: false,
        width: 180
    },
    // {
    //     dataIndex: "roi",
    //     key: "roi",
    //     align: "right",
    //     sorter: false,
    //     width: 160,
    //     slots: {
    //         title: "roi",
    //         slotName: "投产",
    //         slotText: "实收业绩/实际消耗"
    //     }
    // },
    {
        dataIndex: "book_roi",
        key: "book_roi",
        align: "right",
        sorter: false,
        width: 160,
        slots: {
            title: "book_roi",
            slotName: "账面ROI",
            slotText: "实收业绩/账面消耗"
        }
    },
    {
        dataIndex: "deposit_rate",
        key: "deposit_rate",
        align: "right",
        sorter: false,
        width: 160,
        scopedSlots: { customRender: "deposit_rate" },
        slots: {
            title: "deposit_rate",
            slotName: "加粉订金转化率",
            slotText: "订金数/加粉数"
        }
    },
    {
        dataIndex: "fans_arrive_store_rate",
        key: "fans_arrive_store_rate",
        align: "right",
        sorter: false,
        width: 160,
        scopedSlots: { customRender: "fans_arrive_store_rate" },
        slots: {
            title: "fans_arrive_store_rate",
            slotName: "加粉到店转化率",
            slotText: "到店数/加粉数"
        }
    },
    {
        dataIndex: "attrition_count",
        title: "流失人数",
        key: "attrition_count",
        align: "right",
        sorter: false,
        width: 180
    },
    {
        dataIndex: "show_cnt",
        key: "show_cnt",
        align: "right",
        sorter: false,
        width: 160,
        slots: {
            title: "show_cnt",
            slotName: "展示数",
            slotText: "广告展示给用户的次数"
        }
    },
    {
        dataIndex: "cpm_platform",
        key: "cpm_platform",
        align: "right",
        width: 160,
        slots: {
            title: "cpm_platform",
            slotName: "平均千次展现费用(元）",
            slotText: "广告平均每一千次展现所付出的费用，计算公式是：总消耗/展示数*1000"
        }
    },
    {
        dataIndex: "click_cnt",
        key: "click_cnt",
        align: "right",
        width: 160,
        slots: {
            title: "click_cnt",
            slotName: "点击数",
            slotText: "当用户点击广告素材时，触发点击事件，该事件被认为是一次有效的广告点击"
        }
    },
    {
        dataIndex: "ctr",
        key: "ctr",
        align: "right",
        sorter: false,
        width: 130,
        scopedSlots: { customRender: "ctr" },
        slots: {
            title: "ctr",
            slotName: "点击率",
            slotText: "广告被点击的次数占展示次数的百分比。计算方法：点击数/展示数*100%"
        }
    },
    {
        dataIndex: "cpc_platform",
        key: "cpc_platform",
        align: "right",
        width: 160,
        slots: {
            title: "cpc_platform",
            slotName: "平均点击单价(元)",
            slotText: "广告主为每次点击付出的费用成本，计算公式是：总消耗/点击数"
        }
    },
    {
        dataIndex: "convert_cnt",
        key: "convert_cnt",
        align: "right",
        width: 160,
        slots: {
            title: "convert_cnt",
            slotName: "转化数",
            slotText: "在转化行为发生（或回传）之后，将转化行为回记到过去30天内的扣费（消耗产生）时间上。例如：广告在8月20日展示给用户，此时广告花费10元，用户点击广告后于8月23日产生1笔购买，则8月23日这笔购买将会展示在8月20日，8月23日没有转化数。"
        }
    },
    {
        dataIndex: "conversion_rate",
        key: "conversion_rate",
        align: "right",
        width: 130,
        scopedSlots: { customRender: "conversion_rate" },
        slots: {
            title: "conversion_rate",
            slotName: "转化率",
            slotText: "转化率(计费时间) = 转化数（计费时间） / 点击数"
        }
    },
    {
        dataIndex: "conversion_cost",
        key: "conversion_cost",
        align: "right",
        sorter: false,
        width: 160,
        slots: {
            title: "conversion_cost",
            slotName: "平均转化成本",
            slotText: "广告主为每个转化所付出的平均成本，计算方式：总消耗/转化数。"
        }
    },
    {
        dataIndex: "deep_convert_cnt",
        key: "deep_convert_cnt",
        align: "right",
        width: 160,
        slots: {
            title: "deep_convert_cnt",
            slotName: "深度转化数",
            slotText: "将深度转化数记录在转化事件发生的时间上。建议广告主考核深度转化成本时参考“深度转化数（计费时间）”例如您的广告在早上8点进行了展示和点击，用户晚上19点发生了激活行为，我们会把激活数披露在晚上19点"
        }
    },
    {
        dataIndex: "deep_convert_rate",
        key: "deep_convert_rate",
        align: "right",
        width: 160,
        scopedSlots: { customRender: "deep_convert_rate" },
        slots: {
            title: "deep_convert_rate",
            slotName: "深度转化率",
            slotText: "广告被用户进行深度转化的次数占转化次数的百分比。计算方式：深度转化数/转化数*100%"
        }
    },
    {
        dataIndex: "deep_convert_cost",
        key: "deep_convert_cost",
        align: "right",
        width: 160,
        slots: {
            title: "deep_convert_cost",
            slotName: "深度转化成本",
            slotText: "广告主为每个深度转化所付出的平均成本，计算方法：总消耗/深度转化数"
        }
    },
    {
        dataIndex: "label_platform",
        title: "平台标签",
        key: "label_platform",
        align: "right",
        width: 200,
        scopedSlots: { customRender: "label_platform" },
    },
    {
        dataIndex: "label_internal",
        title: "系统标签",
        key: "label_internal",
        align: "right",
        width: 200,
        scopedSlots: { customRender: "label_internal" },
    },
]
export const header = (key = '') => analyServiceColumns.reduce((l, j) => {
    l.push(j);
    return l
}, []).filter(item => item.title != key).map(item => item.title || item.slots.slotName)
export const exportKey = (...keys) => analyServiceColumns.reduce((l, j) => {
    l.push(j);
    return l
}, []).filter(item => {
    return !keys.includes(item.dataIndex)
}).map(item => item.dataIndex)