<template>
  <a-card :bordered="false">
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="推广日期">
          <a-range-picker
            allowClear
            @change="dateChange"
            :disabledDate="disabledDate"
            format="YYYY-MM-DD"
            v-model="defaultTime"
          />
        </self-col>
        <self-col label="开始时段">
          <a-select
            placeholder="请选择开始时段"
            v-model="queryParam.start_hour"
            allowClear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option
              :key="hour"
              :value="hour"
              v-for="hour in hourOptions"
            >
              {{ hour }}:00
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="结束时段">
          <a-select
            placeholder="请选择结束时段"
            v-model="queryParam.end_hour"
            allowClear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option
              :key="hour"
              :value="hour"
              v-for="hour in hourOptions"
            >
              {{ hour }}:00
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="账户名称">
          <a-input
            placeholder="请输入账户名称"
            v-model="queryParam.sub_advertiser_name"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="账户ID">
          <a-input
            placeholder="请输入账户ID"
            v-model="queryParam.sub_advertiser_id"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="账户主体">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'mainBodyList','promoteMainBody')"
            placeholder="请选择主体"
            v-model="queryParam.main_body_id"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in mainBodyList"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </self-col>

        <self-col label="推广渠道">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'channelList')"
            placeholder="请选择渠道"
            v-model="queryParam.promote_id"
            :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in channelList"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="负责人">
          <a-select
            show-search
            style="width: 100%"
            @search="(e) => handleSearch(e, 'personCharge', 'promotePerson')"
            placeholder="请选择负责人"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-model="queryParam.responsible_id"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in personCharge"
            >
              {{ item.username }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="定向">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="directionSelect"
            placeholder="请选择定向"
            value_key="name"
            v-model="queryParam.direction_id"
            :isRequest="true"
          />
        </self-col>
        <self-col label="所属部门">
          <a-tree-select
            show-search
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="dept_list"
            v-model="queryParam.dept_id"
            placeholder="请选择所属部门"
          ></a-tree-select>
        </self-col>
        <self-col label="推广项目">
          <a-tree-select
            show-search
            style="selectDep"
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="Project_tree"
            v-model="queryParam.currentKey"
            @change="treeSelectChange"
            :replaceFields="{
              children: 'child',
              title: 'name',
              value: 'currentKey',
            }"
            placeholder="请选择项目"
          ></a-tree-select>
        </self-col>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          class="mr10"
          :dataFormat="dataFormat"
          :query="{
            ...queryParam,
            isorter,
            field: getQueryField(),
            column: isorter.column,
            order: isorter.order,
          }"
          fileName="时段分析"
          btnName="导出"
          v-has="'analysis:hour-data-export'"
          :limit="1000"
          :queryParam="{
            ...queryParam,
            ...isorter,
            column: isorter.column,
            field: getQueryField(),
            order: isorter.order,
          }"
          :CommentApi="promoteManage.hourDataExport"
          :header="[
            '时段',
            '账面消耗',
            '消耗占比',
            '加粉数',
            '账面加粉成本',
            '订金数',
            '订金成本',
            '订金率',
            '新客到店数',
            '到店成本',
            '到店率',
            '门店实收',
            '客单价',
            '账面ROI',
            '点击率',
            '展示数',
            '点击数'
          ]"
          :csvKey="csvKey"
        ></export-to-csv>
      </template>
    </self-page-header>
    <a-table
      ref="table"
      bordered
      :rowKey="
        (record, index) => {
          return index;
        }
      "
      class="totalTable"
      :scroll="{ x: 1860 }"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :pagination="false"
      :loading="loading"
      @change="handleTableChange"
    >
      <div
        slot="cost"
        class="costbox"
        slot-scope="cost"
        v-PriceDisplay="cost"
      ></div>
      <span
        slot="book_add_fans_cost"
        slot-scope="book_add_fans_cost"
        v-PriceDisplay="book_add_fans_cost"
      ></span>
      <span
        slot="deposit_cost"
        slot-scope="deposit_cost"
        v-PriceDisplay="deposit_cost"
      ></span>
      <span
        slot="store_cost"
        slot-scope="store_cost"
        v-PriceDisplay="store_cost"
      ></span>
      <span
        slot="amount"
        slot-scope="amount"
        v-PriceDisplay="amount"
      ></span>
      <span
        slot="customer_price"
        slot-scope="customer_price"
        v-PriceDisplay="customer_price"
      ></span>
    </a-table>
    <self-pagination :ipagination="ipagination" @change="handleTableChange" />
  </a-card>
</template>
<script>
import { promoteManage, direction } from "@/api/api";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import JEllipsis from "@/components/jeecg/JEllipsis";
import JInput from "@/components/jeecg/JInput";
import moment from "moment";
import { mapActions } from "vuex";
import commentMixin from "@/mixins/commentMixin";

export default {
  name: "hourData",
  mixins: [JeecgListMixin, commentMixin],
  components: {
    JInput,
    JEllipsis,
  },
  data() {
    return {
      csvKey: [
        "hour_display",
        "cost",
        "cost_ratio",
        "add_fans_count",
        "book_add_fans_cost",
        "deposit_count",
        "deposit_cost",
        "deposit_rate",
        "new_store_cus_count",
        "store_cost",
        "fans_arrive_store_rate",
        "amount",
        "customer_price",
        "book_roi",
        "click_through_rate",
        "show",
        "click"
      ],
      // 小时选项：0-23小时
      hourOptions: Array.from({ length: 24 }, (_, i) => i),

      directionSelect: direction.select,
      channelList: [],
      promoteManage,
      timeShow: false,
      queryParam: {
        end_time: [],
        time: [],
        platform: "",
        direction_id: "",
        promote_id: "",
        responsible_id: "",
        start_hour: "",
        end_hour: "",
      },
      dept: [],
      platformList: [],
      loading: true,
      timeDuring: {
        start: "",
        end: "",
      },
      defaultTime: [],
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 24,
        showSizeChanger: false,
        showTotal: function (total, range) {
          let page = "24/页 共" + total + "条";
          return page;
        },
      },
      isorter: {
      },
      columns: [
        {
          title: "时段",
          align: "left",
          width: 120,
          fixed: "left",
          dataIndex: "hour_display",
        },
        {
          title: "账面消耗",
          align: "right",
          sorter: (a, b) => a.cost - b.cost,
          width: 120,
          dataIndex: "cost",
          scopedSlots: { customRender: "cost" },
        },
        {
          title: "消耗占比",
          align: "right",
          dataIndex: "cost_ratio",
          width: 100,
        },
        {
          title: "加粉数",
          align: "right",
          dataIndex: "add_fans_count",
          width: 100,
        },
        {
          title: "账面加粉成本",
          align: "right",
          dataIndex: "book_add_fans_cost",
          width: 120,
          scopedSlots: { customRender: "book_add_fans_cost" },
        },
        {
          title: "订金数",
          align: "right",
          dataIndex: "deposit_count",
          width: 100,
        },
        {
          title: "订金成本",
          align: "right",
          dataIndex: "deposit_cost",
          width: 120,
          scopedSlots: { customRender: "deposit_cost" },
        },
        {
          title: "订金率",
          align: "right",
          dataIndex: "deposit_rate",
          width: 100,
        },
        {
          title: "新客到店数",
          align: "right",
          dataIndex: "new_store_cus_count",
          width: 120,
        },
        {
          title: "到店成本",
          align: "right",
          dataIndex: "store_cost",
          width: 120,
          scopedSlots: { customRender: "store_cost" },
        },
        {
          title: "到店率",
          align: "right",
          dataIndex: "fans_arrive_store_rate",
          width: 100,
        },
        {
          title: "门店实收",
          align: "right",
          dataIndex: "amount",
          width: 120,
          scopedSlots: { customRender: "amount" },
        },
        {
          title: "客单价",
          align: "right",
          dataIndex: "customer_price",
          width: 120,
          scopedSlots: { customRender: "customer_price" },
        },
        {
          title: "账面ROI",
          align: "right",
          dataIndex: "book_roi",
          width: 100,
        },
        {
          title: "点击率",
          align: "right",
          dataIndex: "click_through_rate",
          width: 100,
        },
        {
          title: "展示数",
          align: "right",
          dataIndex: "show",
          width: 100,
        },
        {
          title: "点击数",
          align: "right",
          dataIndex: "click",
          width: 100,
        },
      ],
      disableMixinCreated: true,
      url: {
        list: "/promote/analysis/hour-data",
      },
    };
  },
  async created() {
    this.Department();
    await this.getTimeDuring();
    this.loadData();
    this.promotePerson();
    this.projectSelectTree();
    this.promoteMainBody();
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.channelList = this.dataSource.channelList;
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      this.dept = this.dataSource.deptList; // 部门列表
      this.platformList = this.dataSource.platformList;
      
      // 格式化数据，确保新字段的默认值
      d = d.map(item => {
        return {
          ...item,
          deposit_count: item.deposit_count || 0,
          deposit_cost: item.deposit_cost || 0,
          deposit_rate: item.deposit_rate || '0%',
          new_store_cus_count: item.new_store_cus_count || 0,
          store_cost: item.store_cost || 0,
          fans_arrive_store_rate: item.fans_arrive_store_rate || '0%',
          amount: item.amount || 0,
          customer_price: item.customer_price || 0,
          book_roi: item.book_roi || 0
        };
      });
      
      return d;
    },
    proDep_list: function () {
      return this.$store.state.organizationalStructure.promoteDep;
    },
    dept_list: function () {
      return this.$store.state.organizationalStructure.department;
    },
    expParams() {
      return Object.assign({ is_export: 1 }, this.queryParam);
    },

    Project_tree: function () {
      return this.$store.state.organizationalStructure.Project_tree;
    },
  },
  methods: {
    moment,
    ...mapActions("organizationalStructure", ["Department","projectSelectTree",]),
    treeSelectChange(value, label, extra) {
      const selectedNode = extra.triggerNode._props.dataRef;
      this.queryParam.project_type = selectedNode.type;
      this.queryParam.project_id = selectedNode.id;
    },

    handleOk() {},
    //获取时段数据初始起止时间
    getTimeDuring() {
      let that = this;
      return new Promise((resolve) => {
        promoteManage.hourTime().then(res => {
          if (res.code == 200) {
            that.timeDuring.start = res.data.start_time;
            that.timeDuring.end = res.data.end_time;
            that.queryParam.start_time = res.data.end_time;
            that.queryParam.end_time = res.data.end_time;

            this.$nextTick(() => {
              if (that.timeDuring.end) {
                this.defaultTime = [
                  moment(that.timeDuring.end).format("YYYY-MM-DD"),
                  moment(that.timeDuring.end).format("YYYY-MM-DD")
                ].map(date => moment(date));
              }
            });
            
            that.timeShow = true;
          } else {
            that.$message.warning(res.message);
          }
          resolve();
        });
      });
    },
    //导出-时段分析数据格式
    dataFormat({ list }) {
      try {
        return list;
      } catch (err) {
        console.log("err");
      }
    },

    dateChange(date, dateString) {
      if (date && date.length === 2) {
        this.defaultTime = date.map(d => moment(d));
        this.queryParam.start_time = dateString[0] || "";
        this.queryParam.end_time = dateString[1] || "";
      } else {
        this.defaultTime = [];
        this.queryParam.start_time = "";
        this.queryParam.end_time = "";
      }
    },
    searchReset() {
      this.ipagination.current = 1;
      this.defaultTime = [];
      this.defaultTime[0] = this.timeDuring.end;
      this.defaultTime[1] = this.timeDuring.end;
      this.queryParam = {
        time: [],
        platform: "",
        direction_id: "",
        promote_id: "",
        responsible_id: "",
        start_hour: "",
        end_hour: "",
        start_time: this.timeDuring.end,
        end_time: this.timeDuring.end,
      };
      this.loadData();
    },
    //时间格式
    dateParse(date) {
      let d = moment(new Date(parseInt(date))).format("YYYY-MM-DD HH:mm:ss");
      return d;
    },
    //设置可选日期
    disabledDate(current) {
      let that = this;
      
      const parseDate = (dateStr) => {
        if (!dateStr) return null;
        return moment(dateStr, "YYYY-MM-DD");
      };
      
      const startDate = parseDate(that.timeDuring.start);
      const endDate = parseDate(that.timeDuring.end);
      if (!startDate || !endDate) return false;
      return current && (current < startDate || current > endDate);
    },
    handlePreview(r) {
      let { href } = this.$router.resolve({
        path: "/h5/preview",
        query: { id: r.id },
      });
      window.open(href, "_blank");
    },
    handleCancel() {
      this.modal.visible = false;
      this.modal.record = undefined;
      this.modal.cost_discount = 0;
    },
    getCurrentData() {
      var timeRange = "";
      var time = "";
      timeRange = new Date(new Date() - 1 * 60 * 60 * 1000);
      this.count = 1;
      timeRange = this.formateDate(timeRange);
      time = timeRange.split(":")[0] + ":00:00";
      return time;
    },
  },
};
</script>

<style lang="less" scoped>
.costbox {
  white-space: nowrap;
}



/deep/ .ant-table-thead > tr > th {
  text-align: center !important;
}

/deep/ .ant-table-column-has-actions {
  text-align: center !important;
}
</style>