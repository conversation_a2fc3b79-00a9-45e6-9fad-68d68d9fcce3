<template>
  <a-card>
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
       <!-- 顶部按钮 -->
       <template slot="top">
        <self-col-btn>
          <a-button v-has="'promote-material:index'" @click="operation('素材管理')" type="primary"
            >素材管理</a-button
          >
          <system-label v-has="'promote-cus-label:index'" />
        </self-col-btn>
      </template>
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="时间搜索">
          <self-time
            timeRange="month"
            v-model="selfDefaultTime.promote"
            :reset="timeReset"
            :InitializationTime="true"
            :disabledDate="disabledDate"
            :timeKey="{
              start: 'start_time',
              end: 'end_time',
            }"
          />
        </self-col>
        <self-col label="素材创建时间">
          <self-time
            timeRange="month"
            v-model="selfDefaultTime.createTime"
            :reset="timeReset"
            :InitializationTime="false"
            :disabledDate="(e) => false"
            :timeKey="{
              start: 'create_start_time',
              end: 'create_end_time',
            }"
          />
        </self-col>
        <self-col label="素材ID">
          <a-input
            @click.native="materialHandleClick"
            readOnly
            :placeholder="
              queryParam.material_id.length === 0
                ? `输入素材ID`
                : `${queryParam.material_id.length}条`
            "
          ></a-input>
          <a-modal
            title="素材ID"
            :visible="materialVisible"
            @ok="materialHandleOk"
            @cancel="materialHandleCancel"
          >
            <a-form :form="material_form" style="width: 100%">
              <a-form-item>
                <div>
                  <span style="color: 20px; font-size: 20px; font-weight: bold">
                    素材ID
                  </span>
                  <span class="labelTips">已输入{{ materialIdList.length }}条</span>
                </div>
                <a-textarea
                  type="text"
                  :rows="6"
                  ref="textarea"
                  placeholder="请输入素材ID,多个素材ID换行分隔"
                  @change="materialCutting"
                />
              </a-form-item>
            </a-form>
            <template slot="footer">
              <a-button @click="materialHandleOk">确认</a-button>
            </template>
          </a-modal>
        </self-col>
        <self-col label="素材名称">
          <a-input placeholder="请输入素材名称" v-model="queryParam.material_name" allowClear></a-input>
        </self-col>
        <self-col label="标签创建时间">
          <self-time
            timeRange="month"
            v-model="selfDefaultTime.labelCreateTime"
            :reset="timeReset"
            :InitializationTime="false"
            :disabledDate="(e) => false"
            :timeKey="{
              start: 'label_create_start_time',
              end: 'label_create_end_time',
            }"
          />
        </self-col>
        <self-col v-has="'promote-material-label:get-all'" label="素材标签">
          <self-material-label v-model="queryParam.tag_ids" />
        </self-col>
        <self-col label="账户主体">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'mainBodyList','promoteMainBody')"
            placeholder="请选择主体"
            v-model="queryParam.main_body_id"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in mainBodyList"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="账户ID">
          <a-input placeholder="请输入账户ID" v-model="queryParam.sub_advertiser_id" allowClear></a-input>
        </self-col>
        <self-col label="推广渠道">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'channelList')"
            placeholder="请选择渠道"
            v-model="queryParam.promote_id"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in channelList"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="推广项目">
          <a-tree-select
            show-search
            style="selectDep"
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="Project_tree"
            v-model="queryParam.currentKey"
            @change="treeSelectChange"
            :replaceFields="{
              children: 'child',
              title: 'name',
              value: 'currentKey',
            }"
            placeholder="请选择项目"
          ></a-tree-select>
        </self-col>
        <self-col label="负责人">
          <a-select
            show-search
            style="width: 100%"
            @search="(e) => handleSearch(e, 'personCharge', 'promotePerson')"
            placeholder="请选择负责人"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-model="queryParam.responsible_id"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in personCharge"
            >
              {{ item.username }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="创作人">
          <a-select
            show-search
            style="width: 100%"
            @search="(e) => handleSearch(e, 'personCharge', 'promotePerson')"
            placeholder="请选择创作人"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-model="queryParam.composer_id"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in personCharge"
            >
              {{ item.username }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="翻新人">
          <a-select
            show-search
            style="width: 100%"
            @search="(e) => handleSearch(e, 'personCharge', 'promotePerson')"
            placeholder="请选择翻新人"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-model="queryParam.refurbisher_id"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in personCharge"
            >
              {{ item.username }}
            </a-select-option>
          </a-select>
        </self-col>
      </template>
      <template slot="export">
        <export-to-csv
          :query="{
            ...queryParam,
            isorter,
            field: getQueryField(),
            column: isorter.column,
            order: isorter.order,
          }"
          fileName="素材分析"
          btnName="导出"
          :dataFormat="dataFormat"
          :limit="1000"
          :queryParam="{
            ...Object.fromEntries(
              Object.entries({
                ...queryParam,
                ...isorter,
                column: isorter.column,
                field: getQueryField(),
                order: isorter.order,
              }).filter(([key, value]) => {
                // 只过滤掉标签创建时间字段
                if ((key === 'label_create_start_time' || key === 'label_create_end_time') && !value) {
                  return false;
                }
                return true;
              })
            )
          }"
          :CommentApi="DataAnalysis.materialDataExport"
          :header="header"
          :csvKey="csvKey"
          v-has="'promote-material-analysis:export'"
        ></export-to-csv>
      </template>
    </self-page-header>
    <a-table
      class="totalTable"
      :scroll="{ x: 1200 }"
      :pagination="false"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :loading="loading"
      @change="onChange"
      :row-key="(l, index) => index"
    >
      <span slot="material_id" slot-scope="text, record,index">
        <div v-if="index == 0">{{ record.material_id }}</div>
        <div v-if="index != 0">{{ record.ad_platform_material_name }}</div>
        <div v-if="index != 0" class="Id-lightGray">ID：{{ record.material_id }}</div>
        <div class="tags" v-if="index != 0">
          <a-tag color="blue" v-if="record.main_body_name" class="tag">
            {{record.main_body_name}}
          </a-tag>
          <template v-if="record.label_platform && record.label_platform !== '-'">
            <a-tag 
              style="background-color: #f0e6f0; border-color: #d3b7d3; color: #800080;"
              class="tag" 
              v-for="(item, idx) in record.label_platform.split(',')" 
              :key="`platform-${item}-${idx}`"
            >
              {{item}}
            </a-tag>
          </template>
        </div>
      </span>
      <div slot="video_img" slot-scope="video_img, record, index">
        <template v-if="!record.video_img || record.video_img === '-'">
          -
        </template>
        <template v-else>
          <img :src="video_img" alt class="listimage" :preview="index" v-imgdef/>
        </template>
      </div>
      <div slot="label_internal" slot-scope="text, record, index">
        <template v-if="!record.label_internal || record.label_internal === '-'">
        </template>
        <template v-else>
          <a-tag 
            color="blue" 
            class="tag" 
            v-for="item in record.label_internal.split(',')" 
            :key="item"
          >
            {{item}}
          </a-tag>
        </template>
      </div>
      <div slot="ctr" slot-scope="text">{{text}}%</div>
      <div slot="conversion_rate" slot-scope="text">{{text}}%</div>
      <div slot="deep_convert_rate" slot-scope="text">{{text}}%</div>
      <div slot="deposit_rate" slot-scope="text">{{text}}%</div>
      <div slot="fans_arrive_store_rate" slot-scope="text">{{text}}%</div>
      <template v-for="item in slotAnalyServiceColumns">
        <div v-if="item.hasOwnProperty('slots')" :slot="item.slots.title" :key="item.slots.title">
          <span>{{ item.slots.slotName }}</span>
          <a-tooltip placement="top">
            <template slot="title">
              <span>{{ item.slots.slotText }}</span>
            </template>
            <a class="slotColor">
              <a-icon type="question-circle" class="question-circle_poust ml5" theme="filled" />
            </a>
          </a-tooltip>
        </div>
      </template>
      <span v-has="'promote-material-label:batch-update'" slot="action" slot-scope="text, record, index">
        <a @click="showAddLabelModal(record)" v-if="index != 0">添加系统标签</a>
      </span>
    </a-table>
    <self-pagination :ipagination="ipagination" @change="handleTableChange" />
    
    <!-- 添加系统标签弹窗 -->
    <a-modal v-model="labelModalVisible" title="添加标签" @ok="handleAddLabels">
      <div class="label-selection">
        <a-tag
          v-for="item in systemLabels"
          :key="item.id"
          class="tag-item"
          :color="selectedLabels.includes(item.name) ? '#9373ee' : ''"
          @click="toggleLabelSelection(item)"
        >
          {{ item.name }}
        </a-tag>
      </div>
      <template slot="footer">
        <a-button key="back" @click="closeLabelModal">
          取消
        </a-button>
        <a-button key="submit" type="primary" @click="handleAddLabels" :loading="submitLoading">
          确定
        </a-button>
      </template>
    </a-modal>
  </a-card>
</template>
<script>
import { analyServiceColumns, exportKey, header } from "./materialDataColumns";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import commentMixin from "@/mixins/commentMixin";
import { DataAnalysis, adsCusLabel, adsMaterialLabel } from "@/api/api";
import moment from "moment";
import { mapActions } from "vuex";
import SystemLabel from './SystemLabel.vue'

export default {
  name: "materialData",
  components: {
    SystemLabel
  },
  mixins: [JeecgListMixin, commentMixin],
  data() {
    let baseColumns = analyServiceColumns.filter(l => l.dataIndex != "ad_platform_material_name");

    let filteredColumns = baseColumns.filter(
      col => col.dataIndex !== 'main_body_name' && col.dataIndex !== 'label_platform'
    );

    const videoImgColumnIndex = filteredColumns.findIndex(col => col.dataIndex === 'video_img');
    const materialIdColumnIndex = filteredColumns.findIndex(col => col.dataIndex === 'material_id');

    let finalColumns = [...filteredColumns];

    if (videoImgColumnIndex !== -1 && materialIdColumnIndex !== -1 && videoImgColumnIndex > materialIdColumnIndex) {
      const videoImgCol = finalColumns.splice(videoImgColumnIndex, 1)[0];
      const newMaterialIdColumnIndex = finalColumns.findIndex(col => col.dataIndex === 'material_id');
      finalColumns.splice(newMaterialIdColumnIndex, 0, videoImgCol); 
    }
    
    return {
      DataAnalysis,
      channelList:[],
      projectList:[],
      header: header(),
      csvKey: exportKey(),
      selfDefaultTime: {},
      slotAnalyServiceColumns: JSON.parse(
        JSON.stringify(
          baseColumns.filter(col => col.dataIndex !== 'main_body_name' && col.dataIndex !== 'label_platform')
        )
      ),
      columns: JSON.parse(JSON.stringify(finalColumns)),
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 50,
        showSizeChanger: true,
        showTotal: function(total, range) {
          let page = 50 + "/页 共" + total + "条";
          return page;
        }
      },
      url: {
        list: "/promote/analysis/material-analysis"
      },
      queryParam: {
        material_id: []
      },
      materialIdList: [],
      materialVisible: false,
      material_form: this.$form.createForm(this),
      labelModalVisible: false,
      systemLabels: [],
      selectedLabels: [],
      currentRecord: null,
      submitLoading: false
    };
  },
  created() {
    this.projectSelectTree();
    this.promotePerson();
    this.promoteMainBody();
    // 添加操作列
    this.columns.push({
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      scopedSlots: { customRender: 'action' },
      width: 120
    });
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      this.channelList = this.dataSource.channelList;
      this.projectList = this.dataSource.projectList;
      d = this.$utils.TablefieldCompletion({ list: d });
      return d;
    },
    Project_tree: function () {
      return this.$store.state.organizationalStructure.Project_tree;
    },
  },
  async mounted() {
    await this.$asyncNextTick();
    if (this.$route.params.mid3) {
      this.queryParam.material_id = [this.$route.params.mid3];
      this.materialIdList = [this.$route.params.mid3];
    }
    this.$store.commit('DELETE_PERMISSIONLISTPASSROUTER', ['materialData']);
  },
  methods: {
    ...mapActions("organizationalStructure", [
      "projectSelectTree"
    ]),
    treeSelectChange(value, label, extra) {
      const selectedNode = extra.triggerNode._props.dataRef;
      this.queryParam.project_type = selectedNode.type;
      this.queryParam.project_id = selectedNode.id;
    },
    onChange(pagination, filters, sorter) {
      if (sorter.order) {
        this.queryParam.order_field = sorter.field;
        this.queryParam.order_type =
          sorter.order === "descend" ? "desc" : "asc";
      } else {
        this.queryParam.order_field = "";
        this.queryParam.order_type = "";
      }
      this.ipagination.current = 1;
      this.handleTableChange();
    },
    disabledDate(current) {
      return current && current >= moment().startOf("day");
    },
    dataFormat({ list }) {
      list = list.map(item => {
        item.ctr = item.ctr + "%";
        item.conversion_rate = item.conversion_rate + "%";
        item.deep_convert_rate = item.deep_convert_rate + "%";
        return item;
      });
      return list;
    },
    operation(type) {
      this.$router.push({ name: type });
    },
    showAddLabelModal(record) {
      this.currentRecord = record;
      this.selectedLabels = (record.label_internal && record.label_internal !== '-') 
        ? record.label_internal.split(',')
        : [];
      this.labelModalVisible = true;
      this.submitLoading = false;
      this.getSystemLabels();
    },
    getSystemLabels() {
      adsCusLabel.getAll({ status: 1, type: 2 }).then(res => {
        if (res.code === 200) {
          this.systemLabels = res.data.data || [];
        } else {
          this.$message.error(res.message || '获取标签失败');
        }
      }).catch(err => {
        console.error(err);
        this.$message.error('获取标签失败');
      });
    },
    toggleLabelSelection(item) {
      const index = this.selectedLabels.indexOf(item.name);
      if (index === -1) {
        this.selectedLabels.push(item.name);
      } else {
        this.selectedLabels.splice(index, 1);
      }
    },
    closeLabelModal() {
      this.labelModalVisible = false;
      this.selectedLabels = [];
      this.currentRecord = null;
    },
    handleAddLabels() {
      if (!this.currentRecord || !this.currentRecord.main_body_id || !this.currentRecord.am_id) {
        this.$message.error('当前记录信息不完整');
        return;
      }
      
      const params = {
        labels: this.selectedLabels,
        main_body_id: this.currentRecord.main_body_id,
        material_id: this.currentRecord.am_id
      };
      
      this.submitLoading = true;
      adsMaterialLabel.batchUpdate(params).then(res => {
        if (res.code === 200) {
          this.$message.success('添加标签成功');
          this.closeLabelModal();
          this.loadData();
        } else {
          this.$message.error(res.message || '添加标签失败');
        }
      }).catch(err => {
        console.error(err);
        this.$message.error('添加标签失败');
      }).finally(() => {
        this.submitLoading = false;
      });
    },
    materialHandleClick() {
      this.materialIdList = JSON.parse(JSON.stringify(this.queryParam.material_id));
      this.$nextTick(() => {
        if (this.$refs.textarea) {
          this.$refs.textarea.stateValue = this.materialIdList.join("\r\n");
        }
      });
      this.materialVisible = true;
    },
    materialHandleOk(e) {
      this.queryParam.material_id = this.materialIdList;
      this.materialVisible = false;
    },
    materialHandleCancel() {
      this.$refs.textarea.stateValue = null;
      this.materialIdList = this.queryParam.material_id;
      this.$refs.textarea.stateValue = this.materialIdList.join("\r\n");
      this.materialVisible = false;
    },
    materialCutting({ target: { value } }) {
      this.materialIdList = JSON.parse(
        JSON.stringify(this.queryParam.material_id)
      );
      if (value) {
        this.materialIdList = value.split(/[\s\n]/);
      } else {
        this.materialIdList = [];
      }
      //限制不能超过200行
      if (this.materialIdList.length > 200) {
        this.materialIdList = this.materialIdList.slice(0, 200);
        this.$refs.textarea.stateValue = this.materialIdList.join("\r\n");
      }
      this.materialIdList = this.materialIdList.filter((i) => i);
    }
  }
};
</script>
<style scoped lang="less">
.slotColor {
  color: #d4d6d9;
}
.selectDep {
  max-width: 280px;
}
.employmentStatus {
  border-color: rgba(0, 0, 0, 0);
  transform: scale(0.8);
  position: relative;
  top: -1px;
}
/deep/
  .ant-table-thead
  > tr
  > th
  .ant-table-column-sorter
  .ant-table-column-sortr-inner {
  margin-left: 0.3em !important;
}
/deep/ .ant-table-column-title {
  > div {
    display: flex;
    align-items: center;
    text-align: center;
  }
}

/* 标签选择弹窗样式 */
.label-selection {
  max-height: 300px;
  overflow-y: auto;
}
.tag-item {
  margin: 5px;
  cursor: pointer;
}
.labelTips {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
}
</style>        
<style lang="less" scoped>
/deep/ .ant-table-row-level-1 {
  background: #fcfdfd;
}
.listimage {
  width: 40px;
  cursor: pointer;
}
/deep/ .tag {
  margin-bottom: 5px;
  transform: scale(0.9);
  transform-origin: left;
  &:hover {
    opacity: 1 !important;
  }
}
</style>
    