<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2022-03-01 17:09:06
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-05-04 15:42:20
 * @FilePath: \manageSystem\src\views\promoteManage\analysis\cityData.vue
 * @Description: 城市分析
-->
<template>
  <a-card :bordered="false">
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="账户名称">
          <a-input
            placeholder="请输入账户名称"
            v-model="queryParam.sub_advertiser_name"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="账户ID">
          <a-input
            placeholder="请输入账户ID"
            v-model="queryParam.sub_advertiser_id"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="账户主体">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'mainBodyList','promoteMainBody')"
            placeholder="请选择主体"
            v-model="queryParam.main_body_id"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in mainBodyList"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="省份名称">
          <a-select
            ref="province"
            show-search
            v-model="queryParam.province_id"
            placeholder="搜索省份名称"
            :default-active-first-option="false"
            :show-arrow="true"
            :filter-option="provinceFilterOption"
            :not-found-content="null"
            :defaultValue="0"
            allowClear
          >
            <a-select-option value key>全部</a-select-option>
            <a-select-option
              v-for="d in this.provinces"
              :value="d.id"
              :key="d.id"
              >{{ d.title }}</a-select-option
            >
          </a-select>
        </self-col>
        <self-col label="城市名称">
          <self-cityTreeList :list="cityList" v-model="queryParam.city_id" />
        </self-col>
        <self-col label="推广渠道">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'channelList')"
            placeholder="请选择渠道"
            v-model="queryParam.promote_id"
            :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in channelList"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="负责人">
          <a-select
            show-search
            style="width: 100%"
            @search="(e) => handleSearch(e, 'personCharge', 'promotePerson')"
            placeholder="请选择负责人"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-model="queryParam.responsible_id"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in personCharge"
            >
              {{ item.username }}
            </a-select-option>
          </a-select>
        </self-col>
        <self-col label="定向">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="directionSelect"
            placeholder="请选择定向"
            value_key="name"
            v-model="queryParam.direction_id"
            :isRequest="true"
          />
        </self-col>
        <self-col label="所属部门">
          <a-tree-select
            show-search
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="dept_list"
            v-model="queryParam.dept_id"
            placeholder="请选择所属部门"
          ></a-tree-select>
        </self-col>
        <self-col label="推广日期">
          <a-range-picker
            allowClear
            @change="dateChange"
            :disabledDate="disabledDate"
            format="YYYY-MM-DD"
            v-model="defaultTime"
          />
        </self-col>
        <self-col label="推广项目">
          <a-tree-select
            show-search
            style="selectDep"
            treeNodeFilterProp="title"
            :dropdownStyle="{ maxHeight: '300px', overflow: 'auto' }"
            :treeData="Project_tree"
            v-model="queryParam.currentKey"
            @change="treeSelectChange"
            :replaceFields="{
              children: 'child',
              title: 'name',
              value: 'currentKey',
            }"
            placeholder="请选择项目"
          ></a-tree-select>
        </self-col>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          class="mr10"
          :dataFormat="dataFormat"
          :query="{
            ...queryParam,
            isorter,
            field: getQueryField(),
            column: isorter.column,
            order: isorter.order,
          }"
          fileName="城市分析"
          btnName="城市分析汇总导出"
          v-has="'analysis:city-data-export'"
          :limit="1000"
          :queryParam="{
            ...queryParam,
            ...isorter,
            column: isorter.column,
            field: getQueryField(),
            order: isorter.order,
          }"
          :CommentApi="promoteManage.cityDataExport"
          :header="[
            '城市名称',
            '账面消耗',
            '实际消耗',
            '消耗占比',
            '转化数',
            '账面转化成本',
            '实际转化成本',
            '订金数',
            '账面订金成本',
            '实际订金成本',
            '推广加粉订金转化率',
            '新客到店人数',
            '账面到店成本',
            '实际到店成本',
            '加粉转到店率',
            '门店实收',
            '客单价',
            '账面ROI',
            'ROI',
            '老师人数',
            '可接待客户数',
            '满载率',
            '改期数',
            '点击率',
            '转化率',
            '展示数',
            '点击数'
          ]"
          :csvKey="csvKey"
        ></export-to-csv
        >&nbsp;
        <export-to-csv
          ref="detail"
          class="mr10"
          v-has="'analysis:city-daily-data'"
          :query="{
            ...queryParam,
            isorter,
            column: isorter.column,
            field: getQueryField(),
            order: isorter.order,
          }"
          fileName="城市分析明细"
          btnName="城市分析明细导出"
          :limit="1000"
          :queryParam="{
            ...queryParam,
            column: isorter.column,
            field: getQueryField(),
            order: isorter.order,
          }"
          :CommentApi="promoteManage.cityDataExportDetail"
          :header="[
            '推广日期',
            '账户名称',
            '账户ID',
            '城市名称',
            '账面消耗',
            '转化数',
            '订金数',
            '推广加粉订金转化率',
            '新客到店人数',
            '推广平台',
            '负责人',
            '定向',
            '项目',
            '链路',
            '部门',
            '返点',
            '门店实收',
            '客单价',
            '账面ROI',
            'ROI',
            '点击率',
            '转化率',
            '展示数',
            '点击数'
          ]"
          :csvKey="detailcsvKey"
        ></export-to-csv>
      </template>
    </self-page-header>
    <a-alert class="mb15" type="info" show-icon>
      <p slot="message" class="mb0">
        当前可查询
        <span class="bold primary-color">
          &nbsp;{{ timeDuring.start }}&nbsp;
        </span>
        至
        <span class="bold primary-color">
          &nbsp;{{ timeDuring.end }}&nbsp;
        </span>
        的城市数据；每日14点可查询昨日城市数据。
      </p>
    </a-alert>
    <a-table
      ref="table"
      bordered
      :rowKey="
        (record, index) => {
          return index;
        }
      "
      class="totalTable"
      :scroll="{ x: 3000 }"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :pagination="false"
      :loading="loading"
      @change="handleTableChange"
    >
      <div
        slot="cost"
        class="costbox"
        slot-scope="cost"
        v-PriceDisplay="cost"
      ></div>
      <span
        slot="convertCost"
        slot-scope="convertCost"
        v-PriceDisplay="convertCost"
      ></span>
    </a-table>
    <self-pagination :ipagination="ipagination" @change="handleTableChange" />
  </a-card>
</template>
<script>
import { promoteManage, direction } from "@/api/api";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import JEllipsis from "@/components/jeecg/JEllipsis";
import JInput from "@/components/jeecg/JInput";
import moment from "moment";
import { mapActions } from "vuex";
import commentMixin from "@/mixins/commentMixin";

export default {
  name: "cityData",
  mixins: [JeecgListMixin, commentMixin],
  components: {
    JInput,
    JEllipsis,
  },
  data() {
    return {
      cityChangeTime: null,
      csvKey: [
        "city_name",
        "cost",
        "actual_consume",
        "cost_ratio",
        "convert",
        "book_conversion_cost",
        "conversion_cost",
        "deposit_count",
        "deposit_cost",
        "real_deposit_cost",
        "deposit_rate",
        "new_store_cus_count",
        "store_cost",
        "real_store_cost",
        "fans_arrive_store_rate",
        "amount",
        "customer_price",
        "book_roi",
        "amount_cost_rate",
        "teacher_num",
        "customer_service_num",
        "full_load_rate",
        "reschedule_num",
        "click_through_rate",
        "conversion_rate",
        "show",
        "click"
      ],
      detailcsvKey: [
        "date",
        "sub_advertiser_name",
        "sub_advertiser_id",
        "city_name",
        "cost",
        "convert",
        "deposit_count",
        "deposit_rate",
        "new_store_cus_count",
        "platform",
        "realname",
        "project_name",
        "direction_name",
        "link_name",
        "dept_name",
        "rebates",
        "amount",
        "customer_price",
        "book_roi",
        "amount_cost_rate",
        "ctr",
        "convert_rate",
        "show",
        "click"
      ],
      cityList_: [],
      directionSelect: direction.select,
      channelList: [],
      CitySummary: 0,
      SummaryShow: false,
      CityDetails: 0,
      DetailsShow: false,
      propvisible: false,
      promoteManage,
      timeShow: false,
      provinceList: [],
      cityList: [],
      queryParam: {
        end_time: [],
        time: [],
        platform: "",
        province_id: "",
        direction_id: "",
        promote_id: "",
        city_id: [],
        // dept_id: "",
        responsible_id: "",
      },
      dept: [],
      platformList: [],
      loading: true,
      timeDuring: {
        start: "",
        end: "",
      },
      dataDetailTotal: 0,
      defaultTime: [],
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        showTotal: function (total, range) {
          let page = "20/页 共" + total + "条";
          return page;
        },
      },
      isorter: {
        column: "cost",
        order: "desc",
      },
      columns: [
        {
          title: "城市",
          align: "left",
          width: 220,
          fixed: "left",
          dataIndex: "city_name", //
        },
        {
          title: "账面消耗",
          align: "right",
          defaultSortOrder: "descend",
          sorter: (a, b) => a.age - b.age,
          width: 160,
          dataIndex: "cost",
          scopedSlots: { customRender: "cost" }, //
        },
        {
          title: "实际消耗",
          align: "right",
          dataIndex: "actual_consume", //
          width: 160,
        },
        {
          title: "消耗占比",
          align: "right",
          dataIndex: "cost_ratio", //
        },
        {
          title: "转化数",
          align: "right",
          // width: 100,
          dataIndex: "convert", //
        },
        {
          title: "账面转化成本",
          align: "right",
          dataIndex: "book_conversion_cost",
          scopedSlots: { customRender: "book_conversion_cost" },
          // width: 100,
        },
        {
          title: "实际转化成本",
          align: "right",
          // width: 100,
          dataIndex: "conversion_cost",
          scopedSlots: { customRender: "conversion_cost" },
        },
        {
          title: "订金数",
          align: "right",
          // width: 100,
          dataIndex: "deposit_count",
          scopedSlots: { customRender: "deposit_count" },
        },
        {
          title: "账面订金成本",
          align: "right",
          dataIndex: "deposit_cost",
          scopedSlots: { customRender: "deposit_cost" },
        },
      
        {
          title: "实际订金成本",
          align: "right",
          dataIndex: "real_deposit_cost",
          scopedSlots: { customRender: "real_deposit_cost" },
        },
        {
          title: "推广加粉订金转化率",
          align: "right",
          dataIndex: "deposit_rate",
          scopedSlots: { customRender: "deposit_rate" },
        },
        {
          title: "新客到店人数",
          align: "right",
          dataIndex: "new_store_cus_count",
          scopedSlots: { customRender: "new_store_cus_count" },
        },
        {
          title: "账面到店成本",
          align: "right",
          dataIndex: "store_cost",
          scopedSlots: { customRender: "store_cost" },
        },
        {
          title: "实际到店成本",
          align: "right",
          dataIndex: "real_store_cost",
          scopedSlots: { customRender: "real_store_cost" },
        },
        {
          title: "加粉转到店率",
          align: "right",
          dataIndex: "fans_arrive_store_rate",
          scopedSlots: { customRender: "fans_arrive_store_rate" },
        },
        {
          title: "门店实收",
          align: "right",
          dataIndex: "amount",
          scopedSlots: { customRender: "amount" },
          width: 160,
        },
        {
          title: "客单价",
          align: "right",
          dataIndex: "customer_price",
          scopedSlots: { customRender: "customer_price" },
        },
        {
          title: "账面ROI",
          align: "right",
          dataIndex: "book_roi",
          scopedSlots: { customRender: "book_roi" },
        },
        {
          title: "ROI",
          align: "right",
          dataIndex: "amount_cost_rate",
          scopedSlots: { customRender: "amount_cost_rate" },
        },
        {
          title: "老师人数",
          align: "right",
          dataIndex: "teacher_num",
        },
        {
          title: "可接待客户数",
          align: "right",
          dataIndex: "customer_service_num",
        },
        {
          title: "满载率",
          align: "right",
          dataIndex: "full_load_rate",
        },
        {
          title: "改期数",
          align: "right",
          dataIndex: "reschedule_num",
        },
        {
          title: "点击率",
          align: "right",
          dataIndex: "click_through_rate", //
          // width: 100,
        },
        {
          title: "转化率",
          align: "right",
          // width: 100,
          dataIndex: "conversion_rate",
        },
        {
          title: "展示数",
          align: "right",
          dataIndex: "show",
          scopedSlots: { customRender: "show" }, //
          width: 160,
        },
        {
          title: "点击数",
          align: "right",
          dataIndex: "click",
          scopedSlots: { customRender: "click" }, //
          // width: 100,
        },

      ],
      disableMixinCreated: true,
      url: {
        list: "/promote/analysis/city-data",
      },
    };
  },
  async created() {
    this.Department();
    this.getCityList();
    await this.cityTime();
    this.loadData();
    this.promotePerson();
    this.projectSelectTree();
    this.promoteMainBody();
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.channelList = this.dataSource.channelList;
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      this.dept = this.dataSource.deptList; // 部门列表
      this.platformList = this.dataSource.platformList;
      return d;
    },
    proDep_list: function () {
      return this.$store.state.organizationalStructure.promoteDep;
    },
    dept_list: function () {
      return this.$store.state.organizationalStructure.department;
    },
    expParams() {
      return Object.assign({ is_export: 1 }, this.queryParam);
    },
    provinces() {
      let d = this.$store.getters.cityList;
      this._getCityList(d);
      return d;
    },
    Project_tree: function () {
      return this.$store.state.organizationalStructure.Project_tree;
    },
  },
  methods: {
    moment,
    ...mapActions("organizationalStructure", ["Department","projectSelectTree",]),
    ...mapActions("system", ["getCityList"]),
    treeSelectChange(value, label, extra) {
      const selectedNode = extra.triggerNode._props.dataRef;
      this.queryParam.project_type = selectedNode.type;
      this.queryParam.project_id = selectedNode.id;
    },
    _getCityList(d) {
      let data = [];
      if (d) data = d;
      else data = this.provinces;
      let city = [];
      if (this.queryParam.province_id) {
        city = data.filter((item) => item.id == this.queryParam.province_id);
      } else {
        city = data;
        // city = data.reduce((a, e) => [...a, ...e.child], []);
      }
      this.queryParam.city_id = [];
      this.cityList = city;
      this.cityList_ = city;
    },
    handleOk() {},
    //获取城市数据初始起止时间
    cityTime() {
      let that = this;
      return new Promise((resolve) => {
        promoteManage.cityTime().then(res => {
          if (res.code == 200) {
            that.timeDuring.start = res.data.start_time;
            that.timeDuring.end = res.data.end_time;
            that.queryParam.start_time = res.data.end_time;
            that.queryParam.end_time = res.data.end_time;

            this.$nextTick(() => {
              if (that.timeDuring.end) {
                this.defaultTime = [
                  moment(that.timeDuring.end).format("YYYY-MM-DD"),
                  moment(that.timeDuring.end).format("YYYY-MM-DD")
                ].map(date => moment(date));
              }
            });
            
            that.timeShow = true;
          } else {
            that.$message.warning(res.message);
          }
          resolve();
        });
      });
    },
    //导出-城市分析数据格式
    dataFormat({ list }) {
      try {
        return list;
      } catch (err) {
        console.log("err");
      }
    },
    //省份搜索
    provinceFilterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    cityFilterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    dateChange(date, dateString) {
      if (date && date.length === 2) {
        this.defaultTime = date.map(d => moment(d));
        this.queryParam.start_time = dateString[0] || "";
        this.queryParam.end_time = dateString[1] || "";
      } else {
        this.defaultTime = [];
        this.queryParam.start_time = "";
        this.queryParam.end_time = "";
      }
    },
    searchReset() {
      this.ipagination.current = 1;
      this.defaultTime = [];
      this.defaultTime[0] = this.timeDuring.end;
      this.defaultTime[1] = this.timeDuring.end;
      this.queryParam = {
        end_time: [],
        time: [],
        platform: "",
        province_id: "",
        direction_id: "",
        promote_id: "",
        city_id: [],
        // dept_id: "",
        responsible_id: "",
      };
      this.loadData();
    },
    //时间格式
    dateParse(date) {
      let d = moment(new Date(parseInt(date))).format("YYYY-MM-DD HH:mm:ss");
      return d;
    },
    //设置可选日期
    disabledDate(current) {
      let that = this;
      
      const parseDate = (dateStr) => {
        if (!dateStr) return null;
        return moment(dateStr, "YYYY-MM-DD");
      };
      
      const startDate = parseDate(that.timeDuring.start);
      const endDate = parseDate(that.timeDuring.end);
      if (!startDate || !endDate) return false;
      return current && (current < startDate || current > endDate);
    },
    handlePreview(r) {
      let { href } = this.$router.resolve({
        path: "/h5/preview",
        query: { id: r.id },
      });
      window.open(href, "_blank");
    },
    handleCancel() {
      this.modal.visible = false;
      this.modal.record = undefined;
      this.modal.cost_discount = 0;
    },
    getCurrentData() {
      var timeRange = "";
      var time = "";
      timeRange = new Date(new Date() - 1 * 60 * 60 * 1000);
      this.count = 1;
      timeRange = this.formateDate(timeRange);
      time = timeRange.split(":")[0] + ":00:00";
      return time;
    },
  },
};
</script>

<style lang="less" scoped>
/* .costbox {
  white-space: nowrap;
} */
.cityBox {
  height: 100%;
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
  .tagbox {
    width: 100%;
    flex: 1;
    height: 0;
    overflow: auto;
    padding: 10px 0;
    .cityTag {
      margin: 0 10px 5px 0;
      cursor: pointer;
    }
    h5 {
      font-weight: bolder;
    }
  }
}
.cityMultiple {
  /deep/ div {
    height: 30px;
    overflow-y: auto;
  }
}
</style>
