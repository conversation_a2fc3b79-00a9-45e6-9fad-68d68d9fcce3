/*
 * @Author: chending “<EMAIL>”
 * @Date: 2022-06-25 14:25:16
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-02-16 09:20:47
 * @FilePath: \manageSystem\src\views\serviceWorkbench\analyService\components\columns.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const analyServiceColumns = [
    {
        dataIndex: "index",
        title: "排名",
        key: "index",
        align: "left",
        fixed: 'left',
        width: 80,
        sorter: true,
    },
    {
        dataIndex: "dept_name",
        title: "所属部门",
        key: "dept_name",
        align: "left",
        fixed: 'left',
        width: 120,
        sorter: true,
    },
    {
        dataIndex: "user_name",
        title: "客服姓名",
        key: "user_name",
        align: "left",
        fixed: 'left',
        width: 160,
        sorter: true,
        scopedSlots: {
            customRender: 'user_name'
        }
    },
    {
        dataIndex: "add_fans_count",
        key: "add_fans_count",
        align: "right",
        sorter: true,
        width: 130,
        slots: {
            title: "add_fans_count",
            slotName: "加粉人数",
            slotText: "企微加粉+个微加粉，以加粉时间为准；从erp取数"
        }
    },
    {
        dataIndex: "new_plan_cus_count",
        key: "new_plan_cus_count",
        align: "right",
        sorter: true,
        width: 160,
        slots: {
            title: "plan_cus_count",
            slotName: "今日预约人数(新客/老客)",
            slotText: "预约时间在今日（订单来源为非门店，状态为已预约，到店等候，已开始，已完成）的新客/老客人数，按照预约客服汇总；"
        },
        expand: [
            {
                dataIndex: "new_plan_cus_count",
                slots: {
                    title: "new_plan_cus_count",
                    slotName: "今日新客预约人数",
                },
            },
            {
                dataIndex: "plan_cus_count",
                slots: {
                    title: "plan_cus_count",
                    slotName: "今日老客预约人数",
                },
            },
        ],
        scopedSlots: { customRender: "plan_cus_count_all" }
    },
    {
        dataIndex: "tomorrow_new_plan_cus_count",
        key: "tomorrow_new_plan_cus_count",
        align: "right",
        sorter: true,
        width: 160,
        slots: {
            title: "tomorrow_new_plan_cus_count",
            slotName: "明日预约人数(新客/老客)",
            slotText: "预约时间在明日（订单来源为非门店，状态为已预约，到店等候，已开始，已完成）的新客/老客人数，按照预约客服汇总；该字段在当日24时后，数值不会再发生变化；"
        },
        expand: [
            {
                dataIndex: "tomorrow_new_plan_cus_count",
                slots: {
                    title: "tomorrow_new_plan_cus_count",
                    slotName: "明日新客预约人数",
                },
            },
            {
                dataIndex: "tomorrow_plan_cus_count",
                slots: {
                    title: "tomorrow_plan_cus_count",
                    slotName: "明日老客预约人数",
                },
            },
        ],
        scopedSlots: { customRender: "tomorrow_plan_cus_count_all" }
    },
    // 
    {
        dataIndex: "deposit_count",
        key: "deposit_count",
        align: "right",
        sorter: true,
        width: 150,
        slots: {
            title: "deposit_count",
            slotName: "订金数",
            slotText: "核销时间在搜索时间段内，订金>0的订单数，统计状态不等于已取消/申请退订/已作废状态的订单，按照手机号去重以创建客服为准，按创建客服汇总；从erp取数"
        }
    },
    {
        dataIndex: "deposit_cus_count",
        key: "deposit_cus_count",
        align: "right",
        sorter: true,
        width: 150,
        slots: {
            title: "deposit_cus_count",
            slotName: "订金人数",
            slotText: "核销时间在搜索时间段内，订金>0的客户人数，统计状态不等于已取消/申请退订/已作废状态的订单，按照手机号去重以创建客服为准，按创建客服汇总；从erp取数"
        }
    },
    {
        dataIndex: "day_count",
        key: "day_count",
        align: "right",
        sorter: true,
        width: 150,
        slots: {
            title: "day_count",
            slotName: "订金人天数",
            slotText: "客服开单天数"
        }
    },
    {
        dataIndex: "deposit_sum",
        key: "deposit_sum",
        align: "right",
        sorter: true,
        width: 150,
        slots: {
            title: "deposit_sum",
            slotName: "订金金额",
            slotText: "订单订金金额之和"
        }
    },
    {
        dataIndex: "deposit_count_rate",
        key: "deposit_count_rate",
        align: "right",
        sorter: true,
        width: 180,
        slots: {
            title: "deposit_count_rate",
            slotName: "加粉订金转化率",
            slotText: "订金数/加粉人数"
        },
        scopedSlots: { customRender: "deposit_count_rate" }
    },
    // 
    {
        dataIndex: "deposit_cus_count_rate",
        key: "deposit_cus_count_rate",
        align: "right",
        sorter: true,
        width: 170,
        slots: {
            title: "deposit_cus_count_rate",
            slotName: "加粉订人转化率",
            slotText: "订人数/加粉人数"
        },
        scopedSlots: { customRender: "deposit_cus_count_rate" }
    },
    // {
    //     dataIndex: "new_store_cus_count_before",
    //     key: "new_store_cus_count_before",
    //     align: "right",
    //     sorter: true,
    //     width: 170,
    //     slots: {
    //         title: "new_store_cus_count_before",
    //         slotName: "（售前）总到店新客人数",
    //         slotText: "搜索时间段内到店的新客（订单来源为非门店，状态为已完成）的人数按手机号去重，以创建客服为准；"
    //     }
    // },
    {
        dataIndex: "new_store_cus_count",
        key: "new_store_cus_count",
        align: "right",
        sorter: true,
        width: 170,
        slots: {
            title: "new_store_cus_count",
            // slotName: "（售中）总到店新客人数",
            slotName: "新客到店人数",
            slotText: "搜索时间段内到店的新客（订单来源为非门店，状态为已完成）的人数按手机号去重，以预约客服为准"
        }
    },
    {
        dataIndex: "performance",
        key: "performance",
        align: "right",
        sorter: true,
        width: 170,
        slots: {
            title: "performance",
            slotName: "业绩",
            slotText: "搜索时间段内到店的新客,3天内的业绩归属客服"
        }
    },
    {
        dataIndex: "per_customer",
        key: "per_customer",
        align: "right",
        sorter: true,
        width: 170,
        slots: {
            title: "per_customer",
            slotName: "客单价",
            slotText: "业绩/新客到店人数"
        }
    },
    {
        dataIndex: "new_store_cus_count_rate",
        key: "new_store_cus_count_rate",
        align: "right",
        sorter: true,
        width: 170,
        slots: {
            title: "new_store_cus_count_rate",
            slotName: "总加人转到店率",
            slotText: "总到店新客人数/加粉人数"
        },
        scopedSlots: { customRender: "new_store_cus_count_rate" }
    },
    {
        dataIndex: "deposit_store_rate",
        key: "deposit_store_rate",
        align: "right",
        sorter: true,
        width: 170,
        slots: {
            title: "deposit_store_rate",
            slotName: "订金转到店率",
            slotText: "总到店新客人数/订金人数"
        },
        scopedSlots: { customRender: "deposit_store_rate" }
    },
    {
        dataIndex: "new_fans_store_cus_count",
        key: "new_fans_store_cus_count",
        align: "right",
        sorter: true,
        width: 220,
        slots: {
            title: "new_fans_store_cus_count",
            slotName: "当月到店新客人数",
            slotText: "搜索时间段内加粉且到店的新客（订单来源为非门店，状态为已完成，第三方结算）人数按手机号去重，以预约客服为准；从erp取数"
        }
    },
    {
        dataIndex: "new_fans_store_cus_count_rate",
        key: "new_fans_store_cus_count_rate",
        align: "right",
        sorter: true,
        width: 220,
        slots: {
            title: "new_fans_store_cus_count_rate",
            slotName: "当月加人转到店率",
            slotText: "当月到店新客人数/加粉人数"
        },
        scopedSlots: { customRender: "new_fans_store_cus_count_rate" }
    },
    {
        dataIndex: "month_deposit_cus_count",
        key: "month_deposit_cus_count",
        align: "right",
        sorter: true,
        width: 220,
        slots: {
            title: "month_deposit_cus_count",
            slotName: "当月订金人数",
            slotText: "搜索时间段内加粉且下订的新客"
        }
    },
    {
        dataIndex: "month_deposit_cus_count_rate",
        key: "month_deposit_cus_count_rate",
        align: "right",
        sorter: true,
        width: 220,
        slots: {
            title: "month_deposit_cus_count_rate",
            slotName: "当月订金转化率",
            slotText: "当月订金人数/加粉人数"
        },
        scopedSlots: { customRender: "month_deposit_cus_count_rate" }
    },
    {
        dataIndex: "remote_count",
        key: "remote_count",
        align: "right",
        sorter: false,
        width: 220,
        slots: {
            title: "remote_count",
            slotName: "偏远数",
            slotText: "标签为偏远人数"
        },
    },
    {
        dataIndex: "remote_count_rate",
        key: "remote_count_rate",
        align: "right",
        sorter: false,
        width: 220,
        slots: {
            title: "remote_count_rate",
            slotName: "偏远率",
            slotText: "偏远人数/加粉人数"
        },
        scopedSlots: { customRender: "remote_count_rate" }
    },
    {
        dataIndex: "attrition_count",
        key: "attrition_count",
        align: "right",
        sorter: false,
        width: 220,
        slots: {
            title: "attrition_count",
            slotName: "流失人数",
            slotText: "按客服登记的流失人数去重"
        },
    },
    {
        dataIndex: "attrition_count_rate",
        key: "attrition_count_rate",
        align: "right",
        sorter: false,
        width: 220,
        slots: {
            title: "attrition_count_rate",
            slotName: "流失率",
            slotText: "流失人数/(流失人数+新客到店人数)"
        },
        scopedSlots: { customRender: "attrition_count_rate" }
    },
]
export const header = (key = '') => analyServiceColumns.reduce((l, j) => {
    if (j.expand && Array.isArray(j.expand) && j.expand.length > 0) {
        j.expand.forEach(item => {
            l.push(item)
        })
    } else {
        l.push(j)
    }
    return l
}, []).filter(item => item != key).map(item => item.title || item.slots.slotName)
export const exportKey = (key = '') => analyServiceColumns.reduce((l, j) => {
    if (j.expand && Array.isArray(j.expand) && j.expand.length > 0) {
        j.expand.forEach(item => {
            l.push(item)
        })
    } else {
        l.push(j)
    }
    return l
}, []).filter(item => item != key).map(item => item.dataIndex)