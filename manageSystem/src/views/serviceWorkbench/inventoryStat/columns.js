/*
 * 存量统计 - 列配置
 */
export const columns = [
  {
    title: "门店名称",
    dataIndex: "store_name",
    key: "store_name",
    align: "left"
  },
  {
    // title: "订金人数",
    dataIndex: "deposit_count",
    key: "deposit_count",
    slots: { title: "deposit_count" },
    align: "right"
  },
  {
    // title: "新客到店人数",
    dataIndex: "new_customer_visit_count",
    key: "new_customer_visit_count",
    slots: { title: "new_customer_visit_count" },
    align: "right"
  },
  {
    // title: "订金转到店率",
    dataIndex: "deposit_to_visit_rate",
    key: "deposit_to_visit_rate",
    slots: { title: "deposit_to_visit_rate" },
    align: "right"
  }
];

export const slotName = [
  {
    slots: "deposit_count",
    slotName: "订金人数",
    slotText: "在搜索时间段内，加粉来源为推广，订金金额大于0且订单状态为有效状态的客户人数"
  },
  {
    slots: "new_customer_visit_count",
    slotName: "新客到店人数",
    slotText: "在搜索时间段内，加粉来源为推广，订单状态为已完成且客户首次到店时间与订单预约时间为同一天的客户人数"
  },
  {
    slots: "deposit_to_visit_rate",
    slotName: "订金转到店率",
    slotText: "新客到店人数除以订金人数的百分比"
  }
];