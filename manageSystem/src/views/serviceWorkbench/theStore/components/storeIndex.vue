<!--
 * @Author: superLjj <EMAIL>
 * @Date: 2023-03-08 17:13:39
 * @LastEditors: superLjj <EMAIL>
 * @LastEditTime: 2023-06-28 15:46:25
 * @FilePath: \manageSystem\src\views\serviceWorkbench\theStore\components\storeIndex.vue
 * @Description:  
-->
<template>
  <div>
    <self-page-header @searchQuery="searchQuery" @searchReset="searchReset">
      <!-- 顶部按钮 -->
      <template slot="top">
        <!--  到店未做原因管理 -->
        <self-col-btn v-has="'customer-churn-reason:index'">
          <self-customer-churn-reason></self-customer-churn-reason>
        </self-col-btn>
      </template>
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="预约时间">
          <self-time
            timeRange="day"
            timeType="date"
            :reset="timeReset"
            :exprotOneDay="true"
            v-model="selfDefaultTime.promote"
            :disabledDate="() => false"
            :timeKey="{
              start: 'start_time',
              end: 'end_time',
            }"
          />
        </self-col>
        <self-col label="门店名称">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="searchStoreByEntity"
            placeholder="请选择预约门店"
            value_key="store_name"
            :isRequest="false"
            :query="{
              is_show_warehouse: 0,
            }"
            v-model="queryParam.store_id"
          />
        </self-col>
        <self-col label="订单状态">
          <commone-self-principal
            :selectData="statusList"
            placeholder="请选择到店状态"
            v-model="queryParam.order_status"
          />
        </self-col>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <a-button @click="openModel">导出</a-button>
      </template>
    </self-page-header>
    <template v-if="loading || dataSourceFormat.length == 0">
      <a-collapse v-model="activeKey" class="theStoreItem">
        <a-collapse-panel :key="'panel0'">
          <div class="storeDetail" slot="header">
            <span class="title">{{ '-' }}</span>
            <div class="store-stats">
              <span class="text">总客户数：{{ 0 }}</span>
              <span class="text">新客：{{ 0 }}</span>
              <span class="text">老客：{{ 0 }}</span>
              <span class="text">已完成订单：{{ 0 }}</span>
              <span class="text">老师人数：{{ 0 }}</span>
              <span class="text">可接待客户数：{{ 0 }}</span>
              <span class="text">满载率：{{ 0 }}</span>
              <span class="text">改期数：{{ 0 }}</span>
            </div>
            <!-- <span class="text">老师数：{{ 0 }}</span> -->
            <div class="storeDescription">
                <span class="text"></span>
            </div>
          </div>
          <a-table
            rowKey="id"
            :columns="columns"
            :data-source="[]"
            :scroll="{ x: 1200 }"
            :loading="loading"
            @change="handleTableChange"
            :pagination="false"
          >
            <div slot="order_status" slot-scope="text, record">
              <a-tag
                :color="text && orderStatusList[text] ? orderStatusList[text].theme : ''"
              >{{ text ? orderStatusList[text].name : '' }}</a-tag>
            </div>
            <div slot="action" slot-scope="text, record">
              <a @click="action(record)">到店未做</a>
            </div>
          </a-table>
        </a-collapse-panel>
      </a-collapse>
    </template>
    <template v-else>
      <a-collapse v-model="activeKey" class="theStoreItem">
        <a-collapse-panel v-for="(item, index) in dataSourceFormat" :key="'panel' + index">
          <div class="storeDetail" slot="header">
            <span class="title">
              {{ item.store_name || '-' }} -- <a href="javascript:;" @click="handleShowImg(item)">指路图</a>
            </span>
            <div class="store-stats">
              <span class="text">总客户数：{{ item.sum_customer_num || 0 }}</span>
              <span class="text">新客：{{ item.new_customer_num || 0 }}</span>
              <span class="text">老客：{{ item.old_customer_num || 0 }}</span>
              <span class="text">已完成订单：{{ item.complete_order_num || 0 }}</span>
              <span class="text">老师人数：{{ item.teacher_num || 0 }}</span>
              <span class="text">可接待客户数：{{ item.customer_service_num || 0 }}</span>
              <span class="text">满载率：{{ item.full_load_rate || 0 }}</span>
              <span class="text">改期数：{{ item.reschedule_num || 0 }}</span>
            </div>
            <!-- <span class="text">老师数：{{ item.teacher_num || 0 }}</span> -->
            <div class="storeDescription">
                <span class="text">{{ item.store_intro || '' }}</span>
            </div>
          </div>
          <a-table
            rowKey="id"
            :columns="columns"
            :data-source="item.order_list"
            :scroll="{ x: 1200 }"
            :loading="loading"
            @change="handleTableChange"
            :pagination="false"
            size="small"
          >
            <template slot="cus_name" v-if="recored" slot-scope="text, recored">
              <div class="mb5">{{ recored.cus_name || "-" }}{{ (recored.deposit === 0 || recored.deposit === '0.00') && recored.is_new_customer === 1 ? '（0订金）' : '' }}</div>
              <div>
                {{ recored.cus_mobile }}
                <!-- <a
                v-has="'order-order-header:mobile-check'"
                @click="handleShow(recored)"
              >
                显示
                </a>-->
              </div>
            </template>
            <div slot="order_status" slot-scope="text, record">
              <a-tag
                :color="orderStatusList.find((l) => l.id == text) ? orderStatusList.find((l) => l.id == text).theme : ''
                "
              >{{ orderStatusList.find((l) => l.id == text) ? orderStatusList.find((l) => l.id == text).name : '' }}</a-tag>
            </div>
            <div
              slot="is_new_customer"
              slot-scope="text, record"
              :style="{ color: text == 0 ? 'green' : '#408ff7'}"
            >{{ text == 0 ? "老客" : "新客" }}</div>
            <div slot="action" slot-scope="text, record" v-divider>
              <a
                @click="action(record)"
                v-if="includesStatus(record.order_status, [1,3,4])"
                v-has="'customer-churn-remark:create'"
              >到店未做</a>
              <a @click="orderList(record)" v-has="'order-order-header:index'">订单信息</a>
            </div>
            <div slot="goods_name" slot-scope="text, record">
              <self-tooltip style="font-weight: bold" :text="text" :explainLength="90" />
              <br />
              <self-tooltip :text="record.plan_remark" :explainLength="90" />
            </div>
            <div slot="pay_amount" slot-scope="text, record">
              <self-tooltip style="font-weight: bold" :text="text" :explainLength="90" />
              <br />
              <div>{{ record.cus_today_is_pay && record.order_status != 5  ? "已收款" : "" }}</div>
            </div>
          </a-table>
        </a-collapse-panel>
      </a-collapse>
    </template>
    <self-mobile-look ref="self-mobile-look" />
    <self-detail-modal
      title="到店未做"
      placement="center"
      v-model="propvisible"
      :confirmLoading="confirmLoading"
      :drawerWidth="500"
      :drawerHeight="'auto'"
      :confirmPromise="confirmPromise"
    >
      <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-form-item label="到店未做原因">
          <commone-self-principal
            placeholder="请选择到店未做原因"
            :showAll="false"
            :isSearchrRequest="false"
            value_key="reason"
            :requestFun="reasonStatusSelect"
            v-decorator="['reason_status', validatorRules.reason_status]"
          >
            <template slot-scope="data">
              <span>{{data.record.reason}}</span>
              <span style="color: #bfbfbf;" v-if="data.record.remark">({{data.record.remark}})</span>
            </template>
          </commone-self-principal>
        </a-form-item>
        <a-form-item label="责任老师">
          <commone-self-principal
            placeholder="请选择老师"
            :showAll="false"
            :isRequest="false"
            :isSearchrRequest="true"
            value_key="plan_teacher_id"
            searchKey="username"
            :requestFun="planTeacherList"
            v-decorator="['plan_teacher_id', validatorRules.plan_teacher_id]"
          >
            <template slot-scope="data">
              <span>{{data.record.username}}</span>
            </template>
          </commone-self-principal>
        </a-form-item>
        <a-form-item label="门店反馈">
          <a-textarea
            placeholder="请输入门店反馈"
            allow-clear
            :maxLength="500"
            v-decorator="['store_remark']"
          />
        </a-form-item>
        <a-form-item label="客户反馈">
          <a-textarea
            placeholder="请输入客服反馈"
            allow-clear
            :maxLength="500"
            v-decorator="['servicer_remark']"
          />
        </a-form-item>
      </a-form>
    </self-detail-modal>
    <a-modal :visible="openFlag" title="导出时间确认框" :footer="null" :maskClosable="false">
      <self-time
        timeRange="day"
        :disabledDate="benchmarkTime ? disabledDate : void 0"
        v-model="selfDefaultTime.promote"
        @calendarChange="calendarChange"
        :reset="timeReset"
        :timeKey="{
              start: 'start_time',
              end: 'end_time'
            }"
      />
      <div style="margin-top:20px">
        <export-to-csv
          :dataFormat="dataFormat"
          :query="queryParam"
          fileName="客户到店表"
          :limit="1000"
          :queryParam="queryParam"
          :CommentApi="reachStoreExport"
          :exportObject="[
              { header: '门店', key: 'store_name' },
              { header: '到店时间', key: 'first_store_time_text' },
              { header: '渠道', key: 'channel_name' },
              { header: '客户姓名', key: 'cus_name' },
              { header: '新老客', key: 'is_new_customer' },
              { header: '预约项目', key: 'goods_name' },
              { header: '预约备注', key: 'plan_remark' },
              { header: '下单客服', key: 'created_by_text' },
              { header: '预约客服', key: 'plan_name' },
              { header: '订单状态', key: 'order_status' },
            ]"
          v-has="'order-order-header:reach-store-export'"
        ></export-to-csv>
      </div>
    </a-modal>

    <a-modal
      v-model="directionalMap.visible"
      :title="directionalMap.store_name"
      centered
      :width="600"
      destroyOnClose
      :footer="false"
    >
      <div>
        <img :src="directionalMap.imgUrl" alt class="listimage" preview="1" v-imgdef/>
      </div>
    </a-modal>

  </div>
</template>
<script>
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import {
  storeManage,
  inventoryManage,
  customerChurnRemark,
  customerChurnReason
} from "@/api/api.js";
import moment from "moment";
export default {
  name: "theStore",
  mixins: [JeecgListMixin],
  data() {
    return {
      searchStoreByEntity: inventoryManage.storeSelect,
      reachStoreExport: storeManage.reachStoreExport,
      reasonStatusSelect: customerChurnReason.select,
      planTeacherList: storeManage.planTeacherList,
      activeKey: "panel0",
      columns: this.$actionForAuth(
        [
          {
            title: "预约时间",
            align: "left",
            fixed: "left",
            width: 180,
            dataIndex: "plan_time_text",
            scopedSlots: { customRender: "plan_time_text" }
          },
          {
            title: "渠道",
            align: "left",
            width: 100,
            dataIndex: "channel_name",
            scopedSlots: { customRender: "channel_name" }
          },
          {
            title: "客户信息",
            align: "left",
            width: 150,
            dataIndex: "cus_name",
            scopedSlots: { customRender: "cus_name" }
          },
          {
            title: "新老客",
            align: "left",
            width: 80,
            dataIndex: "is_new_customer",
            scopedSlots: { customRender: "is_new_customer" }
          },
          {
            title: "下单客服",
            align: "left",
            width: 100,
            dataIndex: "created_by_text",
            scopedSlots: { customRender: "created_by_text" }
          },
          {
            title: "预约客服",
            align: "left",
            width: 100,
            dataIndex: "plan_name",
            scopedSlots: { customRender: "plan_name" }
          },
          {
            title: "订单状态",
            align: "left",
            width: 100,
            dataIndex: "order_status",
            scopedSlots: { customRender: "order_status" }
          },
          {
            title: "预约操作时间",
            align: "left",
            width: 100,
            dataIndex: "order_reservation_operate_time",
            scopedSlots: { customRender: "order_reservation_operate_time" }
          },
          {
            title: "实收金额",
            align: "left",
            width: 100,
            dataIndex: "pay_amount",
            scopedSlots: { customRender: "pay_amount" }
          },
          {
            title: "预约项目/预约备注",
            align: "left",
            width: 700,
            dataIndex: "goods_name",
            scopedSlots: { customRender: "goods_name" }
          },
          {
            title: "操作",
            align: "center",
            fixed: "right",
            width: 200,
            dataIndex: "action",
            scopedSlots: { customRender: "action" }
          }
        ],
        ["customer-churn-remark:create", "order-order-header:index"]
      ),
      orderStatusList: this.$store.state.storeCollection.statusList,
      statusList: [
        {
          id: 1,
          name: "已预约",
          status: "blue"
        },
        {
          id: 4,
          name: "已到店",
          status: "orange"
        },
        {
          id: 5,
          name: "已完成",
          status: "green"
        },
        {
          id: 6,
          name: "第三方结算",
          status: "green"
        },
        {
          id: 7,
          name: "申请退订",
          status: "red"
        },
        {
          id: 8,
          name: "售后服务",
          status: "red"
        }
      ],
      selfDefaultTime: {},
      queryParam: {},
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        showSizeChanger: false,
        showTotal: function(total, range) {
          let page = "20/页 共" + total + "条";
          return page;
        }
      },
      url: {
        list: "/order/order-header/reach-store"
      },
      labelCol: {
        xs: { span: 22 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 22 },
        sm: { span: 15 }
      },
      id: 0,
      propvisible: false,
      confirmLoading: false,
      form: this.$form.createForm(this),
      openFlag: false,
      benchmarkTime: null,
      directionalMap:{
        visible:false,
        imgUrl:'',
        store_name:''
      }
    };
  },
  computed: {
    dataSourceFormat: function() {
      let d = Object.assign([], this.dataSource.list);
      d = this.$utils.defaultField(d);
      d.forEach(item => {
        item.phoneShow = true;
        item.order_list = this.$utils.defaultField(item.order_list);
      });
      d = this.$utils.TablefieldCompletion({
        list: d,
        format: "YYYY-MM-DD HH:mm:ss",
        child: "order_list"
      });
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    },
    validatorRules: function() {
      return {
        reason_status: {
          rules: [{ required: true, message: "请选择到店原因" }]
        },
        plan_teacher_id: {
          rules: [{ required: true, message: "请选择责任老师" }]
        },
      };
    }
  },
  methods: {
    orderList({ order_no }) {
      this.$store.commit("SET_PERMISSIONLISTPASSROUTER", ["orderList"]);
      this.$router.push({
        name: "订单列表",
        params: {
          order_no
        }
      });
    },
    openModel() {
      this.openFlag = true;
    },
    calendarChange([times]) {
      if (times.length == 1) {
        this.benchmarkTime = times[0];
      }
    },
    disabledDate(current) {
      const currentBenchmarkTime = moment(current).unix();
      const startBenchmarkTime = moment(
        moment(this.benchmarkTime).subtract(32, "days")
      ).unix();
      const endBenchmarkTime = moment(
        moment(this.benchmarkTime).add(32, "days")
      ).unix();
      return (
        currentBenchmarkTime <= startBenchmarkTime ||
        currentBenchmarkTime >= endBenchmarkTime ||
        (current && current > moment().endOf("day"))
      );
    },
    handleChange(e) {
      console.log(e);
    },
    handleShowImg(info){
      this.directionalMap.imgUrl = info.directional_map;
      this.directionalMap.store_name = info.store_name+'-指路图';
      this.directionalMap.visible = true;
    },
    //导出-数据格式
    dataFormat({ list }) {
      list = list.map(item => {
        item.order_status = this.orderStatusList.find(
          l => l.id == item.order_status
        )
          ? this.orderStatusList.find(l => l.id == item.order_status).name
          : "";
        item.is_new_customer = item.is_new_customer == 0 ? "老客" : "新客";
        return item;
      });
      return list;
    },
    handleShow(data) {
      if (!data.cus_id) return;
      this.$refs["self-mobile-look"].phoneLook(data);
    },
    action({ id }) {
      this.propvisible = true;
      this.id = id;
    },
    confirmPromise() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.confirmLoading = true;
          customerChurnRemark
            .create(
              Object.assign(values, {
                order_id: this.id
              })
            )
            .then(res => {
              if (res.code == 200) {
                this.$message.success(res.message);
                this.propvisible = false;
              }
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        }
      });
    },
    includesStatus(status, arr) {
      return arr.includes(parseInt(status));
    }
  }
};
</script>

<style lang="less" scoped>
.theStoreItem {
  .storeDetail {
    background: #f7f8fc;
    position: relative;
    // padding: 0 20px;
    align-items: left;
    display: flex;
    align-items: center;

    .title {
      font-size: 16px;
      font-weight: bold;
      margin-right: 10px;
      width: 230px;
      text-align: left;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      //   &::after {
      //     display: inline-block;
      //     content: "";
      //     flex: 1;
      //     height: 1px;
      //     background-image: linear-gradient(
      //       to right,
      //       #919191 0%,
      //       #919191 50%,
      //       transparent 50%
      //     );
      //     background-size: 8px 1px;
      //     background-repeat: repeat-x;
      //     margin: 0 auto;
      //     margin-left: 10px;
      //     position: relative;
      //     top: 1px;
      //   }
    }

    .store-stats {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
    }

    .text {
      font-size: 13px;
      color: @primary-color;
      margin-right: 20px;
    }

    .storeDescription {
        margin-left: auto; /* 将简介部分推到右边 */
        flex-shrink: 0;
        white-space: nowrap;
        .text {
            font-size: 14px;
            color: @primary-color;
            margin-right: 0; /* 移除右边距使简介部分对齐 */
        }
    }
    // &::before {
    //   content: "";
    //   display: inline-block;
    //   width: 4px;
    //   background: @primary-color;
    //   position: absolute;
    //   left: 0;
    //   top: 0;
    //   height: 100%;
    // }
  }
}

/deep/ .ant-collapse-content > .ant-collapse-content-box {
  padding: 0;
}
.listimage {
  width: 550px;
  cursor: pointer;
}
</style>
