<?php

namespace console\controllers;

use backendapi\services\promote\TransferMoneyService;
use common\cache\DepartmentCache;
use common\components\Feishu;
use services\common\FeishuExamineService;
use common\models\refund\Refund;
use common\models\common\Entity;
use common\enums\order\RefundStatusEnum;
use common\enums\WhetherEnum;
use common\models\backend\Member;
use common\models\common\DepartmentAssignment;
use common\models\member\FeishuUser;
use common\models\rbac\AuthAssignment;
use common\models\rbac\AuthRole;
use common\models\refund\Process;
use Yii;
use Exception;
use yii\helpers\ArrayHelper;
use console\services\ConfigService;
use services\common\feishu\CusLinkForewarning;
use services\common\feishu\ServiceDataForewarning;
use common\components\feishu\multidimensionalTable\TeacherMonthAmount;
use common\models\data\ServicerAnalysis;
use yii\httpclient\Client;
use common\components\feishu\multidimensionalTable\StoreSchedule;

/**
 * 飞书审批
 */
class FeishuProcessController extends BaseController
{
    public $cache;

    public function __construct($id, $module, $config = [])
    {
        parent::__construct($id, $module, $config = []);

        $this->cache = Yii::$app->cache;
    }
    /**
     * 提交昨天的退款单
     *
     * @return void
     */
    public function actionSubmitRefund()
    {
        $entityList = Entity::find()
            ->select('id, name, code')
            ->all();

        foreach ($entityList as $entity) {
            /** @var array<Refund> */
            $refundList = Refund::find()
                ->with(['owner', 'order', 'customer', 'createdPerson', 'reason'])
                ->where(['status' => RefundStatusEnum::WAIT_4_DING])
                ->andWhere(['entity_id' => $entity->id])
                ->all();

            if (empty($refundList)) {
                continue;
            }

            $feishu = new Feishu($entity->code);


            // $approval_code = ConfigService::getRefundProcessCode($entity->id);
            // //$res = $feishu->getApproval($approval_code, $entity->id);
            // $instance_id = 'C24BA7D4-CF5A-4261-B0C7-02C55734D09D';
            // $res = $feishu->getSingleApproval($entity->id, $instance_id);
            // var_dump($res);exit;

            // 准备数据 主管数据，及错误信息
            $managerWithRefundList = [];
            $newManagerWithRefundList = [];
            $errMsgs = [];
            foreach ($refundList as $refund) {
                if (!isset($errMsgs[$refund->created_by])) {
                    $errMsgs[$refund->created_by] = [];
                }

                // 获取部门主管信息
                try {
                    $applyManager = $this->getManagerByUserId($refund->created_by);
                    $applyManager = ArrayHelper::toArray($applyManager);
                } catch (Exception $e) {
                    $err = $e->getMessage();
                    $errMsgs[$refund->createdPerson->feishu_userid][] = $err;
                    $refund->fail($err);
                    continue;
                }

                $trans = Yii::$app->db->beginTransaction();
                try {
                    $refund->status = RefundStatusEnum::RUNNING;
                    if (!$refund->save()) {
                        $errMsgs[$refund->createdPerson->feishu_userid][] = '退款单保存失败';
                    }

                    $uploadImages = explode(',', $refund->images);
                    $approval_images = [];
                    foreach ($uploadImages as $k => $image) {
                        $saveAsFileName = end(explode('/', $image));
                        ob_start();
                        readfile($image);
                        $img = ob_get_contents();
                        ob_end_clean();
                        file_put_contents('/tmp/' . $saveAsFileName . '.jpg', $img);

                        $file_name = $saveAsFileName . '.jpg';
                        $file_path = '/tmp/' . $saveAsFileName . '.jpg';
                        $approval_images[$k] = $feishu->uploadApprovalFiles($file_name, $file_path);
                        //$approval_images[$k] = $feishu->uploadFile($entity->id, $file_path, $file_name);
                    }
                    var_dump($approval_images);
                    exit;

                    // 提交退款钉钉审批
                    $feishuData = [
                        ['name' => '客户姓名', 'id' => 'widget1649666804402836472113819629', 'type' => 'input', 'value' => $refund->customer->name],
                        ['name' => '退订金额', 'id' => 'widget16496668046646166135664654952', 'type' => 'input', 'value' => $refund->money],
                        //['name' => '收款账户', 'id'=>'widget3', 'type'=>'account', 'value' => ''],//todo
                        ['name' => '下定门店', 'id' => 'widget16838851081800001', 'type' => 'input', 'value' => $refund->store->store_name,], //todo
                        //['name' => '项目', 'id'=>'widget16818908112310001', 'type'=>'radioV2', 'value' => ''],//todo
                        ['name' => '是否到店', 'id' => 'widget16818938262320001', 'type' => 'radioV2', 'value' => $refund->is_store == 1 ? 'lgng5pyw-do74vph0umn-0' : 'lgng5ygy-pbar2t23ris-1', 'option' => [['value' => 'lgng5pyw-do74vph0umn-0', 'text' => '是'], ['value' => 'lgng5ygy-pbar2t23ris-1', 'text' => '否']]],
                        ['name' => '付款时间', 'id' => 'widget1649666804818646570384399914', 'type' => 'date', 'value' => date(DATE_RFC3339, $refund->order->created_at)],
                        ['name' => 'ERP订单编号', 'id' => 'widget16818923883730001', 'type' => 'input', 'value' => $refund->order->order_no,],
                        ['name' => '退订原因', 'id' => 'widget16818928884430001', 'type' => 'textarea', 'value' => $refund->reason->name,],
                        //['name' => '付款截图带商户交易编号', 'id'=>'widget16837900341730001', 'type'=>'image', 'value' => $approval_images,], 
                    ];
                    $approval_code = ConfigService::getRefundProcessCode($entity->id);
                    $ret = $feishu->processCreate($applyManager['feishu_userid'], $feishuData, $approval_code);

                    if (ArrayHelper::getValue($ret, 'code') != 0) {
                        $errMsgs[$refund->createdPerson->feishu_userid][] = $ret['msg'];
                    }
                    // 添加退款审批记录
                    $process = new Process();
                    $process->refund_ids = $refund->id;
                    $process->feishu_no = ArrayHelper::getValue($ret['data'], 'instance_code');
                    $process->entity_id = $entity->id;
                    if (!$process->save()) {
                        $errMsgs[$refund->createdPerson->feishu_userid][] = '退款审批单新增失败';
                    }
                    $trans->commit();
                } catch (Exception $e) {
                    $trans->rollBack();
                    $errMsgs[$refund->createdPerson->feishu_userid][] = $e->getMessage();
                }

                // 错误信息发送到发起人飞书
                foreach ($errMsgs as $userId => $errMsg) {
                    if (empty($errMsg)) continue;
                    $msg = implode(',', array_unique($errMsg));
                    $sendContent = array(
                        'text' => '退款审批单错误信息：' . $msg,
                    );
                    FeishuExamineService::sendFeishu($sendContent, 'd83e649c', 'text');
                }
            }
        }
    }

    /**
     * 获取成员的主管
     *
     * @param int $userId
     * @return void
     */
    protected function getManagerByUserId($userId)
    {
        $deptId = DepartmentAssignment::find()
            ->where(['user_id' => $userId])
            ->cache(60)
            ->select('dept_id')
            ->scalar();

        if (!$deptId) {
            throw new Exception("找不到 {$userId} 对应的部门");
        }

        $managerId = $this->getManagerIdByDeptId($deptId);
        if (!$managerId) {
            throw new Exception("找不到 {$userId} 对应的主管");
        }

        $manager = Member::findOne($managerId);
        if (!$manager) {
            throw new Exception("{$userId} 对应的主管 {$managerId} 不存在");
        }

        return $manager;
    }

    /**
     * 获取部门的负责人ID
     *
     * @param int $deptId
     * @return void
     */
    protected function getManagerIdByDeptId($deptId)
    {
        $dept = DepartmentCache::init($deptId)->getDeptInfo();
        if ($managerId = ArrayHelper::getValue($dept, 'user_id')) {
            return $managerId;
        }

        return null;
    }

    /**
     * 定时任务，每日10点发消息提醒门店上传门店照片
     */
    public function actionNotifyStorePic()
    {
        $notifyUser = array(
            'd83e649c' => 'ankang', //zny
            '5f7a9f8b' => 'weinan', //hj
            'g88fd1dg' => 'ankang', //安康店王倩
            'a3975835' => 'weinan', //渭南店张玉
            '3243d4bd' => 'tongchuan', //铜川店李妮
            'ce62863d' => 'dali', //大荔店帖春艳
            '3967c3c7' => 'pucheng', //蒲城店王李媄
        );
        $go_url = array(
            'ankang' => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcniNGLJetYEyus1sOtDgHjWc',
            'weinan' => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcndeVvboACVvyrBIgdeQcGnf',
            'tongchuan' => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnqdBf10p7diWJxOqeKwwtwc',
            'pucheng'   => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcn1T8FbaWlKCTmW3wRVoetYb',
            'dali'      => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnCUxYplYKuFNR21La1PhDbf',
        );

        foreach ($notifyUser as $user => $location) {
            echo $user . "\r\n";
            $sendContent = array(
                'type'  => 'template',
                'data' => array(
                    'template_id' => 'ctp_AA6534aySF7B',
                    'template_variable' => array(
                        'go_url' => $go_url[$location],
                    ),
                ),
            );

            if (FeishuExamineService::sendFeishu($sendContent, $user, 'interactive')) {
                continue;
            } else {
                break;
            }
        }
    }

    /**
     * 定时任务，每日19点发消息提醒店长填写门店业绩数据
     */
    public function actionNotifyAddDataTest()
    {
        $notifyUser = array(
            '92b12ec5' => 'TCXQ' //林德志
            // '56ebb821' => 'AKHC', //安康店刘可心
            // '9e4a8edc' => 'WNXZ', //渭南新洲店赵幸
            // '9e4a8edc' => 'WNDL', //渭南大荔店赵幸
            // '3b1df898' => 'TCXQ', //铜川店宁丽,
        );

        $go_url = FeishuExamineService::arrGroup();
        foreach ($notifyUser as $user => $location) {
            $sendContent = [
                'type'  => 'template',
                'data' => [
                    'template_id' => 'ctp_AA6lzgmGp4Wr',
                    'template_variable' => [
                        // 'store_name' => $go_url[$location]['name'],
                        'go_url' => $go_url[$location]['form_url'],
                    ],
                ],
            ];

            FeishuExamineService::sendFeishu($sendContent, $user, 'interactive');
        }
        echo 'success';
        exit;
    }

    /**
     * 定时任务，每日19点发消息提醒店长填写门店业绩数据
     */
    public function actionNotifyAddData()
    {
        $notifyUser = array(
            'd83e649c' => 'ankang', //zny
            '5f7a9f8b' => 'weinan', //hj
            '56ebb821' => 'ankang', //安康店刘可心
            '9e4a8edc' => 'weinan', //渭南店赵幸
            '3b1df898' => 'tongchuan', //铜川店宁丽

        );
        $go_url = array(
            'ankang' => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnanhSoGkRNAvE3nmAn0oOBh',
            'weinan' => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnZae8MuefhIDdsJqaYt5rld',
            'tongchuan' => 'https://ywk1i1s9dd.feishu.cn/share/base/form/shrcnkFNUqYWyICvCJCzg92hyEd',
        );

        foreach ($notifyUser as $user => $location) {
            echo $user . "\r\n";
            $sendContent = array(
                'type'  => 'template',
                'data' => array(
                    'template_id' => 'ctp_AA6lzgmGp4Wr',
                    'template_variable' => array(
                        'go_url' => $go_url[$location],
                    ),
                ),
            );

            if (FeishuExamineService::sendFeishu($sendContent, $user, 'interactive')) {
                continue;
            } else {
                break;
            }
        }
    }

    /**
     * 获取机器人群列表
     */
    public function actionGetChatList()
    {
        $feishu = new Feishu();
        $result = $feishu->getChatList();
        var_dump($result);
        exit;
    }

    /**
     * 店长群内自动提醒店长发视频
     */
    public function actionNotifyStoreAssembly($dn)
    {
        if ($dn == 'day') {
            $text = '早安，请大家记得发早会视频，立下<b>今日目标</b>，加油冲鸭！！！💪💪💪';
        } else if ($dn == 'night') {
            $text = '晚上好，请大家记得发晚会视频，<b>复盘和总结</b>一天的工作吧😘😘😘';
        }
        $msg = array(
            'text' => $text . ' <at user_id="56ebb821">刘可心</at>  <at user_id="3b1df898">陈宁丽</at>  <at user_id="9e4a8edc">赵幸</at>  '
        );
        FeishuExamineService::sendFeishu($msg, 'oc_1eef779a30b00db50c38be1987f14fad', 'text', 'chat_id');

        return true;
    }

    /**
     * 提醒门店老师写每日总结
     */
    public function actionNotifyStoreReplay()
    {
        $store_chat_id = array(
            'weinan' => 'oc_160f77b65a69e1e2c056f65d2021d5c5',
            'tongchuan' => 'oc_ef09aafd36e302c865879c7d45bbdeec',
            'ankang' => 'oc_117053f85d7423102e774d2c35379664',
            'dali' => 'oc_561ef956d3dd6bb788c73d354836ff3e'
        );

        foreach ($store_chat_id as $store => $chat) {
            switch ($store) {
                case 'weinan':
                    $store_name = '渭南店的';
                    break;
                case 'tongchuan':
                    $store_name = '铜川店的';
                    break;
                case 'ankang':
                    $store_name = '安康店的';
                    break;
                case 'dali':
                    $store_name = '大荔店的';
                    break;
                default:
                    $store_name = '';
                    break;
            }

            $text = $store_name . '小伙伴们晚上好，请大家记得发今日工作总结，<b>复盘</b>一天的工作😄';
            if ($store == 'weinan') {
                $text .= ' <at user_id="cca36ef1">孟红杰</at> <at user_id="3f2bf22b">董晓燕</at> <at user_id="d4eaegd6">白璐</at> <at user_id="g7g4e5f5">王娟</at>';
            } else if ($store == 'tongchuan') {
                $text .= ' <at user_id="53b92183">魏亚娟</at> <at user_id="3243d4bd">李妮</at>';
            } else if ($store == 'ankang') {
                $text .= ' <at user_id="56ebb821">刘可心</at> <at user_id="gc3e61d5">陈婷</at> <at user_id="1ebgd9dc">唐兰兰</at> <at user_id="523bc7gb">陈芬</at>';
            } else if ($store == 'dali') {
                $text .= ' <at user_id="g182d839">屈文秀</at> <at user_id="81c344fd">张西</at>';
            }

            $msg = array('text' => $text);
            FeishuExamineService::sendFeishu($msg, $chat, 'text', 'chat_id');
        }

        return true;
    }
    /**
     * 提醒各门店每天发送目标到群里
     */
    public function actionStoreTarget()
    {
        $groupList = FeishuExamineService::arrGroup();
        foreach ($groupList as $key => $item) {
            if ($item['type'] != 'store') continue;

            if ($key == 'XAQJ') { //暂时不发
                continue;
            }

            $store_manager = '';
            if (!empty($item['store_manager']['feishu_user_id'])) {
                $store_manager = '<at user_id="' . $item['store_manager']['feishu_user_id'] . '">' . $item['store_manager']['name'] . '</at>';
            }

            $content = $store_manager . ' 请店长把今日门店的总业绩，均产，以及各位老师的目标明确分标出来[碰拳][碰拳][碰拳][碰拳][碰拳][碰拳]';
            Yii::$app->feishuNotice->text($content, $item['chat_id']);
        }

        return true;
    }

    /**
     * 提醒各门店每天发送目标到群里
     */
    public function actionStoreData($time)
    {
        $groupList = FeishuExamineService::arrGroup();
        foreach ($groupList as $key => $item) {
            if ($item['type'] != 'store') continue;

            if ($key == 'XAQJ') { //暂时不发
                continue;
            }

            $store_manager = '';
            if (!empty($item['store_manager']['feishu_user_id'])) {
                $store_manager = '<at user_id="' . $item['store_manager']['feishu_user_id'] . '">' . $item['store_manager']['name'] . '</at>';
            }

            $content = $store_manager . ' 请店长报出门店老师下午' . $time . '点所完成的业绩[碰拳][碰拳][碰拳][碰拳][碰拳][碰拳]';
            Yii::$app->feishuNotice->text($content, $item['chat_id']);
        }

        return true;
    }

    /**
     * 提醒老师发送顾客回访截图
     */
    public function actionCustomerVisit()
    {
        $time = date('w');
        if (!in_array($time, [0, 3])) {
            return true;
        }

        $date = ($time == 3) ? '周三' : '周日';
        $group = FeishuExamineService::arrGroup('YJJBQ');
        $content = '<at user_id="all">所有人</at>' . ' 今天' . $date . '，请各位老师记得发顾客回访截图[玫瑰][玫瑰]';
        Yii::$app->feishuNotice->text($content, $group['chat_id']);
        return true;
    }

    /**
     * 提醒推广成员汇报下当前的消耗、加粉数、成本
     */
    public function actionPromotionData()
    {
        $group = FeishuExamineService::arrGroup('GGYYGTQ');
        $userList = Member::find()->alias('m')
            ->select('m.username,m.feishu_unionid')
            ->leftJoin(['aa' => AuthAssignment::tableName()], 'aa.user_id = m.id')
            ->leftJoin(['r' => AuthRole::tableName()], 'r.id = aa.role_id')
            ->where(['m.status' => WhetherEnum::ENABLED, 'r.title' => AuthRole::promote])
            ->orderBy('m.id desc')
            ->asArray()
            ->all();

        $content = '';
        if ($userList) {
            foreach ($userList as $userInfo) {
                $content .= '<at user_id="' . $userInfo['feishu_unionid'] . '">' . $userInfo['username'] . '</at>';
            }
        } else {
            $content = '<at user_id="all">所有人</at>';
        }

        $content .= PHP_EOL . '汇报下当前的消耗、加粉数、成本、定金数、定金成本 [碰拳][碰拳][碰拳]';
        Yii::$app->feishuNotice->text($content, $group['chat_id']);
        return true;
    }

    /**
     * 客服
     */
    public function actionServicesData()
    {
        $group = FeishuExamineService::arrGroup('KfDJQ2');
        $content = '<at user_id="all">所有人</at>';
        $content .= PHP_EOL .
            '今日目标' . PHP_EOL .
            '定金：结算：' . PHP_EOL .
            '目前完成' . PHP_EOL .
            '定金：结算：' . PHP_EOL .
            '目前改期：' . PHP_EOL .
            '①原因：     ②确定档期时间：';

        Yii::$app->feishuNotice->text($content, $group['chat_id']);
        return true;
    }

    /**
     * 审批、云文档订阅
     */
    public function actionSubscribe()
    {
        $feishu = new Feishu();

        // 审批订阅
        $approvals = 'BF4EFEE7-C7B8-4B7F-A599-7FA0F299E48D'; //审批号
        $data = $feishu->approvalsSubscribe($approvals);
        var_dump($data);
        exit;

        //云文档事件订阅
        $file_token = "W37VbcQP9ay2VGs6tUvcOX5xndg";
        $data = $feishu->fileSubscribe($file_token);
        var_dump($data);
        exit;
    }

    /**
     * 审批、云文档取消订阅
     */
    public function actionUnSubscribe()
    {
        $feishu = new Feishu();

        // 审批取消订阅
        $approvals = 'BF4EFEE7-C7B8-4B7F-A599-7FA0F299E48D'; //审批号
        $data = $feishu->approvalsUnSubscribe($approvals);
        var_dump($data);
        exit;

        //云文档事件取消订阅
        $file_token = "W37VbcQP9ay2VGs6tUvcOX5xndg";
        $data = $feishu->fileUnSubscribe($file_token);
        var_dump($data);
        exit;
    }

    /**
     * 更新成员信息
     */
    public function actionMemberInfo()
    {
        $memberList = Member::find()->where(['<>', 'feishu_userid', ''])->all();
        $feishu = new Feishu();
        try {
            foreach ($memberList as $member) {
                $userInfo = $feishu->getUserInfoByUserID($member->feishu_userid);
                if ($userInfo['code'] != 0) {
                    echo '成员：' . $member->username . ',获取飞书信息失败' . PHP_EOL;
                    continue;
                }

                $member->head_portrait = $userInfo['data']['user']['avatar']['avatar_240'] ?? '';
                $member->save();

                $feishuUser = FeishuUser::find()->where(['member_id' => $member->id])->one();
                if ($feishuUser) {
                    $feishuUser->head_portrait = $member->head_portrait;
                    $feishuUser->save();
                }
            }
        } catch (Exception $e) {
            p($e->getMessage());
        }

        echo '全部执行完成';
        exit;
    }

    /**
     * php yii feishu-process/send-balance-notice
     * 
     * 发送广告备用金账户余额通知
     */
    public function actionSendBalanceNotice()
    {
        $model = new TransferMoneyService();
        $model->sendAccountBalanceNotice();

        //获客助手余额提醒
        CusLinkForewarning::sendBalanceNotice();
        echo '执行完成';
        exit;
    }

    /**
     * 业务数据预警
     */
    public function actionServiceDataRemid()
    {
        ServiceDataForewarning::run();
        echo '执行完成';
        exit;
    }

    /**
     * php yii feishu-process/cus-link-quato
     * 
     * 获客助手余量预警
     */
    public function actionCusLinkQuato()
    {
        CusLinkForewarning::quotaForewarning();
        echo '执行完成';
        exit;
    }

    /**
     * 获取客服数据统计 
     * 
     * php yii feishu-process/get-servicer-data
     */
    public function actionGetServicerData()
    {
        $searchStartTime = strtotime(date('Y-m-d')); // 默认当天开始
        $searchEndTime = strtotime(date('Y-m-d 23:59:59')); // 默认当天结束

        $query = Member::find()
            ->alias('m')
            ->select([
                'm.realname as customer_service_name',
                'm.feishu_unionid as unionid',
                'SUM(COALESCE(sa.add_fans_count, 0)) as add_fans_count',
                'SUM(COALESCE(sa.deposit_count, 0)) as deposit_count',
                'SUM(COALESCE(sa.new_store_cus_count, 0)) as new_store_cus_count'
            ])
            ->leftJoin(['sa' => ServicerAnalysis::tableName()], 'sa.user_id = m.id')
            ->where(['m.status' => WhetherEnum::ENABLED])
            ->andWhere(['between', 'sa.date_time', $searchStartTime, $searchEndTime])
            ->groupBy(['m.id']);

        $list = $query->asArray()->all();
        if ($list) {
            // 计算汇总数据
            $total = [
                'customer_service_name' => '',
                'unionid' => '',
                'add_fans_count' => 0,
                'deposit_count' => 0,
                'new_store_cus_count' => 0
            ];

            // 汇总数据
            foreach ($list as $item) {
                $total['add_fans_count'] += (int)($item['add_fans_count'] ?? 0);
                $total['deposit_count'] += (int)($item['deposit_count'] ?? 0);
                $total['new_store_cus_count'] += (int)($item['new_store_cus_count'] ?? 0);
            }

            // 在列表前插入汇总行
            array_unshift($list, $total);
        }

        $baseUrl = 'https://open.feishu.cn/anycross/trigger/callback/MGE4M2QxYjY5YTY1NWVhOGQxYWE3ZGI4MGY5ZjdkM2U1';
        $client = new Client();
        $client->post($baseUrl, (string)json_encode($list), [
            "Content-Type" => "application/json;charset=utf-8",
        ])->send();

        exit;
    }

    /**
     * 同步老师月度业绩
     * 执行：php yii feishu-process/teacher-month-amount-sync
     */
    public function actionTeacherMonthAmountSync()
    {
        $model = new TeacherMonthAmount(['ComCode' => 'chz']);
        $model->syncTeacherMonthAmount();
        echo '执行完成';
        exit;
    }

    /**
     * 拉取多维表格门店排客情况表数据
     * 执行：php yii feishu-process/pull-store-schedule-data
     */
    public function actionPullStoreScheduleData()
    {
        $model = new StoreSchedule(['ComCode' => 'chz']);
        $data = $model->pullStoreScheduleData();
        var_dump($data);
        exit;
    }
}
