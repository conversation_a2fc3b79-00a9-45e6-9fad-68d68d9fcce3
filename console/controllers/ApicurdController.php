<?php

namespace console\controllers;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * Api模块增删改查
 */
class ApicurdController extends BaseController
{
    // 配置相关字段
    public $authControllerNamespace = 'auth\controllers';
    public $authControllerName;
    public $authModelNamespace = 'auth\models';
    public $authModelName;
    public $authServiceNamespace = 'auth\services';
    public $authServiceName;
    public $backendControllerNamespace = 'backendapi\controllers';
    public $backendControllerName;
    public $backendFormNamespace = 'backendapi\forms';
    public $backendFormName;
    public $backendModelNamespace = 'backendapi\models';
    public $backendModelName;
    public $backendServiceNamespace = 'backendapi\services';
    public $backendServiceName;
    public $commonModelNamespace = 'common\models';
    public $commonModelName;
    public $commonServiceNamespace = 'common\services';
    public $commonServiceName;
    public $mobileControllerNamespace = 'mobileapi\controllers';
    public $mobileControllerName;
    public $mobileModelNamespace = 'mobileapi\models';
    public $mobileModelName;
    public $mobileServiceNamespace = 'mobileapi\services';
    public $mobileServiceName;
    public $tableFields = [];
    public $tableComment;
    
    //mac
    public $authControllerPath = 'auth/controllers';
    public $authModelPath = 'auth/models';
    public $authServicePath = 'auth/services';
    public $backendControllerPath = 'backendapi/controllers';
    public $backendFormPath = 'backendapi/forms';
    public $backendModelPath = 'backendapi/models';
    public $backendServicePath = 'backendapi/services';
    public $commonModelPath = 'common/models';
    public $commonServicePath = 'common/services';
    public $mobileControllerPath = 'mobileapi/controllers';
    public $mobileModelPath = 'mobileapi/models';
    public $mobileServicePath = 'mobileapi/services';

    // 从命令行解析的参数
    public $pOnlyModel;
    public $pTable;
    public $system = 'window';
    public $pNamespace;
    public $pAuthControllerNamespace;
    public $pAuthModelNamespace;
    public $pAuthServiceNamespace;
    public $pBackendControllerNamespace;
    public $pBackendFormNamespace;
    public $pBackendModelNamespace;
    public $pBackendServiceNamespace;
    public $pCommonModelNamespace;
    public $pCommonServiceNamespace;
    public $pMobileControllerNamespace;
    public $pMobileModelNamespace;
    public $pMobileServiceNamespace;
    public $pFolder;
    public $pAuthControllerFolder;
    public $pAuthModelFolder;
    public $pAuthServiceFolder;
    public $pBackendControllerFolder;
    public $pBackendFormFolder;
    public $pBackendModelFolder;
    public $pBackendServiceFolder;
    public $pCommonModelFolder;
    public $pCommonServiceFolder;
    public $pMobileControllerFolder;
    public $pMobileModelFolder;
    public $pMobileServiceFolder;

    /**
     * 从命令行解析的参数
     *
     * @param string $actionID
     * @return string[]
     */
    public function options($actionID)
    {
        return [
            'system',
            'pOnlyModel',
            'pTable',
            'pNamespace',
            'pAuthControllerFolder',
            'pAuthModelFolder',
            'pAuthServiceFolder',
            'pBackendControllerNamespace',
            'pBackendFormNamespace',
            'pBackendModelNamespace',
            'pBackendServiceNamespace',
            'pCommonModelNamespace',
            'pCommonServiceNamespace',
            'pMobileControllerNamespace',
            'pMobileModelNamespace',
            'pMobileServiceNamespace',
            'pFolder',
            'pBackendControllerFolder',
            'pBackendFormFolder',
            'pBackendModelFolder',
            'pBackendServiceFolder',
            'pCommonModelFolder',
            'pCommonServiceFolder',
            'pMobileControllerFolder',
            'pMobileModelFolder',
            'pMobileServiceFolder',
        ];
    }

    /**
     * 命令行参数别称
     *
     * @return array|string[]
     */
    public function optionAliases()
    {
        return [
            'sys' => 'system',
            'om' => 'pOnlyModel',
            't' => 'pTable',
            'ns' => 'pNamespace',
            'acns' => 'pAuthControllerNamespace',
            'amns' => 'pAuthModelNamespace',
            'asns' => 'pAuthServiceNamespace',
            'bcns' => 'pBackendControllerNamespace',
            'bfns' => 'pBackendFormNamespace',
            'bmns' => 'pBackendModelNamespace',
            'bsns' => 'pBackendServiceNamespace',
            'cmns' => 'pCommonModelNamespace',
            'csns' => 'pCommonServiceNamespace',
            'mcns' => 'pMobileControllerNamespace',
            'mmns' => 'pMobileModelNamespace',
            'msns' => 'pMobileServiceNamespace',
            'f' => 'pFolder',
            'acf' => 'pAuthControllerFolder',
            'amf' => 'pAuthModelFolder',
            'asf' => 'pAuthServiceFolder',
            'bcf' => 'pBackendControllerFolder',
            'bff' => 'pBackendFormFolder',
            'bmf' => 'pBackendModelFolder',
            'bsf' => 'pBackendServiceFolder',
            'cmf' => 'pCommonModelFolder',
            'csf' => 'pCommonServiceFolder',
            'mcf' => 'pMobileControllerFolder',
            'mmf' => 'pMobileModelFolder',
            'msf' => 'pMobileServiceFolder',
        ];
    }

    /**
     * 生成代码
     *
     * @return void
     */
    public function actionCreate()
    {
        $this->getTableInfo();
        $this->buildFields();
        $this->rebuild4Namespaces();
        $this->rebuild4Folder();
        $this->buildFolders();
        $this->buildNamespaces();
        $this->buildClassNames();

        // 生成对应代码
        $this->createFiles();
    }

    /**
     * 获取表注释信息
     *
     * @return void
     */
    protected function getTableInfo()
    {
        $db = Yii::$app->db;
        $tableInfos = $db->createCommand("show table status where Name = '{$db->tablePrefix}{$this->pTable}'")->queryAll();
        if (count($tableInfos)) {
            $this->tableComment = str_replace('表', '', $tableInfos[0]['Comment']);
        }
    }

    /**
     * 解析字段信息
     *
     * @return void
     * @throws \yii\db\Exception
     */
    protected function buildFields()
    {
        $tableFields = Yii::$app->db->createCommand("SHOW FULL COLUMNS from {{%{$this->pTable}}}")->queryAll();
        // $this->tableFields = json_decode('[{"Field":"id","Type":"int(10) unsigned","Collation":null,"Null":"NO","Key":"PRI","Default":null,"Extra":"auto_increment","Privileges":"select,insert,update,references","Comment":""},{"Field":"parent_id","Type":"int(10)","Collation":null,"Null":"NO","Key":"","Default":"0","Extra":"","Privileges":"select,insert,update,references","Comment":"上级ID"},{"Field":"name","Type":"varchar(30)","Collation":"utf8mb4_general_ci","Null":"NO","Key":"","Default":null,"Extra":"","Privileges":"select,insert,update,references","Comment":"企业名称"},{"Field":"code","Type":"varchar(20)","Collation":"utf8mb4_general_ci","Null":"NO","Key":"","Default":null,"Extra":"","Privileges":"select,insert,update,references","Comment":"企业编号"},{"Field":"dingTalk_key","Type":"varchar(30)","Collation":"utf8mb4_general_ci","Null":"NO","Key":"","Default":"qiandai","Extra":"","Privileges":"select,insert,update,references","Comment":"钉钉配置键值"},{"Field":"wxcom_token","Type":"varchar(35)","Collation":"utf8mb4_general_ci","Null":"YES","Key":"","Default":"","Extra":"","Privileges":"select,insert,update,references","Comment":"企微token"},{"Field":"wxcom_aes_key","Type":"varchar(50)","Collation":"utf8mb4_general_ci","Null":"YES","Key":"","Default":"","Extra":"","Privileges":"select,insert,update,references","Comment":"企微aesKey"},{"Field":"wxcom_corp_id","Type":"varchar(20)","Collation":"utf8mb4_general_ci","Null":"YES","Key":"","Default":"","Extra":"","Privileges":"select,insert,update,references","Comment":"企微corpid"},{"Field":"wxcom_secret","Type":"varchar(50)","Collation":"utf8mb4_general_ci","Null":"YES","Key":"","Default":"","Extra":"","Privileges":"select,insert,update,references","Comment":"企微secret"},{"Field":"wxcom_cus_secret","Type":"varchar(50)","Collation":"utf8mb4_general_ci","Null":"YES","Key":"","Default":"","Extra":"","Privileges":"select,insert,update,references","Comment":"企微客户secret"},{"Field":"wxcom_con_secret","Type":"varchar(50)","Collation":"utf8mb4_general_ci","Null":"YES","Key":"","Default":"","Extra":"","Privileges":"select,insert,update,references","Comment":"企微通讯录secret"},{"Field":"created_by","Type":"int(11)","Collation":null,"Null":"NO","Key":"","Default":"0","Extra":"","Privileges":"select,insert,update,references","Comment":"创建人"},{"Field":"updated_by","Type":"int(11)","Collation":null,"Null":"NO","Key":"","Default":"0","Extra":"","Privileges":"select,insert,update,references","Comment":"修改人"},{"Field":"deleted_by","Type":"int(11)","Collation":null,"Null":"NO","Key":"","Default":"0","Extra":"","Privileges":"select,insert,update,references","Comment":"删除人"},{"Field":"created_at","Type":"int(11)","Collation":null,"Null":"NO","Key":"","Default":"0","Extra":"","Privileges":"select,insert,update,references","Comment":"创建时间"},{"Field":"updated_at","Type":"int(11)","Collation":null,"Null":"NO","Key":"","Default":"0","Extra":"","Privileges":"select,insert,update,references","Comment":"修改时间"},{"Field":"deleted_at","Type":"int(11)","Collation":null,"Null":"NO","Key":"","Default":"0","Extra":"","Privileges":"select,insert,update,references","Comment":"删除时间"}]');
        // $this->tableFields = ArrayHelper::toArray($this->tableFields);

        // 准备字段数据
        foreach ($tableFields as &$field) {
            $field['Unsigned'] = strpos($field['Type'], 'unsigned') !== false;
            $typeInfo = explode('(', $field['Type']);
            $field['Type'] = $typeInfo[0];
            switch ($field['Type']) {
                case 'varchar':
                case 'char':
                case 'text':
                    $field['Type'] = 'string';
                    break;
                case 'tinyint':
                    $field['Type'] = 'int';
                    break;
                case 'decimal':
                    $field['Type'] = 'double';
                    break;
            }
            $field['Length'] = 0;
            if (count($typeInfo) > 1) {
                $field['Length'] = explode(')', $typeInfo[1])[0];
            }
            $field['Comment'] = $field['Comment'] ?: $field['Field'];
            list($field['Title']) = explode('：', $field['Comment']);
        }
        $this->tableFields = ArrayHelper::index($tableFields, 'Field');

        $apiFoxData = [
            'type' => 'object',
            'properties' => [],
        ];
        foreach ($this->tableFields as $field) {
            $apiFoxField = [
                'title' => $field['Title'],
            ];
            switch ($field['Type']) {
                case 'string':
                    $apiFoxField['type'] = 'string';
                    break;
                case 'int':
                    $apiFoxField['type'] = 'integer';
                    break;
                case 'double':
                    $apiFoxField['type'] = 'number';
                    break;
            }
            if (isset($field['Comment']) && $field['Comment'] != $field['Title']) {
                $apiFoxField['description'] = $field['Comment'];
            }
            $apiFoxData['properties'][$field['Field']] = $apiFoxField;
        }

        $this->echoSuccess(PHP_EOL . '以下json可以直接导入 apiFox 的数据模型');
        $this->echo(json_encode($apiFoxData, 256) . PHP_EOL);
    }

    /**
     * 重组命名空间参数
     * 没有相关参数时，从默认命名空间中解析
     * @return void
     */
    protected function rebuild4Namespaces()
    {
        if ($this->pNamespace) {
            if (!$this->pAuthControllerNamespace) {
                $this->pAuthControllerNamespace = $this->pNamespace;
            }
            if (!$this->pAuthModelNamespace) {
                $this->pAuthModelNamespace = $this->pNamespace;
            }
            if (!$this->pAuthServiceNamespace) {
                $this->pAuthServiceNamespace = $this->pNamespace;
            }
            if (!$this->pBackendControllerNamespace) {
                $this->pBackendControllerNamespace = $this->pNamespace;
            }
            if (!$this->pBackendFormNamespace) {
                $this->pBackendFormNamespace = $this->pNamespace;
            }
            if (!$this->pBackendModelNamespace) {
                $this->pBackendModelNamespace = $this->pNamespace;
            }
            if (!$this->pBackendServiceNamespace) {
                $this->pBackendServiceNamespace = $this->pNamespace;
            }
            if (!$this->pCommonModelNamespace) {
                $this->pCommonModelNamespace = $this->pNamespace;
            }
            if (!$this->pCommonServiceNamespace) {
                $this->pCommonServiceNamespace = $this->pNamespace;
            }
            if (!$this->pMobileControllerNamespace) {
                $this->pMobileControllerNamespace = $this->pNamespace;
            }
            if (!$this->pMobileModelNamespace) {
                $this->pMobileModelNamespace = $this->pNamespace;
            }
            if (!$this->pMobileServiceNamespace) {
                $this->pMobileServiceNamespace = $this->pNamespace;
            }
        }
    }

    /**
     * 重组文件夹参数
     * 没有相关参数时，从默认文件夹中解析
     * @return void
     */
    protected function rebuild4Folder()
    {
        if ($this->pFolder) {
            if (!$this->pAuthControllerFolder) {
                $this->pAuthControllerFolder = $this->pFolder;
            }
            if (!$this->pAuthModelFolder) {
                $this->pAuthModelFolder = $this->pFolder;
            }
            if (!$this->pAuthServiceFolder) {
                $this->pAuthServiceFolder = $this->pFolder;
            }
            if (!$this->pBackendControllerFolder) {
                $this->pBackendControllerFolder = $this->pFolder;
            }
            if (!$this->pBackendFormFolder) {
                $this->pBackendFormFolder = $this->pFolder;
            }
            if (!$this->pBackendModelFolder) {
                $this->pBackendModelFolder = $this->pFolder;
            }
            if (!$this->pBackendServiceFolder) {
                $this->pBackendServiceFolder = $this->pFolder;
            }
            if (!$this->pCommonModelFolder) {
                $this->pCommonModelFolder = $this->pFolder;
            }
            if (!$this->pCommonServiceFolder) {
                $this->pCommonServiceFolder = $this->pFolder;
            }
            if (!$this->pMobileControllerFolder) {
                $this->pMobileControllerFolder = $this->pFolder;
            }
            if (!$this->pMobileModelFolder) {
                $this->pMobileModelFolder = $this->pFolder;
            }
            if (!$this->pMobileServiceFolder) {
                $this->pMobileServiceFolder = $this->pFolder;
            }
        }
    }

    /**
     * 组装命名空间
     *
     * @return void
     */
    protected function buildNamespaces()
    {
        if ($this->pAuthControllerNamespace) {
            $this->authControllerNamespace .= "\\{$this->pAuthControllerNamespace}";
            $this->authControllerPath .= "/{$this->pAuthControllerNamespace}";
        }
        if ($this->pAuthModelNamespace) {
            $this->authModelNamespace .= "\\{$this->pAuthModelNamespace}";
            $this->authModelPath .= "/{$this->pAuthModelNamespace}";
        }
        if ($this->pAuthServiceNamespace) {
            $this->authServiceNamespace .= "\\{$this->pAuthServiceNamespace}";
            $this->authServicePath .= "/{$this->pAuthServiceNamespace}";
        }
        if ($this->pBackendControllerNamespace) {
            $this->backendControllerNamespace .= "\\{$this->pBackendControllerNamespace}";
            $this->backendControllerPath .= "/{$this->pBackendControllerNamespace}";
        }
        if ($this->pBackendFormNamespace) {
            $this->backendFormNamespace .= "\\{$this->pBackendFormNamespace}";
            $this->backendFormPath .= "/{$this->pBackendFormNamespace}";
        }
        if ($this->pBackendModelNamespace) {
            $this->backendModelNamespace .= "\\{$this->pBackendModelNamespace}";
            $this->backendModelPath .= "/{$this->pBackendModelNamespace}";
        }
        if ($this->pBackendServiceNamespace) {
            $this->backendServiceNamespace .= "\\{$this->pBackendServiceNamespace}";
            $this->backendServicePath .= "/{$this->pBackendServiceNamespace}";
        }

        if ($this->pCommonModelNamespace) {
            $this->commonModelNamespace .= "\\{$this->pCommonModelNamespace}";
            $this->commonModelPath .= "/{$this->pCommonModelNamespace}";
        }

        if ($this->pCommonServiceNamespace) {
            $this->commonServiceNamespace .= "\\{$this->pCommonServiceNamespace}";
            $this->commonServicePath .= "/{$this->pCommonServiceNamespace}";
        }

        if ($this->pMobileControllerNamespace) {
            $this->mobileControllerNamespace .= "\\{$this->pMobileControllerNamespace}";
            $this->mobileControllerPath .= "/{$this->pMobileControllerNamespace}";
        }

        if ($this->pMobileModelNamespace) {
            $this->mobileModelNamespace .= "\\{$this->pMobileModelNamespace}";
            $this->mobileModelPath .= "/{$this->pMobileModelNamespace}";
        }

        if ($this->pMobileServiceNamespace) {
            $this->mobileServiceNamespace .= "\\{$this->pMobileServiceNamespace}";
            $this->mobileServicePath .= "/{$this->pMobileServiceNamespace}";
        }
    }

    /**
     * 组装命名空间
     *
     * @return void
     */
    protected function buildFolders()
    {
        if ($this->pAuthControllerFolder) {
            $this->authControllerNamespace .= "\\{$this->pAuthControllerFolder}";
        }
        if ($this->pAuthModelFolder) {
            $this->authModelNamespace .= "\\{$this->pAuthModelFolder}";
        }
        if ($this->pAuthServiceFolder) {
            $this->authServiceNamespace .= "\\{$this->pAuthServiceFolder}";
        }
        if ($this->pBackendControllerFolder) {
            $this->backendControllerNamespace .= "\\{$this->pBackendControllerFolder}";
        }
        if ($this->pBackendFormFolder) {
            $this->backendFormNamespace .= "\\{$this->pBackendFormFolder}";
        }
        if ($this->pBackendModelFolder) {
            $this->backendModelNamespace .= "\\{$this->pBackendModelFolder}";
        }
        if ($this->pBackendServiceFolder) {
            $this->backendServiceNamespace .= "\\{$this->pBackendServiceFolder}";
        }
        if ($this->pCommonModelFolder) {
            $this->commonModelNamespace .= "\\{$this->pCommonModelFolder}";
        }
        if ($this->pCommonServiceFolder) {
            $this->commonServiceNamespace .= "\\{$this->pCommonServiceFolder}";
        }
        if ($this->pMobileControllerFolder) {
            $this->mobileControllerNamespace .= "\\{$this->pMobileControllerFolder}";
        }
        if ($this->pMobileModelFolder) {
            $this->mobileModelNamespace .= "\\{$this->pMobileModelFolder}";
        }
        if ($this->pMobileServiceFolder) {
            $this->mobileServiceNamespace .= "\\{$this->pMobileServiceFolder}";
        }
    }

    /**
     * 组装类名
     *
     * @return void
     */
    protected function buildClassNames()
    {
        $words = explode('_', $this->pTable);
        foreach ($words as $i => &$word) {
            $word = ucfirst($word);
            if ($i == 0 && isset($words[$i + 1])) {
                if ($word != ucfirst($this->pAuthControllerNamespace)) {
                    $this->authControllerName .= $word;
                }
                if ($word != ucfirst($this->pAuthModelNamespace)) {
                    $this->authModelName .= $word;
                }
                if ($word != ucfirst($this->pAuthServiceNamespace)) {
                    $this->authServiceName .= $word;
                }
                if ($word != ucfirst($this->pBackendControllerNamespace)) {
                    $this->backendControllerName .= $word;
                }
                if ($word != ucfirst($this->pBackendFormNamespace)) {
                    $this->backendFormName .= $word;
                }
                if ($word != ucfirst($this->pBackendModelNamespace)) {
                    $this->backendModelName .= $word;
                }
                if ($word != ucfirst($this->pBackendServiceNamespace)) {
                    $this->backendServiceName .= $word;
                }
                if ($word != ucfirst($this->pCommonModelNamespace)) {
                    $this->commonModelName .= $word;
                }
                if ($word != ucfirst($this->pCommonServiceNamespace)) {
                    $this->commonServiceName .= $word;
                }
                if ($word != ucfirst($this->pMobileControllerNamespace)) {
                    $this->mobileControllerName .= $word;
                }
                if ($word != ucfirst($this->pMobileModelNamespace)) {
                    $this->mobileModelName .= $word;
                }
                if ($word != ucfirst($this->pMobileServiceNamespace)) {
                    $this->mobileServiceName .= $word;
                }
            } else {
                $this->authControllerName .= $word;
                $this->authModelName .= $word;
                $this->authServiceName .= $word;
                $this->backendControllerName .= $word;
                $this->backendFormName .= $word;
                $this->backendModelName .= $word;
                $this->backendServiceName .= $word;
                $this->commonModelName .= $word;
                $this->commonServiceName .= $word;
                $this->mobileControllerName .= $word;
                $this->mobileModelName .= $word;
                $this->mobileServiceName .= $word;
            }
        }
        $this->authControllerName .= "Controller";
        $this->authServiceName .= "Service";
        $this->backendControllerName .= "Controller";
        $this->backendFormName .= "Form";
        $this->backendServiceName .= "Service";
        $this->commonServiceName .= "Service";
        $this->mobileControllerName .= "Controller";
        $this->mobileServiceName .= "Service";
    }

    /**
     * 创建文件
     *
     * @return void
     */
    protected function createFiles()
    {
        $fileSettings = $this->getFileSettings();
   
        // 类的属性注释
        $fieldCommons = [];
        foreach ($this->tableFields as $field) {
            $fieldCommons[] = "\n * @property {$field['Type']} \${$field['Field']} {$field['Comment']}";
        }
        $fieldCommonString = implode('', $fieldCommons);

        // 属性标签数组
        $attrs = [];
        foreach ($this->tableFields as $field) {
            $attrs[] = "\n            '{$field['Field']}' => '{$field['Title']}',";
        }
        $attrString = implode('', $attrs);

        // 字段数组
        $fields = [];
        $ignoreRuleFields = ['id', 'entity_id', 'created_by', 'updated_by', 'deleted_by', 'created_at', 'updated_at', 'deleted_at'];
        foreach ($this->tableFields as $field) {
            if (in_array($field['Field'], $ignoreRuleFields)) {
                continue;
            }
            $fields[] = "'{$field['Field']}'";
        }
        $fieldsString = implode(', ', $fields);

        // 组装 rules
        $rules = '';
        $noNullFields = [];
        $stringFields = [];
        $otherStringFields = [];
        $intFields = [];
        $doubleFields = [];
        $ignoreRuleFields = ['id', 'entity_id', 'created_by', 'updated_by', 'deleted_by', 'created_at', 'updated_at', 'deleted_at'];
        foreach ($this->tableFields as $field) {
            if (in_array($field['Field'], $ignoreRuleFields)) {
                continue;
            }
            if ($field['Null'] == 'NO') {
                $noNullFields[] = "'{$field['Field']}'";
            }
            switch ($field['Type']) {
                case 'string':
                    $stringFields[] = "'{$field['Field']}'";
                    if ($field['Length']) {
                        $rules .= "            [['{$field['Field']}'], 'string', 'max' => {$field['Length']}],\n";
                    } else {
                        $otherStringFields[] = "'{$field['Field']}'";
                    }
                    break;
                case 'int':
                    $intFields[] = "'{$field['Field']}'";
                    $field['Unsigned'] && $rules .= "            [['{$field['Field']}'], 'integer', 'min' => 0],\n";
                    break;
                case 'double':
                    $doubleFields[] = "'{$field['Field']}'";
                    $field['Unsigned'] && $rules .= "            [['{$field['Field']}'], 'double', 'min' => 0],\n";
                    break;
            }
        }
        count($noNullFields) && $rules .= "            [[" . implode(', ', $noNullFields) . "], 'required'],\n";
        count($intFields) && $rules .= "            [[" . implode(', ', $intFields) . "], 'integer'],\n";
        count($doubleFields) && $rules .= "            [[" . implode(', ', $doubleFields) . "], 'double'],\n";
        count($stringFields) && $rules .= "            [[" . implode(', ', $stringFields) . "], 'trim'],\n";
        count($otherStringFields) && $rules .= "            [[" . implode(', ', $otherStringFields) . "], 'string'],\n";

        // 操作信息
        $createdAt4Save = '';
        $createdAt4Display = '';
        if ($this->hasField('created_at')) {
            $createdAt4Save .= PHP_EOL . '            $this->created_at = time();';
            $createdAt4Display .= PHP_EOL . PHP_EOL;
            $createdAt4Display .= '    public function getCreatedAtText()' . PHP_EOL;
            $createdAt4Display .= '    {' . PHP_EOL;
            $createdAt4Display .= '        return DateHelper::toDate($this->created_at, \'Y-m-d H:i:s\');' . PHP_EOL;
            $createdAt4Display .= '    }';
        }
        $createdBy4Save = '';
        $createdBy4Display = '';
        if ($this->hasField('created_by')) {
            $createdBy4Save .= PHP_EOL . '            $this->created_by = UserService::getInst()->id;';
            $createdBy4Display .= PHP_EOL . PHP_EOL;
            $createdBy4Display .= '    public function getCreatedPerson()' . PHP_EOL;
            $createdBy4Display .= '    {' . PHP_EOL;
            $createdBy4Display .= '        return $this->hasOne(\common\models\backend\Member::class, [\'id\' => \'created_by\']);' . PHP_EOL;
            $createdBy4Display .= '    }';
            $createdBy4Display .= PHP_EOL . PHP_EOL;
            $createdBy4Display .= '    public function getCreatedByText()' . PHP_EOL;
            $createdBy4Display .= '    {' . PHP_EOL;
            $createdBy4Display .= '        return $this->createdPerson->username ?: \'\';' . PHP_EOL;
            $createdBy4Display .= '    }';
        }
        $updatedAt4Save = '';
        $updatedAt4Display = '';
        if ($this->hasField('updated_at')) {
            $updatedAt4Save .= PHP_EOL . '        $this->updated_at = time();';
            $updatedAt4Display .= PHP_EOL . PHP_EOL;
            $updatedAt4Display .= '    public function getUpdatedAtText()' . PHP_EOL;
            $updatedAt4Display .= '    {' . PHP_EOL;
            $updatedAt4Display .= '        return DateHelper::toDate($this->updated_at, \'Y-m-d H:i:s\');' . PHP_EOL;
            $updatedAt4Display .= '    }';
            $updatedAt4Display .= PHP_EOL . PHP_EOL;
            $updatedAt4Display .= '    public function getUpdatedByText()' . PHP_EOL;
            $updatedAt4Display .= '    {' . PHP_EOL;
            $updatedAt4Display .= '        return $this->updatedPerson->username ?: \'\';' . PHP_EOL;
            $updatedAt4Display .= '    }';
        }
        $updatedBy4Save = '';
        $updatedBy4Display = '';
        if ($this->hasField('updated_by')) {
            $updatedBy4Save .= PHP_EOL . '        $this->updated_by = UserService::getInst()->id;';
            $updatedBy4Display .= PHP_EOL . PHP_EOL;
            $updatedBy4Display .= '    public function getUpdatedPerson()' . PHP_EOL;
            $updatedBy4Display .= '    {' . PHP_EOL;
            $updatedBy4Display .= '        return $this->hasOne(\common\models\backend\Member::class, [\'id\' => \'updated_by\']);' . PHP_EOL;
            $updatedBy4Display .= '    }';
        }

        // 删除时间过滤条件
        $whereDelete = '';
        if ($this->hasField('deleted_at')) {
            $whereDelete = "\n        \$query->andFilterWhere(['deleted_at' => 0]);";
        }

        // entity 过滤条件
        $whereEntity = '';
        $entityId4Save = '';
        if ($this->hasField('entity_id')) {
            $whereEntity = "\n            \$query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);";
            $entityId4Save .= PHP_EOL . '            $this->entity_id = UserService::getInst()->current_entity_id;';
        }

        // select相关
        $selectFields = ['id'];
        $searchField = 'id';
        if ($this->hasField('name')) {
            $selectFields[] = 'name';
            $searchField = 'name';
        } elseif ($this->hasField('title')) {
            $selectFields[] = 'title as name';
            $searchField = 'title';
        } else {
            $selectFields[] = 'id as name';
        }

        $replaceMap = [
            // 表名
            'tableName' => $this->pTable,
            // 表注释
            'tableComment' => $this->tableComment,
            // 名称
            'authControllerName' => $this->authControllerName,
            'authModelName' => $this->authModelName,
            'authServiceName' => $this->authServiceName,
            'backendControllerName' => $this->backendControllerName,
            'backendFormName' => $this->backendFormName,
            'backendModelName' => $this->backendModelName,
            'backendServiceName' => $this->backendServiceName,
            'commonModelName' => $this->commonModelName,
            'commonServiceName' => $this->commonServiceName,
            'mobileControllerName' => $this->mobileControllerName,
            'mobileModelName' => $this->mobileModelName,
            'mobileServiceName' => $this->mobileServiceName,
            // 命名空间
            'authControllerNamespace' => $this->authControllerNamespace,
            'authModelNamespace' => $this->authModelNamespace,
            'authServiceNamespace' => $this->authServiceNamespace,
            'backendControllerNamespace' => $this->backendControllerNamespace,
            'backendFormNamespace' => $this->backendFormNamespace,
            'backendModelNamespace' => $this->backendModelNamespace,
            'backendServiceNamespace' => $this->backendServiceNamespace,
            'commonModelNamespace' => $this->commonModelNamespace,
            'commonServiceNamespace' => $this->commonServiceNamespace,
            'mobileControllerNamespace' => $this->mobileControllerNamespace,
            'mobileModelNamespace' => $this->mobileModelNamespace,
            'mobileServiceNamespace' => $this->mobileServiceNamespace,
            // 类的属性注释
            'classProperty' => $fieldCommonString,
            // 替换属性标签数组
            'attributeLabels' => $attrString,
            // 替换字段列表
            'fieldsString' => $fieldsString,
            // 组装 rules
            'rules' => $rules,
            // 操作信息保存
            'createdAt4Save' => $createdAt4Save,
            'createdAt4Display' => $createdAt4Display,
            'createdBy4Save' => $createdBy4Save,
            'createdBy4Display' => $createdBy4Display,
            'updatedAt4Save' => $updatedAt4Save,
            'updatedAt4Display' => $updatedAt4Display,
            'updatedBy4Save' => $updatedBy4Save,
            'updatedBy4Display' => $updatedBy4Display,
            // 删除时间过滤条件
            'whereDelete' => $whereDelete,
            // entity 过滤条件
            'whereEntity' => $whereEntity,
            'entityId4Save' => $entityId4Save,
            // select相关
            'selectFields' => implode(',', $selectFields),
            'searchField' => $searchField,
        ];
        $createCount = 0;
        $modifyCount = 0;
        foreach ($fileSettings as $fileSetting) {
            // 读取摸板数据
            $modelContent = file_get_contents('console/templates/' . $fileSetting['sourceFileName']);

            foreach ($replaceMap as $source => $target) {
                $modelContent = str_replace("[[{$source}]]", $target, $modelContent);
            }

            $this->checkDirOrCreate($fileSetting['targetPath']);
            if (file_exists("{$fileSetting['targetPath']}/{$fileSetting['targetFileName']}")) {
                $modifyCount++;
                $this->echoError("更新 {$fileSetting['targetPath']}/{$fileSetting['targetFileName']}");
            } else {
                $createCount++;
                $this->echoSuccess("创建 {$fileSetting['targetPath']}/{$fileSetting['targetFileName']}");
            }
            // 写入文件
            $myFile = fopen("{$fileSetting['targetPath']}/{$fileSetting['targetFileName']}", "w") or die("Unable to open file!");
            fwrite($myFile, $modelContent);
            fclose($myFile);
        }
        $this->echo("\n\n！！！");
        if ($createCount) {
            $this->echoSuccess("成功创建 {$createCount} 个文件");
        }
        if ($modifyCount) {
            $this->echoError("{$modifyCount} 个文件被修改，记得审核被修改的内容！");
        }
    }

    /**
     * 检测目录没有则创建
     *
     * @param string $dirPath
     * @return void
     */
    protected function checkDirOrCreate($dirPath)
    {
        if (!is_dir($dirPath)) {
            $this->echoError("目录 {$dirPath} 不存在，执行创建任务。");
            mkdir($dirPath, 0777, true);
        }
    }

    /**
     * 是否存在字段
     *
     * @param string $field
     * @return boolean
     */
    protected function hasField($field)
    {
        return array_key_exists($field, $this->tableFields);
    }

    /**
     * @return array[]
     */
    protected function getFileSettings(): array
    {
        $is_mac = $this->system == 'mac' ? true : false;
        if ($this->pOnlyModel == 1) {
            return [
                [
                    'sourceFileName' => 'CommonModel.php',
                    'targetPath' => $is_mac ?  $this->commonModelPath : $this->commonModelNamespace,
                    'targetFileName' => $this->commonModelName . '.php',
                ],
            ];
        }

        $fileSettings = [
            [
                'sourceFileName' => 'AuthController.php',
                'targetPath' => $is_mac ?  $this->authControllerPath : $this->authControllerNamespace,
                'targetFileName' => $this->authControllerName . '.php',
            ],
            [
                'sourceFileName' => 'AuthModel.php',
                'targetPath' => $is_mac ?  $this->authModelPath : $this->authModelNamespace,
                'targetFileName' => $this->authModelName . '.php',
            ],
            [
                'sourceFileName' => 'AuthService.php',
                'targetPath' => $is_mac ?  $this->authServicePath : $this->authServiceNamespace,
                'targetFileName' => $this->authServiceName . '.php',
            ],
            [
                'sourceFileName' => 'BackendController.php',
                'targetPath' => $is_mac ?  $this->backendControllerPath : $this->backendControllerNamespace,
                'targetFileName' => $this->backendControllerName . '.php',
            ],
            // [
            //     'sourceFileName' => 'BackendForm.php',
            //     'targetPath' => $is_mac ?  $this->backendFormPath : $this->backendFormNamespace,
            //     'targetFileName' => $this->backendFormName . '.php',
            // ],
            [
                'sourceFileName' => 'BackendModel.php',
                'targetPath' => $is_mac ?  $this->backendModelPath : $this->backendModelNamespace,
                'targetFileName' => $this->backendModelName . '.php',
            ],
            [
                'sourceFileName' => 'BackendService.php',
                'targetPath' => $is_mac ?  $this->backendServicePath : $this->backendServiceNamespace,
                'targetFileName' => $this->backendServiceName . '.php',
            ],
            [
                'sourceFileName' => 'CommonModel.php',
                'targetPath' => $is_mac ?  $this->commonModelPath : $this->commonModelNamespace,
                'targetFileName' => $this->commonModelName . '.php',
            ],
            [
                'sourceFileName' => 'CommonService.php',
                'targetPath' => $is_mac ?  $this->commonServicePath : $this->commonServiceNamespace,
                'targetFileName' => $this->commonServiceName . '.php',
            ],
            [
                'sourceFileName' => 'MobileController.php',
                'targetPath' => $is_mac ?  $this->mobileControllerPath : $this->mobileControllerNamespace,
                'targetFileName' => $this->mobileControllerName . '.php',
            ],
            [
                'sourceFileName' => 'MobileModel.php',
                'targetPath' => $is_mac ?  $this->mobileModelPath : $this->mobileModelNamespace,
                'targetFileName' => $this->mobileModelName . '.php',
            ],
            [
                'sourceFileName' => 'MobileService.php',
                'targetPath' => $is_mac ?  $this->mobileServicePath : $this->mobileServiceNamespace,
                'targetFileName' => $this->mobileServiceName . '.php',
            ],
        ];
        return $fileSettings;
    }
}
