<?php

/**
 * 推广数据拉取
 */

namespace console\controllers;

use common\components\PromoteAdData;
use common\enums\AdsAccountRecordStatusEnum;
use common\enums\AdsAccountRecordTypeEnum;
use common\enums\PlatformEnum;
use common\helpers\DateHelper;
use common\models\common\AdsAccount;
use common\models\common\AdsAccountData;
use common\models\common\AdsAccountPullRecord;
use common\models\common\AdsAccountSub;
use common\models\common\Entity;
use common\queues\HourDataEnhanceJob;
use common\queues\AdsAgeGenderDataEnhanceJob;
use common\queues\promoteDataPull\PullFactory;
use common\services\promote\AdsAccountDataCityService;
use common\services\promote\AdsAccountDataService;
use console\controllers\data\AnalysisController;
use common\services\promote\HourDataEnhanceService;
use Exception;
use Yii;

class PromoteAdDataController extends AnalysisController
{
    protected function getDefaultDate()
    {
        return date('Y-m-d', time() - 86400);
    }
    /**
     * 刷新Refresh Token
     *
     * @throws Exception
     */
    public function actionRefreshAccessToken()
    {
        $arrPlatform = [PlatformEnum::TIKTOL, PlatformEnum::QUICKLY];
        foreach ($arrPlatform as $platform) {
            try {
                $tdAccount = AdsAccount::find()->where(['platform' => $platform])->groupBy('refresh_token')->all();
                if ($platform == PlatformEnum::TIKTOL) {
                    $model = (new \common\components\promoteData\Oceanengine());
                } else {
                    $model = (new \common\components\promoteData\Quickly());
                }
                
                foreach ($tdAccount as $item) {
                    $res = $model->getRefreshAccessToken($item);
                    if (!$res) echo '执行失败,时间：' . date('Y-m-d H:i:s', time()) . '失败原因：' . $model->error;
                }
            } catch (Exception $ex) {
                echo $ex->getMessage() . PHP_EOL;
            }
        }

        echo '执行成功,时间：' . date('Y-m-d H:i:s', time());
        return true;
    }

    /**
     * php yii promote-ad-data/account
     * 
     * @throws \yii\base\Exception
     */
    public function actionAccount()
    {
        $arr = [
            ['platform' => PlatformEnum::TIKTOL, 'className' => 'AccountJob'],
            ['platform' => PlatformEnum::WECHAT, 'className' => 'AccountJob'],
            ['platform' => PlatformEnum::ADQ, 'className' => 'AdqAccountJob']
        ];
        foreach ($arr as $item) {
            try {
                $pull = PullFactory::loadObject($item['platform'], $item['className']);
                $pull->setPlatform($item['platform'])->pullAccount();
                sleep(1);
            } catch (Exception $ex) {
                echo $item['platform'] . '报错：' . $ex->getMessage() . PHP_EOL;
            }
        }

        //删除3天前拉取记录
        AdsAccountPullRecord::deleteAll(['<', 'created_at', (strtotime(date('Y-m-d')) - 86400 * 3)]);

        echo '执行时间：' . date('Y-m-d H:i:s', time());
        return true;
    }

    /**
     * php yii promote-ad-data/balance
     * 
     * @throws \yii\base\Exception
     */
    public function actionBalance()
    {
        $arr = [
            ['platform' => PlatformEnum::TIKTOL, 'className' => 'BalanceDataJob'],
            ['platform' => PlatformEnum::WECHAT, 'className' => 'BalanceDataJob'],
            ['platform' => PlatformEnum::ADQ, 'className' => 'AdqBalanceDataJob'],
            ['platform' => PlatformEnum::QUICKLY, 'className' => 'BalanceDataJob'],
        ];

        $this->execute($arr, AdsAccountRecordTypeEnum::ACCOUNT_COST);
        return true;
    }

    /**
     * php yii promote-ad-data/hourly-data
     * 
     * @throws \yii\base\Exception
     */
    public function actionHourlyData()
    {
        $arr = [
            ['platform' => PlatformEnum::TIKTOL, 'className' => 'HourlyDataJob'],
            ['platform' => PlatformEnum::ADQ, 'className' => 'AdqHourlyDataJob'],
            ['platform' => PlatformEnum::QUICKLY, 'className' => 'HourlyDataJob'],
        ];

        $this->execute($arr, AdsAccountRecordTypeEnum::HOURLY_COST);
        return true;
    }

    /**
     * php yii promote-ad-data/daily-data
     * 
     * @throws \yii\base\Exception
     */
    public function actionDailyData()
    {
        $arr = [
            ['platform' => PlatformEnum::TIKTOL, 'className' => 'DailyDataJob'],
            ['platform' => PlatformEnum::WECHAT, 'className' => 'DailyDataJob'],
            ['platform' => PlatformEnum::ADQ, 'className' => 'AdqDailyDataJob'],
            ['platform' => PlatformEnum::QUICKLY, 'className' => 'DailyDataJob'],
        ];
        $this->execute($arr, AdsAccountRecordTypeEnum::DAILY_COST);
        return true;
    }

    /**
     * php yii promote-ad-data/city-data
     * 
     * @throws \yii\base\Exception
     */
    public function actionCityData()
    {
        $arr = [
            ['platform' => PlatformEnum::TIKTOL, 'className' => 'CityDataJob'],
            ['platform' => PlatformEnum::WECHAT, 'className' => 'CityDataJob'],
            ['platform' => PlatformEnum::ADQ, 'className' => 'AdqCityDataJob'],
            ['platform' => PlatformEnum::QUICKLY, 'className' => 'CityDataJob'],
        ];

        $this->execute($arr, AdsAccountRecordTypeEnum::CITY_COST);
        return true;
    }

    /**
     * php yii promote-ad-data/program-data
     * 
     * @throws \yii\base\Exception
     */
    public function actionProgramData()
    {
        $arr = [
            ['platform' => PlatformEnum::TIKTOL, 'className' => 'ProgramDataJob'],
            ['platform' => PlatformEnum::WECHAT, 'className' => 'ProgramDataJob'],
            ['platform' => PlatformEnum::ADQ, 'className' => 'AdqProgramDataJob'],
            ['platform' => PlatformEnum::QUICKLY, 'className' => 'ProgramDataJob'],
        ];

        $this->execute($arr, AdsAccountRecordTypeEnum::ADVERTISING_PROGRAM);
        return true;
    }

    /**
     * 获取线索
     * php yii promote-ad-data/clues-data
     *
     * @throws \yii\base\Exception
     */
    public function actionCluesData()
    {
        $arr = [
            ['platform' => PlatformEnum::WECHAT, 'className' => 'CluesDataJob'],
        ];
        $this->execute($arr, AdsAccountRecordTypeEnum::CLUES);
        return true;
    }

    /**
     * 获取素材数据
     * php yii promote-ad-data/material-data
     *
     * @throws \yii\base\Exception
     */
    public function actionMaterialData()
    {
        $arr = [
            ['platform' => PlatformEnum::TIKTOL, 'className' => 'MaterialDataJob'],
            ['platform' => PlatformEnum::ADQ, 'className' => 'AdqMaterialDataJob'],
        ];

        $this->execute($arr, AdsAccountRecordTypeEnum::MATRRIAL);
        return true;
    }

    /**
     * 获取素材标签
     * php yii promote-ad-data/material-label
     *
     * @throws \yii\base\Exception
     */
    public function actionMaterialLabel()
    {
        $arr = [
            ['platform' => PlatformEnum::TIKTOL, 'className' => 'MaterialLabelJob'],
        ];
        
        $this->execute($arr, AdsAccountRecordTypeEnum::MATERIAL_LABEL);
        return true;
    }

    /**
     * 获取年龄性别数据
     * php yii promote-ad-data/age-gender-data
     *
     * @throws \yii\base\Exception
     */
    public function actionAgeGenderData()
    {
        $arr = [
            ['platform' => PlatformEnum::TIKTOL, 'className' => 'AgeGenderDataJob'],
        ];

        $this->execute($arr, AdsAccountRecordTypeEnum::AGE_GENDER);
        exit();
    }

    public function execute($arr, $type)
    {
        foreach ($arr as $item) {
            try {
                $pull = PullFactory::loadObject($item['platform'], $item['className']);
                $pull->setPlatform($item['platform'])->setWay('auth')->pullData($type);
            } catch (Exception $ex) {
                echo $item['platform'] . '报错：' . $ex->getMessage() . PHP_EOL;
            }
            sleep(1);
        }
        echo '执行时间：' . date('Y-m-d H:i:s', time());
        return true;
    }

    /**
     * 每日消耗、城市消耗-历史数据矫正（拉取三天前的前三天数据）：php yii promote-ad-data/get-history-manual
     */
    public function actionGetHistoryManual()
    {
        $startTimeK = time();
        $pull_time_end = ($startTimeK - 86400 * 4);
        $pull_time_start = ($pull_time_end - 86400 * 2);

        try {
            $this->getAdqHistory($pull_time_start, $pull_time_end);

            $oceanEngine = new PromoteAdData();
            $platforms = [PlatformEnum::QUICKLY];
            foreach ($platforms as $platform) {
                $oceanEngine->historyManualDailyReport($pull_time_start, $pull_time_end, $platform);
            }

            //删除7天前完成的拉取记录
            AdsAccountPullRecord::deleteAll([
                'AND',
                ['<', 'created_at', (strtotime(date('Y-m-d')) - 86400 * 7)],
                ['status' => AdsAccountRecordStatusEnum::COMPLETE]
            ]);
        } catch (Exception $e) {
            echo "\n";
            print_r($e->getMessage());
            exit;
        }

        echo '执行成功，当前时间：' . date('Y-m-d H:i:s', $startTimeK) . '。执行：拉取计划“自动校正数据”的数据，耗时：' . (time() - $startTimeK) . '秒。    ';
        exit();
    }

    /**
     * ADQ（拉取三天前的前三天数据）
     *
     * @param $startDate
     * @param $endDate
     */
    private function getAdqHistory($startDate, $endDate)
    {
        $startDate = DateHelper::toDate($startDate, 'Y-m-d');
        $endDate = DateHelper::toDate($endDate, 'Y-m-d');


        //        for ($date = $startDate; $date <= $endDate;) {
        //            Tencent::getAdqDataByType('dateData', $date);
        //            Tencent::getAdqDataByType('cityData', $date);
        //            $date = date('Y-m-d', strtotime($date . " + 1 day"));
        //        }

        Yii::$app->notice->happy('ADQ重新拉取：' . $startDate . '到' . $endDate . '的数据');
    }

    /**
     * 检测广告账户数据是否70内无消耗数据，禁用账号
     *
     * @return void
     */
    public function actionDetect()
    {
        $seventyDaysAgo = date('Y-m-d', strtotime('-70 days'));
        $oneMonth = date('Y-m-d', strtotime('-30 days'));
        $seventyDaysAgo = date('Ymd', strtotime($seventyDaysAgo));
        $created_at = strtotime($oneMonth);
        $sql = "SELECT s.id
            FROM erp_ads_account_sub s 
            LEFT JOIN (
                SELECT ads_sub_id 
                FROM erp_ads_account_data 
                WHERE date >= {$seventyDaysAgo}
                GROUP BY ads_sub_id 
            ) d ON d.ads_sub_id = s.id
            WHERE d.ads_sub_id IS NULL AND s.`status` = 1 AND s.created_at <= {$created_at}
            AND s.responsible_id != 0";

        $list = Yii::$app->db->createCommand($sql)->queryAll();
        if (empty($list)) return '';
        foreach ($list as $item) {
            $consume = AdsAccountData::find()->where(['ads_sub_id' => $item['id']])->one();
            if (empty($consume)) {
                continue;
            }

            AdsAccountSub::updateAll(['status' => 0], ['id' => $item['id']]);
        }
    }

    /**
     * 完善每日消耗数据
     * 
     * 执行：php yii promote-ad-data/daily-data-perfect-data -d=2024-06-01 -ed=2024-06-02 -r=1
     */
    public function actionDailyDataPerfectData()
    {
        $entityIds = Entity::find()->select('id')->column();

        $time = strtotime($this->date);
        for (;$time <= strtotime($this->endDate);) {
            foreach ($entityIds as $entityId) {
                AdsAccountDataService::dailyDataPerfectData($this->date, $entityId);
            }

            echo '执行完成：' . $this->date . PHP_EOL;
            $time += 24 * 60 * 60;
            $this->date = date('Y-m-d', $time);
        }
    }

    /**
     * 完善城市拓展数据
     * 
     * 执行：php yii promote-ad-data/city-data-perfect-data -d=2024-06-01 -ed=2024-06-02 -r=1
     */
    public function actionCityDataPerfectData()
    {
        $entityIds = Entity::find()->select('id')->column();

        $time = strtotime($this->date);
        for (;$time <= strtotime($this->endDate);) {
            foreach ($entityIds as $entityId) {
                AdsAccountDataCityService::cityDataPerfectData($this->date, $entityId);
            }

            echo '执行完成：' . $this->date . PHP_EOL;
            $time += 24 * 60 * 60;
            $this->date = date('Y-m-d', $time);
        }
    }

    /**
     * 完善时段分析拓展数据
     * 
     * 执行：php yii promote-ad-data/hour-data-perfect-data -d=2024-06-01 -ed=2024-06-02 -r=1
     */
    public function actionHourDataPerfectData()
    {
        $entityIds = Entity::find()->select('id')->column();

        $time = strtotime($this->date);
        for (;$time <= strtotime($this->endDate);) {

            foreach ($entityIds as $entityId) {
                HourDataEnhanceJob::addJob($this->date, $entityId);
            }

            echo '执行完成：' . $this->date . PHP_EOL;
            $time += 24 * 60 * 60;
            $this->date = date('Y-m-d', $time);
        }
    }

    /**
     * 完善年龄性别维度数据
     * 
     * 执行：php yii promote-ad-data/age-gender-data-perfect-data -d=2024-06-01 -ed=2024-06-02 -r=1
     */
    public function actionAgeGenderDataPerfectData()
    {
        $entityIds = Entity::find()->select('id')->column();

        $time = strtotime($this->date);
        for (;$time <= strtotime($this->endDate);) {

            foreach ($entityIds as $entityId) {
                AdsAgeGenderDataEnhanceJob::addJob($this->date, $entityId);
            }

            echo '执行完成：' . $this->date . PHP_EOL;
            $time += 24 * 60 * 60;
            $this->date = date('Y-m-d', $time);
        }
    }
}
