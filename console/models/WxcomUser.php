<?php

namespace console\models;

use backendapi\services\complaint\ComplaintService;
use common\models\backend\Member;
use Exception;
use Yii;

/**
 * This is the model class for table "{{%wxcom_user}}".
 *
 * @property int $id 成员ID
 * @property string $name 成员名称
 * @property string $wxcom_user_id 企微成员ID
 * @property string $department 部门ID
 * @property string $wxcom_department 企微部门ID
 * @property string $position 职务
 * @property string $mobile 手机
 * @property int $gender 性别：1男2女
 * @property string $email 邮箱
 * @property string $avatar 头像
 * @property int $status 状态
 * @property int $enable 状态
 * @property int $isleader 是否负责人
 * @property string $extattr extattr
 * @property int $hide_mobile 是否隐藏手机
 * @property string $telephone 座机
 * @property string $order order
 * @property string $external_profile external_profile
 * @property int $wxcom_main_department 企微主部门ID
 * @property int $main_department 主部门ID
 * @property string $qr_code 企业二维码
 * @property string $alias 昵称
 * @property string $is_leader_in_dept is_leader_in_dept
 * @property string $address 地址
 * @property string $thumb_avatar 缩略头像
 * @property string $direct_leader 直接领导ID
 * @property int $com_id 企微公司ID
 * @property int $entity_id 企业ID
 * @property string $created_at 创建时间
 * @property int $created_by 创建人
 * @property string $updated_at 修改时间
 * @property int $updated_by 操作人
 */
class WxcomUser extends \yii\db\ActiveRecord
{
    const STATUS_ENABLE = 1;
    const STATUS_DISABLED = 2;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%wxcom_user}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name'], 'string', 'max' => 50],
            [['wxcom_user_id'], 'string', 'max' => 100],
            [['department'], 'string', 'max' => 100],
            [['wxcom_department'], 'string', 'max' => 100],
            [['position'], 'string', 'max' => 128],
            [['mobile'], 'string', 'max' => 20],
            [['email'], 'string', 'max' => 100],
            [['avatar'], 'string', 'max' => 200],
            [['extattr'], 'string', 'max' => 200],
            [['telephone'], 'string', 'max' => 20],
            [['order'], 'string', 'max' => 100],
            [['external_profile'], 'string', 'max' => 1000],
            [['qr_code'], 'string', 'max' => 200],
            [['alias'], 'string', 'max' => 50],
            [['is_leader_in_dept'], 'string', 'max' => 100],
            [['address'], 'string', 'max' => 200],
            [['thumb_avatar'], 'string', 'max' => 200],
            [['direct_leader'], 'string', 'max' => 200],
            [['name', 'com_id'], 'required'],
            [['gender', 'status', 'enable', 'isleader', 'hide_mobile', 'wxcom_main_department', 'main_department', 'com_id'], 'integer'],
            [['name', 'wxcom_user_id', 'department', 'wxcom_department', 'position', 'mobile', 'email', 'avatar', 'extattr', 'telephone', 'order', 'external_profile', 'qr_code', 'alias', 'is_leader_in_dept', 'address', 'thumb_avatar', 'direct_leader'], 'trim'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '成员ID',
            'name' => '成员名称',
            'wxcom_user_id' => '企微成员ID',
            'department' => '部门ID',
            'wxcom_department' => '企微部门ID',
            'position' => '职务',
            'mobile' => '手机',
            'gender' => '性别：1男2女',
            'email' => '邮箱',
            'avatar' => '头像',
            'status' => '状态',
            'enable' => '状态',
            'isleader' => '是否负责人',
            'extattr' => '',
            'hide_mobile' => '是否隐藏手机',
            'telephone' => '座机',
            'order' => '',
            'external_profile' => '',
            'wxcom_main_department' => '企微主部门ID',
            'main_department' => '主部门ID',
            'qr_code' => '企业二维码',
            'alias' => '昵称',
            'is_leader_in_dept' => '',
            'address' => '地址',
            'thumb_avatar' => '缩略头像',
            'direct_leader' => '直接领导ID',
            'com_id' => '企微公司ID',
            'entity_id' => '企业ID',
            'created_at' => '创建时间',
            'created_by' => '创建人',
            'updated_at' => '修改时间',
            'updated_by' => '操作人',
        ];
    }

    public function getMember()
    {
        return $this->hasOne(Member::className(), ['id' => 'user_id']);
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            //新增成员增加“投诉建议”
            try {
                ComplaintService::updateUserWxcom($this->id, $this->entity_id);
            } catch (Exception $e) {
                Yii::$app->feishuNotice->text('企微成员：' . $this->name . '，增加“投诉建议”失败原因：' . $e->getMessage());
            }
        }
        
        return parent::afterSave($insert, $changedAttributes);
    }
}
