<?php

/**
 * @var \omnilight\scheduling\Schedule $schedule
 */
$todayDate = date('Y-m-d');

$path = Yii::getAlias('@runtime') . '/logs/' . date('Ymd') . '.';

#推广自动拉取
$schedule->command('promote-ad-data/refresh-access-token')->cron('30 3,15 * * *');
$schedule->command('promote-ad-data/account')->cron('1 0 * * *');
$schedule->command('promote-ad-data/balance')->cron('1 0 * * *');
$schedule->command('promote-ad-data/hourly-data')->cron('0 12 * * *');
$schedule->command('promote-ad-data/daily-data')->cron('30 8 * * *');
$schedule->command('promote-ad-data/daily-data')->cron('0 13 * * *');
$schedule->command('promote-ad-data/city-data')->cron('40 8 * * *');
$schedule->command('promote-ad-data/city-data')->cron('10 13 * * *');
$schedule->command('promote-ad-data/program-data')->cron('*/30 7-23 * * *');
$schedule->command('promote-ad-data/material-data')->cron('0 9 * * *');
$schedule->command('promote-ad-data/material-label')->cron('0 10 * * *');
$schedule->command('promote-ad-data/age-gender-data')->cron('20 9 * * *');
$schedule->command('promote-ad-data/detect')->cron('0 3 * * *');
//完善每日数据
$schedule->command('promote-ad-data/daily-data-perfect-data')->cron('0 9 * * *');
$schedule->command('promote-ad-data/daily-data-perfect-data -d=' . $todayDate)->cron('*/50 * * * *');
$schedule->command('promote-ad-data/city-data-perfect-data')->cron('10 9 * * *');
$schedule->command('promote-ad-data/hour-data-perfect-data')->cron('30 12 * * *');
$schedule->command('promote-ad-data/age-gender-data-perfect-data')->cron('50 9 * * *');

/**
 * 订金到店转化分析加粉订人转化分析
 *
 * 每天 1:00 执行
 */
$schedule->command('data-trans-analysis/execute')->cron('0 1 * * *');
//每天 13:30 执行
$schedule->command('data-trans-analysis/city-execute')->cron('30 13 * * *');

/**
 * 落地页上报数据从 redis 备份到 MySQL
 *
 * 每天 4:00 ~ 5:00 之间，每 5 分钟执行
 */
$schedule->command('report/redis-report-backup')->cron('*/5 4-5 * * *');

/**
 * 客户手机号写入手机项目表
 *
 * 每 5 分钟执行
 */
// $schedule->command('bis/mobile-project')->cron('*/5 * * * *');

/**
 * 活码统计
 *
 * 每天 4:00 执行
 */
$schedule->command('wxcom/statistics-yesterday')->cron('0 4 * * *');

/**
 * 微信链路数据分析
 *
 * 每 10 分钟执行
 */
$schedule->command('matching-add-fans/wechat-servicer-statistics')->cron('*/10 * * * *');

/**
 * 加粉数及订金数统计基础表
 *
 * 每 10 分钟执行
 */
$schedule->command('matching-add-fans/cal-add-fans-and-deposit')->cron('*/10 * * * * ');

/**
 * 推广数据看板数据
 *
 * 每 5 分钟执行
 */
$schedule->command('servicer/cal-all')->cron('*/5 * * * *');

/**
 * 推广数据分析
 *
 * 每 5 分钟执行
 */
$schedule->command('promote/cal-bis-analysis')->cron('*/5 * * * *');

/**
 * 城市分析、门店分析汇总
 *
 * 每 5 分钟执行
 */
$schedule->command('data/city/cal-analysis')->cron('*/5 * * * *');

/**
 * 发送钉钉文件
 *
 * 每 5 分钟执行
 */
// $schedule->command('ding-send-file/send-cast-people-analysis')->cron('*/5 * * * *');

/**
 * 落地页统计
 *
 * 每 30 分钟执行
 */
$schedule->command('report/data-to-db')->cron('*/30 * * * *');

/**
 * 检测小程序状态
 *
 * 每五分钟执行
 */
// $filePath = $path . 'appletStatus.log';
// $schedule->command('check/applet-status')->cron('*/5 * * * *')->appendOutputTo($filePath);

/**
 * 拉取企微支付流水
 *
 * 每分钟执行
 */
$filePath = $path . 'crawling.log';
$schedule->command('external-pay/crawling-bill-list')->cron('*/1 * * * *')->appendOutputTo($filePath);

/**
 * 同步企微成员
 *
 * 每 5 分钟执行
 */
$filePath = $path . 'syncUserFromWxcom.log';
$schedule->command('wxcom/sync-user-from-wxcom')->cron('*/5 * * * *')->appendOutputTo($filePath);

/**
 * 同步企微外部标签
 *
 * 每分钟执行
 */
$filePath = $path . 'syncCusTagFromWxcom.log';
$schedule->command('wxcom/sync-cus-tag-from-wxcom')->cron('*/1 * * * *')->appendOutputTo($filePath);

/**
 * 扣款记录，将上个月的锁定
 *
 * 每月 5号 00:00 执行
 */
$filePath = $path . 'penaltyLockStatus.log';
$schedule->command('penalty/lock-status')->cron('0 0 5 * *')->appendOutputTo($filePath);

/**
 * 门店业绩分析
 *
 * 每 10 分钟执行
 */
$filePath = $path . 'StorePerformanceAnalysis.log';
$schedule->command('data/store/store-performance-analysis')->cron('*/10 * * * *')->appendOutputTo($filePath);

/**
 * 请求日志表维护
 *
 * 每天 23:00 执行
 */
$schedule->command('log/create-request-tables 5')->cron('0 23 * * *');
$schedule->command('log/del-request-tables 5')->cron('0 23 * * *');

/**
 * 公共日志表维护
 *
 * 每天 23:00 执行
 */
$schedule->command('log/create-common-tables 3')->cron('0 23 * * *');

/**
 * 检测服务器状态
 *
 * 五分钟执行
 */
// $schedule->exec('at -f /home/<USER>/scripts/erp/checkState.sh now')->cron('*/5 * * * *');

/**
 * 分析请求日志
 *
 * 每天 23:58 执行
 */
// $schedule->exec('at -f /home/<USER>/scripts/erp/calRequestLog.sh now')->cron('58 23 * * *');

/**
 * 清理日志文件
 *
 * 每天 00:00 执行
 */
$schedule->exec('at -f /home/<USER>/scripts/erp/clearLog.sh now')->cron('0 0 * * *');

/**
 * ipg处理
 */
$schedule->command('deal-data/ip-conversion')->cron('*/12 * * * *');

/**
 * 检测落地页审批状态
 *
 * 每分钟执行
 */
// $schedule->command('yxt/land-page-check-status')->cron('* * * * *');

/**
 * 已预约到店-同步到多维表格
 *
 * 每天凌晨2分执行
 */
$schedule->command('data/order/already-reserved-order')->cron('2 0 * * *');

/**
 * 每日发送门店上传环境照片提醒
 *
 * 每天 10:00 执行
 */
// $schedule->command('feishu-process/notify-store-pic')->cron('0 10 * * *');

/**
 * 提醒各门店每天发送目标到群里
 *
 * 每天 10:00 执行
 */
// $schedule->command('feishu-process/store-target')->cron('0 10 * * *');

/**
 * 提醒店长报出门店老师业绩
 *
 * 每天 13:00 执行
 */
// $schedule->command('feishu-process/store-data 1')->cron('0 13 * * *');

/**
 * 提醒店长报出门店老师业绩
 *
 * 每天 16:00 执行
 */
// $schedule->command('feishu-process/store-data 4')->cron('0 16 * * *');

/**
 * 提醒店长报出门店老师业绩
 *
 * 每周3周日 17:30 执行
 */
// $schedule->command('feishu-process/customer-visit')->cron('30 17 * * 3,7');

/**
 * 提醒推广汇报数据
 *
 * 每天11:58 17:58 执行
 */
$schedule->command('feishu-process/promotion-data')->cron('58 11,17 * * *');

/**
 * 客服数据汇报
 *
 * 每天 12点、16点、19点 执行
 */
$schedule->command('feishu-process/services-data')->cron('0 12,16,19 * * *');

/**
 * 每日发送门店业绩填写提醒
 *
 * 每天 19:00 执行
 */
// $schedule->command('feishu-process/notify-add-data')->cron('0 19 * * *');

/**
 * 每日发送店长发视频提醒
 *
 * 每天 9:50 执行
 */
// $schedule->command('feishu-process/notify-store-assembly day')->cron('50 9 * * *');

/**
 * 每日发送店长发视频提醒
 *
 * 每天 18:00 执行
 */
// $schedule->command('feishu-process/notify-store-assembly night')->cron('0 18 * * *');

/**
 * 每日发送店长发视频提醒
 *
 * 每天 9:00 执行
 */
// $schedule->command('feishu-process/notify-store-replay')->cron('0 21 * * *');

//处理企业微信消息
// $schedule->command('wxcom/deal-message')->cron('*/1 * * * *');

/**
 * 新客维度数据分析
 * 
 * 每日 1:20 执行
 */
$schedule->command('data/teacher-customer/cal-analysis')->cron('20 1 * * *');

/**
 * 继承客户
 */
$filePath = $path . 'customerInherit.log';
$schedule->command('wxcom/customer-inherit')->cron('* * * * *')->appendOutputTo($filePath);

/**
 * 广告备用金账户余额通知
 */
$schedule->command('feishu-process/send-balance-notice')->cron('30 9 * * *');
$schedule->command('feishu-process/send-balance-notice')->cron('0 12,17,20 * * *');

/**
 * 业务数据预警
 */
// $schedule->command('feishu-process/service-data-remid')->cron('0 18,20 * * *');

/**
 * 获客助手余量预警
 */
$schedule->command('feishu-process/cus-link-quato')->cron('0 * * * *');

/**
 * 获取客服数据统计 
 */
$schedule->command('feishu-process/get-servicer-data')->cron('0 */3 * * *');

/**
 * 同步老师月度业绩
 */
$schedule->command('feishu-process/teacher-month-amount-sync')->cron('0 15 * * *');
