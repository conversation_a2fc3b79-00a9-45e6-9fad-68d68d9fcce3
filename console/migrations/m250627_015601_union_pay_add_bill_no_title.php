<?php

use common\components\migrate\Migration;

/**
 * Class m250627_015601_union_pay_add_bill_no_title
 */
class m250627_015601_union_pay_add_bill_no_title extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            ALTER TABLE `erp_union_pay`
	            ADD COLUMN `bill_no_title` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '来源编号' COLLATE 'utf8_general_ci' AFTER `key`;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250627_015601_union_pay_add_bill_no_title cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250627_015601_union_pay_add_bill_no_title cannot be reverted.\n";

        return false;
    }
    */
}
