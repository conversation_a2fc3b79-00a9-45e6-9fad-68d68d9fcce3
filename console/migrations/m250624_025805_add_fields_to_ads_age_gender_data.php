<?php

use common\components\migrate\Migration;

/**
 * 为ads_age_gender_data表添加新字段
 */
class m250624_025805_add_fields_to_ads_age_gender_data extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // 添加新字段
        $this->addColumn('{{%ads_age_gender_data}}', 'new_store_cus_count', $this->integer(11)->notNull()->defaultValue(0)->comment('新客到店人数')->after('deep_convert_cost'));
        $this->addColumn('{{%ads_age_gender_data}}', 'amount', $this->decimal(10, 2)->notNull()->defaultValue(0.00)->comment('门店实收金额')->after('new_store_cus_count'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // 删除字段
        $this->dropColumn('{{%ads_age_gender_data}}', 'amount');
        $this->dropColumn('{{%ads_age_gender_data}}', 'new_store_cus_count');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250624_025805_add_fields_to_ads_age_gender_data cannot be reverted.\n";

        return false;
    }
    */
}
