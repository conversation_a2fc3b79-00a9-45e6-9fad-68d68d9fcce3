<?php

use common\components\migrate\Migration;

/**
 * Class m250620_081421_ads_report_log
 */
class m250620_081421_ads_report_log extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_ads_report_log` (
            	`id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
            	`channel_id` SMALLINT(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'promote_channel表ID',
            	`order_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'order_header表ID',
            	`sub_advertiser_id` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '广告子账户ID' COLLATE 'utf8_general_ci',
            	`customer_user_id` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '企微客户成员表ID：erp_wxcom_cus_customer_user表ID',
            	`type` SMALLINT(5) UNSIGNED NOT NULL DEFAULT '0' COMMENT '上报类型',
            	`is_auto_report` TINYINT(4) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否自动上报（1-是 0-否）',
            	`report_params` TEXT NOT NULL COMMENT '上报请求参数' COLLATE 'utf8_general_ci',
            	`report_result` TEXT NOT NULL COMMENT '上报响应结果' COLLATE 'utf8_general_ci',
            	`report_status` TINYINT(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '上报状态（1-成功 2-失败）',
            	`created_at` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
            	PRIMARY KEY (`id`),
            	INDEX `channel_and_type` (`channel_id`, `type`),
            	INDEX `sub_advertiser_id` (`sub_advertiser_id`),
            	INDEX `customer_user_id` (`customer_user_id`)
            )
            COMMENT='广告渠道上报日志表'
            COLLATE='utf8_general_ci'
            ;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250620_081421_ads_report_log cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250620_081421_ads_report_log cannot be reverted.\n";

        return false;
    }
    */
}
