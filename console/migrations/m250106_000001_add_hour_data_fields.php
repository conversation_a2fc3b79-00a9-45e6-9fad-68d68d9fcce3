<?php

use yii\db\Migration;

/**
 * 时段分析功能扩展 - 为ads_account_data_hour表添加新字段
 */
class m250106_000001_add_hour_data_fields extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // 添加新字段
        $this->addColumn('{{%ads_account_data_hour}}', 'deposit_count', $this->integer(11)->notNull()->defaultValue(0)->comment('订金数')->after('convert_rate'));
        $this->addColumn('{{%ads_account_data_hour}}', 'new_store_cus_count', $this->integer(11)->notNull()->defaultValue(0)->comment('新客到店人数')->after('deposit_count'));
        $this->addColumn('{{%ads_account_data_hour}}', 'amount', $this->decimal(10, 2)->notNull()->defaultValue(0.00)->comment('门店实收金额')->after('new_store_cus_count'));
        $this->addColumn('{{%ads_account_data_hour}}', 'actual_consume', $this->decimal(10, 2)->notNull()->defaultValue(0.00)->comment('实际消耗')->after('amount'));
        
        // 添加性能优化索引
        // $this->createIndex('idx_date_hour_entity', '{{%ads_account_data_hour}}', ['date', 'hour', 'entity_id']);
        // $this->createIndex('idx_ads_sub_date', '{{%ads_account_data_hour}}', ['ads_sub_id', 'date']);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // 删除索引
        // $this->dropIndex('idx_date_hour_entity', '{{%ads_account_data_hour}}');
        // $this->dropIndex('idx_ads_sub_date', '{{%ads_account_data_hour}}');
        
        // 删除字段
        $this->dropColumn('{{%ads_account_data_hour}}', 'deposit_count');
        $this->dropColumn('{{%ads_account_data_hour}}', 'new_store_cus_count');
        $this->dropColumn('{{%ads_account_data_hour}}', 'amount');
        $this->dropColumn('{{%ads_account_data_hour}}', 'actual_consume');
    }
}