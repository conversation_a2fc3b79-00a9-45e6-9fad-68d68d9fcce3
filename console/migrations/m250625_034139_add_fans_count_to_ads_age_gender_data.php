<?php

use common\components\migrate\Migration;

/**
 * Class m250625_034139_add_fans_count_to_ads_age_gender_data
 */
class m250625_034139_add_fans_count_to_ads_age_gender_data extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%ads_age_gender_data}}', 'add_fans_count', $this->integer(11)->notNull()->defaultValue(0)->comment('加粉数')->after('amount'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%ads_age_gender_data}}', 'add_fans_count');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250625_034139_add_fans_count_to_ads_age_gender_data cannot be reverted.\n";

        return false;
    }
    */
}
