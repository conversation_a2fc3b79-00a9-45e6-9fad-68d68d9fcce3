<?php

use common\components\migrate\Migration;

/**
 * Class m250613_024534_procurement_pay_add_remark
 */
class m250613_024534_procurement_pay_add_remark extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%procurement_pay}}', 'remark', 'varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT \'\' COMMENT \'备注\' AFTER `status`');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%procurement_pay}}', 'remark');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250613_024534_procurement_pay_add_remark cannot be reverted.\n";

        return false;
    }
    */
}
