<?php

use common\components\migrate\Migration;

/**
 * Class m250614_064449_create_erp_store_schedule
 */
class m250614_064449_create_erp_store_schedule extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_store_schedule` (
            	`id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
            	`sn` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '编号' COLLATE 'utf8_general_ci',
            	`store_id` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'store表ID',
            	`date` VARCHAR(14) NOT NULL DEFAULT '' COMMENT '日期：yyyymmdd' COLLATE 'utf8_general_ci',
            	`teacher_num` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '老师人数',
            	`customer_service_num` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '接待客户数',
            	`reschedule_num` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '改期人数',
            	`remark` VARCHAR(250) NOT NULL DEFAULT '' COMMENT '备注' COLLATE 'utf8_general_ci',
            	`created_at` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
            	UNIQUE INDEX `sn` (`sn`),
            	PRIMARY KEY (`id`)
            )
            COMMENT='门店排客情况表'
            COLLATE='utf8_general_ci'
            ;
            ALTER TABLE `erp_store_schedule` 
                ADD INDEX `idx_date_store_id` (`date`, `store_id`) USING BTREE;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250614_064449_create_erp_store_schedule cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250614_064449_create_erp_store_schedule cannot be reverted.\n";

        return false;
    }
    */
}
