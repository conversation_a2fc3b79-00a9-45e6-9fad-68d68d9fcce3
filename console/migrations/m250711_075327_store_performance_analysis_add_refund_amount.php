<?php

use common\components\migrate\Migration;

/**
 * Class m250711_075327_store_performance_analysis_add_refund_amount
 */
class m250711_075327_store_performance_analysis_add_refund_amount extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            ALTER TABLE `erp_data_store_performance_analysis`
            	ADD COLUMN `refund_amount` DECIMAL(10,2) NULL DEFAULT '0.00' COMMENT '退款金额' AFTER `received_amount`;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250711_075327_store_performance_analysis_add_refund_amount cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250711_075327_store_performance_analysis_add_refund_amount cannot be reverted.\n";

        return false;
    }
    */
}
