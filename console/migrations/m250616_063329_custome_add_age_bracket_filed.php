<?php

use common\components\migrate\Migration;

/**
 * Class m250616_063329_custome_add_age_bracket_filed
 */
class m250616_063329_custome_add_age_bracket_filed extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
         ALTER TABLE erp_customer ADD age_bracket TINYINT(4) NOT NULL DEFAULT 0 COMMENT '年龄段' AFTER gender;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250616_063329_custome_add_age_bracket_filed cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250616_063329_custome_add_age_bracket_filed cannot be reverted.\n";

        return false;
    }
    */
}
