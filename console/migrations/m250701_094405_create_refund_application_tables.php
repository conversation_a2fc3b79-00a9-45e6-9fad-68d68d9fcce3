<?php

use common\components\migrate\Migration;

/**
 * Class m250701_094405_create_refund_application_tables
 */
class m250701_094405_create_refund_application_tables extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_refund_application` (
              `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
              `application_no` varchar(50) NOT NULL DEFAULT '' COMMENT '申请单号',
              `cus_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID，customer表ID',
              `store_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '门店ID，store表ID',
              `total_order_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
              `total_refund_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '申请退款总金额',
              `refund_reason` text COMMENT '退款原因',
              `attachment_urls` text COMMENT '附件URL（JSON格式）',
              `bank_account_type` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '账户类型：1-个人，2-对公', 
              `bank_account_info` text COMMENT '银行账号信息（JSON格式）',
              `process_instance_id` varchar(100) NOT NULL DEFAULT '' COMMENT '使用的审批模板',
              `process_detail` text COMMENT '审批流程明细',
              `status` tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '审批状态',
              `approval_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审批完成时间',
              `teacher_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '老师ID，backend_member表ID',
              `cashier_confirm` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '出纳确认（飞书回调数据）' COLLATE 'utf8_general_ci',
              `entity_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '企业ID',
              `created_by` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
              `created_at` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
              `updated_at` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
              PRIMARY KEY (`id`),
              UNIQUE KEY `uk_application_no` (`application_no`),
              KEY `idx_cus_id` (`cus_id`),
              KEY `idx_store_id` (`store_id`),
              KEY `idx_status` (`status`)
            );       

            CREATE TABLE `erp_refund_application_detail` (
              `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
              `application_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '申请主表ID',
              `order_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID，order_header表ID',
              `order_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '订单金额',
              `refund_amount` decimal(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '退款金额',
              `product_record_ids` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '客户项目记录ID，customer_product_record表ID' COLLATE 'utf8_general_ci',
              `created_at` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
              `updated_at` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
              PRIMARY KEY (`id`),
              KEY `idx_application_id` (`application_id`),
              KEY `idx_order_id` (`order_id`)
            );       
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250701_094405_create_refund_application_tables cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250701_094405_create_refund_application_tables cannot be reverted.\n";

        return false;
    }
    */
}
