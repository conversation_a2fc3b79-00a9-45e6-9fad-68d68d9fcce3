<?php

use common\components\migrate\Migration;

/**
 * Class m250611_012045_add_hour_data_add_fans
 */
class m250611_012045_add_hour_data_add_fans extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('{{%ads_account_data_hour}}', 'add_fans_count', $this->integer(11)->notNull()->defaultValue(0)->comment('加粉数')->after('actual_consume'));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%ads_account_data_hour}}', 'add_fans_count');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250611_012045_add_hour_data_add_fans cannot be reverted.\n";

        return false;
    }
    */
}
