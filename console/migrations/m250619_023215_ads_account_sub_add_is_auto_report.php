<?php

use common\components\migrate\Migration;

/**
 * Class m250619_023215_ads_account_sub_add_is_auto_report
 */
class m250619_023215_ads_account_sub_add_is_auto_report extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            ALTER TABLE erp_ads_account_sub ADD `is_auto_report` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '自动上报' AFTER is_heavy_powder_report;
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250619_023215_ads_account_sub_add_is_auto_report cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250619_023215_ads_account_sub_add_is_auto_report cannot be reverted.\n";

        return false;
    }
    */
}
