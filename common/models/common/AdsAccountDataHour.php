<?php

namespace common\models\common;

use Yii;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "{{%ads_account_data_hour}}".
 *
 * @property int $id
 * @property string $date 日期：yyyymmdd格式
 * @property int $hour 小时
 * @property int $ads_sub_id 子账户id
 * @property string $cost 消耗
 * @property int $show 展示数
 * @property string $avg_show_cost 千次展示费用
 * @property int $click 点击数
 * @property string $avg_click_cost 平均点击单价
 * @property string $ctr 点击率
 * @property int $convert 转化数
 * @property string $convert_cost 转化成本
 * @property string $convert_rate 转化率
 * @property int $deposit_count 订金数
 * @property int $new_store_cus_count 新客到店人数
 * @property string $amount 门店实收
 * @property string $actual_consume 实际消耗
 * @property int $add_fans_count 加粉数
 * @property int $entity_id 企业ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 */
class AdsAccountDataHour extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%ads_account_data_hour}}';
    }

    /**
     * @return array
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['ads_sub_id', 'hour', 'show', 'click', 'convert', 'deposit_count', 'new_store_cus_count', 'entity_id', 'created_at', 'updated_at', 'add_fans_count'], 'integer'],
            [['cost', 'avg_show_cost', 'avg_click_cost', 'ctr', 'convert_cost', 'convert_rate', 'amount', 'actual_consume'], 'number'],
            [['date'], 'string', 'max' => 14],
            [['deposit_count', 'new_store_cus_count', 'add_fans_count'], 'default', 'value' => 0],
            [['amount', 'actual_consume'], 'default', 'value' => 0.00],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'date' => '日期',
            'hour' => '小时',
            'ads_sub_id' => '子账户id',
            'cost' => '账面消耗',
            'show' => '展示数',
            'avg_show_cost' => '千次展示费用',
            'click' => '点击数',
            'avg_click_cost' => '平均点击单价',
            'ctr' => '点击率',
            'convert' => '转化数',
            'convert_cost' => '转化成本',
            'convert_rate' => '转化率',
            'deposit_count' => '订金数',
            'new_store_cus_count' => '新客到店人数',
            'amount' => '门店实收',
            'actual_consume' => '实际消耗',
            'add_fans_count' => '加粉数',
            'entity_id' => '企业ID',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    public function getAccountSub()
    {
        return $this->hasOne(AdsAccountSub::class, ['id' => 'ads_sub_id']);
    }
}
