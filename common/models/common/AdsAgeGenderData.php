<?php

namespace common\models\common;

use common\helpers\DateHelper;
use yii\behaviors\TimestampBehavior;

/**
 * This is the model class for table "{{%ads_age_gender_data}}".
 *
 * @property int $id
 * @property string $date 日期：yyyymmdd格式
 * @property int $ads_sub_id 子账户id
 * @property string $age 年龄
 * @property string $gender 性别
 * @property double $stat_cost 消耗
 * @property int $show_cnt 展示数
 * @property double $cpm_platform 平均千次展现费用(元)  广告平均每一千次展现所付出的费用，计算公式是：总消耗/展示数*1000
 * @property int $click_cnt 点击数
 * @property double $ctr 点击率
 * @property double $cpc_platform 平均点击单价（元）
 * @property int $convert_cnt 转化数
 * @property double $conversion_rate 转化率
 * @property double $conversion_cost 平均转化成本
 * @property int $deep_convert_cnt 深度转化数
 * @property double $deep_convert_rate 深度转化率
 * @property double $deep_convert_cost 深度转成本
 * @property int $new_store_cus_count 新客到店人数
 * @property double $amount 门店实收金额
 * @property int $add_fans_count 加粉数
 * @property int $entity_id 企业ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 */
class AdsAgeGenderData extends \common\models\Base
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return '{{%ads_age_gender_data}}';
    }

    /**
     * @return array
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['ads_sub_id', 'show_cnt', 'click_cnt', 'convert_cnt', 'deep_convert_cnt', 'new_store_cus_count', 'add_fans_count', 'entity_id', 'created_at', 'updated_at'], 'integer'],
            [['stat_cost', 'cpm_platform', 'ctr', 'cpc_platform', 'conversion_rate', 'conversion_cost', 'deep_convert_rate', 'deep_convert_cost', 'amount'], 'number'],
            [['date'], 'string', 'max' => 14],
            [['age',], 'string', 'max' => 32],
            [['gender',], 'string', 'max' => 10],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'date' => '日期',
            'ads_sub_id' => '子账户id',
            'age' => '年龄',
            'gender' => '性别',
            'stat_cost' => '消耗',
            'show_cnt' => '展示数',
            'cpm_platform' => '千展',
            'click_cnt' => '点击数',
            'ctr' => '点击率',
            'cpc_platform' => '平均点击单价（元）',
            'convert_cnt' => '转化数',
            'conversion_rate' => '转化率',
            'conversion_cost' => '平均转化成本',
            'deep_convert_cnt' => '深度转化数',
            'deep_convert_rate' => '深度转化率',
            'deep_convert_cost' => '深度转成本',
            'new_store_cus_count' => '新客到店人数',
            'amount' => '门店实收金额',
            'add_fans_count' => '加粉数',
            'entity_id' => '企业ID',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    public function getAccountSub()
    {
        return $this->hasOne(AdsAccountSub::class, ['id' => 'ads_sub_id']);
    }

    public function getDateText()
    {
        if (empty($this->date)) {
            return '';
        }
        return DateHelper::toDate(strtotime($this->date), 'Y-m-d');
    }

    /**
     * 格式化性别数据
     * 将不同的性别表示方式统一格式化为标准的"男"和"女"
     *
     * @param string $gender 原始性别数据
     * @return string 格式化后的性别："男"、"女"或原始值
     */
    public static function formatGender($gender)
    {
        if (in_array($gender, ['男', '男性'])) {
            return '男';
        } elseif (in_array($gender, ['女', '女性'])) {
            return '女';
        } else {
            return $gender;
        }
    }

    /**
     * 格式化年龄数据
     * 将不同的年龄表示方式统一格式化为标准的"60岁以上"
     *
     * @param string $age 原始年龄数据
     * @return string 格式化后的年龄："60岁以上"或原始值
     */
    public static function formatAge($age)
    {
        if (in_array($age, ['60岁及以上'])) {
            return '60岁以上';
        } else {
            return $age;
        }
    }
}
