<?php

namespace common\models\feishu;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\helpers\ArrayHelper;
use services\UserService;
use common\models\backend\Store;
use Yii;

/**
 * This is the model class for table "{{%store_schedule}}".
 *
 * @property int $id ID
 * @property string $sn 编号
 * @property int $store_id store表ID
 * @property string $date 日期：yyyymmdd
 * @property int $teacher_num 老师人数
 * @property int $customer_service_num 接待客户数
 * @property int $reschedule_num 改期人数
 * @property string $remark 备注
 * @property int $created_at 创建时间
 */
class StoreSchedule extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%store_schedule}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sn', 'store_id', 'date'], 'required'],
            [['store_id', 'teacher_num', 'customer_service_num', 'reschedule_num', 'created_at'], 'integer'],
            [['teacher_num', 'customer_service_num', 'reschedule_num'], 'integer', 'min' => 0],
            [['store_id'], 'integer', 'min' => 1],
            [['sn'], 'string', 'max' => 50],
            [['date'], 'string', 'max' => 14],
            [['remark'], 'string', 'max' => 250],
            [['sn', 'date', 'remark'], 'trim'],
            [['sn'], 'unique'],
            [['date'], 'match', 'pattern' => '/^\d{8}$/', 'message' => '日期格式必须为yyyymmdd'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sn' => '编号',
            'store_id' => 'store表ID',
            'date' => '日期',
            'teacher_num' => '老师人数',
            'customer_service_num' => '接待客户数',
            'reschedule_num' => '改期人数',
            'remark' => '备注',
            'created_at' => '创建时间',
        ];
    }

    /**
     * 场景配置
     * @return array
     */
    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['sn', 'store_id', 'date', 'teacher_num', 'customer_service_num', 'reschedule_num', 'remark'],
        ];
    }

    /**
     * 查询构建器
     * @return \yii\db\ActiveQuery
     */
    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            // 可以根据用户权限添加查询条件
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
        }

        return parent::beforeSave($isInsert);
    }

    /**
     * 获取创建时间文本
     * @return string
     */
    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    /**
     * 关联门店信息
     * @return \yii\db\ActiveQuery
     */
    public function getStore()
    {
        return $this->hasOne(\common\models\backend\Store::class, ['id' => 'store_id']);
    }

    /**
     * 获取门店名称
     * @return string
     */
    public function getStoreName()
    {
        return $this->store ? $this->store->store_name : '';
    }

    /**
     * 获取按城市分组的门店排班统计数据
     * @param string $startTime 开始时间（Ymd格式）
     * @param string $endTime 结束时间（Ymd格式）
     * @param array $cityIds 城市ID数组（可选，为空则查询所有城市）
     * @return array 返回包含统计数据和汇总数据的数组
     */
    public static function getCityStoreScheduleStats($startTime, $endTime, $cityIds = [])
    {
        $query = Store::find()
            ->alias('s')
            ->select('s.city_id, SUM(ss.teacher_num) as teacher_num, SUM(ss.customer_service_num) as customer_service_num, SUM(ss.reschedule_num) as reschedule_num')
            ->leftJoin(['ss' => static::tableName()], 'ss.store_id = s.id')
            ->where(['between', 'ss.date', $startTime, $endTime])
            ->groupBy('s.city_id');
            
        // 如果指定了城市ID，则添加过滤条件
        if (!empty($cityIds)) {
            $query->andWhere(['s.city_id' => $cityIds]);
        }
            
        $storeScheduleData = $query->asArray()->all();
        
        // 以城市ID为键重新索引数据
        $indexedData = ArrayHelper::index($storeScheduleData, 'city_id');
        
        // 计算汇总数据
        $sumData = [
            'teacher_num' => array_sum(array_column($storeScheduleData, 'teacher_num')),
            'customer_service_num' => array_sum(array_column($storeScheduleData, 'customer_service_num')),
            'reschedule_num' => array_sum(array_column($storeScheduleData, 'reschedule_num'))
        ];
        
        return [
            'data' => $indexedData,
            'summary' => $sumData
        ];
    }
} 