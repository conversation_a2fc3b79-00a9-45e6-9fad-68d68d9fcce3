<?php

namespace common\models;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\enums\StatusEnum;
use common\helpers\Tool;
use common\enums\pay\UnionPayBillNoTitleEnum;
use common\models\pay\UnionPayTag;
use Yii;

/**
 * This is the model class for table "{{%union_pay}}".
 *
 * @property int $id ID
 * @property string $name 分店名称
 * @property string $licence 商户名称
 * @property string $app_id 商户appId
 * @property string $app_key 商户appKey
 * @property string $key 通讯密钥
 * @property string $bill_no_title 来源编号
 * @property string $mid C扫B商户号
 * @property string $tid C扫B终端号
 * @property string $merchantCode B扫C支付：商户编号
 * @property string $terminalCode B扫C支付：终端编号
 * @property int $type 使用类型：0 门店 1客服
 * @property string $pay_tag_ids 银联收款标签IDs，union_pay_tag 表:ID
 * @property int $status 状态：0 禁用 1 启用
 * @property string $remark 备注
 * @property int $sort 排序：从大到小
 * @property int $entity_id 企业ID
 * @property int $created_by 创建人
 * @property int $updated_by 操作人
 * @property int $created_at 创建时间
 * @property int $updated_at 修改时间
 */
class UnionPay extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%union_pay}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'app_id', 'app_key', 'mid', 'tid', 'merchantCode', 'terminalCode', 'remark'], 'trim'],
            [['name'], 'string', 'max' => 50],
            [['app_id'], 'string', 'max' => 40],
            [['app_key'], 'string', 'max' => 40],
            [['key'], 'string', 'max' => 64],
            [['bill_no_title'], 'string', 'max' => 50],
            [['mid', 'merchantCode', 'terminalCode'], 'string', 'max' => 20],
            [['tid'], 'string', 'max' => 8],
            [['remark', 'licence'], 'string', 'max' => 100],
            [['name', 'licence', 'app_id', 'app_key', 'mid', 'tid', 'bill_no_title'], 'required'],
            [['status', 'sort', 'type'], 'integer'],
            ['status', 'in', 'range' => StatusEnum::getKeys()],
            ['bill_no_title', 'in', 'range' => UnionPayBillNoTitleEnum::getKeys()],
            ['type', 'in', 'range' => [0, 1]],
            [['pay_tag_ids'], 'filter', 'filter' => [Tool::class, 'getArrayToString']],
            ['pay_tag_ids', 'validatePayTagIds'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => '分店名称',
            'licence' => '商户名称',
            'app_id' => '商户appId',
            'app_key' => '商户appKey',
            'key' => '通讯密钥',
            'bill_no_title' => '来源编号',
            'mid' => 'C扫B商户号',
            'tid' => 'C扫B终端号',
            'merchantCode' => 'B扫C商户号',
            'terminalCode' => 'B扫C终端号',
            'type' => '使用类型',
            'pay_tag_ids' => '银联收款标签IDs',
            'status' => '状态',
            'remark' => '备注',
            'sort' => '排序',
            'entity_id' => '企业ID',
            'created_by' => '创建人',
            'updated_by' => '操作人',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['name', 'licence', 'app_id', 'app_key', 'key', 'bill_no_title', 'mid', 'tid', 'merchantCode', 'terminalCode', 'type', 'pay_tag_ids', 'status', 'remark', 'sort'],
        ];
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
        }
        $this->updated_at = time();

        return parent::beforeSave($isInsert);
    }

    /**
     * entity_id name 分组唯一
     *
     * @param [type] $attribute
     * @param [type] $params
     * @return void
     */
    public function uniqueNameWithEntity($attribute)
    {
        $nameExits = $this->find()
            ->andWhere(['=', 'name', $this->name])
            ->andWhere(['=', 'licence', $this->licence])
            ->andWhere(['=', 'entity_id', Yii::$app->user->identity->current_entity_id])
            ->andWhere(['<>', 'id', intval($this->id)])
            ->exists();

        if ($nameExits) {
            $this->addError($attribute, '该分店名称已存在');
        }
    }

    public function validatePayTagIds($attribute)
    {
        if ($this->pay_tag_ids) {
            $pay_tag_ids = Tool::getStringToArray($this->pay_tag_ids);
            foreach ($pay_tag_ids as $id) {
                if (!UnionPayTag::find()->select('id')->where(['id' => $id, 'status' => StatusEnum::ENABLED])->one()) {
                    $this->addError($attribute, '选择的标签不存在，请重新选择');
                }
            }
        }

        return true;
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    public function getUpdatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'updated_by']);
    }

    public function getTagNameArray()
    {
        $tagNames = [];
        if ($this->pay_tag_ids) {
            $tagIds = Tool::getStringToArray($this->pay_tag_ids);
            $tags = UnionPayTag::find()->select('name')->where(['in', 'id', $tagIds])->all();
            $tagNames = array_column($tags, 'name');
        }
        return $tagNames;
    }
}
