<?php

namespace common\models\order;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%refund_application_detail}}".
 *
 * @property int $id id
 * @property int $application_id 申请主表ID
 * @property int $order_id 订单ID，order_header表ID
 * @property double $order_amount 订单金额
 * @property double $refund_amount 退款金额
 * @property string $product_record_ids 客户项目记录ID，customer_product_record表ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 */
class RefundApplicationDetail extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%refund_application_detail}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['application_id'], 'integer', 'min' => 0],
            [['order_id'], 'integer', 'min' => 0],
            [['order_amount'], 'double', 'min' => 0],
            [['refund_amount'], 'double', 'min' => 0],
            [['application_id', 'order_id', 'order_amount', 'refund_amount'], 'required'],
            [['application_id', 'order_id'], 'integer'],
            [['order_amount', 'refund_amount'], 'double'],
            [['product_record_ids'], 'string', 'max' => 500],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'id',
            'application_id' => '申请主表ID',
            'order_id' => '订单ID',
            'order_amount' => '订单金额',
            'refund_amount' => '退款金额',
            'product_record_ids' => '客户项目记录ID',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['application_id', 'order_id', 'order_amount', 'refund_amount', 'product_record_ids'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
        }
        $this->updated_at = time();

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }
}
