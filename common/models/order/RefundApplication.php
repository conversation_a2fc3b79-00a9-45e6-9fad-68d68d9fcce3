<?php

namespace common\models\order;

use common\enums\AgentAccountTypeEnum;
use common\enums\order\RefundApplicationStatusEnum;
use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\helpers\ResultHelper;
use services\UserService;
use common\models\backend\Store;
use common\models\backend\Member;
use common\models\Customer;
use Yii;

/**
 * This is the model class for table "{{%refund_application}}".
 *
 * @property int $id id
 * @property string $application_no 申请单号
 * @property int $cus_id 客户ID，customer表ID
 * @property int $store_id 门店ID，store表ID
 * @property double $total_order_amount 订单总金额
 * @property double $total_refund_amount 申请退款总金额
 * @property string $refund_reason 退款原因
 * @property string $attachment_urls 附件URL（JSON格式）
 * @property int $bank_account_type 账户类型：1-个人，2-对公
 * @property string $bank_account_info 银行账号信息（JSON格式）
 * @property string $process_instance_id 使用的审批模板
 * @property string $process_detail 审批流程明细
 * @property int $status 审批状态
 * @property int $approval_time 审批完成时间
 * @property int $teacher_id 老师ID，backend_member表ID
 * @property string $cashier_confirm 出纳确认（飞书回调数据）
 * @property int $entity_id 企业ID
 * @property int $created_by 创建人
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 */
class RefundApplication extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%refund_application}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['application_no'], 'string', 'max' => 50],
            [['cus_id'], 'integer', 'min' => 0],
            [['store_id'], 'integer', 'min' => 0],
            [['total_order_amount', 'total_refund_amount'], 'double'],
            [['total_refund_amount'], 'compare', 'compareValue' => 0, 'operator' => '>', 'message' => '退款金额必须大于0'],
            [['bank_account_type'], 'integer', 'min' => 0],
            [['process_instance_id'], 'string', 'max' => 100],
            [['status'], 'integer', 'min' => 0],
            [['approval_time'], 'integer', 'min' => 0],
            [['teacher_id'], 'integer', 'min' => 0],
            [['cus_id', 'store_id', 'total_order_amount', 'total_refund_amount', 'bank_account_type', 'status'], 'required'],
            [['cus_id', 'store_id', 'bank_account_type', 'status', 'approval_time', 'teacher_id'], 'integer'],
            [['application_no', 'refund_reason', 'attachment_urls', 'bank_account_info', 'process_instance_id', 'process_detail', 'cashier_confirm'], 'trim'],
            [['refund_reason', 'attachment_urls', 'bank_account_info', 'process_detail', 'cashier_confirm'], 'string'],
            [['bank_account_type'], 'in', 'range' => AgentAccountTypeEnum::getKeys()],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'id',
            'application_no' => '申请单号',
            'cus_id' => '客户ID',
            'store_id' => '门店ID',
            'total_order_amount' => '订单总金额',
            'total_refund_amount' => '申请退款总金额',
            'refund_reason' => '退款原因',
            'attachment_urls' => '附件URL',
            'bank_account_type' => '账户类型',
            'bank_account_info' => '银行账号信息',
            'process_instance_id' => '使用的审批模板',
            'process_detail' => '审批流程明细',
            'status' => '审批状态',
            'approval_time' => '审批完成时间',
            'teacher_id' => '老师ID',
            'cashier_confirm' => '出纳确认',
            'entity_id' => '企业ID',
            'created_by' => '创建人',
            'created_at' => '创建时间',
            'updated_at' => '更新时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['application_no', 'cus_id', 'store_id', 'total_order_amount', 'total_refund_amount', 'refund_reason', 'attachment_urls', 'bank_account_type', 'bank_account_info', 'process_instance_id', 'process_detail', 'status', 'approval_time', 'teacher_id', 'cashier_confirm'],
        ];
    }

    /**
     * 保存前处理
     */
    public function beforeSave($insert)
    {
        if ($insert) {
            $this->created_at = time();
            $this->entity_id = Yii::$app->user->identity->entity_id ?? 1;
            $this->created_by = Yii::$app->user->id;
            
            // 生成申请单号
            if (empty($this->application_no)) {
                $this->application_no = $this->generateApplicationNo();
            }
        }
        
        $this->updated_at = time();
        
        return parent::beforeSave($insert);
    }

    /**
     * 生成申请单号
     * @return string
     */
    private function generateApplicationNo()
    {
        return 'TK' . date('YmdHis', time()) . mt_rand(0, 9) . mt_rand(0, 9) . mt_rand(0, 9) . mt_rand(0, 9) . mt_rand(0, 9) . mt_rand(0, 9) . mt_rand(0, 9);
    }

    public static function find()
    {
        $query = parent::find();
        // if (UserService::getInst()->id) {
        //     $query->andFilterWhere(['ra.entity_id' => UserService::getInst()->current_entity_id]);
        // }
        return $query;
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(Member::class, ['id' => 'created_by']);
    }

    public function getTeacher()
    {
        return $this->hasOne(Member::class, ['id' => 'teacher_id']);
    }

    public function getStore()
    {
        return $this->hasOne(Store::class, ['id' => 'store_id']);
    }

    public function getStoreName()
    {
        return $this->store->store_name;
    }

    public function getCustomer()
    {
        return $this->hasOne(Customer::class, ['id' => 'cus_id']);
    }

    public function getCusName()
    {
        return $this->customer->name;
    }

    public function getCusPhone()
    {
        return ResultHelper::mobileEncryption($this->customer->mobile);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }

    public function getTeacherName()
    {
        return $this->teacher->username ?: '';
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    public function getStatusText()
    {
        return RefundApplicationStatusEnum::getValue($this->status);
    }
}
