<?php

namespace common\models\promote;

use common\enums\ScenariosEnum;
use common\enums\WhetherEnum;
use common\models\common\AdsAccountSub;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%ads_main_body}}".
 *
 * @property int $id id
 * @property string $name 主体名称
 * @property string $sub_advertiser_id 备用金账户
 * @property int $status 状态：0禁用、1启用
 * @property int $entity_id 企业ID
 */
class AdsMainBody extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];
    public static $reidsKey = 'adsMainBodyList';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%ads_main_body}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'sub_advertiser_id'], 'trim'],
            [['name'], 'string', 'max' => 255],
            [['name'], 'required'],
            [['status'], 'integer'],
            [['entity_id', 'name'], 'unique', 'message' => '{attribute}已存在'],
            [['sub_advertiser_id'], 'string', 'max' => 255],
            ['sub_advertiser_id', 'exist', 'targetClass' => AdsAccountSub::className(), 'targetAttribute' => 'sub_advertiser_id', 'filter' => ['entity_id' => $this->entity_id], 'message' => '{attribute}不存在'],
            ['status', 'in', 'range' => WhetherEnum::getKeys()],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'id',
            'name' => '主体名称',
            'sub_advertiser_id' => '备用金账户',
            'status' => '状态',
            'entity_id' => '企业ID',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['name', 'status', 'sub_advertiser_id'],
            ScenariosEnum::SET_STATUS => ['status'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->entity_id = UserService::getInst()->current_entity_id;
        }

        return parent::beforeSave($isInsert);
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            // 新增主体后，自动匹配并填充相关广告子账户的主体ID
            $this->updateRelatedAdsAccountSub();
        }

        $key = self::$reidsKey;
        $redis = Yii::$app->cache;
        $redis->delete($key);

        parent::afterSave($insert, $changedAttributes);
    }

    /**
     * 更新相关广告子账户的主体ID
     */
    private function updateRelatedAdsAccountSub()
    {
        if (empty($this->name) || empty($this->entity_id)) {
            return;
        }

        // 直接使用 LIKE 查询匹配包含主体名称的广告子账户，并批量更新
        AdsAccountSub::updateAll(
            ['main_body_id' => $this->id],
            [
                'and',
                ['entity_id' => $this->entity_id],
                ['main_body_id' => 0],
                ['like', 'sub_advertiser_name', $this->name]
            ]
        );
    }
}
