<?php

namespace common\models\promote;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\helpers\ArrayHelper;
use common\enums\WhetherEnum;
use common\enums\log\AdsReportStatusEnum;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%ads_report_log}}".
 *
 * @property int $id ID
 * @property int $channel_id promote_channel表ID
 * @property int $order_id order_header表ID
 * @property string $sub_advertiser_id 广告子账户ID
 * @property int $customer_user_id 企微客户成员表ID：erp_wxcom_cus_customer_user表ID
 * @property int $type 上报类型
 * @property int $is_auto_report 是否自动上报（1-是 0-否）
 * @property string $report_params 上报请求参数
 * @property string $report_result 上报响应结果
 * @property int $report_status 上报状态（1-成功 2-失败）
 * @property int $created_at 创建时间
 */
class ReportLog extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    public static function getDb()
    {
        return Yii::$app->erp_log2;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%ads_report_log}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['channel_id', 'order_id', 'sub_advertiser_id', 'type', 'customer_user_id'], 'required'],
            [['channel_id', 'order_id', 'type', 'is_auto_report', 'created_at', 'report_status', 'customer_user_id'], 'integer'],
            [['channel_id', 'order_id'], 'integer', 'min' => 0],
            [['type'], 'integer', 'min' => 0],
            [['is_auto_report'], 'in', 'range' => WhetherEnum::getKeys()],
            [['sub_advertiser_id'], 'string', 'max' => 255],
            [['report_params', 'report_result'], 'string'],
            [['sub_advertiser_id', 'report_params', 'report_result'], 'trim'],
            [['sub_advertiser_id'], 'default', 'value' => ''],
            [['is_auto_report'], 'default', 'value' => WhetherEnum::ENABLED],
            [['report_status'], 'in', 'range' => AdsReportStatusEnum::getKeys()],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'channel_id' => '推广渠道ID',
            'order_id' => '订单ID',
            'sub_advertiser_id' => '广告子账户ID',
            'customer_user_id' => '企微客户成员ID',
            'type' => '上报类型',
            'is_auto_report' => '是否自动上报',
            'report_params' => '上报请求参数',
            'report_result' => '上报响应结果',
            'report_status' => '上报状态',
            'created_at' => '创建时间',
        ];
    }

    /**
     * 场景配置
     * @return array
     */
    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['channel_id', 'order_id', 'sub_advertiser_id', 'type', 'is_auto_report', 'report_params', 'report_result', 'report_status', 'customer_user_id'],
        ];
    }

    /**
     * 查询构建器
     * @return \yii\db\ActiveQuery
     */
    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            // 可以根据用户权限添加查询条件
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
        }

        return parent::beforeSave($isInsert);
    }

    /**
     * 获取创建时间文本
     * @return string
     */
    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    /**
     * 判断同一个客户用户在同一类型下当天是否已有成功上报记录
     * @param int $customerUserId 企微客户成员ID
     * @param int $type 上报类型
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return bool
     */
    public static function hasSuccessReportToday($customerUserId, $type, $startDate = '', $endDate = '')
    {
        $todayTime = DateHelper::turnTimestamp($startDate, $endDate);
        
        $count = static::find()
            ->where(['customer_user_id' => $customerUserId])
            ->andWhere(['type' => $type])
            ->andWhere(['report_status' => AdsReportStatusEnum::SUCCESS])
            ->andWhere(['BETWEEN', 'created_at', $todayTime[0], $todayTime[1]])
            ->count();
            
        return $count > 0;
    }

    /**
     * 获取指定账户当日的上报日志统计
     * @param int $subAdvertiserId 广告子账户ID
     * @param int $type 上报类型
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return int
     */
    public static function getReportStats($subAdvertiserId, $type = null, $startDate = '', $endDate = '')
    {
        $todayTime = DateHelper::turnTimestamp($startDate, $endDate);
        
        $count = static::find()
            ->select(['COUNT(*) as count'])
            ->where(['sub_advertiser_id' => $subAdvertiserId])
            ->andWhere(['report_status' => AdsReportStatusEnum::SUCCESS])
            ->andFilterWhere(['type' => $type])
            ->andWhere(['BETWEEN', 'created_at', $todayTime[0], $todayTime[1]])
            ->scalar();

        return (int)$count;
    }
} 