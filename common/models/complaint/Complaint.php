<?php

namespace common\models\complaint;

use Yii;
use common\models\wxcom\User;
use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\helpers\ResultHelper;
use common\models\backend\Store;
use common\models\backend\Member;
use common\enums\complaint\TypeEnum;
use common\enums\complaint\StatusEnum;
use common\enums\complaint\SourceTypeEnum;
use common\queues\feishu\ComplaintJob;

/**
 * This is the model class for table "{{%apis}}".
 *
 * @property int $id ID
 * @property string $name 投诉人名称
 * @property string $mobile 手机
 * @property string $content 投诉内容
 * @property int $store_id 投诉门店:store表ID
 * @property int $type 投诉类型(1:客服;2门店)
 * @property string $source_user_id 投诉用户userid
 * @property int $source_type 被投诉来源（1:企微）
 * @property int $reason_id 投诉原因:complaint_reason表ID
 * @property int $com_id 企微公司:wxcom_com表ID
 * @property int $respondent_id 被投诉人:wxcom_user表ID
 * @property int $respondent_user_id 被投诉人:backend_member表ID
 * @property int $status 状态（0:未处理;1:处理中;2:已处理;）
 * @property json $img 图片
 * @property int $principal_id 负责人:backend_member表ID
 * @property int $entity_id 企业:entity表ID
 * @property int $created_at 创建时间
 * @property int $updated_at 修改时间
 */
class Complaint extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%complaint}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name'], 'string', 'max' => 20],
            [['mobile'], 'string', 'max' => 11],
            [['content'], 'string', 'max' => 500],
            [['source_user_id'], 'string', 'max' => 100],
            [['name', 'mobile', 'content', 'entity_id'], 'required'],
            [['store_id', 'type', 'source_type', 'reason_id', 'com_id', 'respondent_id', 'status', 'principal_id', 'entity_id'], 'integer'],
            [['name', 'mobile', 'content', 'source_user_id'], 'trim'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => '投诉人名称',
            'mobile' => '手机',
            'content' => '投诉内容',
            'store_id' => '投诉门店:store表ID',
            'type' => '投诉类型(1:客服;2门店)',
            'source_user_id' => '投诉用户userid',
            'source_type' => '被投诉来源（1:企微）',
            'reason_id' => '投诉原因:complaint_reason表ID',
            'com_id' => '企微公司:wxcom_com表ID',
            'respondent_id' => '被投诉人:wx_user表ID',
            'respondent_user_id' => '被投诉人:backend_member表ID',
            'status' => '状态（0:未处理;1:处理中;2:已处理;）',
            'img' => '图片',
            'principal_id' => '负责人:backend_member表ID',
            'entity_id' => '企业:entity表ID',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['name', 'mobile', 'content', 'store_id', 'type', 'source_user_id', 'source_type', 'reason_id', 'com_id', 'respondent_id', 'status', 'img', 'principal_id', 'entity_id', 'respondent_user_id'],
            'status' => ['status'],
        ];
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
        }
        $this->updated_at = time();

        return parent::beforeSave($isInsert);
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert && !in_array(YII_ENV, ['local', 'dev'])) {
            //新增到多维度表格队列
            ComplaintJob::addJob($this->id);
        }
        parent::afterSave($insert, $changedAttributes);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getRespondent()
    {
        return $this->hasOne(User::class, ['id' => 'respondent_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getRespondentUser()
    {
        return $this->hasOne(Member::class, ['id' => 'respondent_user_id']);
    }

    /**
     * 负责人
     * @return \yii\db\ActiveQuery
     */
    public function getPrincipal()
    {
        return $this->hasOne(Member::class, ['id' => 'principal_id']);
    }

    /**
     * 门店
     * @return \yii\db\ActiveQuery
     */
    public function getStore()
    {
        return $this->hasOne(Store::class, ['id' => 'store_id']);
    }

    public function getReason()
    {
        return $this->hasOne(Reason::class, ['id' => 'reason_id']);
    }

    public function getMobileText()
    {
        return $this->mobile ? ResultHelper::mobileEncryption($this->mobile) : '-';
    }

    public function getTypeText()
    {
        return TypeEnum::getValue($this->type);
    }

    public function getSourceTypeText()
    {
        return SourceTypeEnum::getValue($this->source_type);
    }

    public function getStatusText()
    {
        return StatusEnum::getValue($this->status);
    }

    public function getImgText()
    {
        return !empty($this->img) ? json_decode($this->img) : '';
    }

    public function getComName()
    {
        return isset($this->respondent->com) ? $this->respondent->com->name : '';
    }
}
