<?php

namespace common\models\wxcom;

use backendapi\services\complaint\ComplaintService;
use backendapi\services\wxcom\ComService;
use common\enums\StatusEnum;
use common\models\Config;
use common\queues\CreateUserQrcodeJob;
use common\queues\UploadUserQrcode2YxtJob;
use console\models\WxcomDepartment;
use EasyWeChat\Factory;
use Exception;
use Yii;

/**
 * This is the model class for table "{{%wxcom_user}}".
 *
 * @property int $id 成员ID
 * @property int $user_id 客服ID
 * @property string $name 成员名称
 * @property string $wxcom_user_id 企微成员ID
 * @property string $department 部门ID
 * @property string $wxcom_department 企微部门ID
 * @property string $position 职务
 * @property string $mobile 手机
 * @property int $gender 性别：1男2女
 * @property string $email 邮箱
 * @property string $avatar 头像
 * @property int $status 状态
 * @property int $enable 状态
 * @property int $promote_status 推广状态 0 禁用 1启用
 * @property int $isleader 是否负责人
 * @property string $extattr extattr
 * @property int $hide_mobile 是否隐藏手机
 * @property string $telephone 座机
 * @property string $order order
 * @property string $external_profile external_profile
 * @property int $wxcom_main_department 企微主部门ID
 * @property int $main_department 主部门ID
 * @property string $qr_code 企业二维码
 * @property string $alias 昵称
 * @property string $is_leader_in_dept is_leader_in_dept
 * @property string $address 地址
 * @property string $thumb_avatar 缩略头像
 * @property string $direct_leader 直接领导ID
 * @property int $com_id 企微公司ID
 * @property int $entity_id 企业ID
 * @property string $created_at 创建时间
 * @property int $created_by 创建人
 * @property string $updated_at 修改时间
 * @property int $updated_by 操作人
 * @property int $deleted_at 删除时间
 * @property int $deleted_by 删除人
 */
class User extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    // 扩展操作信息
    use \common\traits\ModelOperateInfo;

    const STATUS_ENABLE = 1;
    const STATUS_DISABLED = 2;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%wxcom_user}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name'], 'string', 'max' => 50],
            [['wxcom_user_id'], 'string', 'max' => 100],
            [['department'], 'string', 'max' => 100],
            [['wxcom_department'], 'string', 'max' => 100],
            [['position'], 'string', 'max' => 128],
            [['mobile'], 'string', 'max' => 20],
            [['email'], 'string', 'max' => 100],
            [['avatar'], 'string', 'max' => 200],
            [['extattr'], 'string', 'max' => 200],
            [['telephone'], 'string', 'max' => 20],
            [['order'], 'string', 'max' => 100],
            [['external_profile'], 'string', 'max' => 1000],
            [['qr_code'], 'string', 'max' => 200],
            [['alias'], 'string', 'max' => 50],
            [['is_leader_in_dept'], 'string', 'max' => 100],
            [['address'], 'string', 'max' => 200],
            [['thumb_avatar'], 'string', 'max' => 200],
            [['direct_leader'], 'string', 'max' => 200],
            [['name', 'com_id'], 'required'],
            [['user_id', 'gender', 'status', 'enable','promote_status', 'isleader', 'hide_mobile', 'wxcom_main_department', 'main_department', 'com_id'], 'integer'],
            [['name', 'wxcom_user_id', 'department', 'wxcom_department', 'position', 'mobile', 'email', 'avatar', 'extattr', 'telephone', 'order', 'external_profile', 'qr_code', 'alias', 'is_leader_in_dept', 'address', 'thumb_avatar', 'direct_leader'], 'trim'],

            // [['name'], 'uniqueNameWithParent'],
        ];
    }

    /**
     * entity_id name 分组唯一
     *
     * @param [type] $attribute
     * @param [type] $params
     * @return void
     */
    public function uniqueNameWithParent($attribute, $params)
    {
        // $dataCount = $this->find()
        //     ->andWhere(['=', 'name', $this->name])
        //     ->andWhere(['=', 'entity_id', Yii::$app->user->identity->current_entity_id])
        //     ->andWhere(['=', 'parent_id', $this->parent_id])
        //     ->andWhere(['<>', 'id', intval($this->id)])
        //     ->count();

        // if ($dataCount) $this->addError($attribute, '同一层级不能存在相同名称的部门');
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '成员ID',
            'user_id' => '客服ID',
            'name' => '成员名称',
            'wxcom_user_id' => '企微成员ID',
            'department' => '部门ID',
            'wxcom_department' => '企微部门ID',
            'position' => '职务',
            'mobile' => '手机',
            'gender' => '性别：1男2女',
            'email' => '邮箱',
            'avatar' => '头像',
            'status' => '状态',
            'enable' => '状态',
            'promote_status' => '推广状态',
            'isleader' => '是否负责人',
            'extattr' => '',
            'hide_mobile' => '是否隐藏手机',
            'telephone' => '座机',
            'order' => '',
            'external_profile' => '',
            'wxcom_main_department' => '企微主部门ID',
            'main_department' => '主部门ID',
            'qr_code' => '企业二维码',
            'alias' => '昵称',
            'is_leader_in_dept' => '',
            'address' => '地址',
            'thumb_avatar' => '缩略头像',
            'direct_leader' => '直接领导ID',
            'com_id' => '企微公司ID',
            'entity_id' => '企业ID',
            'created_at' => '创建时间',
            'created_by' => '创建人',
            'updated_at' => '修改时间',
            'updated_by' => '操作人',
            'deleted_at' => '删除时间',
            'deleted_by' => '删除人',
        ];
    }

    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            //新增成员增加“投诉建议”
            try {
                ComplaintService::updateUserWxcom($this->id, $this->entity_id);
            } catch (Exception $e) {
                Yii::$app->feishuNotice->text('企微成员：' . $this->name . '，增加“投诉建议”失败原因：' . $e->getMessage());
            }
        }

        return parent::afterSave($insert, $changedAttributes);
    }

    public static function getListByDeptIds($deptIds, $keyword = null)
    {
        if (!is_array($deptIds)) {
            $deptIds = [$deptIds];
        }

        if (!count($deptIds)) {
            return [];
        }

        $where = [];
        foreach ($deptIds as $deptId) {
            $where[] = "FIND_IN_SET({$deptId},department)";
        }

        $query = self::find()
            ->with(['com', 'user' => function ($query) {
                $query->with(['departmentAssignment']);
            }])
            ->andWhere(['status' => self::STATUS_ENABLE])
            ->andWhere(implode(' or ', $where));

        if ($keyword) {
            $query->andWhere(['or', ['like', 'name', $keyword], ['=', 'mobile', $keyword]]);
        }

        $userList = $query->all();
        return $userList;
    }

    public function getCom()
    {
        return $this->hasOne(Com::class, ['id' => 'com_id']);
    }

    public function getCusUsers()
    {
        return $this->hasMany(CusCustomerUser::class, ['user_id' => 'id']);
    }

    public function getCusCount()
    {
        return $this->getCusUsers()->count();
    }

    public function getUser()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'user_id']);
    }

    /**
     * 判断企微账号是否绑定活码
     *
     * @return boolean
     */
    public function isBindQrcode()
    {
        return CusQrcodeUser::find()->where(['user_id' => $this->id])->exists();
    }

    /**
     * 判断企微账号是否存在二维码
     *
     * @return boolean
     */
    public function hasQrcode()
    {
        return UserQrcode::find()->where(['user_id' => $this->id])->exists();
    }

    public function getUserQrcodeList()
    {
        return $this->hasMany(UserQrcode::class, ['user_id' => 'id']);
    }

    /**
     * 企微账号是否可用
     */
    public function isUseful()
    {
        $config = ComService::getWxcomConfigByIdForCus($this->com_id);
        $conWay = Factory::work($config)->contact_way;

        $qrcodeConfig = [
            'state' => 'checked:' . time(),
            'user' => [$this->wxcom_user_id],
        ];
        $type = 1;
        $scene = 2;
        $res = $conWay->create($type, $scene, $qrcodeConfig);
        if (!$res['errcode']) {
            $conWay->delete($res['config_id']);
            return true;
        }

        throw new Exception($res['errmsg']);
    }

    /**
     * 加入客服部
     *
     * @return void
     */
    public function joinServicerDept()
    {
        $deptIds = Config::getByName('serviceDeptIds', false, $this->entity_id);
        $servicerDept = WxcomDepartment::find()
            ->where(['id' => $deptIds])
            ->andWhere(['com_id' => $this->com_id])
            ->one();
        $userDeptIds = array_filter(explode(',', $this->department));
        if (!in_array($servicerDept->id, $userDeptIds)) {
            $wxcomDeptIds = array_filter(explode(',', $this->wxcom_department));
            $wxcomDeptIds[] = $servicerDept->wxcom_id;

            $userDeptIds[] = $servicerDept->id;
            $this->wxcom_department = implode(',', $wxcomDeptIds);
            $this->department = implode(',', $userDeptIds);
            if (!$this->save()) {
                throw new Exception(current($this->getFirstErrors()));
            }

            $config = ComService::getWxcomConfigByIdForCon($this->com_id);
            $app = Factory::work($config);
            $ret = $app->user->update($this->wxcom_user_id, [
                'department' => $wxcomDeptIds,
            ]);
            if ($ret['errcode']) {
                throw new Exception($ret['errmsg']);
            }
        }

        // 成员二维码如果没上传到营销通则创建任务
        $userQrcodeList = UserQrcode::find()->where(['user_id' => $this->id, 'status' => 1, 'media_id' => ''])->all();
        foreach ($userQrcodeList as $userQrcode) {
            UploadUserQrcode2YxtJob::addJob($userQrcode->id);
        }

        // 成员没有二维码时生成两百张
        $hasQrcode = UserQrcode::find()->where(['user_id' => $this->id])->exists();
        if (!$hasQrcode) {
            CreateUserQrcodeJob::addJob($this->id, 150);
        }
    }

    /**
     * 获取可用的二维码列表
     *
     * @return array<UserQrcode>
     */
    public function getUsefulQrcodeList()
    {
        /** @var array<UserQrcode> $userQrcodeList */
        $userQrcodeList = $this->userQrcodeList;
        $newUserQrcodeList = [];
        foreach ($userQrcodeList as $userQrcode) {
            if (!$userQrcode->isUsed()) {
                $newUserQrcodeList[] = $userQrcode;
            }
        }
        if ((count($newUserQrcodeList) - 1) / count($userQrcodeList) <= 0.1) {
            CreateUserQrcodeJob::addJob($this->id, count($userQrcodeList) * 0.1);
        }
        return $newUserQrcodeList;
    }

    /**
     * 获取下一个二维码
     *
     * @return UserQrcode
     */
    public function getNextQrcode()
    {
        $userQrcodeList = $this->getUsefulQrcodeList();
        shuffle($userQrcodeList);
        return current($userQrcodeList);
    }

    /**
     * 使用一个二维码
     *
     * @param array $params
     * @return UserQrcode
     */
    public function useQrcode($params)
    {
        for ($i = 1; $i <= 5; $i++) {
            try {
                $qrcode = $this->getNextQrcode();
                if (!$qrcode) {
                    usleep(500000);
                    continue;
                }

                $qrcode->beUsed($params);
                return $qrcode;
            } catch (Exception $e) {
            }
        }
    }

    public function getButtonUsers()
    {
        return $this->hasMany(CusButtonUser::class, ['user_id' => 'id']);
    }

    /**
     * 获取绑定的企微直加规则
     *
     * @return CusButton
     */
    public function getButton()
    {
        $buttonUsers = $this->getButtonUsers()->with('button')->all();
        if (!empty($buttonUsers)) {
            foreach ($buttonUsers as $buttonUser) {
                if (empty($buttonUser->button)) {
                    continue;
                }

                if ($buttonUser->button->status == StatusEnum::ENABLED) {
                    return $buttonUser->button;
                }
            }
        }

        return null;
    }
}
