<?php

namespace common\models;

use common\enums\CustomerAgeBracket;
use common\enums\GenderEnum;
use common\enums\ScenariosEnum;
use common\helpers\ArrayHelper;
use common\helpers\DateHelper;
use common\helpers\ResultHelper;
use common\models\backend\order\OrderHeader;
use common\models\backend\promote\PromoteLink;
use common\models\backend\promote\PromoteProject;
use common\models\customer_recharge\CustomerRechargeCard;
use common\models\promote\Direction;
use common\models\wechat\FansRecord;
use common\models\wxcom\CusCustomer;
use common\models\wxcom\CusCustomerUser;
use Yii;

/**
 * This is the model class for table "{{%customer}}".
 *
 * @property int $id 客户ID
 * @property int $wxcom_cus_id 企微客户ID：erp_wxcom_cus_customer.id
 * @property int $add_fans_id 个微加粉ID：erp_wechat_fans_record表ID
 * @property string $name 姓名
 * @property int $age_bracket 年龄段
 * @property int $gender 性别：0未知，1男，2女
 * @property string $avatar 头像
 * @property string $nick_name 微信昵称
 * @property int $birthday 出生日期
 * @property string $mobile_code 手机区号
 * @property string $mobile 手机号
 * @property string $address 地址
 * @property string $remark 备注
 * @property int $channel_id 来源渠道ID
 * @property string $sub_advertiser_name 广告子账户名称
 * @property string $sub_advertiser_id 广告子账户id
 * @property int $material_id 广告素材ID:ads_material 表 material_id 一致
 * @property string $callback 回调信息
 * @property int $link_id 链路ID
 * @property int $project_id 项目ID
 * @property int $responsible_id 推广责任人ID
 * @property int $servicer_user_id 客服人员ID：backend_member表ID
 * @property int $direction_id 定向id
 * @property string $adid 计划id
 * @property string $code 代号
 * @property int $store_id 归属门店ID
 * @property string $unionid 微信unionid
 * @property int $generation_id 代下单客户ID：本表ID
 * @property int $first_store_time 首次订单完成时间--对于客服、推广而言
 * @property int $first_visit_time 首次到店时间--对于门店而言
 * @property int $last_store_time 最后到店时间
 * @property int $entity_id 企业ID
 * @property int $created_by 创建人
 * @property int $created_at 创建时间
 * @property int $updated_by 修改人
 * @property int $updated_at 修改时间
 */
class Customer extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%customer}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['name', 'avatar', 'nick_name', 'mobile_code', 'mobile', 'address', 'remark', 'sub_advertiser_name', 'sub_advertiser_id', 'callback', 'adid', 'code', 'unionid'], 'trim'],
            [['name','material_id'], 'string', 'max' => 32],
            [['avatar'], 'string', 'max' => 255],
            [['nick_name'], 'string', 'max' => 50],
            [['mobile_code'], 'string', 'max' => 10],
            [['mobile'], 'string', 'max' => 20],
            [['address'], 'string', 'max' => 100],
            [['remark'], 'string', 'max' => 100],
            [['sub_advertiser_name'], 'string', 'max' => 255],
            [['sub_advertiser_id'], 'string', 'max' => 255],
            [['callback'], 'string', 'max' => 500],
            [['adid'], 'string', 'max' => 100],
            [['code'], 'string', 'max' => 20],
            [['unionid'], 'string', 'max' => 60],
            [['wxcom_cus_id', 'add_fans_id', 'gender', 'channel_id', 'link_id', 'project_id', 'responsible_id', 'servicer_user_id', 'direction_id', 'store_id', 'generation_id', 'first_store_time','first_visit_time', 'last_store_time','birthday','age_bracket'], 'integer'],
            ['mobile', 'validateMobile'],
            ['age_bracket', 'in', 'range' => CustomerAgeBracket::getKeys()],
            ['gender', 'in', 'range' => GenderEnum::getKeys()],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'wxcom_cus_id' => '企微客户ID',
            'add_fans_id' => '个微加粉ID',
            'name' => '姓名',
            'gender' => '性别',
            'age_bracket' => '年龄段',
            'avatar' => '头像',
            'nick_name' => '微信昵称',
            'birthday' => '出生日期',
            'mobile_code' => '手机区号',
            'mobile' => '手机号',
            'address' => '地址',
            'remark' => '备注',
            'channel_id' => '来源渠道ID',
            'sub_advertiser_name' => '广告子账户名称',
            'sub_advertiser_id' => '广告子账户id',
            'material_id' => '素材ID',
            'callback' => '回调信息',
            'link_id' => '链路ID',
            'project_id' => '项目ID',
            'responsible_id' => '推广责任人ID',
            'servicer_user_id' => '客服人员ID',
            'direction_id' => '定向ID',
            'adid' => '计划id',
            'code' => '代号',
            'store_id' => '归属门店ID',
            'unionid' => '微信unionid',
            'generation_id' => '代下单客户ID',
            'first_store_time' => '首次订单完成时间',
            'first_visit_time' => '首次到店时间',
            'last_store_time' => '最后到店时间',
            'entity_id' => '企业ID',
            'created_by' => '创建人',
            'created_at' => '创建时间',
            'updated_by' => '修改人',
            'updated_at' => '修改时间',
        ];
    }

    /**
     * 验证手机号
     *
     * @param $attribute
     * @return bool
     */
    public function validateMobile($attribute)
    {
        if (!preg_match("/^\d*$/", $this->mobile)) {
            $this->addError($attribute, '手机号验证失败，请重新输入');
            return false;
        }

        return true;
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['wxcom_cus_id', 'add_fans_id', 'name', 'gender', 'avatar', 'nick_name', 'mobile_code', 'mobile', 'address', 'remark', 'channel_id', 'sub_advertiser_name', 'sub_advertiser_id', 'material_id', 'callback', 'link_id', 'project_id', 'responsible_id', 'servicer_user_id', 'direction_id', 'adid', 'code', 'store_id', 'unionid', 'generation_id', 'first_store_time', 'last_store_time', 'entity_id','age_bracket'],
            'serviceLog' => ['name', 'gender', 'remark', 'birthday'],
            'updateAgeBracketAndGender' => ['age_bracket', 'gender'],
        ];
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
        }
        $this->updated_at = time();

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }

    public function getMobileText()
    {
        return $this->mobile ? ResultHelper::mobileEncryption($this->mobile) : '';
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    public function getFirstStoreTimeText()
    {
        return DateHelper::toDate($this->first_store_time, 'Y-m-d H:i:s');
    }

    public function getFirstVisitTimeText()
    {
        return DateHelper::toDate($this->first_visit_time, 'Y-m-d H:i:s');
    }

    public function getLastStoreTimeText()
    {
        return DateHelper::toDate($this->last_store_time, 'Y-m-d H:i:s');
    }

    public function getUpdatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'updated_by']);
    }

    public function getStore()
    {
        return $this->hasOne(\common\models\backend\Store::class, ['id' => 'store_id']);
    }

    public function getChannel()
    {
        return $this->hasOne(\common\models\backendapi\PromoteChannel::class, ['id' => 'channel_id'])->where('1=1');
    }

    public function getDirection()
    {
        return $this->hasOne(Direction::class, ['id' => 'direction_id']);
    }

    public function getLink()
    {
        return $this->hasOne(PromoteLink::class, ['id' => 'link_id']);
    }

    public function getOrders()
    {
        return $this->hasMany(OrderHeader::class, ['cus_id' => 'id']);
    }

    //获取代下单用户信息
    public function getGenerationCustomer()
    {
        return $this->hasOne(Customer::class, ['id' => 'generation_id']);
    }

    //获取企微用户信息
    public function getCusCustomer()
    {
        return $this->hasOne(CusCustomer::class, ['id' => 'wxcom_cus_id']);
    }

    public function getProject()
    {
        return $this->hasOne(PromoteProject::class, ['id' => 'project_id']);
    }

    public function getRemainingProduct()
    {
        return $this->hasMany(CustomerProduct::class, ['cus_id' => 'id'])->andWhere(['AND', ['>', 'left_num', 0], ['>=', 'expire_at', time()]]);
    }

    public function getRechargeCard()
    {
        return $this->hasMany(CustomerRechargeCard::class, ['cus_id' => 'id']);
    }

    public function getReferrer()
    {
        return $this->hasOne(static::class, ['id' => 'generation_id']);
    }

    /**
     * 用户剩余储值卡金额
     *
     * @return float|int
     */
    public function getRemainingCardAmount()
    {
        $amount = 0;
        $rechargeCard = $this->rechargeCard;
        if ($rechargeCard) {
            $amount = array_sum(array_column($rechargeCard, 'left_amount'));
        }

        return $amount;
    }

    /**
     * 填充推广信息
     *
     * @param OrderHeader $order
     * @return void
     */
    public function fillPromoteInfo(OrderHeader $order)
    {
        // 存在推广人信息时不做处理
        if ($this->responsible_id) {
            return true;
        }

        // 同行人则从老客复制推广信息
        if ($this->generation_id) {
            if (!$this->generationCustomer) {
                return true;
            }
            if(empty($this->channel_id)){
                $this->channel_id = ArrayHelper::getValue($this->generationCustomer, 'channel_id', 0);
            }

            $this->sub_advertiser_name = ArrayHelper::getValue($this->generationCustomer, 'sub_advertiser_name', '');
            $this->sub_advertiser_id = ArrayHelper::getValue($this->generationCustomer, 'sub_advertiser_id', '');
            $this->callback = ArrayHelper::getValue($this->generationCustomer, 'callback', '');
            $this->link_id = ArrayHelper::getValue($this->generationCustomer, 'link_id', 0);
            $this->project_id = ArrayHelper::getValue($this->generationCustomer, 'project_id', 0);
            $this->responsible_id = ArrayHelper::getValue($this->generationCustomer, 'responsible_id', 0);
            $this->servicer_user_id = ArrayHelper::getValue($this->generationCustomer, 'servicer_user_id', 0);
            $this->direction_id = ArrayHelper::getValue($this->generationCustomer, 'direction_id', 0);
            $this->adid = ArrayHelper::getValue($this->generationCustomer, 'adid', '');
            $this->code = ArrayHelper::getValue($this->generationCustomer, 'code', '');
            $this->material_id = ArrayHelper::getValue($this->generationCustomer, 'material_id', '');
            return $this->save();
        }

        // 企微下单
        if ($this->wxcom_cus_id) {
            /** @var CusCustomerUser */
            $customerUser = $order->customerUser;
            if(empty($this->channel_id)){
                $this->channel_id = ArrayHelper::getValue($customerUser, 'channel_id', 0);
            }
            $this->sub_advertiser_name = ArrayHelper::getValue($customerUser, 'sub_advertiser_name', '');
            $this->sub_advertiser_id = ArrayHelper::getValue($customerUser, 'sub_advertiser_id', '');
            $this->callback = ArrayHelper::getValue($customerUser, 'callback', '');
            $this->link_id = ArrayHelper::getValue($customerUser, 'link_id', 0);
            $this->project_id = ArrayHelper::getValue($customerUser, 'project_id', 0);
            $this->direction_id = ArrayHelper::getValue($customerUser, 'direction_id', 0);
            $this->adid = ArrayHelper::getValue($customerUser, 'adid', '');
            $this->code = ArrayHelper::getValue($order, 'code', '');
            $this->responsible_id = ArrayHelper::getValue($order, 'promoter_user_id', 0);
            $this->servicer_user_id = Yii::$app->user->id;
            $material_id = ArrayHelper::getValue($customerUser, 'mid3', '');
            $this->material_id = (strpos($material_id, 'MID') !== false) ? '' : $material_id ;
            return $this->save();
        }

        // 个微下单
        if ($this->add_fans_id) {
            $fansRecord = FansRecord::findOne($this->add_fans_id);
            if(empty($this->channel_id)){
                $this->channel_id = ArrayHelper::getValue($fansRecord, 'channel_id', 0);
            }
            $this->link_id = ArrayHelper::getValue($fansRecord, 'link_id', 0);
            $this->project_id = ArrayHelper::getValue($fansRecord, 'project_id', 0);
            $this->direction_id = ArrayHelper::getValue($fansRecord, 'direction_id', 0);
            $this->code = ArrayHelper::getValue($order, 'code', '');
            $this->responsible_id = ArrayHelper::getValue($order, 'promoter_user_id', 0);
            $this->servicer_user_id = Yii::$app->user->id;
            return $this->save();
        }

        return false;
    }

    /**
     * 修复推广数据
     *
     * @return void
     */
    public function fixPromoteInfo()
    {
        // 同行人则从老客复制推广信息
        if ($this->generation_id) {
            if (!$this->generationCustomer) {
                return true;
            }
            if(empty($this->channel_id)){
                $this->channel_id = ArrayHelper::getValue($this->generationCustomer, 'channel_id', 0);
            }
            $this->channel_id = ArrayHelper::getValue($this->generationCustomer, 'channel_id', 0);
            $this->sub_advertiser_name = ArrayHelper::getValue($this->generationCustomer, 'sub_advertiser_name', '');
            $this->sub_advertiser_id = ArrayHelper::getValue($this->generationCustomer, 'sub_advertiser_id', '');
            $this->callback = ArrayHelper::getValue($this->generationCustomer, 'callback', '');
            $this->link_id = ArrayHelper::getValue($this->generationCustomer, 'link_id', 0);
            $this->project_id = ArrayHelper::getValue($this->generationCustomer, 'project_id', 0);
            $this->responsible_id = ArrayHelper::getValue($this->generationCustomer, 'responsible_id', 0);
            $this->servicer_user_id = ArrayHelper::getValue($this->generationCustomer, 'servicer_user_id', 0);
            $this->direction_id = ArrayHelper::getValue($this->generationCustomer, 'direction_id', 0);
            $this->adid = ArrayHelper::getValue($this->generationCustomer, 'adid', '');
            $this->code = ArrayHelper::getValue($this->generationCustomer, 'code', '');
            $this->material_id = ArrayHelper::getValue($this->generationCustomer, 'material_id', '');
            return $this->save();
        }

        // 企微下单
        if ($this->wxcom_cus_id) {
            $order = OrderHeader::find()
                ->where(['cus_id' => $this->id])
                ->andWhere(['>', 'customer_user_id', 0])
                ->andWhere(['pre_pay_status' => 2])
                ->orderBy('id')
                ->one();
            if (!$order) {
                return false;
            }

            /** @var CusCustomerUser */
            $customerUser = $order->customerUser;
            if($this->channel_id){
                $this->channel_id = ArrayHelper::getValue($customerUser, 'channel_id', 0);
            }

            $this->sub_advertiser_name = ArrayHelper::getValue($customerUser, 'sub_advertiser_name', '');
            $this->sub_advertiser_id = ArrayHelper::getValue($customerUser, 'sub_advertiser_id', '');
            $this->callback = ArrayHelper::getValue($customerUser, 'callback', '');
            $this->link_id = ArrayHelper::getValue($customerUser, 'link_id', 0);
            $this->project_id = ArrayHelper::getValue($customerUser, 'project_id', 0);
            $this->direction_id = ArrayHelper::getValue($customerUser, 'direction_id', 0);
            $this->adid = ArrayHelper::getValue($customerUser, 'adid', '');
            $this->code = ArrayHelper::getValue($customerUser, 'code', ArrayHelper::getValue($order, 'code', ''));
            $this->responsible_id = ArrayHelper::getValue($order, 'promoter_user_id', 0);
            $material_id = ArrayHelper::getValue($customerUser, 'mid3', '');
            $this->material_id = (strpos($material_id, 'MID') !== false) ? '' : $material_id ;
            return $this->save();
        }
    }

    //中间带*手机号
    public function getMobileStar()
    {
        return ResultHelper::mobileEncryption($this->mobile);
    }

    //获取已购项目数量
    public function getCustomerProductNum()
    {
        return CustomerProduct::find()->andFilterWhere(['cus_id' => $this->id])
            ->andWhere(['>', 'expire_at', time()])
            ->andWhere(['>', 'left_num', 0])
            ->count();
    }

    /**
     * 是否新客-推广/客服
     *
     * @return bool
     */
    public function getIsNew()
    {
        // 首次订单完成时间为空 - 新客
        if (empty($this->first_store_time)) {
            return true;
        }

        // 当天到店 - 新客
        $todayBegin = strtotime(date('Y-m-d'));
        if ($this->first_store_time >= $todayBegin) {
            return true;
        }

        return false;
    }

     /**
     * 是否新客-对于门店而言
     *
     * @return bool
     */
    public function getIsNewToStore()
    {
        // 首次到店时间为空 - 新客
        if (empty($this->first_visit_time)) {
            return true;
        }

        // 当天到店 - 新客
        $todayBegin = strtotime(date('Y-m-d'));
        if ($this->first_visit_time >= $todayBegin) {
            return true;
        }

        return false;
    }

     /**
     * 获取年龄
     */
    public static function getAge($birthday)
    {
        $age = 0;
        if ($birthday) {
            $birthdayYear = date('Y', $birthday);
            $currentYear = date('Y', time());
            $age = $currentYear - $birthdayYear;
        }

        return $age;
    }
}
