<?php

namespace common\models\material;

use common\enums\AgentAccountTypeEnum;
use common\enums\ProcessStatusEnum;
use common\enums\ProcurementPayTypeEnum;
use common\enums\ProcurementStatusEnum;
use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\helpers\Tool;
use common\models\backend\material\Supplier;
use common\models\backend\ProcurementDetails;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%procurement_pay}}".
 *
 * @property int $id id
 * @property int $list_no 付款单号
 * @property string $detail_ids 采购单详情ids,procurement_details表ID
 * @property string $reason_title 付款事由标题
 * @property double $amount 付款金额
 * @property int $pay_type 付款方式：0其他 1支付宝 2微信 3转账
 * @property int $pay_time 付款日期
 * @property int $supplier_id 供应商id，supplier表ID
 * @property string $bank_info 银行账户信息
 * @property string $serial_number 飞书审批单编号
 * @property string $process_instance_id 审批ID
 * @property string $process_detail 审批流程明细
 * @property string $attachment 附件
 * @property int $approval_time 审批结束时间
 * @property int $status 状态：0草稿 1审核中、2审核未通过、3已取消、5已完成
 * @property string $remark 备注
 * @property int $entity_id 企业ID
 * @property int $dept_id 创建人的部门ID
 * @property int $created_by 创建人
 * @property int $created_at 创建时间
 * @property int $updated_by 更新人
 * @property int $updated_at 更新时间
 */
class ProcurementPay extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%procurement_pay}}';
    }

    /**
     * @return array
     */
    public function behaviors()
    {
        return [
            [
                'class' => \yii\behaviors\TimestampBehavior::class
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['reason_title', 'bank_info', 'serial_number', 'process_instance_id', 'process_detail', 'attachment', 'remark'], 'trim'],
            [['reason_title', 'detail_ids', 'amount', 'pay_type', 'pay_time', 'supplier_id', 'status'], 'required'],
            ['detail_ids', 'validateDetailIDs'],
            ['attachment', 'validateAttachment'],
            ['pay_type', 'in', 'range' => ProcurementPayTypeEnum::getKeys()],
            ['status', 'in', 'range' => ProcessStatusEnum::getKeys()],
            ['supplier_id', 'validateSupplierID'],
            [['amount'], 'double'],
            [['pay_type', 'pay_time', 'supplier_id', 'approval_time', 'status', 'dept_id'], 'integer'],
            [['bank_info', 'process_detail', 'attachment'], 'string'],
            [['reason_title', 'detail_ids', 'remark'], 'string', 'max' => 500],
            [['amount'], 'double', 'min' => 0],
            [['serial_number','list_no'], 'string', 'max' => 50],
            [['process_instance_id'], 'string', 'max' => 100],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'id',
            'detail_ids' => '采购单详情ids',
            'reason_title' => '付款事由标题',
            'amount' => '付款金额',
            'pay_type' => '付款方式',
            'pay_time' => '付款日期',
            'supplier_id' => '供应商id,supplier表ID',
            'bank_info' => '银行账户信息',
            'serial_number' => '飞书审批单编号',
            'process_instance_id' => '审批ID',
            'process_detail' => '审批流程明细',
            'attachment' => '附件',
            'approval_time' => '审批结束时间',
            'status' => '状态',
            'entity_id' => '企业ID',
            'dept_id' => '创建人的部门ID',
            'created_by' => '创建人',
            'created_at' => '创建时间',
            'updated_by' => '更新人',
            'updated_at' => '更新时间',
            'remark' => '备注',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['reason_title', 'detail_ids', 'amount', 'pay_type', 'pay_time', 'supplier_id', 'bank_info', 'serial_number', 'process_instance_id', 'process_detail', 'attachment', 'approval_time', 'status', 'dept_id', 'remark'],
            'approval' => ['process_instance_id','serial_number','status']
        ];
    }

    /**
     * 采购详情单验证
     */
    public function validateDetailIDs($attribute)
    {
        $detail_ids = $this->detail_ids;
        if (!is_array($detail_ids)) {
            $this->addError($attribute, '采购单格式有误');
            return false;
        }

        foreach ($detail_ids as $id) {
            $info = ProcurementDetails::find()->select('id,status,material_name')->where(['id' => $id, 'entity_id' => UserService::getInst()->current_entity_id])->one();
            if (empty($info)) {
                $this->addError($attribute, '采购单不存在，请重新选择');
                return false;
            }
            if (!in_array($info->status,[ ProcurementStatusEnum::WAIT_PAY,ProcurementStatusEnum::PAY_NOT_PASS,ProcurementStatusEnum::PAY_CANCEL])) {
                $this->addError($attribute, '采购的：“' . $info->material_name . '”不是待付款状态，请认真核对');
                return false;
            }
        }

        $this->detail_ids = implode(',', $detail_ids);
        return true;
    }

    public function validateSupplierID($attribute)
    {
        $supplierInfo = Supplier::find()->andWhere(['id' => $this->supplier_id])->one();
        if (empty($supplierInfo)) {
            $this->addError($attribute, '供应商不存在，请重新选择');
            return false;
        }

        $data = [
            '账户类型' => AgentAccountTypeEnum::getValue($supplierInfo->bank_account_type),
            '户名' => $supplierInfo->bank_account_name,
            '账号' => $supplierInfo->bank_account,
            '银行' => $supplierInfo->bank_name
        ];

        if ($supplierInfo->bank_account_type == AgentAccountTypeEnum::ENTERPRISE) {
            $data['银行所在地区'] = $supplierInfo->bank_location;
            $data['银行支行'] = $supplierInfo->bank_branch_name;
        }

        $this->bank_info = json_encode($data, 256);
        return true;
    }

    public function validateAttachment($attribute)
    {
        $this->attachment = Tool::getArrayToString($this->attachment);
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    public function getUpdatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'updated_by']);
    }

    public function getApprovalTimeText()
    {
        return DateHelper::toDate($this->approval_time, 'Y-m-d H:i:s');
    }

    public function getPayTimeText()
    {
        return DateHelper::toDate($this->pay_time, 'Y-m-d');
    }

}
