<?php

namespace common\models\data;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use common\models\backend\Store;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%data_store_performance_analysis}}".
 *
 * @property int $id id
 * @property int $store_id 门店store.id
 * @property int $date 时间日期
 * @property double $received_amount 实收业绩
 * @property double $refund_amount 退款金额
 * @property double $divide_amount 分成业绩
 * @property int $new_customer 新客人数
 * @property int $new_transaction_num 新客成交人数
 * @property double $new_transaction_amount 新客首日成交业绩
 * @property int $old_customer 老客到店人数
 * @property int $old_customer_frequency 老客到店人次
 * @property double $old_amount 老客到店业绩
 * @property int $loss_num 流失人数
 * @property int $entity_id 企业ID
 * @property int $created_at 创建时间
 * @property int $updated_at 修改时间
 */
class StorePerformanceAnalysis extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%data_store_performance_analysis}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['store_id'], 'integer', 'min' => 0],
            [['date'], 'integer', 'min' => 0],
            [['new_customer'], 'integer', 'min' => 0],
            [['new_transaction_num'], 'integer', 'min' => 0],
            [['old_customer'], 'integer', 'min' => 0],
            [['old_customer_frequency'], 'integer', 'min' => 0],
            [['old_amount'], 'double', 'min' => 0],
            [['loss_num'], 'integer', 'min' => 0],
            [['store_id', 'date'], 'required'],
            [['store_id', 'date', 'new_customer', 'new_transaction_num', 'old_customer', 'old_customer_frequency', 'loss_num', 'entity_id'], 'integer'],
            [['received_amount', 'divide_amount', 'new_transaction_amount', 'old_amount', 'refund_amount'], 'double'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'id',
            'store_id' => '门店store.id',
            'date' => '时间日期',
            'received_amount' => '实收业绩',
            'refund_amount' => '退款金额',
            'divide_amount' => '分成业绩',
            'new_customer' => '新客人数',
            'new_transaction_num' => '新客成交人数',
            'new_transaction_amount' => '新客首日成交业绩',
            'old_customer' => '老客到店人数',
            'old_customer_frequency' => '老客到店人次',
            'old_amount' => '老客到店业绩',
            'loss_num' => '流失人数',
            'entity_id' => '企业ID',
            'created_at' => '创建时间',
            'updated_at' => '修改时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['store_id', 'date', 'received_amount', 'refund_amount', 'divide_amount', 'new_customer', 'new_transaction_num', 'new_transaction_amount', 'old_customer', 'old_customer_frequency', 'old_amount', 'loss_num', 'entity_id'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
            $this->entity_id = UserService::getInst()->current_entity_id;
        }
        $this->updated_at = time();

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    public function getStore()
    {
        return $this->hasOne(Store::class, ['id' => 'store_id']);
    }

    public function getAreaText()
    {
        return $this->store->area->name ?? '';
    }
}
