<?php

namespace common\models\data;

use common\enums\ScenariosEnum;
use common\models\backend\Member;
use common\models\common\Department;
use common\models\common\DepartmentAssignment;
use services\UserService;

/**
 * This is the model class for table "{{%data_servicer_analysis}}".
 *
 * @property int $user_id 客服id：backend_member表id
 * @property int $date_time 日期
 * @property int $dept_id 部门id：department 表id
 * @property int $project_id 项目id：promote_project 表id
 * @property string $system 系统标识：lzn莲姿娜、kemei珂美、meizi盯颜、amd倾城工坊、td永恩、erp
 * @property int $add_fans_count 加粉数
 * @property int $deposit_count 订金数
 * @property int $month_deposit_cus_count 月订金数(当月加粉当月付定金的) [待废弃，已改为实时计算]
 * @property int $plan_cus_count 老客预约人数
 * @property int $new_plan_cus_count 新客预约人数
 * @property int $tomorrow_plan_cus_count 老客明日预约人数
 * @property int $tomorrow_new_plan_cus_count 新客明日预约人数
 * @property int $deposit_cus_count 订人数
 * @property double $deposit_sum 订金金额
 * @property int $new_store_cus_count 售后新客到店人数
 * @property int $new_store_cus_count_before 售前新客到店人数
 * @property int $remote_count 偏远标签人数
 * @property int $attrition_count 流失人数
 * @property int $created_at 创建时间
 * @property int $entity_id 企业ID
 */
class ServicerAnalysis extends \common\models\Base
{
    public $day_count = 0;

    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%data_servicer_analysis}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id'], 'integer', 'min' => 0],
            [['date_time'], 'integer', 'min' => 0],
            [['dept_id'], 'integer', 'min' => 0],
            [['system'], 'string', 'max' => 10],
            [['add_fans_count'], 'integer', 'min' => 0],
            [['deposit_count', 'month_deposit_cus_count'], 'integer', 'min' => 0],
            [['plan_cus_count'], 'integer', 'min' => 0],
            [['new_plan_cus_count'], 'integer', 'min' => 0],
            [['tomorrow_plan_cus_count'], 'integer', 'min' => 0],
            [['tomorrow_new_plan_cus_count'], 'integer', 'min' => 0],
            [['deposit_cus_count'], 'integer', 'min' => 0],
            [['deposit_sum'], 'double', 'min' => 0],
            [['new_store_cus_count'], 'integer', 'min' => 0],
            [['new_store_cus_count_before'], 'integer', 'min' => 0],
            [['remote_count'], 'integer', 'min' => 0],
            [['attrition_count'], 'integer', 'min' => 0],
            [['user_id', 'date_time', 'dept_id', 'project_id', 'system'], 'required'],
            [['user_id', 'date_time', 'dept_id', 'project_id', 'add_fans_count', 'deposit_count', 'month_deposit_cus_count', 'plan_cus_count', 'new_plan_cus_count', 'tomorrow_plan_cus_count', 'tomorrow_new_plan_cus_count', 'deposit_cus_count', 'new_store_cus_count', 'new_store_cus_count_before', 'remote_count', 'attrition_count'], 'integer'],
            [['deposit_sum'], 'double'],
            [['system'], 'trim'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'user_id' => '客服id',
            'date_time' => '日期',
            'dept_id' => '部门id',
            'project_id' => '项目id',
            'system' => '系统标识',
            'add_fans_count' => '加粉数',
            'deposit_count' => '订金数',
            'month_deposit_cus_count' => '月订金数',
            'plan_cus_count' => '老客预约人数',
            'new_plan_cus_count' => '新客预约人数',
            'tomorrow_plan_cus_count' => '老客明日预约人数',
            'tomorrow_new_plan_cus_count' => '新客明日预约人数',
            'deposit_cus_count' => '订人数',
            'deposit_sum' => '订金金额',
            'new_store_cus_count' => '售后新客到店人数',
            'new_store_cus_count_before' => '售前新客到店人数',
            'remote_count' => '偏远人数',
            'attrition_count' => '流失人数',
            'created_at' => '创建时间',
            'entity_id' => '企业ID',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['user_id', 'date_time', 'dept_id', 'project_id', 'system', 'add_fans_count', 'deposit_count', 'month_deposit_cus_count', 'plan_cus_count', 'new_plan_cus_count', 'tomorrow_plan_cus_count', 'tomorrow_new_plan_cus_count', 'deposit_cus_count', 'deposit_sum', 'new_store_cus_count', 'new_store_cus_count_before', 'remote_count', 'attrition_count'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
            $this->entity_id = UserService::getInst()->current_entity_id;
        }

        return parent::beforeSave($isInsert);
    }

    public function getDept()
    {
        return $this->hasOne(Department::class, ['id' => 'dept_id']);
    }

    public function getUser()
    {
        return $this->hasOne(Member::class, ['id' => 'user_id']);
    }

    public function getDeptAssignment()
    {
        return $this->hasOne(DepartmentAssignment::class, ['user_id' => 'user_id']);
    }

    /**
     * 订人数加粉转化率
     *
     * @return string
     */
    public function getDepositCusCountRate()
    {
        return round($this['deposit_cus_count'] / ($this['add_fans_count'] ?: 1) * 100, 2);
    }

    /**
     * 总加人转到店率
     *
     * @return string
     */
    public function getNewStoreCusCountRate()
    {
        return round($this['new_store_cus_count'] / ($this['add_fans_count'] ?: 1) * 100, 2);
    }

    /**
     * 订金数加人转化率
     *
     * @return string
     */
    public function getDepositCountRate()
    {
        return round($this['deposit_count'] / ($this['add_fans_count'] ?: 1) * 100, 2);
    }

    /**
     * 偏远率
     *
     * @return string
     */
    public function getRemoteCountRate()
    {
        return round($this['remote_count'] / ($this['add_fans_count'] ?: 1) * 100, 2);
    }

    /**
     * 流失率=流失人数/（流失人数+新客到店人数）
     *
     * @return string
     */
    public function getAttritionCountRate()
    {
        return round($this['attrition_count'] / (($this['attrition_count'] + $this['new_store_cus_count']) ?: 1) * 100, 2);
    }

    /**
     * 订金转到店率
     *
     * @return string
     */
    public function getDepositStoreRate()
    {
        return round($this['new_store_cus_count'] / ($this['deposit_cus_count'] ?: 1) * 100, 2);
    }

    /**
     * 月订人数加粉转化率
     */
    public function getMonthDepositCusCountRate()
    {
        return round($this['month_deposit_cus_count'] / ($this['add_fans_count'] ?: 1) * 100, 2);
    }
}
