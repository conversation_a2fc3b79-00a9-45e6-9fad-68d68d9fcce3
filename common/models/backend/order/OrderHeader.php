<?php

namespace common\models\backend\order;

use auth\services\UnionPayService;
use common\components\feishu\multidimensionalTable\AdvanceOrder;
use common\components\report\ReportFactory;
use common\enums\OperateLogTypeEnum;
use common\enums\order\OrderHeaderCheckTimeEnum;
use common\enums\order\OrderHeaderSourceTypeEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\PayStatusEnum;
use common\enums\RerunDepositTypeEnum;
use common\helpers\ArrayHelper;
use common\helpers\DateHelper;
use common\helpers\ResultHelper;
use common\models\backend\CustomerService;
use common\models\backendapi\PromoteChannel;
use common\models\common\DepartmentAssignment;
use common\models\Customer;
use common\models\customer_recharge\CustomerRechargeCard;
use common\models\GroupRecord;
use common\models\OperateLog;
use common\models\OrderPay;
use common\models\TeacherJob;
use common\models\wxcom\CusCustomerUser;
use common\queues\FinancialDataJob;
use common\queues\HourDataEnhanceJob;
use common\queues\AdsAgeGenderDataEnhanceJob;
use common\queues\OperateLogJob;
use common\queues\RerunDepositJob;
use common\queues\TeacherAnalysisJob;
use common\services\customer_recharge\CustomerRechargeCardService;
use common\services\CustomerProductService;
use common\services\material\InventoryUseRecordService;
use console\services\OrderHeaderService;
use common\queues\StorePerformanceAnalysisJob;
use Exception;
use Yii;

/**
 * This is the model class for table "{{%order_header}}".
 *
 * @property int $id 订单ID
 * @property string $order_no 订单编号
 * @property int $cus_id 客户ID
 * @property int $customer_user_id 企微客户成员表ID：erp_wxcom_cus_customer_user表ID
 * @property int $channel_id 渠道ID
 * @property int $store_id 预约门店ID
 * @property int $is_check_time 是否确认时间：0否，1是
 * @property int $plan_time 预约时间
 * @property int $plan_user_id 预约老师UserID：backend_member表ID
 * @property int $plan_teacher_id 预约老师ID：teacher_job表ID
 * @property int $settlement_time 结算时间
 * @property string $plan_remark 预约备注
 * @property int $plan_operate_time 预约操作时间：确定预约时间的操作时间
 * @property int $plan_by 预约人员
 * @property double $original_amount 总原价：商品展示原价总和
 * @property double $amount 订单总金额：不扣除各种优惠
 * @property double $pay_amount 总应支付金额：扣除各种优惠后的支付金额
 * @property double $received_amount 已收金额：客户到店后已经支付的总金额
 * @property double $refund_amount 退款金额：退款金额
 * @property int $cus_recharge_id 客户储值卡ID：customer_recharge_card表ID
 * @property double $card_amount 储值卡抵扣金额
 * @property double $card_real_amount 储值卡实销金额：抵扣金额*（充值金额/(充值金额+赠送金额)）
 * @property double $group_amount 团购折扣金额
 * @property double $coupon_amount 优惠券抵扣金额
 * @property double $integral_amount 积分抵扣金额
 * @property double $shopping_gold_amount 购物金抵扣金额
 * @property int $schedule_num 占用档期小时数
 * @property int $source_type 订单来源：1客服 2门店
 * @property int $order_status 订单状态：0待预约、1已预约、2已取消、3已到店、4待结算、5已完成、6第三方结算、7申请退订、8售后服务、9已放弃、10作废
 * @property double $deposit 总订金金额
 * @property double $other_deposit 第三方预收金
 * @property double $other_service_name 第三方客服人员名称
 * @property int $order_type_id 订单类型id：erp_order_type表ID
 * @property int $reach_time 到店时间
 * @property string $settlement_remark 结单备注
 * @property int $pre_pay_status 订金支付状态：0未支付、2已支付
 * @property int $pre_pay_time 订金收款时间
 * @property string $code 推广代号
 * @property int $promoter_user_id 推广人ID：backend_member表ID
 * @property int $dept_id 部门ID：created_by所属部门
 * @property int $entity_id 企业ID
 * @property int $created_by 创建人
 * @property int $created_at 创建时间
 * @property int $updated_by 修改人
 * @property int $updated_at 修改时间
 * @property int $write_off_time 核销时间：后期确定是否删除
 */
class OrderHeader extends \common\models\Base
{
    const SOURCE_TYPE_SERVICER = 1;
    const SOURCE_TYPE_STORE = 2;

    //合计累计分摊金额
    public $amountList = [];

    // 订单原始数据
    public $originalAttributes = [];

    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%order_header}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_no'], 'unique'],
            [['order_no'], 'string', 'max' => 38],
            [['cus_id'], 'integer', 'min' => 0],
            [['channel_id'], 'integer', 'min' => 0],
            [['store_id'], 'integer', 'min' => 0],
            [['plan_time', 'plan_operate_time'], 'integer', 'min' => 0],
            [['settlement_time'], 'integer', 'min' => 0],
            [['plan_remark'], 'string', 'max' => 200],
            [['plan_by'], 'integer', 'min' => 0],
            [['original_amount'], 'double', 'min' => 0],
            [['amount'], 'double', 'min' => 0],
            [['pay_amount'], 'double', 'min' => 0],
            [['received_amount'], 'double', 'min' => 0],
            [['refund_amount'], 'double', 'min' => 0],
            [['cus_recharge_id'], 'integer', 'min' => 0],
            [['card_amount'], 'double', 'min' => 0],
            [['card_real_amount'], 'double', 'min' => 0],
            [['group_amount'], 'double', 'min' => 0],
            [['coupon_amount'], 'double', 'min' => 0],
            [['integral_amount'], 'double', 'min' => 0],
            [['shopping_gold_amount'], 'double', 'min' => 0],
            [['schedule_num'], 'integer', 'min' => 0],
            [['deposit'], 'double', 'min' => 0],
            [['other_deposit'], 'double', 'min' => 0],
            [['order_type_id'], 'integer', 'min' => 0],
            [['reach_time'], 'integer', 'min' => 0],
            [['settlement_remark', 'other_service_name'], 'string', 'max' => 200],
            [['pre_pay_time'], 'integer', 'min' => 0],
            [['code'], 'string', 'max' => 20],
            [['promoter_user_id'], 'integer', 'min' => 0],
            [['write_off_time'], 'integer', 'min' => 0],
            [['cus_id', 'customer_user_id', 'channel_id', 'store_id', 'is_check_time', 'plan_time', 'plan_operate_time', 'plan_user_id', 'plan_teacher_id', 'settlement_time', 'plan_by', 'cus_recharge_id', 'schedule_num', 'source_type', 'order_status', 'order_type_id', 'reach_time', 'pre_pay_status', 'pre_pay_time', 'promoter_user_id', 'dept_id', 'write_off_time', 'entity_id', 'created_by', 'created_at', 'updated_by', 'updated_at'], 'integer'],
            [['original_amount', 'amount', 'pay_amount', 'received_amount', 'refund_amount', 'card_amount', 'card_real_amount', 'group_amount', 'coupon_amount', 'integral_amount', 'shopping_gold_amount', 'deposit', 'other_deposit'], 'double'],
            [['order_no', 'plan_remark', 'settlement_remark', 'code'], 'trim'],
            ['is_check_time', 'in', 'range' => OrderHeaderCheckTimeEnum::getKeys()],
            ['order_status', 'in', 'range' => OrderHeaderStatusEnum::getKeys()],
            ['source_type', 'in', 'range' => OrderHeaderSourceTypeEnum::getKeys()],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '订单ID',
            'order_no' => '订单编号',
            'cus_id' => '客户ID',
            'customer_user_id' => '企微客户成员表ID',
            'channel_id' => '渠道ID',
            'store_id' => '预约门店ID',
            'is_check_time' => '是否确认时间',
            'plan_time' => '预约时间',
            'plan_user_id' => '预约老师UserID',
            'plan_teacher_id' => '预约老师ID',
            'settlement_time' => '结算时间',
            'plan_remark' => '预约备注',
            'plan_operate_time' => '预约操作时间',
            'plan_by' => '预约人员',
            'original_amount' => '总原价',
            'amount' => '订单总金额',
            'pay_amount' => '总应支付金额',
            'received_amount' => '已收金额',
            'refund_amount' => '退款金额',
            'cus_recharge_id' => '客户储值卡ID',
            'card_amount' => '储值卡抵扣金额',
            'card_real_amount' => '储值卡实销金额',
            'group_amount' => '团购折扣金额',
            'coupon_amount' => '优惠券抵扣金额',
            'integral_amount' => '积分抵扣金额',
            'shopping_gold_amount' => '购物金抵扣金额',
            'schedule_num' => '占用档期小时数',
            'source_type' => '订单来源',
            'order_status' => '订单状态',
            'deposit' => '总订金金额',
            'other_deposit' => '第三方预收金',
            'other_service_name' => '第三方客服人员名称',
            'order_type_id' => '订单类型id',
            'reach_time' => '到店时间',
            'settlement_remark' => '结单备注',
            'pre_pay_status' => '订金支付状态',
            'pre_pay_time' => '订金收款时间',
            'code' => '推广代号',
            'promoter_user_id' => '推广人ID',
            'dept_id' => '部门ID',
            'entity_id' => '企业ID',
            'created_by' => '创建人',
            'created_at' => '创建时间',
            'updated_by' => '修改人',
            'updated_at' => '修改时间',
            'write_off_time' => '核销时间',
        ];
    }

    public function scenarios()
    {
        return [
            'update_for_plan' => ['store_id', 'is_check_time', 'plan_time', 'plan_operate_time', 'plan_user_id', 'plan_teacher_id', 'plan_remark', 'schedule_num'],
            'update_plan_time' => ['plan_time', 'plan_operate_time'],
            'update_plan_by' => ['plan_by', 'created_by', 'source_type'],
            'update_price' => ['amount', 'pay_amount'],
        ];
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->order_no = $this->order_no ?: 'E' . date("YmdHis") . sprintf("%05d", $this->store_id) . mt_rand(100000, 999999);    //E+时间+门店+5位随机数、唯一值
            $this->created_at = $this->created_at ?: time();
            $this->created_by = $this->created_by ?: \services\UserService::getInst()->id;
            $this->entity_id = $this->entity_id ?: \services\UserService::getInst()->currentEntityId;

            $dept = DepartmentAssignment::find()->where(['user_id' => $this->created_by])->one();
            if ($dept) {
                $this->dept_id = $dept->dept_id;
            }
        }
        $this->updated_at = time();
        if (\services\UserService::getInst()->id) {
            $this->updated_by = $this->updated_by ?: \services\UserService::getInst()->id;
        }

        // 修改订单状态重跑订金数
        if ($this->isAttrModified('order_status') || ($this->order_status == OrderHeaderStatusEnum::STATUS_OTHER_SETTLEMENT && $this->isAttrModified('updated_at'))) {
            RerunDepositJob::addJobByTime($this->pre_pay_time, RerunDepositTypeEnum::DEPOSIT);
            RerunDepositJob::addJobByTime($this->plan_time, RerunDepositTypeEnum::DEPOSIT);
            RerunDepositJob::addJobByTime($this->pre_pay_time, RerunDepositTypeEnum::ANALYSIS);
            RerunDepositJob::addJobByTime($this->plan_time, RerunDepositTypeEnum::ANALYSIS);
        }

        if ($this->isAttrModified('plan_by') && $this->order_status == OrderHeaderStatusEnum::STATUS_COMPLETED) {
            RerunDepositJob::addJobByTime($this->plan_time, RerunDepositTypeEnum::DEPOSIT);
        }

        //设置预约操作时间
        if ($this->isAttrModified('is_check_time')) {
            if ($this->is_check_time == OrderHeaderCheckTimeEnum::CHECK_TIME_YES) {
                $this->plan_operate_time = time();
            } else {
                $this->plan_operate_time = 0;
            }
        }

        if ($this->is_check_time == OrderHeaderCheckTimeEnum::CHECK_TIME_YES && $this->isAttrModified('plan_time')) {
            $this->plan_operate_time = time();
        }

        $this->originalAttributes = $this->oldAttributes;

        return parent::beforeSave($isInsert);
    }

    /**
     * 订单保存后运行
     *
     * @param bool $isInsert
     * @param array $changedAttributes
     * @return bool|void
     */
    public function afterSave($isInsert, $changedAttributes)
    {
        //操作日志
        OperateLogJob::addJob([
            'type' => 'order',
            'operate_by' => \services\UserService::getInst()->id,
            'source_id' => $this->id,
            'newData' => $this->toArray(),
            'oldData' => $this->originalAttributes
        ]);

        $orderList = [
            OrderHeaderStatusEnum::STATUS_COMPLETED,
            OrderHeaderStatusEnum::STATUS_AFTER_SALE
        ];
        //重新计算财务数据
        if (in_array($this->order_status, $orderList)) {
            //财务数据
            FinancialDataJob::addJob(['entity_id' => $this->entity_id, 'order_id' => $this->id, 'start_time' => '', 'end_time' => '']);
            $this->addAnalysisJobs($this->plan_time, $this->entity_id);
            if (isset($changedAttributes['plan_time'])) {
                $this->addAnalysisJobs($changedAttributes['plan_time'], $this->entity_id);
            }
        }

        //已完成、售后服务的订单状态发生变更，删除计算好的财务数据
        if (in_array($changedAttributes['order_status'], $orderList) && !in_array($this->order_status, $orderList)) {
            OrderHeaderService::deleteFinancialData([$this->id]);
            $this->addAnalysisJobs($this->plan_time, $this->entity_id);
            if (isset($changedAttributes['plan_time'])) {
                $this->addAnalysisJobs($changedAttributes['plan_time'], $this->entity_id);
            }
        }

        if ($isInsert && !in_array(YII_ENV, ['local', 'dev'])) {
            //下订上报和订单完成也上报
            try {
                $reportService = ReportFactory::getReportServiceByChannelId($this->channel_id);
                $reportService->orderCreatedReport($this);
            } catch (Exception $e) {
                Yii::error('订单号:' . $this->order_no . ',上报失败:' . $e->getMessage());
            }
        }

        //已到店发送通知
        if ($this->order_status == OrderHeaderStatusEnum::STATUS_ARRIVED_STORE && $changedAttributes['order_status'] == OrderHeaderStatusEnum::STATUS_PLAN) {
            AdvanceOrder::sendInform($this);
        }

        \backendapi\services\order\OrderHeaderService::setTableNewCusData($this->id);

        return parent::afterSave($isInsert, $changedAttributes);
    }

    public function getPrePayAtText()
    {
        return DateHelper::toDate($this->pre_pay_time, 'Y-m-d H:i:s');
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    public function getProject()
    {
        return $this->hasMany(OrderProject::class, ['order_id' => 'id']);
    }

    public function getProjects()
    {
        return $this->hasMany(OrderProject::class, ['order_id' => 'id']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getPlanTimeText()
    {
        return DateHelper::toDate($this->plan_time, 'Y-m-d H:i:s');
    }

    public function getPlanOperateTimeText()
    {
        return DateHelper::toDate($this->plan_operate_time, 'Y-m-d H:i:s');
    }

    public function getPrePayTime()
    {
        return DateHelper::toDate($this->pre_pay_time, 'Y-m-d H:i:s');
    }

    public function getSettlementTimeText()
    {
        return DateHelper::toDate($this->settlement_time, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    // 获取订单来源
    public function getSourceTypeName()
    {
        return OrderHeaderSourceTypeEnum::getMap()[$this->source_type];
    }

    // 获取订单状态
    public function getOrderStatusName()
    {
        return OrderHeaderStatusEnum::getMap()[$this->order_status];
    }

    // 获取订单客户手机号
    public function getCusMobile()
    {
        return ResultHelper::mobileEncryption($this->customer->mobile);
    }

    public function getUpdatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'updated_by']);
    }

    public function getPromoterUser()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'promoter_user_id']);
    }

    public function getStoreName()
    {
        return $this->store->store_name ?: '';
    }

    //获取预约客服信息
    public function getCreateBy()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    //获取预约客服信息
    public function getPlanBy()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'plan_by']);
    }

    //获取订单门店信息
    public function getStore()
    {
        return $this->hasOne(\common\models\backend\Store::class, ['id' => 'store_id']);
    }

    //获取用户信息
    public function getCustomer()
    {
        return $this->hasOne(Customer::class, ['id' => 'cus_id']);
    }

    //获取订单渠道信息
    public function getPromoteChannel()
    {
        return $this->hasOne(PromoteChannel::class, ['id' => 'channel_id'])->where('1 = 1');
    }

    //获取其他系统客服绑定ID信息
    public function getCustomerService()
    {
        return $this->hasOne(CustomerService::class, ['id' => 'channel_id']);
    }

    //获取其他系统客服绑定ID信息
    public function getOrderRelay()
    {
        return $this->hasOne(OrderRelay::class, ['order_id' => 'id']);
    }

    // 微信客户客服关系记录
    public function getCustomerUser()
    {
        return $this->hasOne(CusCustomerUser::class, ['id' => 'customer_user_id']);
    }

    // 客户储值卡
    public function getCusRecharge()
    {
        return $this->hasOne(CustomerRechargeCard::class, ['id' => 'cus_recharge_id']);
    }

    // 预约商品列表
    public function getPlanDetails()
    {
        return $this->hasMany(OrderPlanDetail::class, ['order_id' => 'id']);
    }

    // 获取对应推广信息
    public function getPromoteInfo()
    {
        if ($this->customer_user_id > 0) {
            $user = $this->customerUser;
        } else {
            $user = $this->customer;
        }

        return $user;
    }

    // 获取链路
    public function getLinkName()
    {
        return $this->promoteInfo->link->name;
    }

    // 获取定向
    public function getDirectionName()
    {
        return $this->promoteInfo->direction->name;
    }

    // 获取项目
    public function getPromoteProjectName()
    {
        return $this->promoteInfo->ProjectName;
    }

    // 获取账户名称
    public function getSubAdvertiserName()
    {
        return $this->promoteInfo->sub_advertiser_name;
    }

    // 获取账户名称
    public function getAdid()
    {
        return $this->promoteInfo->adid;
    }

    // 档期预约老师
    public function getPlanTeacherJob()
    {
        return $this->hasOne(TeacherJob::class, ['id' => 'plan_teacher_id']);
    }

    //获取预约老师后台信息
    public function getPlanTeacherMember()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'plan_user_id']);
    }

    public function getOrderPays()
    {
        $recordList = array_column($this->orderPay, 'transaction_id');

        return implode(',', $recordList);
    }

    //获取预约老师名称
    public function getPlanTeacherName()
    {
        $planTeacherName = '';
        if ($this->planTeacherJob) {
            $planTeacherName = $this->planTeacherJob->is_virtual ? '虚拟老师-' . $this->planTeacherJob->id : $this->planTeacherMember->realname;
        }

        return $planTeacherName;
    }

    public function getGroupRecord()
    {
        return $this->hasOne(GroupRecord::class, ['order_id' => 'id'])->orderBy('id DESC');
    }

    public function getOrderPay()
    {
        return $this->hasMany(OrderPay::class, ['order_id' => 'id'])->onCondition(['deleted_at' => 0]);
    }

    public function getCusTodayIsPay()
    {
        $start_time = strtotime(date('Y-m-d',$this->plan_time));
        $end_time = $start_time + 86400 - 1;
        $amount = UnionPayService::getUserUnionPayAmount($this->cus_id, $start_time, $end_time);
        if ($amount > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 判断订单是否已核销
     *
     * @return boolean
     */
    public function isPaid()
    {
        return $this->pre_pay_status == PayStatusEnum::YES;
    }

    /**
     * 判断订单是否可以核销
     *
     * @return boolean
     */
    public function canPay()
    {
        $canPayStatusList = [
            OrderHeaderStatusEnum::STATUS_PENDING_APPOINTMENT,
            OrderHeaderStatusEnum::STATUS_PLAN,
        ];
        return in_array($this->order_status, $canPayStatusList) && $this->deposit > 0;
    }

    /**
     * 判断订单是否可以修改预约
     *
     * @return boolean
     */
    public function canUpdateForPlan()
    {
        $allowStatusList = [
            OrderHeaderStatusEnum::STATUS_PENDING_APPOINTMENT,
            OrderHeaderStatusEnum::STATUS_PLAN,
            OrderHeaderStatusEnum::STATUS_ARRIVED_STORE,
        ];
        return in_array($this->order_status, $allowStatusList);
    }

    /**
     * 判断订单是否可以结算
     *
     * @return boolean
     */
    public function canSettlement()
    {
        $canSettlementStatusList = [
            OrderHeaderStatusEnum::STATUS_PLAN,
            OrderHeaderStatusEnum::STATUS_ARRIVED_STORE,
            OrderHeaderStatusEnum::STATUS_SETTLEMENT,
        ];
        return in_array($this->order_status, $canSettlementStatusList);
    }

    /**
     * 到店表数据状态
     *
     * @return array
     */
    public static function reachStoreStatusList()
    {
        return [
            OrderHeaderStatusEnum::STATUS_PLAN,
            OrderHeaderStatusEnum::STATUS_ARRIVED_STORE,
            OrderHeaderStatusEnum::STATUS_SETTLEMENT,
            OrderHeaderStatusEnum::STATUS_COMPLETED,
            OrderHeaderStatusEnum::STATUS_AFTER_SALE
        ];
    }

    /**
     * 订单定金状态-计算客服
     *
     * @return array
     */
    public static function depositOrderStatusList()
    {
        return [0, 1, 3, 4, 5, 6, 8, 9];
    }

    /**
     * 订单定金状态-计算推广
     */
    public static function promoteDepositOrderStatusList()
    {
        return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
    }

    /**
     * 订单完成状态
     *
     * @return array
     */
    public static function orderCompletedStatusList()
    {
        // 当前状态：[5, 8]
        return [
            OrderHeaderStatusEnum::STATUS_COMPLETED,
            OrderHeaderStatusEnum::STATUS_AFTER_SALE
        ];
    }

    /**
     * 订单完成
     *
     * @return boolean
     */
    public function beFinish()
    {
        // 验证订单状态
        $canFinishStatusList = [
            OrderHeaderStatusEnum::STATUS_SETTLEMENT,
        ];
        if (!in_array($this->order_status, $canFinishStatusList)) {
            throw new Exception('订单不可完结');
        }
        if ($this->pay_amount > $this->received_amount) {
            throw new Exception('订单未完全支付');
        }
        if ($this->pay_amount < $this->received_amount) {
            throw new Exception('订单已付金额大于应付金额');
        }

        // 修改订单状态
        $this->order_status = OrderHeaderStatusEnum::STATUS_COMPLETED;
        if (!$this->save()) {
            throw new Exception('订单保存失败');
        }

        // 更新客户项目信息
        /** @var OrderProject $project */
        foreach ($this->projects as $project) {
            if ($project->isCustomerProduct()) {
                CustomerProductService::subProductForOrder($project);
            } elseif ($project->checkSelected()) {
                CustomerProductService::addProductForOrder($project);
            }
            //物料库存明细扣减操作
            // InventoryUseRecordService::addUseRecord($project);
        }
        // 储值卡抵扣
        CustomerRechargeCardService::subForOrder($this);
        // 存在团购优惠时修改团购券状态
        if ($this->group_amount > 0) {
            /** @var GroupRecord $groupRecord */
            $groupRecord = $this->groupRecord;
            $groupRecord->beUsed($this);
        }

        //更新客户首次订单完成时间
        \auth\services\CustomerService::updateCustomerFirstStoreTime($this->cus_id);
        //设置客户首次到店时间
        \auth\services\CustomerService::setCustomerFirstVisitTime($this->cus_id);
    }

    /**
     * 订单反结算
     *
     * @return boolean
     */
    public function revertFinish()
    {
        // 验证订单状态
        $canRevertFinishStatusList = [
            OrderHeaderStatusEnum::STATUS_COMPLETED,
        ];
        if (!in_array($this->order_status, $canRevertFinishStatusList)) {
            throw new Exception('未完成订单不可反结算');
        }

        // 验证是否被划卡
        /** @var OrderProject $project */
        foreach ($this->projects as $project) {
            if ($project->isUsed()) {
                throw new Exception('已划卡订单不可反结算');
            }
        }

        $tran = Yii::$app->db->beginTransaction();
        try {
            // 修改订单状态
            $this->order_status = OrderHeaderStatusEnum::STATUS_SETTLEMENT;
            if (!$this->save()) {
                throw new Exception('订单保存失败');
            }

            // 更新客户项目信息
            /** @var OrderProject $project */
            foreach ($this->projects as $project) {
                if ($project->isCustomerProduct()) {
                    CustomerProductService::revertSubProductForOrder($project);
                } elseif ($project->checkSelected()) {
                    CustomerProductService::revertAddProductForOrder($project);
                }
                //物料库存明细扣减操作
                // InventoryUseRecordService::addUseRecord($project); 
            }

            // 撤销储值卡抵扣
            CustomerRechargeCardService::revertSubForOrder($this);

            // 撤销团购券
            if ($this->group_amount > 0) {
                /** @var GroupRecord $groupRecord */
                $groupRecord = $this->groupRecord;
                $groupRecord->unUsed();
            }

            // 矫正客户首次订单已完成时间
            \auth\services\CustomerService::updateCustomerFirstStoreTime($this->cus_id);
            // 设置客户首次到店时间
            \auth\services\CustomerService::setCustomerFirstVisitTime($this->cus_id);
            $tran->commit();
        } catch (Exception $e) {
            $tran->rollBack();
            throw $e;
        }
    }

    /**
     * 重新计算订单项目分摊
     *
     * @return bool
     * @throws Exception
     */
    public function calcOrderProjects()
    {
        $orderProjects = $this->projects;

        //重新组装数据
        if ($orderProjects) {
            $orderProjects = ArrayHelper::index($orderProjects, null, 'package_id');
        }

        $projectNum = count($orderProjects);
        foreach ($orderProjects as $key => $orderProjectList) {
            $projectNum--;

            if ($key > 0) {
                //将项目按选中项目在前重新排序
                $projectSort = array_column($orderProjectList, 'is_select');
                array_multisort($projectSort, SORT_ASC, $orderProjectList);
            } else {
                //将项目按照抵扣项目在前重新排序，用于后续计算分摊金额最后一笔使用
                $projectSort = array_column($orderProjectList, 'customer_product_id');
                array_multisort($projectSort, SORT_DESC, $orderProjectList);
            }

            $projectNum2 = count($orderProjectList);
            foreach ($orderProjectList as $orderProject) {
                $projectNum2--;
                //判断当前数据是否为最后一条
                if (($projectNum == 0 && $projectNum2 == 0) || ($orderProject['package_id'] > 0 && $projectNum2 == 0)) {
                    $endNum = true;
                } else {
                    $endNum = false;
                }

                // 重新计算金额分摊
                $orderProject->fillGoodsAndAmountInfo($this, $projectNum == 0, $endNum);
                if (!$orderProject->save()) {
                    throw new Exception('订单项目保存失败');
                }
            }
        }

        return true;
    }

    /**
     * 添加分析数据相关队列任务
     *
     * @param int $planTime 预约时间戳
     * @param int $entityId 企业ID
     * @param int $orderId 订单ID
     */
    private function addAnalysisJobs($planTime, $entityId, $orderId = null)
    {
        $dateString = date("Y-m-d", $planTime);
        
        //老师业绩分析
        if ($orderId) {
            // TeacherAnalysisJob::addJob(['order_id' => $orderId, 'plan_time' => $planTime, 'entity_id' => $entityId]);
        }
        //门店业绩
        StorePerformanceAnalysisJob::addJob($dateString, $entityId);
        //时段分析数据完善
        HourDataEnhanceJob::addJob($dateString, $entityId);
        //年龄性别维度数据完善
        AdsAgeGenderDataEnhanceJob::addJob($dateString, $entityId);
    }
}
