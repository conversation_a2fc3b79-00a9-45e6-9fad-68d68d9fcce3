<?php

namespace common\cache;

use common\helpers\Tool;
use Yii;

class WxcomCache extends CacheDocker
{
    private static $date;

    const Pre = 'Report_Statistics:';
    const DYNAMIC_PRE = 'userBehaviorData:';
    const USER_QRCODE = 'userQrcode:';  //动态码存储数据类型
    const LINK = 'link:';//获客助手
    const CUS_ACQUISITION = 'cusAcquisition:';//获客助手-弹窗
    const LAND_PAGE = 'landpage:';//第三方平台的落地页

    const wxComAddFansEvent = 'wxComAddFansEvent:';

    public function __construct()
    {
        static::$date = date('Ymd', time());
        $this->redis = Yii::$app->redis;
    }

    //hash设置
    function hset($keyName, $value)
    {
        if (is_array($value)) $value = json_encode($value);
        $res = $this->redis->hset($this->key, $keyName, $value);
        if ($res) Yii::info('HsetCache,listName:' . $this->key . ',key:' . $keyName . "=>" . json_encode($value), 'redisLog');
        return $res;
    }

    //hash设置
    function newHset($keyName, $cloumn, $value)
    {
        if (is_array($value)) $value = json_encode($value);
        $res = $this->redis->hset($keyName, $cloumn, $value);
        if ($res) Yii::info('HsetCache,listName:' . $this->key . ',key:' . $keyName . "=>" . json_encode($value), 'redisLog');
        return $res;
    }

    //hash获取
    function newHget($keyName, $cloumn)
    {
        $result = $this->redis->hget($keyName, $cloumn);
        if (Tool::is_json($result)) {
            return $result = Tool::jsonToArray(json_decode($result));
        } else {
            return $result;
        }
    }

    function hlen()
    {
        return $this->redis->hlen($this->key);
    }

    function setex($unionid, $expires_time, $val)
    {
        return $this->redis->setex($unionid, $expires_time, $val);
    }
}
