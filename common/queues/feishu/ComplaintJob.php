<?php

namespace common\queues\feishu;

/**
 * 投诉数据处理队列
 *
 * Class ComplaintJob
 */

use common\components\feishu\multidimensionalTable\Complaint as MultidimensionalTableComplaint;
use common\models\complaint\Complaint;
use Yii;
use common\queues\BaseJob;
use Exception;

class ComplaintJob extends BaseJob
{
    // 限制的时间范围：单位秒，默认一分钟
    public $time = 60;
    // 延迟时间：单位秒，默认10
    public $delay = 30;
    //重试次数
    public $retryTimes = 1;
    //投诉ID
    public $complaintId;

    public function run($queue)
    {
        try {
            // 根据ID查询投诉数据
            $complaint = Complaint::findOne($this->complaintId);
            if (!$complaint) {
                Yii::error('投诉数据不存在，ID：' . $this->complaintId, 'ComplaintJob');
                return true;
            }

            // 处理投诉数据到多维度表格
            $complaintTable = new MultidimensionalTableComplaint(['ComCode' => 'chz']);
            $complaintTable->createRecords($complaint);
        } catch (Exception $e) {
            $error = ['error_msg' => $e->getMessage(), 'complaint_id' => $this->complaintId];
            Yii::error('投诉数据同步到多维度表格失败-原因：' . json_encode($error, 256), 'ComplaintJob');
        }
        return true;
    }

    public static function addJob($complaintId)
    {
        $que = Yii::$app->que;
        $job = new static([
            'complaintId' => $complaintId
        ]);

        $isExists = $que->has($job);
        if ($isExists) {
            return true;
        }

        $que->push($job);
    }
}
