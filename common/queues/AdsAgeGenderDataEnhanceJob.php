<?php

namespace common\queues;

use common\services\promote\AdsAgeGenderDataEnhanceService;
use Yii;

/**
 * 年龄性别分析数据完善队列任务
 * Class AdsAgeGenderDataEnhanceJob
 * @package common\queues
 */
class AdsAgeGenderDataEnhanceJob extends BaseJob
{
    // 时间内限制的次数：默认0不限制
    public $times = 30;
    // 限制的时间范围：单位秒，默认一分钟
    public $time = 1;
    // 延迟时间：单位秒，默认10
    public $delay = 1;
    // 日期参数
    public $date = '';
    // 企业ID
    public $entityId = 1;

    /**
     * 执行队列任务
     *
     * @param $queue
     * @return bool
     */
    public function run($queue)
    {
        try {
            // 调用年龄性别分析数据完善服务
            AdsAgeGenderDataEnhanceService::enhanceAgeGenderData($this->date, $this->entityId);
        } catch (\Exception $e) {
            // 记录错误日志但不中断执行
            Yii::error('年龄性别数据完善任务执行失败: ' . $e->getMessage(), __CLASS__);
        }

        return true;
    }

    /**
     * 添加年龄性别数据完善任务到队列
     *
     * @param string $date 日期，格式：Y-m-d
     * @param int $entityId 企业ID
     * @return mixed
     */
    public static function addJob($date = '', $entityId = 1)
    {
        return Yii::$app->que->push(new self([
            'date' => $date ?: date("Y-m-d"),
            'entityId' => $entityId
        ]));
    }
}