<?php

namespace common\queues;

use common\models\wxcom\CusCustomerUser;
use common\services\wxcom\CusCustomerUserService;
use Exception;
use Yii;

/**
 * 客户加粉后-处理
 * 
 * @package common\queues
 *
 */
class CusCustomerUserJob extends BaseJob
{
    public $id;
    //操作类型
    public $type;
    public function run($queue)
    {
        try {
            /**@var CusCustomerUser $cusUser */
            $cusUser = CusCustomerUserService::find()->where(['id' => $this->id])->one();
            if (!$cusUser) {
                return false;
            }

            //检测是否”疑似钓鱼“打标签
            $cusUser->isPossibleFishing();
            return true;
        } catch (Exception $e) {
        }
    }

    public static function addJob(int $id)
    {
        $job = new static([
            'id' => $id,
            'type' => 'isPossibleFishing',
        ]);

        $que = Yii::$app->que->setImportant();
        if ($que->has($job)) {
            return true;
        }

        $que->delay(60)->push($job);
    }

    /**
     * 加粉后120秒处理ip
     */
    public static function addJobDealIp(int $id)
    {
        $job = new static([
            'id' => $id,
            'type' => 'dealIp',
        ]);

        $que = Yii::$app->que;
        if ($que->has($job)) {
            return true;
        }

        $que->delay(120)->push($job);
    }
}
