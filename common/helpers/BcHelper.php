<?php

namespace common\helpers;

/**
 * bc 高精度库
 *
 * 四舍六入(银行家舍入)
 * round(1.2849, 2, PHP_ROUND_HALF_EVEN);
 *
 * Class BcHelper
 * @package common\helpers
 *
 */
class BcHelper
{
    /**
     * 将二个高精确度数字相除
     *
     * @param $dividend
     * @param $divisor
     * @param int $scale
     * @return string|null
     */
    public static function div($dividend, $divisor, $scale = 2)
    {
        $num = $scale + 1;
        if ($divisor == 0) return 0;
        return static::sprintf(bcdiv($dividend, $divisor, $num),$scale);
    }

    /**
     * 将二个高精确度数字相乘
     *
     * @param $dividend
     * @param $divisor
     * @param int $scale
     * @return string|null
     */
    public static function mul($dividend, $divisor, $scale = 2)
    {
        return bcmul($dividend, $divisor, $scale);
    }

    /**
     * 两个高精度数求余/取模
     *
     * @param $dividend
     * @param $divisor
     * @param int $scale
     * @return string|null
     */
    public static function mod($dividend, $divisor, $scale = 2)
    {
        return bcmod($dividend, $divisor, $scale);
    }

    /**
     * 将二个高精确度数字相加
     *
     * @param $left_operand
     * @param $right_operand
     * @param int $scale
     * @return string
     */
    public static function add($left_operand, $right_operand, $scale = 2)
    {
        return bcadd($left_operand, $right_operand, $scale);
    }

    /**
     * 将二个高精确度数字相减
     *
     * @param $left_operand
     * @param $right_operand
     * @param int $scale
     * @return string
     */
    public static function sub($left_operand, $right_operand, $scale = 2)
    {
        return bcsub($left_operand, $right_operand, $scale);
    }

    /**
     * 比较二个高精确度数字
     *
     * @param $left_operand
     * @param $right_operand
     * @param int $scale
     * @return string
     */
    public static function comp($left_operand, $right_operand, $scale = 2)
    {
        return bccomp($left_operand, $right_operand, $scale);
    }

    /**
     * 求一高精确度数字次方值
     *
     * @param $base
     * @param $exponent
     * @param int $scale
     * @return string
     */
    public static function pow($base, $exponent, $scale = 2)
    {
        return bcpow($base, $exponent, $scale);
    }

    /**
     * 求一高精确度数字次方值
     *
     * @param $operand
     * @param null $scale
     * @return string
     */
    public static function sqrt($operand, $scale = null)
    {
        return bcsqrt($operand, $scale);
    }

    /**
     * 设置所有bc数学函数的默认小数点保留位数
     *
     * @param $scale
     * @return bool
     */
    public static function scale($scale)
    {
        return bcscale($scale);
    }

    /**
     * 四舍五入
     *
     * @param $num
     * @param $scale
     * @return float
     */
    private static function round($num, $scale = 2)
    {
        return round($num, $scale);
    }

    /**
     * 保留几位小数  1.2 =》 1.20
     *
     * @param $num
     * @param int $scale
     * @return string
     */
    public static function sprintf($num, $scale = 2)
    {
        if (!is_numeric($num)) {
            $num = 0;
        }
        return sprintf('%01.' . $scale . 'f', $num);
    }

    /**
     * 计算百分比并格式化为百分比字符串
     * 
     * @param $numerator 分子（被除数）
     * @param $denominator 分母（除数）
     * @param int $scale 百分比数值的小数位数，默认2位
     * @param int $calcScale 计算时的精度，默认4位
     * @return string 格式化的百分比字符串，如 "25.50%"
     */
    public static function percentage($numerator, $denominator, $scale = 2, $calcScale = 4)
    {
        if ($denominator == 0) {
            return static::sprintf(0, $scale) . '%';
        }
        $result = static::div($numerator, $denominator, $calcScale);
        $percentage = $result * 100;
        return static::sprintf($percentage, $scale) . '%';
    }
}
