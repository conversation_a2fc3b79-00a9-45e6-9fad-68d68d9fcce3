<?php

namespace common\helpers;

use DateTime;

/**
 * 日期数据格式返回
 *
 * Class DateHelper
 * @package common\helpers
 *
 */
class DateHelper
{
    /**
     * 获取今日开始时间戳和结束时间戳
     *
     * 语法：mktime(hour,minute,second,month,day,year) => (小时,分钟,秒,月份,天,年)
     */
    public static function today()
    {
        return [
            'start' => mktime(0, 0, 0, date('m'), date('d'), date('Y')),
            'end' => mktime(0, 0, 0, date('m'), date('d') + 1, date('Y')) - 1,
        ];
    }

    /**
     * 昨日
     *
     * @return array
     */
    public static function yesterday()
    {
        return [
            'start' => mktime(0, 0, 0, date('m'), date('d') - 1, date('Y')),
            'end' => mktime(0, 0, 0, date('m'), date('d'), date('Y')) - 1,
        ];
    }

    /**
     * 这周
     *
     * @return array
     */
    public static function thisWeek()
    {
        $length = 0;
        // 星期天直接返回上星期，因为计算周围 星期一到星期天，如果不想直接去掉
        if (date('w') == 0) {
            $length = 7;
        }

        return [
            'start' => mktime(0, 0, 0, date('m'), date('d') - date('w') + 1 - $length, date('Y')),
            'end' => mktime(23, 59, 59, date('m'), date('d') - date('w') + 7 - $length, date('Y')),
        ];
    }

    /**
     * 上周
     *
     * @return array
     */
    public static function lastWeek()
    {
        $length = 7;
        // 星期天直接返回上星期，因为计算周围 星期一到星期天，如果不想直接去掉
        if (date('w') == 0) {
            $length = 14;
        }

        return [
            'start' => mktime(0, 0, 0, date('m'), date('d') - date('w') + 1 - $length, date('Y')),
            'end' => mktime(23, 59, 59, date('m'), date('d') - date('w') + 7 - $length, date('Y')),
        ];
    }

    /**
     * 本月
     *
     * @return array
     */
    public static function thisMonth()
    {
        return [
            'start' => mktime(0, 0, 0, date('m'), 1, date('Y')),
            'end' => mktime(23, 59, 59, date('m'), date('t'), date('Y')),
        ];
    }

    /**
     * 某一天往前 X 天
     *
     * @param int $days 往前推的天数，默认30天
     * @param bool $includeToday 是否包含当天，默认包含
     * @param string|int $date 指定的日期，默认为当天，可以是时间戳或日期字符串
     * @return array
     */
    public static function lastDays($days = 30, $includeToday = true, $date = null)
    {
        // 如果没有指定日期，则使用当前日期
        $timestamp = $date === null ? time() : (is_numeric($date) ? $date : strtotime($date));
        
        // 获取指定日期的年、月、日
        $year = date('Y', $timestamp);
        $month = date('m', $timestamp);
        $day = date('d', $timestamp);
        
        // 计算开始日期
        $startDay = $includeToday ? $day - $days + 1 : $day - $days;
        $start = mktime(0, 0, 0, $month, $startDay, $year);
        
        // 计算结束日期
        $end = $includeToday ? 
            mktime(23, 59, 59, $month, $day, $year) : 
            mktime(23, 59, 59, $month, $day - 1, $year);
        
        return [
            'start' => $start,
            'end' => $end,
        ];
    }
    
    /**
     * 上个月
     *
     * @return array
     */
    public static function lastMonth()
    {
        $start = mktime(0, 0, 0, date('m') - 1, 1, date('Y'));
        $end = mktime(23, 59, 59, date('m') - 1, date('t'), date('Y'));

        if (date('m', $start) != date('m', $end)) {
            $end -= 60 * 60 * 24;
        }

        return [
            'start' => $start,
            'end' => $end,
        ];
    }

    /**
     * 几个月前
     *
     * @param integer $month 月份
     * @return array
     */
    public static function monthsAgo($month)
    {
        return [
            'start' => mktime(0, 0, 0, date('m') - $month, 1, date('Y')),
            'end' => mktime(23, 59, 59, date('m') - $month, date('t'), date('Y')),
        ];
    }

    /**
     * 某年
     *
     * @param $year
     * @return array
     */
    public static function aYear($year)
    {
        $start_month = 1;
        $end_month = 12;

        $start_time = $year . '-' . $start_month . '-1 00:00:00';
        $end_month = $year . '-' . $end_month . '-1 23:59:59';
        $end_time = date('Y-m-t H:i:s', strtotime($end_month));

        return [
            'start' => strtotime($start_time),
            'end' => strtotime($end_time)
        ];
    }

    /**
     * 某月
     *
     * @param int $year
     * @param int $month
     * @return array
     */
    public static function aMonth($year = 0, $month = 0)
    {
        $year = $year ?? date('Y');
        $month = $month ?? date('m');
        $day = date('t', strtotime($year . '-' . $month));

        return [
            "start" => strtotime($year . '-' . $month),
            "end" => mktime(23, 59, 59, $month, $day, $year)
        ];
    }

    /**
     * @param int $time
     * @param string $format
     * @return mixed
     */
    public static function getWeekName(int $time, $format = "周")
    {
        $week = date('w', $time);
        $weekname = ['日', '一', '二', '三', '四', '五', '六'];
        foreach ($weekname as &$item) {
            $item = $format . $item;
        }

        return $weekname[$week];
    }

    /**
     * 格式化小时
     *
     * @param array $hours
     * @return array
     */
    public static function formatHours(array $hours)
    {
        $time = 3600 * 24;
        foreach ($hours as &$hour) {
            if ($hour == $time) {
                $hour = '24:00';
            } else {
                $hour = date('H:i', $hour + strtotime(date('Y-m-d')));
            }
        }

        return $hours;
    }

    /**
     * 格式化时间戳
     *
     * @param $time
     * @return string
     */
    public static function formatTimestamp($time)
    {
        $min = $time / 60;
        $hours = $time / 3600;
        $days = floor($hours / 24);
        $hours = floor($hours - ($days * 24));
        $min = floor($min - ($days * 60 * 24) - ($hours * 60));

        return $days . " 天 " . $hours . " 小时 " . $min . " 分钟 ";
    }

    /**
     * 时间戳
     *
     * @param integer $accuracy 精度 默认微妙
     * @return int
     */
    public static function microtime($accuracy = 1000)
    {
        list($msec, $sec) = explode(' ', microtime());
        $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * $accuracy);

        return $msectime;
    }

    /**
     * 时间转换  1594606456  =》 2020-07-13 10:14:16
     *
     * @param int $time 时间搓
     * @param string $format 格式
     * @return false|string
     */
    public static function toDate($time, $format = 'Y-m-d H:i:s')
    {
        return !empty($time) ? date($format, $time) : '';
    }

    /**
     * 获取前一天时间
     *
     * @param string $start_time
     * @param string $end_time
     * @return array
     */
    public static function AgoDate($start_time = '', $end_time = '')
    {
        if (empty($start_time)) {
            $time = strtotime("-1 day");
            $start_time = date('Ym01', $time);
            $end_time = date('Ymd', $time);
        } else {
            $start_time = date('Ymd', $start_time);
            $end_time = date('Ymd', $end_time);
        }

        return [$start_time, $end_time];
    }

    /**
     * 获取逾期天数
     * @param $end_time
     * @param $start_time
     * @return string
     */
    public static function getOverdueTime($end_time, $start_time)
    {
        $now_time = '';
        if (empty($end_time) || empty($start_time)) return $now_time;

        $times = abs($end_time - $start_time); //开始与结束之间相差多少秒
        switch ($times) {
            case $times < 60:
                $now_time = intval(($times / 60)) . '秒前';
                break;
            case $times >= 60 && $times < 3600:
                $now_time = intval(($times / 3600)) . '分前';
                break;
            case $times >= 3600 && $times < 24 * 3600:
                $now_time = intval(($times / (24 * 3600))) . '小时前';
                break;
            case $times >= 24 * 3600:
                $now_time = intval(($times / (24 * 3600))) . '天前';
                break;
        }

        return $now_time;
    }

    /**
     * 获取指定时间
     *
     * @param string $type
     * @return array
     */
    public static function specifiedTime($type = 'thisMonth')
    {
        switch ($type) {
            case 'lastMonth': //上个月时间
                $start = mktime(0, 0, 0, date('m') - 1, 1, date('Y'));
                $end = mktime(0, 0, 0, date('m'), 1, date('Y')) - 1;
                break;
            default:    //本月时间（截止前一天）
                $start = mktime(0, 0, 0, date('m'), 1, date('Y'));
                $end = mktime(0, 0, 0, date('m'), date('d'), date('Y')) - 1;

                if (date('Y-m-d') == date('Y-m-1')) {   //当前时间为1号时，去上个月的时间
                    list($start, $end) = static::specifiedTime('lastMonth');
                }
        }

        return [$start, $end];
    }

    /**
     *获取传值进来判断是否加一天
     *
     * @param $data
     * @param $fields
     * @param bool $isEnd
     * @return false|int
     */
    public static function getRequestTime($data, $fields, $isEnd = false)
    {
        if ($isEnd) {
            $oneDay = 86399;
        } else {
            $oneDay = 0;
        }

        $ss = $data[$fields] ? strtotime(static::toDate(strtotime($data[$fields]), 'Y-m-d')) + $oneDay : '';
        return $ss;
    }

    /**
     * 判断当前时间段是否处于指定时间区间段
     *
     * @param string $date_a
     * @param string $date_b
     * @return bool
     */
    public static function isIntervalTime($date_a = '', $date_b = '')
    {
        if (empty($date_a) || empty($date_b)) return false;

        if ($date_a > $date_b) {    //跨天
            if ((date('H:i:s') >= $date_a && date('H:i:s') <= date('23:59:59')) || (date('H:i:s') >= '00:00:00' && date('H:i:s') <= $date_b)) {
                return true;
            }
        } else {
            if (date('H:i:s') >= $date_a && date('H:i:s') <= $date_b) {
                return true;
            }
        }

        return false;
    }


    /**
     * 获取指定2个日期范围内所有日期
     * @param $startDate
     * @param $endDate
     * @param string $format
     * @return array
     */
    public static function createDateRange($startDate, $endDate, $format = "Y-m-d")
    {
        $begin = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $interval = new \DateInterval('P1D'); // 1 Day
        $dateRange = new \DatePeriod($begin, $interval, $end);
        $range = [];
        foreach ($dateRange as $date) {
            $range[] = $date->format($format);
        }
        $range[] = $endDate;
        return $range;
    }

    /**
     * 日期转时间戳
     *
     * @param string $start_time
     * @param string $end_time
     * @return array
     */
    public static function turnTimestamp($start_time = '', $end_time = '')
    {
        if (empty($start_time)) {
            $start_time = strtotime(date('Y-m-d'));
        } else {
            $start_time = strtotime($start_time);
        }

        if (empty($end_time)) {
            $end_time = strtotime(date('Y-m-d 23:59:59'));
        } else {
            $end_time = strtotime(date($end_time . ' 23:59:59'));
        }

        return [$start_time, $end_time];
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param string $date1 第一个日期，格式为 'YYYY-MM-DD'
     * @param string $date2 第二个日期，格式为 'YYYY-MM-DD'
     * @return int 相差的天数
     */
    public static function getDaysDifference($date1, $date2)
    {
        $datetime1 = new DateTime($date1);
        $datetime2 = new DateTime($date2);
        $interval = $datetime1->diff($datetime2);
        return $interval->days;
    }

    /**
     * 根据传入的时间戳获取当天开始和结束的时间戳
     *
     * @param int $timestamp 时间戳
     * @return array 包含开始和结束时间戳的数组 [开始时间戳, 结束时间戳]
     */
    public static function getDayStartEndTimestamp($timestamp)
    {
        $date = date('Y-m-d', $timestamp);
        
        return [
            strtotime($date . ' 00:00:00'),
            strtotime($date . ' 23:59:59')
        ];
    }

    /**
     * 获取前一天所在月份的第一天到前一天的开始/结束时间戳
     * 
     * @return array 包含开始和结束时间戳的数组 [开始时间戳, 结束时间戳]
     */
    public static function getPreviousMonthStartToYesterdayRange()
    {
        $yesterday = strtotime('-1 day');
        $monthFirstDay = date('Y-m-01', $yesterday);
        
        return [
            strtotime($monthFirstDay . ' 00:00:00'),
            strtotime(date('Y-m-d', $yesterday) . ' 23:59:59')
        ];
    }
    
}
