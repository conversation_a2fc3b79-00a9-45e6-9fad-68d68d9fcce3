<?php

namespace common\services\order;

use auth\models\goods\PayRecordDetail;
use auth\models\OrderPay;
use auth\models\PayRecord;
use auth\models\TeacherType;
use auth\services\view\GoodsUnionService;
use backendapi\services\pay\PayRecordService;
use auth\services\PayRecordService as AuthPayRecordService;
use common\components\feishu\multidimensionalTable\AdvanceOrder;
use common\components\report\ReportFactory;
use common\enums\ChannelTypeEnum;
use common\enums\GoodsTypeEnum;
use common\enums\order\OrderHeaderCheckTimeEnum;
use common\enums\order\OrderHeaderPrePayStatusEnum;
use common\enums\order\OrderHeaderSourceTypeEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\order\PayRecordDetailEnum;
use common\enums\PayRecordStatusEnum;
use common\enums\PayStatusEnum;
use common\enums\ProductTagEnum;
use common\enums\StatusEnum;
use common\enums\StoreBrand;
use common\enums\WhetherEnum;
use common\helpers\ArrayHelper;
use common\helpers\BcHelper;
use common\helpers\DateHelper;
use common\helpers\ResultHelper;
use common\models\backend\goods\Package;
use common\models\backend\goods\PackageDetail;
use common\models\backend\Member;
use common\models\backend\order\OrderHeader;
use common\models\backend\order\OrderPlanDetail;
use common\models\backend\order\OrderProject;
use common\models\backend\Store;
use common\models\backendapi\PromoteChannel;
use common\models\Customer;
use common\models\goods\OrderRelay;
use common\models\goods\Product;
use common\models\OrderTeacher;
use common\models\ProductCategory;
use common\models\promote\Code;
use common\models\TeacherJob;
use common\models\view\GoodsUnion;
use common\models\wechat\FansRecord;
use common\models\wxcom\Com;
use common\models\wxcom\CusCustomer;
use common\models\wxcom\CusCustomerUser;
use common\models\wxcom\User;
use common\queues\feishu\AdvanceOrderJob;
use common\services\BaseService;
use common\services\CustomerService;
use common\services\CustomerServiceService;
use common\services\promote\ChannelService;
use common\models\common\AdsAccountSub;
use Exception;
use services\common\NewLogService;
use services\UserService;
use common\models\StorePayRecord;
use Yii;

class OrderHeaderService extends BaseService
{
    /**
     * @var OrderHeader
     */
    public static $modelClass = OrderHeader::class;
    public static $teacherFields = [];

    /**
     * 获取 query 对象
     *
     * @param array $params
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = parent::getQuery($params);
        $query->andFilterWhere(['=', 'entity_id', \services\UserService::getInst()->currentEntityId]);

        return $query;
    }

    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andWhere(['=', 'id', $id]);
        $query->andFilterWhere(['=', 'entity_id', \services\UserService::getInst()->currentEntityId]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    /**
     * 获取订单详情
     *
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function getView($id)
    {
        OrderHeader::setExtendAttrs([
            'store.store_name' => 'store_name',
            'customer.name' => 'cus_name',
            'customer.nick_name' => 'nick_name',
            'customer.avatar' => 'avatar',
            'customer.remark' => 'remark',
            'customer.mobile_code' => 'cus_mobile_code',
            'customer.mobile' => 'cus_mobile',
            'promoteChannel.name' => 'channel_name',
            'planBy.username' => 'plan_name',
            'customer.wxcom_cus_id' => 'external_userid',
            'customer.generation_id' => 'generation_id',
            'orderRelay.system_code' => 'system_code',
            'orderRelay.third_order_no' => 'third_order_no',
            'created_by_text',
            'plan_teacher_name',
        ]);

        $order = static::$modelClass::find()
            ->select([
                'h.id',
                'h.order_no',
                'h.plan_remark',
                'h.settlement_remark',
                'h.deposit',
                'h.original_amount',
                'h.amount',
                'h.pay_amount',
                'h.group_amount',
                'h.cus_id',
                'h.store_id',
                'h.received_amount',
                'h.refund_amount',
                'h.plan_time',
                'h.plan_user_id',
                'h.plan_teacher_id',
                'h.order_status',
                'h.pre_pay_status',
                'h.created_at',
                'h.created_by',
                'h.plan_by',
                'h.channel_id',
                'h.other_service_name',
                'h.other_deposit'
            ])
            ->alias('h')->with(['createdPerson', 'planBy', 'promoteChannel', 'store', 'customer', 'orderRelay'])
            ->andWhere(['h.id' => $id])
            ->andFilterWhere(['h.entity_id' => UserService::getInst()->currentEntityId])
            ->one();
        if (!$order) throw new Exception('订单不存在，请认真核对！');
        $table_head = static::getTableHead($id);
        $goodsList = static::goodsList($order);
        $order = ArrayHelper::toArray($order);

        $order['system_code'] = StoreBrand::getBrandEnName($order['system_code']);
        $order['order_status_name'] = OrderHeaderStatusEnum::getMap()[$order['order_status']];
        $order['created_at'] = DateHelper::toDate($order['created_at']);
        $order['plan_time'] = DateHelper::toDate($order['plan_time']);
        $order['deposit'] = $goodsList['deposit_sum'];
        $order['goods_name'] = $goodsList['goods_name'];
        $order['goods_list'] = $goodsList['goods_list'];
        $order['table_head'] = $table_head;
        $order['cus_mobile'] = ResultHelper::mobileEncryption($order['cus_mobile']);
        $order['other_amount'] = $order['order_status'] == OrderHeaderStatusEnum::STATUS_OTHER_SETTLEMENT ? OrderRelay::findOne(['order_id' => $order['id']])->amount : 0;

        return $order;
    }

    /**
     * 处理订单商品详情
     *
     * @param $order
     * @return array
     */
    public static function goodsList($order)
    {
        $sumGoodsPrice = 0;
        $projectList = $packageList = $goodsName = $packageIds = [];
        foreach ($order->project as $projects) {
            $orderTeacher = $projects->orderTeacher;
            $orderTeacher = ArrayHelper::index($orderTeacher, 'type');

            if (!$projects->package_name) {
                $goodsProductCode = Product::find()
                    ->select(['code'])
                    ->andWhere(['id' => $projects->goods_id])
                    ->scalar();

                $newProject = [
                    'id' => $projects->goods_id,
                    'name' => $projects->goods_name,
                    'num' => $projects->num,
                    'use_num' => $projects->use_num,
                    'left_num' => $projects->left_num,
                    'goods_code' => $goodsProductCode,
                    'price' => BcHelper::mul($projects->num, $projects->goods_price),
                    'goods_type' => $projects->goods_type,
                    'details' => []
                ];

                $newProject = static::getOrderTeacherData($newProject, $orderTeacher);
                $projectList[] = $newProject;
                $goodsName[] = $projects->goods_name;
                $sumGoodsPrice += BcHelper::mul($projects->num, $projects->goods_price);
            } else {    //套餐
                if (!in_array($projects->package_id, $packageIds)) {
                    $goodsPackageCode = \common\models\goods\Package::find()
                        ->select(['code'])
                        ->andWhere(['id' => $projects->package_id])
                        ->scalar();
                    $goodsName[] = $projects->package_name;
                    $packageIds[] = $projects->package_id;
                    $sumGoodsPrice += BcHelper::mul($projects->num, $projects->package_price);

                    $packageList[$projects->package_id] = [
                        'id' => $projects->package_id,
                        'name' => $projects->package_name,
                        'num' => $projects->num,
                        'use_num' => '-',
                        'left_num' => '-',
                        'goods_code' => $goodsPackageCode,
                        'price' => BcHelper::mul($projects->num, $projects->package_price),
                        'goods_type' => GoodsTypeEnum::PACKAGE,
                    ];
                }

                $newPackage = [
                    'id' => $projects->goods_id,
                    'name' => $projects->goods_name,
                    'num' => $projects->times * $projects->num,
                    'use_num' => $projects->use_num,
                    'left_num' => $projects->left_num,
                    'price' => $projects->goods_price,
                ];

                $newPackage = static::getOrderTeacherData($newPackage, $orderTeacher);
                $packageList[$projects->package_id]['details'][] = $newPackage;
            }
        }
        $goodsName = implode('，', $goodsName);
        $goodsList = array_merge($projectList, $packageList);

        $surplusDeposit = $order->deposit;
        foreach ($goodsList as &$project) {
            if (end($goodsList) == $project) {
                $project['deposit'] = $surplusDeposit;
            } else {
                $project['deposit'] = BcHelper::mul(BcHelper::div($project['price'], $sumGoodsPrice), $order->deposit);
                $surplusDeposit = BcHelper::sub($surplusDeposit, $project['deposit']);
            }
        }

        //        $goodsList = [
        //            'table_head' => static::getTableHead(),   //详情表头字段数据
        //            'data' => $goodsList
        //        ];

        return [
            'deposit_sum' => $order->deposit,
            'goods_name' => $goodsName,
            'goods_list' => $goodsList
        ];
    }

    /**
     * 订单下拉搜索
     *
     * @param $params
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,order_no');
        $query->andFilterWhere(['=', 'entity_id', \services\UserService::getInst()->currentEntityId]);
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andFilterWhere(['OR', ['LIKE', 'id', $params['keyword']], ['LIKE', 'order_no', $params['keyword']]]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    //预约到店列表
    public static function storeList($params = [])
    {
        list($list, $totalCount) = self::storeSearch($params);
        $complete_list = [];
        $complete_totalCount = 0;
        $order_status = $params['order_status'];
        if ($order_status == -1) {
            $params['order_status'] = 5;
            list($complete_list, $complete_totalCount) = self::storeSearch($params);
        }
        //统计状态分组统计数量
        $order = ['all_num' => 0, 'plan_num' => 0, 'settlement_num' => 0, 'complete_num' => 0];
        $order_status_num_arr = self::storeOrderStatusGroupNum($params);
        $total_num = 0;
        foreach ($order_status_num_arr as $value) {
            $total_num += $value['num'];
            if ($value['order_status'] == 1) {
                $order['plan_num'] = $value['num'];
            } else if ($value['order_status'] == 3 || $value['order_status'] == 4) {
                $order['settlement_num'] += $value['num'];
            } else if ($value['order_status'] == 5) {
                $order['complete_num'] = $value['num'];
            }
        }
        $order['all_num'] = $total_num;
        return [$list, $totalCount, $complete_list, $complete_totalCount, $order];
    }

    //门店客户可使用订单
    public static function storeCusList($params = [])
    {
        OrderHeader::setExtendAttrs([
            'store.store_name' => 'store_name',
            'customer.name' => 'cus_name',
            'customer.mobile' => 'cus_mobile',
            'customer.avatar' => 'cus_avatar',
            'customer.age_bracket' => 'cus_age_bracket',
            'customer.gender' => 'cus_gender',
            'createdPerson.realname' => 'realname',
            'project',
        ]);
        $query = OrderHeader::find()->alias('h')->andFilterWhere(['h.entity_id' => UserService::getInst()->currentEntityId])
            ->with(['customer', 'createdPerson', 'project'])
            ->select('h.deposit,h.order_no,h.id,h.cus_id,h.store_id,h.plan_time,h.pay_amount,h.received_amount,h.created_at,h.order_status,h.created_by,h.plan_remark,h.source_type');
        $query->andWhere(['h.pre_pay_status' => OrderHeaderPrePayStatusEnum::HAVE_PAID]);

        //门店
        if ($params['store_id']) {
            $query->andFilterWhere(['h.store_id' => $params['store_id']]);
        }

        $query->andFilterWhere(['<>', 'h.id', $params['order_id']]);

        //本月预约时间
        $query->andWhere(['h.cus_id' => $params['cus_id']])
            ->andFilterWhere(['in', 'h.order_status', [3, 4]])
            ->andFilterWhere(['>=', 'h.plan_time', strtotime(date('Y-m'))]);
        $totalCount = $query->count();
        $list = $query->orderBy('h.plan_time desc')->all();
        $list = ArrayHelper::toArray($list);
        //数据解析
        foreach ($list as &$value) {
            $value['cus_mobile'] = ResultHelper::mobileEncryption($value['cus_mobile']);
            $value['plan_date'] = DateHelper::toDate($value['plan_time']);
            $project = $value['project'];
            $num = count($project);
            $goods_name = '';
            foreach ($project as $k => $value1) {
                $goods_name .= $value1['goods_name'];
                if ($k != $num - 1) {
                    $goods_name .= ',';
                }
            }
            $value['goods_name'] = $goods_name;
            $value['package_name'] = $project[0]['package_name'];
            unset($value['project']);
        }
        return [$list, $totalCount];
    }

    /**
     * 门店订单状态分组统计数量
     * @param array $params
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function storeOrderStatusGroupNum($params = [])
    {
        $query = OrderHeader::find()->alias('h')->andFilterWhere(['h.entity_id' => UserService::getInst()->currentEntityId])
            ->select('h.order_status,count(*) as num')
            ->andWhere(['h.pre_pay_status' => OrderHeaderPrePayStatusEnum::HAVE_PAID]);
        //门店
        if ($params['store_id']) {
            $query->andFilterWhere(['h.store_id' => $params['store_id']]);
        } else {
            return [[], 0];
        }
        // 客户过滤
        if ($params['search_customer']) {
            $cusIds = CustomerService::getIdsByKeyword($params['search_customer'], 1) ?: '-1';
            $query->andFilterWhere(['cus_id' => $cusIds]);
        }
        if ($params['plan_start_time'] && $params['plan_end_time']) {   //订单预约时间
            $query->andFilterWhere(['between', 'h.plan_time', $params['plan_start_time'], $params['plan_end_time']]);
        }
        $query->andFilterWhere(['in', 'h.order_status', [1, 3, 4, 5, 8]]);
        $query->groupBy('h.order_status');
        return $query->asArray()->all();
    }


    //预约到店列表
    public static function storeSearch($params = [])
    {
        OrderHeader::setExtendAttrs([
            'store.store_name' => 'store_name',
            'customer.name' => 'cus_name',
            'customer.mobile' => 'cus_mobile',
            'customer.avatar' => 'cus_avatar',
            'customer.customerProductNum' => 'product_num',
            'planBy.realname' => 'realname',
            'plan_teacher_name',
            'project',
            [
                'field' => 'cus_is_new',
                'value' => function ($row) {
                    // 首次到店时间为空 - 新客
                    if (empty($row->customer->first_store_time)) {
                        return true;
                    }

                    // 当天到店 - 新客
                    $dayBegin = strtotime(date('Y-m-d', $row->plan_time));
                    if ($row->customer->first_store_time >= $dayBegin) {
                        return true;
                    }

                    return false;
                }
            ]
        ]);
        $query = OrderHeader::find()->alias('h')->andFilterWhere(['h.entity_id' => UserService::getInst()->currentEntityId])
            ->with(['customer', 'createdPerson', 'project'])
            ->select([
                'h.id',
                'h.cus_id',
                'h.deposit',
                'h.store_id',
                'h.plan_time',
                'h.created_at',
                'h.created_by',
                'h.pay_amount',
                'h.plan_remark',
                'h.source_type',
                'h.order_status',
                'h.plan_user_id',
                'h.received_amount',
                'h.plan_teacher_id',
                'h.plan_by'
            ]);
        //        $query->andWhere(['h.pre_pay_status' => OrderHeaderPrePayStatusEnum::HAVE_PAID]);

        //门店
        if ($params['store_id']) {
            $query->andFilterWhere(['h.store_id' => $params['store_id']]);
        } else {
            return [[], 0];
        }
        // 客户过滤
        if ($params['search_customer']) {
            $cusIds = CustomerService::getIdsByKeyword($params['search_customer'], 1) ?: '-1';
            $query->andFilterWhere(['cus_id' => $cusIds]);
        }
        if ($params['plan_start_time'] && $params['plan_end_time']) {   //订单预约时间
            $query->andFilterWhere(['between', 'h.plan_time', $params['plan_start_time'], $params['plan_end_time']]);
        }
        if (isset($params['order_status']) && $params['order_status'] > 0) {
            if ($params['order_status'] == 4) {
                $query->andFilterWhere(['in', 'h.order_status', [3, 4]]);
            } else {
                $query->andFilterWhere(['h.order_status' => $params['order_status']]);
            }
        } else {
            $query->andFilterWhere(['in', 'h.order_status', [1, 3, 4, 8]]);
        }
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;
        $totalCount = $query->count();
        $list = $query->offset($offset)->limit($limit)->orderBy('h.plan_time asc')->all();
        $list = ArrayHelper::toArray($list);

        //数据解析
        foreach ($list as &$value) {
            $value['real_mobile'] = $value['cus_mobile'];
            $value['cus_mobile'] = ResultHelper::mobileEncryption($value['cus_mobile']);
            $value['plan_date'] = DateHelper::toDate($value['plan_time']);

            $project = $value['project'];
            $goods_name_arr = static::getOrderGoodsName($project);
            $value['goods_name'] = implode(',', $goods_name_arr);
            unset($value['project']);
        }

        return [$list, $totalCount];
    }

    /**
     * 更新订单状态
     * @param $id
     * @param array $params
     * @return OrderHeader
     * @throws Exception
     */
    public static function updateOrderStatus($id, $params = [])
    {
        $model = static::$modelClass::findOne($id);
        if (!$model) {
            throw new Exception('记录不存在');
        }

        $scopeStoreList = Yii::$app->services->scopeDataService->getMenuStore();
        if ($scopeStoreList && !in_array($model->store_id, $scopeStoreList)) {
            throw new Exception('你暂无该订单的操作权限');
        }

        if ($params['order_status'] == OrderHeaderStatusEnum::STATUS_ARRIVED_STORE) {
            if ($model->order_status > OrderHeaderStatusEnum::STATUS_PLAN) {
                throw new Exception('只有预约才可操作');
            }

            // $store_id = 0;
            // if ($store_id) {
                $teacherInfo =  TeacherJob::find()->alias('tj')
                    ->select('tj.id,tj.user_id')
                    ->leftJoin('{{%backend_member}} m', 'm.id = tj.user_id')
                    ->where(['tj.entity_id' => UserService::getInst()->current_entity_id])
                    ->andWhere(['tj.user_id' => $params['teacher_id'], 'm.status' => 1])
                    ->orderby('tj.id asc')
                    ->one();

                if (empty($teacherInfo) || !$teacherInfo->user_id) {
                    throw new Exception('选择的老师不存在，请刷新重试');
                }

                $model->plan_user_id = $teacherInfo->user_id;
                $model->plan_teacher_id = $teacherInfo->id;
            // }

            $model->order_status = OrderHeaderStatusEnum::STATUS_ARRIVED_STORE;
            $model->reach_time = time();
            if (!$model->save()) {
                throw new Exception(current($model->getFirstErrors()));
            }
        }
        return $model;
    }

    /**
     * 获取列表
     *
     * @param array $params
     * @param bool $is_export
     * @param string $type
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function search($params = [], $is_export = false, $type = '')
    {
        OrderHeader::setExtendAttrs([
            'store.store_name' => 'store_name',
            'customer.name' => 'cus_name',
            'customer.mobile_code' => 'cus_mobile_code',
            'customer.customerProductNum' => 'product_num',
            'cusMobile' => 'cus_mobile',
            'customer.mobile' => 'real_mobile',
            'promoteChannel.name' => 'channel_name',
            'planBy.username' => 'plan_name',
            'created_by_text',
            'created_at_text',
            'plan_time_text',
            'source_type_name',
            'order_status_name',
            'plan_teacher_name',
        ]);

        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query = OrderHeader::find()->alias('h')
            ->with(['customer', 'createdPerson', 'planBy', 'promoteChannel', 'store'])
            ->select('h.id,h.order_no,h.cus_id,h.channel_id,h.store_id,h.plan_user_id,h.plan_teacher_id,h.plan_time,h.created_at,h.plan_by,h.deposit,h.order_status,h.created_by,
                             h.source_type,h.pre_pay_status,h.pay_amount,h.received_amount,h.source_type,h.other_service_name,h.other_deposit')
            ->andFilterWhere(['h.entity_id' => UserService::getInst()->currentEntityId]);

        // 客户过滤
        $query->andFilterWhere(['cus_id' => $params['cus_id']]);
        if ($params['search_customer']) {
            $cusIds = CustomerService::getIdsByKeyword($params['search_customer']) ?: '-1';
            $query->andFilterWhere(['cus_id' => $cusIds]);
        }

        // 创建人过滤
        if ($params['created_by']) {
            $userIds = Member::getIdsByKeyword($params['created_by']) ?: '-1';
            $query->andFilterWhere(['created_by' => $userIds]);
        }

        // 预约人过滤
        if ($params['plan_by']) {
            $userIds = Member::getIdsByKeyword($params['plan_by']) ?: '-1';
            $query->andFilterWhere(['plan_by' => $userIds]);
        }

        // 渠道过滤
        if ($params['channel_name']) {
            $channelIds = ChannelService::getIdsByKeyword($params['channel_name']) ?: '-1';
            $query->andFilterWhere(['channel_id' => $channelIds]);
        }

        // 支付流水号过滤
        if ($params['out_trade_no']) {
            $storePayRecordId = StorePayRecord::find()->select('id')->where(['out_trade_no' => $params['out_trade_no']])->scalar();
            $orderIds = $storePayRecordId ? OrderPay::find()->select('order_id')->where(['store_pay_record_id' => $storePayRecordId, 'deleted_at' => 0])->column() : [];
            $query->andWhere(['h.id' => $orderIds ?: 0]);
        }

        if ($type == 'store') {
            $orderStatusList = [
                OrderHeaderStatusEnum::STATUS_PLAN,
                OrderHeaderStatusEnum::STATUS_ARRIVED_STORE,
                OrderHeaderStatusEnum::STATUS_SETTLEMENT,
                OrderHeaderStatusEnum::STATUS_COMPLETED,
                OrderHeaderStatusEnum::STATUS_OTHER_SETTLEMENT,
                OrderHeaderStatusEnum::STATUS_AFTER_SALE
            ];

            // $query->andWhere(['h.pre_pay_status' => OrderHeaderPrePayStatusEnum::HAVE_PAID])
            //     ->andWhere(['order_status' => $orderStatusList]);
            $query->andWhere(['order_status' => $orderStatusList]);
        }

        $query->andFilterWhere(['h.id' => $params['id']])
            ->andFilterWhere(['like', 'h.order_no', trim($params['order_no'])])
            ->andFilterWhere(['h.order_status' => $params['order_status']])
            ->andFilterWhere(['h.source_type' => $params['source_type']])
            ->andFilterWhere(['h.store_id' => $params['store_id']]);

        if ($params['created_start_time'] && $params['created_end_time']) { //订单创建时间
            $query->andFilterWhere(['between', 'h.created_at', $params['created_start_time'], $params['created_end_time']]);
        }
        if ($params['plan_start_time'] && $params['plan_end_time']) {   //订单预约时间
            $query->andFilterWhere(['between', 'h.plan_time', $params['plan_start_time'], $params['plan_end_time']]);
        }
        if ($params['pre_begin_time'] && $params['pre_end_time']) { //预收金核销时间
            $query->andFilterWhere(['between', 'h.pre_pay_time', $params['pre_begin_time'], $params['pre_end_time']]);
        }
        if ($params['settlement_begin_time'] && $params['settlement_end_time']) { //结算时间
            $query->andFilterWhere(['between', 'h.settlement_time', $params['settlement_begin_time'], $params['settlement_end_time']]);
        }

        $storeScope = Yii::$app->services->scopeDataService->getMenuStore();
        if (!Yii::$app->services->auth->isSuperAdmin()) {
            $query->andWhere(['OR', ['h.created_by' => Yii::$app->user->id], ['h.store_id' => $storeScope]]);
        }

        if ($is_export) { //导出
            if (!empty($params['getTotal'])) return [[], $query->count()]; //获取总条数
        }

        $totalCount = $query->count();
        $list = $query->offset($offset)->limit($limit)->orderBy('h.plan_time DESC')->all();
        $list = ArrayHelper::toArray($list);

        return [$list, $totalCount];
    }

    /**
     * 门店待结算统计
     *
     * @param array $params
     * @return array
     */
    public static function settlementCountByStore($params)
    {
        $query = OrderHeader::find()->alias('h')
            ->select('h.id')
            ->andFilterWhere(['h.entity_id' => UserService::getInst()->currentEntityId])
            ->andFilterWhere(['h.order_status' => $params['order_status']])
            ->andFilterWhere(['h.store_id' => $params['store_id']]);
        $storeScope = Yii::$app->services->scopeDataService->getMenuStore();
        if (!Yii::$app->services->auth->isSuperAdmin()) {
            $query->andWhere(['OR', ['h.created_by' => Yii::$app->user->id], ['h.store_id' => $storeScope]]);
        }
        $totalOrdersCount = $query->count();

        $query = StorePayRecord::find()
            ->select('id')
            ->andWhere(['store_id' => $params['store_id']])
            ->andWhere(['is_use' => WhetherEnum::DISABLED])
            ->andWhere(['>', 'left_amount', 0]);
        $totalTransactionsCount = $query->count();

        return [$totalOrdersCount, $totalTransactionsCount];
    }

    /**
     * 通过推广获取推广工号
     *
     * @param string $brandEn
     * @param string $codeStr
     * @return string
     */
    protected static function getJobNumberBySystemAndCode($brandEn, $codeStr)
    {
        /** @var Code $code */
        $code = Code::find()
            ->select('id,user_id,code')
            ->andWhere([
                'code' => $codeStr,
            ])
            ->one();

        if (!$code) {
            Yii::$app->notice->orderMessage('订单同步ERP系统代号匹配错误！', '', ['message' => "ERP系统暂无匹配数据！系统：“{$brandEn}”、代号：“{$codeStr}”。"]);
            return '';
        }
        if (!$code->user_id || !$code->user) {
            Yii::$app->notice->orderMessage('订单同步ERP系统代号匹配错误！', '', ['message' => "ERP系统当前代号未绑定用户！系统：“{$brandEn}”、代号：“{$codeStr}”。"]);
            return '';
        }
        if (!$code->user->jobnumber) {
            Yii::$app->notice->orderMessage('订单同步ERP系统代号匹配错误！', '', ['message' => "ERP系统当前系统代号绑定的用户暂无工号！系统：“{$brandEn}”、代号：“{$codeStr}”。"]);
            return '';
        }

        return $code->user->jobnumber;
    }

    /**
     * 转单数据erp
     *
     * @param $params
     * @return bool
     */
    public function relayOrderErp($params)
    {
        $logger = new NewLogService();
        $logger->setName('TransferToBis');
        $logger->setLogPath(Yii::getAlias('@backendapi/runtime/logs/goods/TransferToBisQueue' . date('Y-m-d') . '.log'));
        $logger->getLogger()->info($params['order_id'] . '转单信息队列进入', ['>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>']);

        $data = [
            'order_id' => $params['order_id'],
            'goods_names' => $params['goods_names'],
            'system_code' => $params['system_code'],
            'third_order_id' => $params['third_order_id'],
            'third_order_no' => $params['third_order_no'],
            'amount' => $params['third_amount'],
            'entity_id' => $params['entity_id'],
            'created_by' => $params['created_by'],
            'plan_user_id' => $params['plan_user_id'],
            'plan_teacher_id' => $params['plan_teacher_id'],
            'date' => $params['date'],
            'hour_str' => $params['hour_str'],
            'relay_goods_list' => $params['relay_goods_list'],
        ];
        $logger->getLogger()->info($params['order_id'] . '转单信息队列数据：', $data);

        $orderRelay = OrderRelay::find()
            ->andWhere(['order_id' => $params['order_id']])
            ->one();

        if (empty($orderRelay)) {
            $orderRelay = new OrderRelay();
            $orderRelay->attributes = $data;
            if (!$orderRelay->save()) {
                $logger->getLogger()->info($params['order_id'] . '转单信息队列执行失败：', $orderRelay->getErrors());
                Yii::warning("erp_order_relay插入失败，参数：" . json_encode($data), 'relayOrderLog');
                return false;
            }
            $logger->getLogger()->info($params['order_id'] . '转单信息队列执行成功：', []);
        } else {
            $logger->getLogger()->info($params['order_id'] . '转单信息队列执行重复，无需执行：', ArrayHelper::toArray($orderRelay));
        }
        $logger->getLogger()->info($params['order_id'] . '转单信息队列结束', ['------------------------------------------------------------>']);

        return true;
    }

    /**
     * 转单数据erp
     *
     * @param $params
     * @return bool
     */
    public static function relayOrderErp2($params)
    {
        $logger = new NewLogService();
        $logger->setName('TransferToBis');
        $logger->setLogPath(Yii::getAlias('@backendapi/runtime/logs/goods/TransferToBisQueue' . date('Y-m-d') . '.log'));
        $logger->getLogger()->info($params['order_id'] . '转单信息队列进入', ['>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>']);

        $data = [
            'order_id' => $params['order_id'],
            'goods_names' => $params['goods_names'],
            'system_code' => $params['system_code'],
            'third_order_id' => $params['third_order_id'],
            'third_order_no' => $params['third_order_no'],
            'amount' => $params['third_amount'],
            'entity_id' => $params['entity_id'],
            'created_by' => $params['created_by'],
            'plan_user_id' => $params['plan_user_id'],
            'plan_teacher_id' => $params['plan_teacher_id'],
            'date' => $params['date'],
            'hour_str' => $params['hour_str'],
            'relay_goods_list' => $params['relay_goods_list'],
        ];
        $logger->getLogger()->info($params['order_id'] . '转单信息队列数据：', $data);

        $orderRelay = OrderRelay::find()
            ->andWhere(['order_id' => $params['order_id']])
            ->one();

        if (empty($orderRelay)) {
            $orderRelay = new OrderRelay();
            $orderRelay->attributes = $data;
            if (!$orderRelay->save()) {
                $logger->getLogger()->info($params['order_id'] . '转单信息队列执行失败：', $orderRelay->getErrors());
                Yii::warning("erp_order_relay插入失败，参数：" . json_encode($data), 'relayOrderLog');
                return false;
            }
            $logger->getLogger()->info($params['order_id'] . '转单信息队列执行成功：', []);
        } else {
            $logger->getLogger()->info($params['order_id'] . '转单信息队列执行重复，无需执行：', ArrayHelper::toArray($orderRelay));
        }
        $logger->getLogger()->info($params['order_id'] . '转单信息队列结束', ['------------------------------------------------------------>']);

        return true;
    }

    /**
     * 新增
     * @route post /goods/stored-value-card/create
     * {"mobile_code":"86","mobile":"18126284637","cus_id":0,"name":"运精员","cus_gender":1,"cus_avatar":"http://dummyimage.com/100x100","cus_nick_name":"龙平","wxcom_cus_id":87,"channel_id":1,"channel_name":"新1","channel_tag":"广告投放","store_id":262,"store_name":"重庆江北店","brand_en":"lzn","plan_remark":"nisi aute anim","plan_time":1648718246,"goods_list":[{"goods_type":3,"name":"asd123f","price_one":323.15,"deposit":25,"id":70},{"goods_type":1,"name":"asdf","price_one":123.65,"deposit":35,"id":1},{"goods_type":2,"name":"122212asdf","price_one":33.65,"deposit":45,"id":68}],"relay_goods_list":[{"goods_type":1,"name":"半永久补色","price_one":20,"id":38},{"goods_type":2,"name":"asd123f","price_one":323.15,"id":61},{"goods_type":2,"name":"122212asdf","price_one":33.65,"id":70}],"is_check_time":1}
     * @param array $params
     * @return array|bool
     * @throws Exception
     * @remarks 获取客户微信号接口，转单商品，预约老师时间
     */
    public static function create($params = [])
    {
        if (!$params['goods_list'] || !$params['goods_list'][0]) {
            throw new Exception('下单商品不能为空！');
        }

        if ($params['deposit'] && $params['other_deposit']) {
            throw new Exception('订金异常');
        }

        $channel = PromoteChannel::findOne($params['channel_id']);
        if (!$channel) { //校验渠道
            throw new Exception('下单所选渠道不存在，请认真核对！');
        }

        // 判断推广代号
        if ($channel->type != ChannelTypeEnum::THIRD_PARTY && !$params['code']) {
            throw new Exception('请选择推广代号');
        }

        $params['is_other_channel'] = false;
        if ($channel->type == ChannelTypeEnum::THIRD_PARTY) {
            $params['is_other_channel'] = true;
        }

        // 附加推广人信息
        $code = Code::find()
            ->andWhere(['code' => $params['code']])
            ->andWhere(['entity_id' => UserService::getInst()->current_entity_id])
            ->andWhere(['>', 'user_id', 0])
            ->one();
        if ($code) {
            $params['promoter_user_id'] = $code->user_id;
        }

        $model = new self::$modelClass();
        //转单中商品和套餐只能选择一种
        $sourceDetails = $params['goods_list'];
        $goodsTypeArr = array_unique(array_column($sourceDetails, 'goods_type'));
        if ((count($goodsTypeArr) > 1) && in_array(GoodsTypeEnum::PACKAGE, $goodsTypeArr)) { //禁止套餐、单品混合下
            throw new Exception('当前版本禁止套餐、单品混合下单');
        }

        if (!($store = Store::findOne($params['store_id']))) {  //校验门店
            throw new Exception('下单所选门店不存在，请认真核对！');
        }

        //匹配客户信息（客户不存在则插入）
        if (!$params['is_pc']) {
            $customer = self::saveCustomer($params);    //客户信息
        } else {
            $customer = self::saveCustomerPc($params);    //客户信息
        }

        $params['cus_id'] = $customer['id'];
        $params['amount'] = $scheduleNum = 0;  //总金额、总订金金额、档期
        //过滤商品
        foreach ($params['goods_list'] as &$v) {
            if (in_array($v['goods_type'], [GoodsTypeEnum::PROJECT, GoodsTypeEnum::MATERIAL])) {
                $goodsProduct = Product::findOne(['id' => $v['id'], 'entity_id' => UserService::getInst()->currentEntityId]);
            } else {
                $goodsProduct = Package::findOne(['id' => $v['id'], 'entity_id' => UserService::getInst()->currentEntityId]);
            }
            if (empty($goodsProduct)) throw new Exception('商品“' . $v['name'] . '”不存在，请认真核对');

            //限制商品标签限购
            CustomerServiceService::checkTagLimit($goodsProduct, $params['cus_id']);
            $productSchedule = ProductCategory::getScheduleNum($goodsProduct->cate_id);    //获取档期
            $scheduleNum = $scheduleNum > $productSchedule ? $scheduleNum : $productSchedule;  //比较档期，以最长时间段档期为主
            //检测档期占用
            if ($params['is_check_time'] == OrderHeaderCheckTimeEnum::CHECK_TIME_YES) {
                static::checkSchedule($params, $productSchedule);
            }

            $params['amount'] += $v['price_one'];
            $params['original_price'] += $goodsProduct->original_price;
            $params['deposit'] += $v['deposit'];
            $params['other_deposit'] += $v['other_deposit'];
            $v['original_price'] = $goodsProduct->original_price;
        }
        $params['source_type'] = OrderHeaderSourceTypeEnum::SOURCE_TYPE_CUSTOMER;
        $params['order_status'] = $params['is_check_time'] ? OrderHeaderStatusEnum::STATUS_PLAN : OrderHeaderStatusEnum::STATUS_PENDING_APPOINTMENT;
        $params['schedule_num'] = $scheduleNum;
        $params['store_name'] = $store->store_name;
        $params['channel_name'] = $channel->name;
        $params['dingtalk_unionid_id'] = Yii::$app->user->identity->dingtalk_unionid_id;

        //支付金额
        $params['pay_amount'] = $params['amount'];
        if ($params['is_check_time'] == OrderHeaderCheckTimeEnum::CHECK_TIME_YES) {
            $params['plan_by'] = Yii::$app->user->id;
        }

        //判断商品购买次数限制
        foreach ($params['goods_list'] as $value) {
            if (empty($v['customer_product_id'])) { //抵扣项目不验证出售限制
                continue;
            }

            //套餐判断
            if ($value['goods_type'] == GoodsTypeEnum::PACKAGE) {
                $package = Package::find()->andWhere(['id' => $value['id']])->one();
                //限制商品限购
                GoodsUnionService::checkoutGoodsLimit($package, $params['cus_id'], GoodsTypeEnum::PACKAGE);
            } else {
                $product = Product::find()->andWhere(['id' => $value['id']])->one();
                //限制商品限购
                GoodsUnionService::checkoutGoodsLimit($product, $params['cus_id'], GoodsTypeEnum::PROJECT);
            }
        }

        //判断老师档期是否被占用
        $is_check_time = $params['is_check_time'];
        if ($is_check_time == 1) {
            $teacherJob = TeacherJob::find()
                ->select('id')
                ->andWhere([
                    'id' => $params['plan_teacher_id'],
                    'store_id' => $params['store_id']
                ])
                ->scalar();
            if (empty($teacherJob)) {
                throw new Exception('下单门店暂无该老师档期');
            }
        }
        //记录下单客服对应子表ID
        if (!$params['is_pc'] && ($wxcom_user_id = \mobileapi\services\UserService::getCurrentWxcomUserId())) {
            $params['customer_user_id'] = CusCustomerUser::findOne(['cus_id' => $params['wxcom_cus_id'], 'user_id' => $wxcom_user_id])->id;
        }

        //保存订单
        if (isset($model->scenarios()['create'])) $model->scenario = 'create';
        $model->attributes = $params;
        if ($model->deposit == 0) $model->pre_pay_status = PayStatusEnum::YES;
        if (!$model->save()) throw new Exception('新增订单失败');

        $params['order_id'] = $model->id;
        $params['order_no'] = $model->order_no;
        //订单明细
        if (isset($params['goods_list']) && is_array($params['goods_list'])) {
            self::saveDetails($params);
        }

        $customerModel = Customer::findOne($model->cus_id);
        if ($customerModel) {
            $customerModel->fillPromoteInfo($model);
        }

        $data = [
            'id' => $model->id,
            'order_no' => $model->order_no,
            'deposit' => $model->deposit,
            'created_by' => $model->created_by,
            'created_by_text' => $model->createdByText,
            'plan_by' => $model->plan_by,
            'plan_name' => $model->planBy->username,
            'created_at' => DateHelper::toDate($model->created_at),
        ];
        $data['customer'] = $customer;
        return $data;
    }

    /**
     * 新版新增预约
     *
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function newCreate($params)
    {
        if (!$params['goods_list']) throw new Exception('下单商品、转单商品都不能为空！');

        //售前下单过滤
        if ($params['source_type'] == OrderHeaderSourceTypeEnum::SOURCE_TYPE_CUSTOMER) {
            $params = static::promoteCheck($params);
        }

        if ($params['source_type'] != OrderHeaderSourceTypeEnum::SOURCE_TYPE_CUSTOMER) {
            $params['relay_goods_list'] = [];
        }

        //校验门店
        $store = Store::findOne($params['store_id']);
        if (!$store) {
            throw new Exception('下单所选门店不存在，请认真核对！');
        }

        if (!$store->brand) {
            throw new Exception('下单所选门店暂无品牌信息，请先维护门店品牌信息！');
        }

        if (!($channel = PromoteChannel::findOne($params['channel_id']))) { //校验渠道
            throw new Exception('下单所选渠道不存在，请认真核对！');
        }

        //过滤商品
        $params['amount'] = $scheduleNum = 0;  //总金额、总订金金额、档期
        foreach ($params['goods_list'] as &$v) {
            if (!$v) throw new Exception('选择的商品异常，请刷新后重试！');
            $goodsProduct = GoodsUnion::findOne(['id' => $v['id'], 'goods_type' => $v['goods_type']]);

            if (empty($goodsProduct)) throw new Exception('商品“' . $v['name'] . '”不存在，请认真核对！');
            if (empty($v['customer_product_id'])) { //抵扣项目不验证出售限制
                // 验证商品可售门店
                $goodsProduct->checkStore($params['store_id']);
                // 验证商品可选角色
                $goodsProduct->checkRole();
            }

            $productSchedule = ProductCategory::getScheduleNum($goodsProduct->cate_id);    //获取档期
            $scheduleNum = $scheduleNum > $productSchedule ? $scheduleNum : $productSchedule;  //比较档期，以最长时间段档期为主

            if (empty($v['customer_product_id'])) {
                $params['amount'] += $v['price_one'];
                $v['price_one'] = $goodsProduct['price_one'];
            } else {
                $v['price_one'] = 0;
            }

            $params['original_price'] += $v['original_price'];
            $v['original_price'] = $goodsProduct['original_price'];
        }

        $params['schedule_num'] = $scheduleNum;
        $params['channel_name'] = $channel->name;
        $params['store_name'] = $store->store_name;
        $params['other_service_name'] = Yii::$app->user->identity->realname;
        $params['brand_en'] = StoreBrand::getBrandEn($store->brand);
        $params['dingtalk_unionid_id'] = Yii::$app->user->identity->dingtalk_unionid_id;
        if ($params['order_status'] != OrderHeaderStatusEnum::STATUS_ARRIVED_STORE) {   //非开单收银处下单（actionStoreCreate()）
            $params['order_status'] = $params['is_check_time'] ? OrderHeaderStatusEnum::STATUS_PLAN : OrderHeaderStatusEnum::STATUS_PENDING_APPOINTMENT;
        }

        //支付金额
        if (is_int($params['plan_time']) && strlen($params['plan_time']) >= 10) {    //是否确认时间预约时间
            $params['is_check_time'] = OrderHeaderCheckTimeEnum::CHECK_TIME_YES;
            $params['plan_by'] = Yii::$app->user->id;

            $currentMonth = strtotime(date('Y-m'));
            if ($params['plan_time'] < $currentMonth) {
                throw new Exception('档期只允许下当月的单子，如需下跨月的单子，先下完单找信息部修改');
            } 
        }

        //匹配客户信息（客户不存在则插入）
        if (!$params['is_pc']) {
            $customer = self::saveCustomer($params);    //客户信息

            //记录下单客服对应子表ID
            if (($wxcom_user_id = \mobileapi\services\UserService::getCurrentWxcomUserId())) {
                $params['customer_user_id'] = CusCustomerUser::findOne(['cus_id' => $params['wxcom_cus_id'], 'user_id' => $wxcom_user_id])->id;
            }
        } else {
            $customer = self::saveCustomerPc($params);    //客户信息
        }
        $params['cus_id'] = $customer['id'];

        //判断商品购买次数限制
        static::checkGoodsLimited($params);

        //保存订单
        $model = new self::$modelClass();
        if (isset($model->scenarios()['create'])) $model->scenario = 'create';
        $model->attributes = $params;
        if ($model->deposit == 0) $model->pre_pay_status = PayStatusEnum::YES;
        if (!$model->save()) {
            throw new Exception('新增订单失败');
        }

        $params['order_id'] = $model->id;
        $params['order_no'] = $model->order_no;
        //订单明细
        if (isset($params['goods_list']) && is_array($params['goods_list'])) {
            self::saveDetails($params);
        }

        $data = [
            'id' => $model->id,
            'order_no' => $model->order_no,
            'deposit' => $model->deposit,
            'created_by' => $model->created_by,
            'created_by_text' => $model->createdByText,
            'plan_by' => $model->plan_by,
            'plan_name' => $model->planBy->username,
            'created_at' => DateHelper::toDate($model->created_at),
            'customer' => $customer
        ];

        return $data;
    }

    /**
     * 售前下单过滤
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    public static function promoteCheck($params)
    {
        if (!$params['goods_list'] || !$params['relay_goods_list'] || !$params['relay_goods_list'][0]) throw new Exception('下单商品、转单商品都不能为空！');

        // 判断推广代号
        if (!$params['code']) {
            throw new Exception('请选择推广代号');
        }

        // 附加推广人信息
        $code = Code::find()
            ->andWhere(['entity_id' => UserService::getInst()->current_entity_id])
            ->andWhere(['code' => $params['code']])
            ->andWhere(['>', 'user_id', 0])
            ->one();

        if ($code) {
            $params['promoter_user_id'] = $code->user_id;
        }

        //转单中商品和套餐只能选择一种
        $sourceDetails = $params['goods_list'];
        $goodsTypeArr = array_unique(array_column($sourceDetails, 'goods_type'));
        if ((count($goodsTypeArr) > 1) && in_array(GoodsTypeEnum::PACKAGE, $goodsTypeArr)) { //禁止套餐、单品混合下
            throw new Exception('当前版本禁止套餐、单品混合下单');
        }

        $sourceDetails = $params['relay_goods_list'];
        $goodsTypeArr = array_unique(array_column($sourceDetails, 'goods_type'));
        if ((count($goodsTypeArr) > 1) && in_array(GoodsTypeEnum::PACKAGE, $goodsTypeArr)) {
            throw new Exception('当前版本转单时禁止套餐、单品混合下单');
        }

        return $params;
    }

    /**
     * 判断商品购买次数限制
     *
     * @param $params
     * @return bool
     * @throws Exception
     */
    public static function checkGoodsLimited($params)
    {
        foreach ($params['goods_list'] as $value) {
            if ($value['customer_product_id'] > 0) {    //抵扣项目不限制
                continue;
            }

            $goodsId = $value['id'];
            //套餐判断
            if ($value['goods_type'] == GoodsTypeEnum::PACKAGE) {
                $package = Package::find()->andWhere(['id' => $value['id']])->one();
                if ($package['has_buy_limit'] == 1) {
                    $hasNum = OrderHeader::find()->joinWith(['planDetails' => function ($query) use ($goodsId) {
                        $query->andWhere(['goods_id' => $goodsId]);
                        $query->andWhere(['goods_type' => GoodsTypeEnum::PACKAGE]);
                    }])->andWhere(['cus_id' => $params['cus_id']])
                        ->andWhere(['not in', 'order_status', [OrderHeaderStatusEnum::STATUS_APPLY_UNSUBSCRIBE, OrderHeaderStatusEnum::STATUS_INVALID]])
                        ->count();
                    //判断商品购买次数限制 已买的 商品限制购买次数
                    if (intval($hasNum) >= intval($package['limit_times'])) {
                        throw new Exception($package['name'] . '超出购买次数');
                    }
                }
            } else {
                $product = Product::find()->andWhere(['id' => $value['id']])->one();
                if ($product['has_buy_limit'] == 1) {
                    $hasNum = OrderHeader::find()->joinWith(['project' => function ($query) use ($goodsId) {
                        $query->andWhere(['goods_id' => $goodsId]);
                        $query->andWhere(['<>', 'goods_type', GoodsTypeEnum::PACKAGE]);
                    }])->andWhere(['cus_id' => $params['cus_id']])
                        ->andWhere(['not in', 'order_status', [OrderHeaderStatusEnum::STATUS_APPLY_UNSUBSCRIBE, OrderHeaderStatusEnum::STATUS_INVALID]])
                        ->count();
                    //判断商品购买次数限制 已买的+当前购买数 商品限制购买次数
                    if (intval($hasNum) >= intval($product['limit_times'])) {
                        throw new Exception($product['name'] . '商品超出购买次数');
                    }
                }
            }
        }

        //查询转单信息
        if ($params['source_type'] == OrderHeaderSourceTypeEnum::SOURCE_TYPE_CUSTOMER) {
            //判断是否为bis体验商品 用户是否已下过单
            $dbName = $params['brand_en'];
            $userInfo = Yii::$app->$dbName
                ->createCommand('SELECT id,username FROM qd_user WHERE username=:username OR mobile=:mobile')
                ->bindValue(':username', $params['mobile'])
                ->bindValue(':mobile', $params['mobile'])
                ->queryOne();
            //存在用户才判断
            if ($userInfo) {
                //获取门店数据
                $storeInfo = Yii::$app->$dbName
                    ->createCommand('SELECT id,store_name,brand FROM qd_store WHERE store_name=:store_name')
                    ->bindValue(':store_name', $params['store_name'])->queryOne();
                if (!$storeInfo) {
                    throw new Exception('bis门店不存在！');
                }

                foreach ($params['relay_goods_list'] as $val) {
                    //套餐判断
                    if ($val['goods_type'] == GoodsTypeEnum::PACKAGE) {
                        $productMix = Yii::$app->$dbName
                            ->createCommand("SELECT id,`type`,mix_name FROM qd_product_mix WHERE id=:id")
                            ->bindValue(':id', $val['id'])->queryOne();
                        //体验类型
                        if ($productMix['type'] == '体验卡套餐') {
                            $order = Yii::$app->$dbName
                                ->createCommand('SELECT * FROM qd_order_header WHERE buyer_id=:buyer_id and mix_id=:mix_id')
                                ->bindValue(':buyer_id', $userInfo['id'])
                                ->bindValue(':mix_id', $val['id'])
                                ->queryOne();
                            if ($order) {
                                throw new Exception($productMix['mix_name'] . '体验套餐,客户已在bis系统下过单不能体验');
                            }
                        }
                    } else {
                        $product = Yii::$app->$dbName
                            ->createCommand('SELECT id,tag,title FROM qd_product WHERE id=:id')
                            ->bindValue(':id', $val['id'])->queryOne();
                        //体验类型
                        if ($product['tag'] == ProductTagEnum::EXPERIENCE_PROJECT) {
                            $order = Yii::$app->$dbName
                                ->createCommand('SELECT * FROM qd_order_header h LEFT JOIN qd_order_detail d ON h.id = d.order_id WHERE h.mix_id=0 and buyer_id=:buyer_id  and d.product_id=:product_id')
                                ->bindValue(':buyer_id', $userInfo['id'])
                                ->bindValue(':product_id', $val['id'])
                                ->queryOne();
                            if ($order) {
                                throw new Exception($product['title'] . '体验商品,客户已在bis系统下过单不能体验');
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    //项目档期占用查询-bis
    public static function scheduleNumQuery($db, $product, $service_type = 0)
    {
        $map = self::belongsToMap($db);
        $cat_id = $product['cat_id'];
        $root_id = $map[$cat_id] ?? 0;
        if ($service_type == 1 && (strpos(($product['title']), '补色') !== false || strpos(($product['title']), '洗眉') !== false || strpos(($product['title']), '洗眼线') !== false)) {
            return 1;
        }
        if ($root_id == 26) {
            return 2;
        }
        if ($root_id == 12) {
            return 3;
        }
        return 1;
    }

    /***
     * 获取分类归属map，数据结构如下，子id对应一级分类的id
     *
     *  [
     *      1 => 1,
     *      2 => 1,
     *      3 => 1,
     *      4 => 4,
     *      5 => 5,
     *      6 => 4
     *  ]
     *
     * @return array
     */
    public static function belongsToMap($db)
    {
        $data = $db->createCommand("SELECT `id`, `parent_id` FROM `qd_prodcut_category` WHERE `cat_type`='普通'")
            ->queryAll();
        $data = ArrayHelper::index($data, 'id');
        $categories = [];
        foreach ($data as $k => $c) {
            if (empty($c['parent_id']) || empty($data[$c['parent_id']])) {
                $categories[$k] = $k;
                continue;
            }
            $p = $data[$c['parent_id']];
            $deep = 1; //深度
            while (true) {
                if ($deep > 10) { //防止死循环
                    break;
                }
                if (empty($p['parent_id']) || empty($data[$p['parent_id']])) {
                    $categories[$k] = $p['id'];
                    break;
                }
                $p = $data[$p['parent_id']];
                $deep++;
            }
        }
        return $categories;
    }

    /**
     * 检测档期占用
     *
     * @param $params
     * @param $scheduleNum
     * @return bool
     * @throws Exception
     */
    public static function checkSchedule($params, $scheduleNum)
    {
        if (empty($params['cus_id']) || empty($params['store_id']) || empty($params['plan_teacher_id']) || empty($params['plan_time'])) {
            throw new Exception('检测档期占用数据异常');
        }

        $planStartTime = $params['plan_time'];
        $planEndTime = $planStartTime + $scheduleNum * 60 - 1;  //占用档期区间-1秒，防止整点占用
        $order = self::$modelClass::find()
            ->andWhere([
                'store_id' => $params['store_id'],
                'plan_teacher_id' => $params['plan_teacher_id'],
                'order_status' => [
                    OrderHeaderStatusEnum::STATUS_PLAN,
                    OrderHeaderStatusEnum::STATUS_ARRIVED_STORE,
                    OrderHeaderStatusEnum::STATUS_SETTLEMENT
                ]
            ])
            ->andWhere([
                'BETWEEN',
                'plan_time',
                $planStartTime,
                $planEndTime
            ])
            ->one();

        if (empty($order)) {
            return true;
        }

        if ($params['cus_id'] != $order->cus_id) {
            throw new Exception('该老师当前档期被占用');
        }

        return true;
    }

    /**
     * 保存订单详情
     *
     * @param array $params
     * @throws Exception
     */
    public static function saveDetails($params = [])
    {
        $sourceDetails = $params['goods_list'];
        $data = $detailData = [];
        $userId = Yii::$app->user->id;
        $entityId = Yii::$app->user->identity->current_entity_id;

        foreach ($sourceDetails as $detail) {
            //类型：1服务，2客装，3套餐
            $type = (int)$detail['goods_type'];
            if (!in_array($type, [GoodsTypeEnum::PROJECT, GoodsTypeEnum::MATERIAL, GoodsTypeEnum::PACKAGE])) {
                throw new Exception('商品类型错误');
            }
            //订单预约子表数据
            $detailData[] = [
                'order_id' => $params['order_id'],
                'goods_id' => $detail['id'],
                'goods_type' => $type,
                'original_price' => $detail['original_price'],
                'goods_price' => $detail['price_one'],
                'deposit' => ArrayHelper::getValue($detail, 'deposit', 0),
                'entity_id' => $entityId,
                'created_by' => $userId,
                'created_at' => time(),
            ];

            //订单项目表数据
            if (($type == GoodsTypeEnum::PROJECT) || ($type == GoodsTypeEnum::MATERIAL)) {
                $data[] = [
                    'order_id' => $params['order_id'],
                    'num' => $detail['num'] ?: 1,
                    'times' => $detail['customer_product_id'] ? ($detail['num'] ?: 1) : ($detail['times'] ?: 1),
                    'left_num' => $detail['customer_product_id'] ? ($detail['num'] ?: 1) : BcHelper::mul(($detail['num'] ?: 1), ($detail['times'] ?: 1)),
                    'max_times' => $detail['max_times'] ?: 1,
                    'goods_id' => $detail['id'],
                    'goods_type' => $type,
                    'goods_name' => $detail['name'] ?? '',
                    'goods_price' => $detail['price_one'] ?? 0,
                    'goods_original_price' => $detail['original_price'] ?? 0,
                    'customer_product_id' => $detail['customer_product_id'] ?? 0,
                    'package_id' => 0,
                    'package_name' => '',
                    'package_price' => 0,
                    'package_original_price' => 0,
                    'child_package_id' => 0,
                    'child_package_name' => '',
                    'child_package_price' => 0,
                    'entity_id' => $entityId,
                    'created_by' => $userId,
                    'created_at' => time(),
                    'updated_by' => $userId,
                    'updated_at' => time(),
                ];
            }
            if ($type == GoodsTypeEnum::PACKAGE) {
                $packageDetailList = PackageDetail::find()
                    ->select('package_id,goods_id,goods_type,times,max_times')
                    ->where(['package_id' => $detail['id']])
                    ->indexBy('goods_id')
                    ->asArray()
                    ->all();

                //区分是否套餐嵌套餐数据
                $childPackageId = $goodsId = [];
                foreach ($packageDetailList as $value) {
                    if ($value['goods_type'] == GoodsTypeEnum::PACKAGE) {
                        $childPackageId[] = $value['goods_id'];
                    } else {
                        $goodsId[] = $value['goods_id'];
                    }
                }

                $childPackageList = Package::find()
                    ->select('pd.package_id,p.name as package_name,p.price_one as package_price_one,pd.goods_id,pd.times,pr.name as goods_name,pr.goods_type,pr.price_one as goods_price_one,pr.original_price AS goods_original_price,pd.max_times')
                    ->alias('p')
                    ->leftJoin('{{%goods_package_detail}} pd', 'pd.package_id=p.id')
                    ->leftJoin('{{%goods_product}} pr', 'pd.goods_id=pr.id')
                    ->where(['in', 'p.id', $childPackageId])
                    ->asArray()
                    ->all();

                foreach ($childPackageList as $childValue) {
                    $data[] = [
                        'order_id' => $params['order_id'],
                        'num' => $detail['num'] ?: 1,
                        'times' => $childValue['times'],
                        'left_num' => BcHelper::mul(($detail['num'] ?: 1), $childValue['times']),
                        'max_times' => $childValue['max_times'] ?? 0,
                        'goods_id' => $childValue['goods_id'],
                        'goods_type' => $childValue['goods_type'],
                        'goods_name' => $childValue['goods_name'],
                        'goods_price' => $childValue['goods_price_one'],
                        'goods_original_price' => $childValue['goods_original_price'],
                        'customer_product_id' => 0,
                        'package_id' => $detail['id'],
                        'package_name' => $detail['name'],
                        'package_price' => $detail['price_one'],
                        'package_original_price' => $detail['original_price'],
                        'child_package_id' => $childValue['package_id'],
                        'child_package_name' => $childValue['package_name'],
                        'child_package_price' => $childValue['package_price_one'],
                        'entity_id' => $entityId,
                        'created_by' => $userId,
                        'created_at' => time(),
                        'updated_by' => $userId,
                        'updated_at' => time(),
                    ];
                }

                $goodsList = Product::find()
                    ->select('id,name,price_one,original_price,goods_type')
                    ->where(['id' => $goodsId])
                    ->andWhere(['entity_id' => Yii::$app->user->identity->current_entity_id])
                    ->asArray()
                    ->all();
                if ($goodsId && !$goodsList) throw new Exception('套餐归属的商品不存在，请联系管理员！');

                foreach ($goodsList as $goodsValue) {
                    $data[] = [
                        'order_id' => $params['order_id'],
                        'num' => $detail['num'] ?: 1,
                        'times' => $detail['customer_product_id'] ? ($detail['num'] ?: 1) : $packageDetailList[$goodsValue['id']]['times'],
                        'left_num' => $detail['customer_product_id'] ? ($detail['num'] ?: 1) : BcHelper::mul(($detail['num'] ?: 1), $packageDetailList[$goodsValue['id']]['times']),
                        'max_times' => $packageDetailList[$goodsValue['id']]['max_times'],
                        'goods_id' => $goodsValue['id'],
                        'goods_type' => $goodsValue['goods_type'],
                        'goods_name' => $goodsValue['name'],
                        'goods_price' => $goodsValue['price_one'] ?? 0,
                        'goods_original_price' => $goodsValue['original_price'],
                        'customer_product_id' => $detail['customer_product_id'] ?? 0,
                        'package_id' => $detail['id'],
                        'package_name' => $detail['name'],
                        'package_price' => $detail['price_one'] ?? 0,
                        'package_original_price' => $detail['original_price'] ?? 0,
                        'child_package_id' => 0,
                        'child_package_name' => '',
                        'child_package_price' => 0,
                        'entity_id' => $entityId,
                        'created_by' => $userId,
                        'created_at' => time(),
                        'updated_by' => $userId,
                        'updated_at' => time(),
                    ];
                }
            }
        }

        $res = $res2 = 1;
        if ($data) {
            $res = Yii::$app->db->createCommand()
                ->batchInsert(OrderProject::tableName(), array_keys($data[0]), $data)
                ->execute();
        }

        if ($detailData) {
            $res2 = Yii::$app->db->createCommand()
                ->batchInsert(OrderPlanDetail::tableName(), array_keys($detailData[0]), $detailData)
                ->execute();
        }

        if (!$res || !$res2) throw new Exception('新增订单明细失败');
    }

    /**
     * 新增客户
     *
     * @param array $params
     * @return array|Customer|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function saveCustomer($params = [])
    {
        if (!isset($params['mobile_code']) || !isset($params['mobile']) || !isset($params['name']) || !isset($params['wxcom_cus_id'])) {
            throw new Exception('新增客户信息参数错误！');
        }

        //手机号下过单直接返回用户信息
        $customer_user = Customer::find()->where(['mobile' => $params['mobile']])->andWhere(['entity_id' => UserService::getInst()->current_entity_id])->one();
        $customer_unionid = CusCustomer::find()->select('unionid')->andWhere(['id' => $params['wxcom_cus_id']])->andWhere(['entity_id' => UserService::getInst()->current_entity_id])->scalar();
        if ($customer_user) {
            // 判断企微账号是否相同
            if ($customer_user->wxcom_cus_id && $customer_user->unionid != $customer_unionid) {
                throw new Exception('客户微信信息与当前微信信息不符！');
            }

            //判断改客户是否有绑定过企微信息，没绑定过且当前非多人同行下单。则更新下单客户的企微绑定信息
            if (empty($customer_user->wxcom_cus_id) && empty($params['is_generation_order']) && $params['wxcom_cus_id']) {   //判断是否缺失企微信息
                $wx_cus_customer = self::getCusCustomer($params);
                $customer_user->wxcom_cus_id = $wx_cus_customer->id;
                $customer_user->unionid = $wx_cus_customer->unionid;
                if (!$customer_user->save()) throw new Exception('创建订单，更新客户下单信息失败！' . current($customer_user->getFirstErrors()));
            }

            return $customer_user->toArray();
        }

        //多人同行客户使用
        if ($params['is_generation_order']) {
            $generation_channel_info = Customer::find()->where(['unionid' => ($customer_unionid ?: -1)])->andWhere(['entity_id' => UserService::getInst()->current_entity_id])->asArray()->one();
            if (!$generation_channel_info) throw new Exception('暂无同行人数据！');

            $params['generation_id'] = $generation_channel_info['id'];  //代下单人ID

            return self::saveCustomerData($params);
        }

        //有企微信息的正常下单创建用户流程
        $customer = Customer::find()->where(['OR', ['id' => $params['cus_id']], ['wxcom_cus_id' => $params['wxcom_cus_id']]])->asArray()->one();
        if (!$customer) {
            $wx_cus_customer = self::getCusCustomer($params);

            $headers = getallheaders();
            $wxcomUserId = Yii::$app->cache->get('loginUserWxcomUser:' . $headers['X-Access-Token']);
            $customerUser = CusCustomerUser::find()
                ->where(['user_id' => $wxcomUserId, 'cus_id' => $wx_cus_customer->id])
                ->one();
            if (!$customerUser) {
                Yii::$app->notice->orderMessage('下单保存客户信息时找不到企微客户客服联系信息', '', $params);
                $wxcomUser = User::findOne($wxcomUserId);
                Yii::$app->notice->important('客户客服信息找不到，请及时同步', '', [
                    'external_user_id' => $wx_cus_customer->external_user_id,
                    'wxcom_user_id' => $wxcomUser->wxcom_user_id,
                ]);
                throw new Exception('客户微信信息不存在2，请联系管理员！');
            }

            //过滤代号异常数据（前端提交的代号、渠道与加粉子表数据不一致），channel_id=7为转单渠道
            if ($params['channel_id'] != 7 && ((!empty($customerUser->code) && $customerUser->code != $params['code']) || (!empty($customerUser->channel_id) && $customerUser->channel_id != $params['channel_id']))) {
                throw new Exception('数据异常请联系信息部处理！');
            }

            $customer = self::saveCustomerData($params, $wx_cus_customer);
        }

        return $customer;
    }

    /**
     * PC端下单客户
     *
     * @param array $params
     * @return array|Customer|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function saveCustomerPc($params = [])
    {
        if (!isset($params['mobile_code']) || !isset($params['mobile']) || !isset($params['name'])) {
            throw new Exception('新增客户信息参数错误！');
        }

        $customer_user = Customer::find()->where(['mobile' => $params['mobile']])->andWhere(['entity_id' => UserService::getInst()->current_entity_id])->asArray()->one();
        if ($customer_user) {
            return $customer_user;
        }

        //获取个微加粉数据
        $fans_data = FansRecord::find()->select('link_id,project_id,responsible_id,direction_id')->andWhere(['id' => $params['add_fans_id']])->asArray()->one();

        $wx_cus_customer = [
            'name' => $fans_data['wechat']
        ];

        return self::saveCustomerData($params, $wx_cus_customer);
    }

    /**
     * 订单订金核销（未完善：order_ids怎么存储）
     * order_id 订单ID/cus_id 客户ID/user_id 客服ID/pay_record_ids 支付流水ID
     *
     * @param array $params
     * @return array
     * @throws \yii\db\Exception
     */
    public static function orderPrepay($params = [])
    {
        //订单信息
        $order = static::$modelClass::findOne($params['order_id']);
        if (!$order) throw new Exception('订单不存在，请认真核对！');
        if ($order->isPaid()) {
            throw new Exception('该订单已核销，请勿重复操作！');
        }
        if (!$order->canPay()) {
            throw new Exception('该订单不允许支付预收金操作！');
        }

        // 核销时修改定金金额
        $deposit = ArrayHelper::getValue($params, 'deposit', 0);
        if ($deposit != $order->deposit) {
            $order->deposit = $deposit;
            $orderPlanList = $order->planDetails;
            $avgDeposit = round($order->deposit / count($orderPlanList), 2);
            foreach ($orderPlanList as $orderPlan) {
                $orderPlan->deposit = $avgDeposit;
                if (!$orderPlan->save()) {
                    throw new Exception('订单预约子表保存失败');
                }
            }
            if (!$order->save()) {
                throw new Exception('订单表保存失败');
            }
        }

        //流水信息
        $payRecordQuery = PayRecord::find()
            ->andWhere(['id' => $params['pay_record_ids']])
            ->andWhere('total_fee > used_amount')
            ->andFilterWhere(['entity_id' => Yii::$app->user->identity->current_entity_id])
            ->indexBy('id');

        //客户信息
        if (!Yii::$app->services->auth->isSuperAdmin()) {
            //获取订单客户wxcom_cus_id，同行客户则获取代下单客户wxcom_cus_id
            $unionId = $order->customer->unionid;
            if (!$order->customer->wxcom_cus_id) {
                $unionId = $order->customer->generationCustomer->unionid;
            }
            $cusIds = CusCustomer::find()->andWhere(['unionid' => $unionId])->select('id')->column();
            $payRecordQuery->andFilterWhere(['OR', ['wxcom_cus_id' => $cusIds], ['wxcom_cus_id' => 0, 'user_id' => Yii::$app->user->id]]);
        }

        //流水信息
        $pay_records = $payRecordQuery->all();
        if (!$pay_records) throw new Exception('该流水号已被使用、或不存在，请认真核对！');
        if (count($params['pay_record_ids']) != count($pay_records)) throw new Exception('选择的流水中存在异常，请刷新页面后重试！');

        $sum_amount = ArrayHelper::getSum($pay_records, 'total_fee');
        $used_amount = ArrayHelper::getSum($pay_records, 'used_amount');
        $sum_record_amount = BcHelper::sub($sum_amount, $used_amount);   //流水总额
        // 应收预收金
        $leftDeposit = $order->deposit;
        if ($leftDeposit > $sum_record_amount) throw new Exception('支付金额不足，请认真核对！');

        //开始核销流水
        $record_details = [];
        foreach ($pay_records as $v) {
            if (!$leftDeposit) {
                break;
            }

            // 本流水抵扣金额
            $subAmount = min($leftDeposit, $v->total_fee - $v->used_amount);

            $v->used_amount += $subAmount;
            $leftDeposit -= $subAmount;

            $order_ids = $v->order_ids ? explode(',', $v->order_ids) : [];
            array_push($order_ids, $order->id);
            $v->order_ids = implode(',', $order_ids);
            $v->status = PayRecordStatusEnum::HAVE_BEEN_USED;
            if (!$v->user_id) {
                $v->user_id = Yii::$app->user->id;
            }
            if (!$v->save()) throw new Exception('核销流水失败，请重试！');

            //添加流水明细核销记录
            $record_details[] = [
                'order_id' => $order->id,
                'record_id' => $v->id,
                'transaction_id' => $v->transaction_id,
                'amount' => $subAmount,
                'store_id' => $order->store_id,
                'type' => PayRecordDetailEnum::DEPOSIT,
                'entity_id' => Yii::$app->user->identity->current_entity_id,
                'created_by' => Yii::$app->user->id,
                'created_at' => time(),
            ];
        }

        //记录订单支付预收金记录
        $order->pre_pay_status = PayStatusEnum::YES;
        $order->pre_pay_time = $order->write_off_time = time();
        if (!$order->save()) throw new Exception('订单核销失败，请重试！');

        // 核销后填充客户推广信息
        $customer = Customer::findOne($order->cus_id);
        if ($customer) {
            $customer->fillPromoteInfo($order);
        }

        //开始记录订单核销订单明细
        if ($record_details) {
            $res = Yii::$app->db->createCommand()
                ->batchInsert(PayRecordDetail::tableName(), array_keys($record_details[0]), $record_details)
                ->execute();

            if (!$res) throw new Exception('订单核销明细记录失败，请重试！');
        }

        //下订上报
        // try{
        //     $reportService = ReportFactory::getReportServiceByChannelId($order->channel_id);
        //     $reportService->orderCreatedReport($order);
        // }catch(Exception $e){
        //     Yii::error('订单号:'.$order->order_no.',上报失败:'.$e->getMessage());
        // }

        return [
            'id' => $order->id,
            'plan_by' => Yii::$app->user->identity->username,
            'order_no' => $order->order_no,
            'deposit' => $order->deposit,
            'created_at' => DateHelper::toDate($order->created_at),
            'pre_pay_time' => DateHelper::toDate($order->pre_pay_time),
        ];
    }

    /**
     * 刷新流水
     *
     * @return bool
     * @throws Exception
     */
    public static function refreshWriteList()
    {
        $user = User::find()->select('com_id,wxcom_user_id')->where(['user_id' => Yii::$app->user->id])->andWhere("wxcom_user_id not like '%@____'")->asArray()->all();  //客服企微信息
        if (!$user) throw new Exception('您暂未绑定企微信息，请先绑定！');

        $beginTime = time() - 86400;  //5分钟之前的时间
        $endTime = time();
        $comList = Com::find()
            ->select('id, corp_id, externalpay_secret, code, entity_id')
            ->indexBy('id')
            ->asArray()
            ->all();

        foreach ($user as $v) {
            $payeeUserid = $v['wxcom_user_id'];
            if (!(new PayRecordService)->billListInsertDB($comList[$v['com_id']], $beginTime, $endTime, $payeeUserid)) {
                throw new Exception('刷新失败，请重试！');
            }
        }

        return true;
    }

    /**
     * 获取流水
     *
     * @param array $params
     * @return array|void|\yii\db\ActiveRecord[]
     * @throws Exception
     */
    public static function getWriteList($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query = static::getWriteListQuery($params);

        $totalCount = $query->count();
        $list = $query->offset($offset)->limit($limit)->orderBy('wxcom_cus_id DESC,id DESC')->asArray()->all();
        foreach ($list as &$v) {
            $v['pay_time'] = DateHelper::toDate($v['pay_time']);
        }

        return [$list, $totalCount];
    }

    /**
     * 订金流水列表
     *
     * @param array $params
     * @return \yii\db\ActiveQuery
     */
    public static function getWriteListQuery($params = [])
    {
        return PayRecord::find()
            ->select('id,transaction_id,total_fee,used_amount,remark,payment_type,pay_type,pay_time')
            ->andWhere('total_fee > used_amount')
            ->andWhere(['<>', 'status', PayRecordStatusEnum::FREEZE])
            ->andFilterWhere(['like', 'transaction_id', $params['transaction_id']]);
    }

    /**
     * 客服-取消订单
     *
     * @param $id
     * @return bool
     * @throws Exception
     */
    public static function cancel($id)
    {
        /**@var  OrderHeader $order*/
        $order = static::$modelClass::find()->where(['id' => $id, 'entity_id' => Yii::$app->user->identity->current_entity_id])->one();

        if (!$order) throw new Exception('订单不存在，请认真核对');

        if ($order->source_type == OrderHeader::SOURCE_TYPE_STORE) throw new Exception('不允许取消门店下的订单');

        if (!in_array($order->order_status, [OrderHeaderStatusEnum::STATUS_PENDING_APPOINTMENT, OrderHeaderStatusEnum::STATUS_PLAN])) {
            throw new Exception('只能取消待预约、已预约的订单');
        }

        if ($order->deposit > 0 && $order->pre_pay_status == PayStatusEnum::YES) {
            throw new Exception('该订单预收金已支付，不可取消');
        }

        $order->order_status = OrderHeaderStatusEnum::CANCEL;
        if (!$order->save()) throw new Exception('订单取消失败，请重试');

        return true;
    }

    /**
     * 门店-取消订单
     *
     * @param $id
     * @return bool
     * @throws Exception
     */
    public static function storeCancel($id)
    {
        /** @var OrderHeader $order */
        $order = static::$modelClass::find()->where(['id' => $id, 'entity_id' => Yii::$app->user->identity->current_entity_id])->one();

        if (!$order) throw new Exception('订单不存在，请认真核对');

        if ($order->source_type != OrderHeader::SOURCE_TYPE_STORE) throw new Exception('不允许取消客服下的订单');

        if (!in_array($order->order_status, [OrderHeaderStatusEnum::STATUS_PENDING_APPOINTMENT, OrderHeaderStatusEnum::STATUS_PLAN, OrderHeaderStatusEnum::STATUS_ARRIVED_STORE])) {
            throw new Exception('只能取消待预约、已预约、已到店的订单');
        }

        $order->order_status = OrderHeaderStatusEnum::CANCEL;
        if (!$order->save()) throw new Exception('订单取消失败，请重试');

        return true;
    }

    /**
     * 门店-待结算撤回状态到已预约
     *
     * @param $id
     * @return bool
     * @throws Exception
     */
    public static function storeRetract($id)
    {
        /**@var OrderHeader $order*/
        $order = static::$modelClass::find()->where(['id' => $id, 'entity_id' => Yii::$app->user->identity->current_entity_id])->one();

        if (!$order) throw new Exception('订单不存在，请认真核对');

        if (!in_array($order->order_status, [OrderHeaderStatusEnum::STATUS_SETTLEMENT, OrderHeaderStatusEnum::STATUS_ARRIVED_STORE])) {
            throw new Exception('只有已到店/待结算的状态才能操作');
        }

        $order->order_status = OrderHeaderStatusEnum::STATUS_PLAN;
        if (!$order->save()) throw new Exception('订单修改为已预约失败，请重试');

        return true;
    }

    /**
     * 获取客户企微信息
     *
     * @param $params
     * @return CusCustomer|null
     * @throws Exception
     */
    public static function getCusCustomer($params)
    {
        $wx_cus_customer = CusCustomer::findOne($params['wxcom_cus_id']);
        if (!$wx_cus_customer) {
            Yii::$app->notice->orderMessage('订单模块', '', ['message' => '客户微信信息不存在，请联系管理员！', 'data' => $params]);
            throw new Exception('客户微信信息不存在，请联系管理员！');
        }

        return $wx_cus_customer;
    }

    /**
     * 添加下单用户信息
     *
     * @param $params
     * @param array $wx_cus_customer
     * @return Customer
     * @throws Exception
     */
    public static function saveCustomerData($params, $wx_cus_customer = [])
    {
        $mobile = ArrayHelper::getValue($params, 'mobile', '');

        $customer_data = [
            'wxcom_cus_id' => ArrayHelper::getValue($wx_cus_customer, 'id', 0),
            'add_fans_id' => ArrayHelper::getValue($params, 'add_fans_id', 0),
            'name' => ArrayHelper::getValue($params, 'name', ''),
            'gender' => ArrayHelper::getValue($wx_cus_customer, 'gender', 0),
            'avatar' => ArrayHelper::getValue($wx_cus_customer, 'avatar', ''),
            'nick_name' => ArrayHelper::getValue($wx_cus_customer, 'name', ''),
            'unionid' => ArrayHelper::getValue($wx_cus_customer, 'unionid', ''),
            'mobile_code' => ArrayHelper::getValue($params, 'mobile_code', ''),
            'mobile' => $mobile,
            'channel_id' => ArrayHelper::getValue($params, 'channel_id', 0),
            'store_id' => ArrayHelper::getValue($params, 'store_id', 0),
            'generation_id' => ArrayHelper::getValue($params, 'generation_id', 0),
            'first_store_time' => 0,
            'entity_id' => Yii::$app->user->identity->current_entity_id,
            'created_by' => Yii::$app->user->id,
            'created_at' => time(),
        ];
        if (isset($params['code']) && $params['code']) {
            $customer_data['code'] = $params['code'];
        }
        $customer = new Customer();
        $customer->attributes = $customer_data;
        if (!$customer->save()) throw new Exception(current($customer->getFirstErrors()));

        return $customer;
    }

    /**
     *  拉卡拉pos机列表
     *
     * @param array $params
     * @return array
     * @throws Exception
     */
    public static function getLklList($params = [])
    {
        OrderHeader::setExtendAttrs([
            'store.store_name' => 'store_name',
            'customer.name' => 'cus_name',
            'customer.mobile' => 'cus_mobile',
            'customer.avatar' => 'cus_avatar',
            'createdPerson.realname' => 'realname',
            'project',
            'orderPay'
        ]);
        OrderPay::setShowAttrs(['transaction_id', 'refer_number'], true);
        OrderPay::setExtendAttrs([
            'storePayRecord.transaction_id' => 'transaction_id',
            'storePayRecord.refer_number' => 'refer_number',
        ]);
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;
        $query = OrderHeader::find()->alias('h')->andFilterWhere(['h.entity_id' => UserService::getInst()->currentEntityId])
            ->with(['customer', 'createdPerson', 'project', 'orderPay' => function ($query) {
                $query->with(['storePayRecord']);
            }])
            ->select('h.deposit,h.order_no,h.id,h.cus_id,h.store_id,h.plan_time,h.pay_amount,h.received_amount,h.created_at,h.order_status,h.created_by,h.plan_remark,h.source_type');
        $query->andWhere(['h.store_id' => $params['store_id']]);
        if ($params['search_customer']) {
            $cusIds = CustomerService::getIdsByKeyword($params['search_customer'], 1) ?: '-1';
            $query->andFilterWhere(['h.cus_id' => $cusIds]);
        }
        if ($params['plan_start_time'] && $params['plan_end_time']) {   //订单预约时间
            $query->andFilterWhere(['between', 'h.plan_time', $params['plan_start_time'], $params['plan_end_time']]);
        } else {
            $planTime = date('Y-m-d');
            $query->andWhere(['between', 'h.plan_time', strtotime($planTime . ' 00:00:00'), strtotime($planTime . ' 23:59:59')]);
        }
        $query->andFilterWhere(['in', 'h.order_status', [OrderHeaderStatusEnum::STATUS_ARRIVED_STORE, OrderHeaderStatusEnum::STATUS_SETTLEMENT]]);
        $totalCount = $query->count();
        $list = $query->offset($offset)->limit($limit)->orderBy('h.plan_time desc')->all();
        $list = ArrayHelper::toArray($list);
        //数据解析
        foreach ($list as &$value) {
            $value['cus_mobile'] = ResultHelper::mobileEncryption($value['cus_mobile']);
            $value['plan_date'] = DateHelper::toDate($value['plan_time']);
            $project = $value['project'];
            $num = count($project);
            $goods_name = '';
            foreach ($project as $k => $value1) {
                $goods_name .= $value1['goods_name'];
                if ($k != $num - 1) {
                    $goods_name .= ',';
                }
            }
            $value['goods_name'] = $goods_name;
            $value['package_name'] = $project[0]['package_name'];
            unset($value['project']);
        }
        return [$list, $totalCount];
    }

    /**
     * 作废订单
     *
     * @param $id
     * @return bool
     * @throws Exception
     */
    public static function cancellation($id)
    {
        $order = static::$modelClass::findOne($id);
        if (!$order) {
            throw new Exception('订单不存在，请认真核对');
        }

        if (!in_array($order->order_status, [OrderHeaderStatusEnum::STATUS_PENDING_APPOINTMENT, OrderHeaderStatusEnum::STATUS_PLAN])) {
            throw new Exception('只有“待预约”、“已预约”，的订单才能作废');
        }

        if ($order->orderRelay) {
            throw new Exception('该订单已同步至BIS系统，请到BIS上作废该订单');
        }

        $scopeStoreList = Yii::$app->services->scopeDataService->getMenuStore();
        if ($scopeStoreList && !in_array($order->store_id, $scopeStoreList)) {
            throw new Exception('你暂无该订单的操作权限');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $order->order_status = OrderHeaderStatusEnum::STATUS_INVALID;
            if (!$order->save()) {
                throw new Exception('作废订单失败');
            }

            if ($order->deposit > 0) {
                AuthPayRecordService::releaseWaterByOrder($order);
            }
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
        $transaction->commit();

        //删除作废订单redis
        Yii::$app->redis->hdel('TransferToBisDetails', $order->order_no);

        return true;
    }

    /**
     * 获取订单项目名称
     *
     * @param array $project
     * @return array
     */
    public static function getOrderGoodsName($project = [])
    {
        $goods_name_arr = [];
        foreach ($project as $k => $goods) {
            if ($goods['package_name']) {
                if (!in_array($goods['package_name'], $goods_name_arr)) {
                    $goods_name_arr[] = $goods['package_name'];
                }
            } else {
                $goods_name_arr[] = $goods['goods_name'];
            }
        }

        return $goods_name_arr;
    }

    /**
     * 订单反结算
     *
     * @return void
     */
    public static function revertSettlement($params)
    {
        // 订单信息
        $order = static::$modelClass::findOne($params['id']);
        if (!$order) throw new Exception('订单不存在，请认真核对！');
        $order->revertFinish();
    }

    /**
     * 添加、获取门店老师数据
     *
     * @param $data
     * @param $teacherList
     * @return array
     */
    public static function getOrderTeacherData($data, $teacherList)
    {
        //获取老师类型
        $teacherTypes = TeacherType::find()
            ->select('id,name,status')
            ->orderBy('sort DESC')
            ->cache(10)
            ->asArray()
            ->all();

        foreach ($teacherTypes as $teacherType) {
            $typeId = $teacherType['id'];
            if ($teacherList[$typeId]) {
                $data[$teacherType['name']] = $teacherList[$typeId]->user->realname ?: '';
            }
        }

        return $data;
    }

    /**
     * 获取详情表头字段
     *
     * @param $order_id
     * @return array
     */
    public static function getTableHead($order_id)
    {
        $tableFields = [
            ['dataIndex' => 'name', 'title' => '商品名称'],
            ['dataIndex' => 'num', 'title' => '数量'],
            ['dataIndex' => 'use_num', 'title' => '本单使用'],
            ['dataIndex' => 'left_num', 'title' => '剩余'],
            ['dataIndex' => 'price', 'title' => '售价（元）'],
        ];

        $arrType = OrderTeacher::find()->alias('ot')
            ->select('t.name')
            ->leftJoin('{{%teacher_type}} t', 't.id = ot.type')
            ->where(['ot.order_id' => $order_id])
            ->orderBy('t.sort DESC,t.id asc')
            ->groupBy('t.id')
            ->column();

        $teacherFields = [];
        foreach ($arrType as $item) {
            $teacherFields[] = ['dataIndex' => $item, 'title' => $item];
        }

        //补充老师字段
        $tableFields = array_merge($tableFields, $teacherFields);

        return $tableFields;
    }

    /**
     * 修改预约门店
     */
    public static function changePlanStore($params)
    {
        $order = static::getInfoById($params['order_id']);
        if (empty($params['store_id'])) {
            throw new Exception('门店不能为空');
        }

        if (!in_array($order->order_status, [OrderHeaderStatusEnum::STATUS_PENDING_APPOINTMENT, OrderHeaderStatusEnum::STATUS_PLAN])) {
            throw new Exception('只有待预约、已预约状态才能修改门店');
        }

        $store = Store::find()->where([
            'id' => $params['store_id'],
            'entity_id' => UserService::getInst()->current_entity_id,
            'status' => StatusEnum::ENABLED,
        ])->one();

        if (empty($store)) {
            throw new Exception('门店不存在');
        }
        $order->store_id = $store->id;
        if (!$order->save(false)) {
            throw new Exception('修改预约门店失败，请重试');
        }
        return true;
    }

    public static function setTableNewCusData($order_id = '')
    {
        //新增当天新客数据
        $today_time = DateHelper::today();
        $orderList = OrderHeader::find()->alias('oh')
            ->select('oh.id,oh.store_id')
            ->leftJoin(['c' => Customer::tableName()], 'c.id = oh.cus_id')
            ->where(['oh.order_status' => OrderHeaderStatusEnum::STATUS_PLAN])
            ->andWhere(['between', 'oh.plan_time', $today_time['start'], $today_time['end']])
            ->andWhere(['=', 'c.first_store_time', 0])
            ->andWhere(['oh.entity_id' => 1])
            ->andFilterWhere(['oh.id' => $order_id])
            ->asArray()
            ->all();

        $date = date('Y-m-d');
        if (empty($orderList)) {
            static::updateTableNewCusData($date, $order_id);
            return true;
        }

        if ($order_id) {
            $isAdd = static::updateTable($date, $order_id, $orderList[0]['store_id']);
            if ($isAdd) {
                return true;
            }
        }

        foreach ($orderList as $order) {
            AdvanceOrderJob::addJob(['order_id' => $order['id']], AdvanceOrderJob::TYPE_CREATE);
        }
        return true;
    }

    public static function updateTable($date, $order_id, $store_id)
    {
        $redis = Yii::$app->cache;
        $redisData = $redis->get('AdvanceOrder_' . $date . ':' . $store_id);
        if (!$redisData) {
            return false;
        }

        $isAdd = false;
        foreach ($redisData as $value) {
            if ($value['order_id'] != $order_id) {
                continue;
            }

            $isAdd = true;
            $record_id = $value['record_id'];
            $orderModel = OrderHeader::find()->where(['id' => $order_id])->limit(1)->one();
            AdvanceOrderJob::addJob(
                [
                    'record_id' => $record_id,
                    'store_id' => $store_id,
                    'order_id' => $order_id,
                    'plan_time' => $orderModel->plan_time,
                ],
                AdvanceOrderJob::TYPE_UPDATE
            );

            break;
        }

        return $isAdd;
    }

    public static function updateTableNewCusData($date, $order_id)
    {
        $orderModel = OrderHeader::find()->where(['id' => $order_id])->limit(1)->one();
        if (!$orderModel) {
            return true;
        }
        $redis = Yii::$app->cache;
        $redisKey = 'AdvanceOrder_' . $date . ':' . $orderModel->store_id;
        $addOrderData = $redis->get($redisKey);
        if (!$addOrderData) {
            return true;
        }

        $arrAddOrderId = ArrayHelper::getColumn($addOrderData, 'order_id');
        if (!in_array($order_id, $arrAddOrderId)) {
            return true;
        }

        $duration = strtotime($date) + 86399 -  time();
        $status = [
            OrderHeaderStatusEnum::STATUS_ARRIVED_STORE,
            OrderHeaderStatusEnum::STATUS_SETTLEMENT,
            OrderHeaderStatusEnum::STATUS_COMPLETED,
            OrderHeaderStatusEnum::STATUS_OTHER_SETTLEMENT,
            OrderHeaderStatusEnum::STATUS_APPLY_UNSUBSCRIBE,
            OrderHeaderStatusEnum::STATUS_AFTER_SALE,
        ];
        $delKey = null;
        $record_id = '';
        foreach ($addOrderData as $key => $value) {
            if ($value['order_id'] == $order_id) {
                $delKey = $key;
                $record_id = $value['record_id'];
                break;
            }
        }

        if ($delKey === null) {
            return true;
        }

        //订单状态在这些状态中修改多维表格中的数据
        if (in_array($orderModel->order_status, $status)) {
            if ($orderModel->order_status == OrderHeaderStatusEnum::STATUS_ARRIVED_STORE) {
                AdvanceOrderJob::addJob(
                    [
                        'record_id' => $record_id,
                        'store_id' => $orderModel->store_id,
                        'order_id' => $order_id,
                        'plan_user_id' => $orderModel->plan_user_id,
                    ],
                    AdvanceOrderJob::TYPE_UPDATE
                );
            }
            return true;
        }

        //删除多维表格上订单数据
        AdvanceOrderJob::addJob(
            [
                'record_id' => $record_id,
                'store_id' => $orderModel->store_id,
                'order_id' => $order_id
            ],
            AdvanceOrderJob::TYPE_DELETE
        );
        unset($addOrderData[$delKey]);
        $duration = strtotime($date) + 86399 -  time();
        $redis->set($redisKey, $addOrderData, $duration);
        return true;
    }

    /**
     * 已预约数据-多维表格
     */
    public static function alreadyReservedData($orderID)
    {
        $orderData = OrderHeader::find()->alias('oh')
            ->select('oh.id,oh.store_id,c.name,c.mobile,oh.plan_time,s.store_name')
            ->leftJoin(['c' => Customer::tableName()], 'c.id = oh.cus_id')
            ->leftJoin(['s' => Store::tableName()], 's.id = oh.store_id')
            ->where(['oh.id' => $orderID])
            ->asArray()
            ->limit(1)
            ->one();

        if (empty($orderData)) {
            return [];
        }

        $addData = [
            '订单ID(请勿修改)' => $orderData['id'],
            '手机尾号' => ResultHelper::mobileTailNumber($orderData['mobile']),
            '客户姓名' => $orderData['name'],
            '日期' => $orderData['plan_time'] * 1000,
            '门店名称' => $orderData['store_name'],
            '是否到店' => '未到店',
            'store_id' => $orderData['store_id'],
        ];
        return $addData;
    }

    /**
     * 获取子账户当日下订数
     */
    public static function getSubAccountOrderCount($sub_advertiser_id, $startDate = '', $endDate = '')
    {
        $todayTime = DateHelper::turnTimestamp($startDate, $endDate);
        $data = OrderHeader::find()
            ->select([
                'COUNT(DISTINCT h.customer_user_id) AS order_count',
            ])
            ->alias('h')
            ->leftJoin(['cu' => CusCustomerUser::tableName()], 'cu.id = h.customer_user_id')
            ->leftJoin(['as' => AdsAccountSub::tableName()], 'as.sub_advertiser_id = cu.sub_advertiser_id')
            ->where(['h.entity_id' => UserService::getInst()->current_entity_id])
            ->andWhere(['>', 'h.customer_user_id', 0])
            ->andWhere(['h.order_status' => OrderHeader::depositOrderStatusList()])
            ->andWhere(['as.sub_advertiser_id' => $sub_advertiser_id])
            ->andWhere(['BETWEEN', 'h.created_at', $todayTime[0], $todayTime[1]])
            ->asArray()
            ->one();

        return $data['order_count'] ?? 0;
    }
}
