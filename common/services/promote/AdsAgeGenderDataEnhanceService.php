<?php

namespace common\services\promote;

use common\models\backend\order\OrderHeader;
use common\models\common\AdsAgeGenderData;
use common\models\backendapi\PromoteChannel;
use common\models\wxcom\CusCustomerUser;
use common\models\common\AdsAccountSub;
use common\models\Customer;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\CustomerAgeBracket;
use common\enums\GenderEnum;
use yii\db\Exception;

/**
 * 年龄性别数据完善服务类
 */
class AdsAgeGenderDataEnhanceService
{
    /**
     * 完善年龄性别数据
     * 
     * @param string $date 日期格式：2025-01-01
     * @param int $entityId 企业ID
     * @return bool
     * @throws Exception
     */
    public static function enhanceAgeGenderData($date, $entityId)
    {
        $date = date("Ymd", strtotime($date));
        $current_date = date("Ymd", time());
        if ($date == $current_date) {
            return true;
        }

        // 清除所有数据
        AdsAgeGenderData::updateAll(['new_store_cus_count' => 0, 'amount' => 0], ['date' => $date, 'entity_id' => $entityId]);

        $startTime = strtotime($date);
        $endTime = strtotime($date . ' 23:59:59');

        // 获取新客人数 & 门店实收（按年龄性别维度）
        static::getAgeGenderCompleteData($entityId, $startTime, $endTime);

        return true;
    }
    
    /**
     * 获取按年龄性别维度的新客到店人数和门店实收
     * 
     * @param int $entityId 企业ID
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     * @return bool
     */
    protected static function getAgeGenderCompleteData($entityId, $startTime, $endTime)
    {
        $list = OrderHeader::find()
            ->alias('oh')
            ->select([
                'c.age_bracket AS age_bracket_num',
                'c.gender AS gender_num',
                'IFNULL(as.id, 0) AS ads_sub_id',
                'COUNT(DISTINCT if(FROM_UNIXTIME(oh.plan_time,"%Y-%m-%d") = FROM_UNIXTIME(c.first_store_time,"%Y-%m-%d") AND pc.is_calculate_results = 0, c.id,NULL)) as new_store_cus_count',
                'sum(IFNULL( oh.received_amount, 0 ) + IFNULL( oh.card_real_amount, 0 ) + IFNULL( oh.group_amount,0)) as amount',
            ])
            ->leftJoin(['c' => Customer::tableName()], 'c.id = oh.cus_id')
            ->leftJoin(['cu' => CusCustomerUser::tableName()], 'cu.id = oh.customer_user_id')
            ->leftJoin(['as' => AdsAccountSub::tableName()], 'as.sub_advertiser_id = cu.sub_advertiser_id')
            ->leftJoin(['pc' => PromoteChannel::tableName()], 'pc.id = oh.channel_id')
            ->where(['oh.entity_id' => $entityId])
            ->andWhere(['oh.order_status' => OrderHeaderStatusEnum::STATUS_COMPLETED])
            ->andFilterWhere(['BETWEEN', 'oh.plan_time', $startTime, $endTime])
            ->groupBy('as.id, c.age_bracket, c.gender')
            ->asArray()
            ->all();

        if (empty($list)) {
            return true;
        }

        foreach ($list as $item) {
            if ($item['new_store_cus_count'] == 0 && $item['amount'] == 0) {
                continue;
            }

            $attributes = [
                'date' => date("Ymd", $startTime),
                'entity_id' => $entityId,
                'ads_sub_id' => $item['ads_sub_id'],
                'age' => CustomerAgeBracket::getValue($item['age_bracket_num']) ?: '其他',
                'gender' => GenderEnum::getValue($item['gender_num']) ?: '其他',
                'new_store_cus_count' => $item['new_store_cus_count'],
                'amount' => $item['amount'],
            ];

            static::refineData($attributes);
        }

        return true;
    }
    
    /**
     * 精细化数据
     * 
     * @param array $data
     * @return bool
     */
    public static function refineData(array $data)
    {
        if (!is_numeric($data['ads_sub_id'])) {
            throw new Exception('ads_sub_id 无效');
        }

        $adsAgeGenderData = AdsAgeGenderData::find()
            ->where([
                'date' => $data['date'], 
                'ads_sub_id' => $data['ads_sub_id'], 
                'entity_id' => $data['entity_id'],
                'age' => $data['age'],
                'gender' => $data['gender']
            ])
            ->one();

        if (empty($adsAgeGenderData)) {
            $adsAgeGenderData = new AdsAgeGenderData();
            $adsAgeGenderData->attributes = $data;
        } else {
            $adsAgeGenderData->attributes = $data;
        }

        if (!$adsAgeGenderData->save()) {
            throw new Exception('保存年龄性别分析数据失败：' . current($adsAgeGenderData->getFirstErrors()));
        }

        return true;
    }
}