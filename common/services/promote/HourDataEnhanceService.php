<?php

namespace common\services\promote;

use common\models\backend\order\OrderHeader;
use common\models\common\AdsAccountData;
use common\models\common\AdsAccountDataHour;
use common\models\wxcom\CusCustomerUser;
use common\helpers\BcHelper;
use common\models\common\AdsAccountSub;
use common\models\Customer;
use common\enums\order\OrderHeaderStatusEnum;
use common\models\wechat\WechatUser;
use common\models\wxcom\CusCustomer;
use yii\db\Exception;

/**
 * 时段数据完善服务类
 */
class HourDataEnhanceService
{
    /**
     * 完善时段数据
     * 
     * @param string $date 日期格式：2025-01-01
     * @param int $entityId 企业ID
     * @return bool
     * @throws Exception
     */
    public static function enhanceHourData($date, $entityId)
    {
        $date = date("Ymd", strtotime($date));
        $current_date = date("Ymd", time());
        if ($date == $current_date) {
            return true;
        }

        // 清除所有数据
        AdsAccountDataHour::updateAll(['deposit_count' => 0, 'new_store_cus_count' => 0, 'amount' => 0, 'add_fans_count' => 0], ['date' => $date, 'entity_id' => $entityId]);

        $startTime = strtotime($date);
        $endTime = strtotime($date . ' 23:59:59');

        // 获取订金数
        static::getDepositCount($entityId, $startTime, $endTime);
        // 获取新客人数 & 门店实收
        static::getCompleteData($entityId, $startTime, $endTime);
        // 获取企微加粉人数
        static::getAddFansCount($entityId, $startTime, $endTime);

        return true;
    }
    
    /**
     * 获取时段内的订金数
     * 
     * @param int $entityId 企业ID
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     * @return int
     */
    protected static function getDepositCount($entityId, $startTime, $endTime)
    {
        $list = OrderHeader::find()
            ->select([
                'HOUR(FROM_UNIXTIME(cu.add_time)) AS hour',
                'IFNULL(as.id, 0) AS ads_sub_id',
                'COUNT(DISTINCT h.id) AS deposit_count',
            ])
            ->alias('h')
            ->leftJoin(['cu' => CusCustomerUser::tableName()], 'cu.id = h.customer_user_id')
            ->leftJoin(['as' => AdsAccountSub::tableName()], 'as.sub_advertiser_id = cu.sub_advertiser_id')
            ->where(['h.entity_id' => $entityId])
            ->andWhere(['and', ['>', 'h.customer_user_id', 0], ['>', 'deposit', 0]])
            ->andWhere(['>', 'cu.sub_advertiser_id', 0])
            ->andWhere(['h.order_status' => OrderHeader::promoteDepositOrderStatusList()])
            ->andFilterWhere(['BETWEEN', 'cu.add_time', $startTime, $endTime])
            ->andWhere(['in', 'cu.add_way', CusCustomerUser::getComputeAddWay()])
            ->groupBy('as.id,HOUR(FROM_UNIXTIME(cu.add_time))')
            ->asArray()
            ->all();

        if (empty($list)) {
            return true;
        }

        foreach ($list as $item) {
            if ($item['deposit_count'] == 0) {
                continue;
            }

            $attributes = [
                'date' => date("Ymd", $startTime),
                'entity_id' => $entityId,
                'ads_sub_id' => $item['ads_sub_id'],
                'hour' => $item['hour'],
                'deposit_count' => $item['deposit_count'],
            ];

            static::refineData($attributes);
        }
                
        return true;
    }
    
    /**
     * 获取时段内的新客到店人数和门店实收
     * 
     * @param int $entityId 企业ID
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     * @return int
     */
    protected static function getCompleteData($entityId, $startTime, $endTime)
    {
        $list = OrderHeader::find()
            ->alias('oh')
            ->select([
                'HOUR(FROM_UNIXTIME(cu.add_time)) AS hour',
                'IFNULL(as.id, 0) AS ads_sub_id',
                'COUNT(DISTINCT if(FROM_UNIXTIME(oh.plan_time,"%Y-%m-%d") = FROM_UNIXTIME(c.first_store_time,"%Y-%m-%d"), c.id,NULL)) as new_store_cus_count',
                'sum(IFNULL( oh.received_amount, 0 ) + IFNULL( oh.card_real_amount, 0 ) + IFNULL( oh.group_amount,0)) as amount',
            ])
            ->leftJoin(['c' => Customer::tableName()], 'c.id = oh.cus_id')
            ->leftJoin(['cu' => CusCustomerUser::tableName()], 'cu.id = oh.customer_user_id')
            ->leftJoin(['as' => AdsAccountSub::tableName()], 'as.sub_advertiser_id = cu.sub_advertiser_id')
            ->where(['oh.entity_id' => $entityId])
            ->andWhere(['oh.order_status' => OrderHeaderStatusEnum::STATUS_COMPLETED])
            ->andFilterWhere(['BETWEEN', 'cu.add_time', $startTime, $endTime])
            ->andWhere(['in', 'cu.add_way', CusCustomerUser::getComputeAddWay()])
            ->andWhere(['>', 'c.sub_advertiser_id', 0])
            ->groupBy('as.id,HOUR(FROM_UNIXTIME(cu.add_time))')
            ->asArray()
            ->all();

        if (empty($list)) {
            return true;
        }

        foreach ($list as $item) {
            if ($item['new_store_cus_count'] == 0 && $item['amount'] == 0) {
                continue;
            }

            $attributes = [
                'date' => date("Ymd", $startTime),
                'entity_id' => $entityId,
                'ads_sub_id' => $item['ads_sub_id'],
                'hour' => $item['hour'],
                'new_store_cus_count' => $item['new_store_cus_count'],
                'amount' => $item['amount'],
            ];

            static::refineData($attributes);
        }

        return true;
    }

    /**
     * 获取时段内的企微加粉人数
     * 
     * @param int $entityId 企业ID
     * @param int $startTime 开始时间戳
     * @param int $endTime 结束时间戳
     * @return int
     */
    public static function getAddFansCount($entityId, $startTime, $endTime)
    {
        $wechatUser = WechatUser::find()->select('unionid')->all();
        $unionids = array_column($wechatUser, 'unionid');

        $list = CusCustomerUser::find()->alias('cu')
            ->select(['HOUR(FROM_UNIXTIME(cu.add_time)) AS hour', 'IFNULL(aas.id, 0) AS ads_sub_id', 'COUNT(DISTINCT cu.id) AS wxcom_add_fans'])
            ->leftJoin(['wcc' => CusCustomer::tableName()], 'cu.cus_id = wcc.id')
            ->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = cu.sub_advertiser_id')
            ->where(['cu.entity_id' => $entityId])
            ->andWhere(['add_way' => CusCustomerUser::getComputeAddWay()])
            ->andFilterWhere(['not in', 'wcc.unionid', $unionids])
            ->andFilterWhere(['BETWEEN', 'cu.add_time', $startTime, $endTime])
            ->andFilterWhere(['and', ['>', 'cu.channel_id', 0], ['>', 'cu.qrcode_created_by', 0]])
            ->groupBy('aas.id, HOUR(FROM_UNIXTIME(cu.add_time))')
            ->asArray()
            ->all();
        
        if (empty($list)) {
            return true;
        }

        foreach ($list as $item) {
            if ($item['wxcom_add_fans'] == 0) {
                continue;
            }

            $attributes = [
                'date' => date("Ymd", $startTime),
                'entity_id' => $entityId,
                'ads_sub_id' => $item['ads_sub_id'],
                'hour' => $item['hour'],
                'add_fans_count' => $item['wxcom_add_fans'],
            ];

            static::refineData($attributes);
        }

        return true;
    }
    
    /**
     * 计算实际消耗
     * 
     * @param array $data
     * @param float $cost
     * @return float
     */
    protected static function calculateActualConsume(array $data, $cost)
    {
        $adsAccountData = AdsAccountData::find()
            ->select('compensate,rebates')
            ->where(['date' => $data['date'], 'ads_sub_id' => $data['ads_sub_id'], 'entity_id' => $data['entity_id']])
            ->one();

        if (empty($adsAccountData)) {
            return 0;
        }

        $costSum = AdsAccountDataHour::find()
            ->where(['date' => $data['date'], 'ads_sub_id' => $data['ads_sub_id'], 'entity_id' => $data['entity_id']])
            ->sum('cost') ?: 0;

        if ($costSum == 0) {
            return 0;
        }

        // 实际消耗的等于(总账面消耗-赔付金额）/ 返点*（单城市消耗/账户总消耗）
        $compensate = $adsAccountData ? $adsAccountData->compensate : 0;
        $rebates = $adsAccountData ? $adsAccountData->rebates : 1;
        $dividend = BcHelper::div(BcHelper::sub($costSum, $compensate, 8), $rebates, 8);

        return BcHelper::mul($dividend, BcHelper::div($cost, $costSum));
    }
    
    /**
     * 精细化数据
     * 
     * @param array $data
     * @return bool
     */
    public static function refineData(array $data)
    {
        if (!is_numeric($data['ads_sub_id'])) {
            throw new Exception('ads_sub_id 无效');
        }

        $adsAccountDataHour = AdsAccountDataHour::find()
            ->where(['date' => $data['date'], 'ads_sub_id' => $data['ads_sub_id'], 'entity_id' => $data['entity_id'], 'hour' => $data['hour']])
            ->one();

        if (empty($adsAccountDataHour)) {
            $adsAccountDataHour = new AdsAccountDataHour();
            $adsAccountDataHour->attributes = $data;
        } else {
            // $data['actual_consume'] = static::calculateActualConsume($data, $adsAccountDataHour->cost);
            $adsAccountDataHour->attributes = $data;
        }

        if (!$adsAccountDataHour->save()) {
            throw new Exception('保存时段分析数据失败：' . current($adsAccountDataHour->getFirstErrors()));
        }

        return true;
    }
}