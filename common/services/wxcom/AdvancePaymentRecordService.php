<?php

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/6/17
 * Time: 16:07
 * 
 * 垫款记录
 */

namespace common\services\wxcom;

use common\components\Feishu;
use common\helpers\BcHelper;
use common\models\common\AdsAccountSub;
use common\models\promote\AdsMainBody;
use Yii;
use Exception;
use services\common\FeishuExamineService;

class AdvancePaymentRecordService
{
    private static $CHZ_TIKTOK_AD001 = '超荟妆-抖音AD001';
    private static $CHZ_WECHAT_K4001 = '超荟妆-腾讯K4001';
    private static $CHZ_TIKTOK_LOCAL = '超荟妆-抖音本地推';

    public static function groupName()
    {
        return [
            static::$CHZ_TIKTOK_AD001,
            static::$CHZ_WECHAT_K4001,
            static::$CHZ_TIKTOK_LOCAL,
        ];
    }

    public function dealData($params)
    {
        try {
            if (strpos($params['spoken'], '充值成功') === false || strpos($params['spoken'], '充值时间') === false) {
                return true;
            }

            $dataMap = $this->disposalData($params);
            $time = $dataMap['充值时间'] ? strtotime($dataMap['充值时间']) : time();

            if (empty($dataMap['账户ID'])) {
                throw new Exception('数据有误，账户ID为空');
            }

            if ($params['groupName'] == static::$CHZ_TIKTOK_LOCAL) {
                $accountInfo = [
                    'main_body_name' => '本地推',
                    'rebates' => 1.05,
                    'promote_id' => 0
                ];
            } else {
                $accountInfo = AdsAccountSub::find()->alias('aas')
                    ->select('aas.promote_id,amb.name as main_body_name,aas.rebates')
                    ->leftJoin(['amb' => AdsMainBody::tableName()], 'aas.main_body_id = amb.id')
                    ->where(['aas.sub_advertiser_id' => $dataMap['账户ID']])
                    ->asArray()
                    ->one();

                if (empty($accountInfo)) {
                    throw new Exception('“' . $dataMap['账户ID'] . '“不存在系统账户中');
                }

                if (empty($accountInfo['main_body_name'])) {
                    throw new Exception('“' . $dataMap['账户ID'] . '“主体不存在');
                }
            }

            $main_body_name = $accountInfo['main_body_name'];
            $dataMap['充值账户币'] = str_replace(',', '', $dataMap['充值账户币']) * 1;
            $actual_account = BcHelper::div($dataMap['充值账户币'], $accountInfo['rebates']) * 1;

            $addData = [
                '充值日期' =>  $time * 1000,
                '类型' => '垫款',
                '充值金额' => $dataMap['充值账户币'],
                '政策' => (string)$accountInfo['rebates'],
                '回款金额' => $actual_account,
                '业务序号' => (string)$dataMap['业务序号'],
                '转入账户' => (string)$main_body_name,
                '转入ID' => (string)$dataMap['账户ID']
            ];

            $moreTableConfig = $this->getConfigByMainBody($main_body_name, $accountInfo['promote_id']);

            $isExist = $this->isExist($moreTableConfig, $dataMap['业务序号']);
            if ($isExist) { //已存在，无需在新增
                return true;
            }

            $this->createMoreTableData($addData, $moreTableConfig);
        } catch (Exception $e) {
            $error = ['type' => '垫款记录存到多维表格失败', '原因' => $e->getMessage(), 'data' => $params];
            $group = FeishuExamineService::arrGroup('JSGTQ');
            Yii::$app->feishuNotice->text($error, $group['chat_id']);
            Yii::error($error, 'AdvancePaymentRecordService');
        }
    }

    public function disposalData($params)
    {
        //按换行分割字符串
        $spoken = explode("\n", $params['spoken']);
        $dataMap = [
            '业务序号' => '',
            '账户ID' => '',
            '充值时间' => '',
            '充值账户币' => '',
        ];

        foreach ($spoken as $line) {
            foreach ($dataMap as $key => $val) {
                if (strpos($line, $key) !== false) {
                    $parts = explode('：', $line);
                    if (count($parts) == 2) {
                        $dataMap[$key] = trim($parts[1]);
                    }
                }
            }
        }

        return $dataMap;
    }

    public function isExist($moreTableConfig, $business_serial_number)
    {
        $params = [
            'filter' => [
                'conjunction' => "and",
                'conditions' => [
                    [
                        'field_name' => '业务序号',
                        'operator' => 'is',
                        'value' => [
                            $business_serial_number
                        ]
                    ]
                ]
            ]
        ];

        $feishuClass = new Feishu();
        $res = $feishuClass->getMoreTableDataSearch($moreTableConfig['table_file_token'], $moreTableConfig['table_id'], $params);
        unset($feishuClass);
        if ($res['code'] != 0) {
            throw new Exception('推广企业微信充值记录，获取多维表格数据失败，原因：' . $res['error']['message']);
        }

        if ($res['data']['total'] > 0) {
            return true;
        }

        return false;
    }

    public function createMoreTableData($addData, $moreTableConfig)
    {
        $feishuClass = new Feishu();
        $res = $feishuClass->createMoreTableData($moreTableConfig['table_file_token'], $moreTableConfig['table_id'], $addData);
        unset($feishuClass);
        if ($res['code'] != 0) {
            throw new Exception('推广企业微信充值记录，新增到多维表格数据失败，原因：' . $res['error']['message']);
        }
    }

    public function getConfigByMainBody($main_body_name, $channel_id)
    {
        if ($channel_id == 2) {
            $main_body_name = '腾讯';
        }

        $config = $this->moreTableConfig();
        foreach ($config['tableArr'] as $table) {
            if (strpos($table['table_name'], $main_body_name) !== false) {
                return [
                    'table_file_token' => $config['table_file_token'],
                    'table_id' => $table['table_id']
                ];
            }
        }
        throw new Exception('主体名称：' . $main_body_name . '未找到对应的多维表格配置信息');
    }

    /**
     * 多维表格配置
     */
    public function moreTableConfig()
    {
        //正式使用的配置
        return [
            'table_file_token' => 'ZYzwbCVU8aP9DqsAEoIcYhISnRe',
            'tableArr' => [
                [
                    'table_name' => '皓蕴-云赢',
                    'table_id' => 'tblA22Mkkv5lDuxF'
                ],
                [
                    'table_name' => '花好容-云赢',
                    'table_id' => 'tbl3ewJcaYJxwjo0'
                ],
                [
                    'table_name' => '奥莉耶-云赢',
                    'table_id' => 'tblPVtJfAbXgN61B'
                ],
                [
                    'table_name' => '花婉禾-云赢',
                    'table_id' => 'tblEnXSFtJahCwlI'
                ],
                [
                    'table_name' => '腾讯',
                    'table_id' => 'tbloFC3NEDiGD9JE'
                ],
                [
                    'table_name' => '本地推',
                    'table_id' => 'tblyaKssyWyts597'
                ],
            ]
        ];
    }
}
