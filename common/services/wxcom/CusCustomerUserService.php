<?php

namespace common\services\wxcom;

use common\components\Ip;
use common\models\wxcom\CusCustomerUser;
use Yii;

class CusCustomerUserService extends CusCustomerUser
{
    /**
     * 处理用户的ip
     * @param int $id
     * @return bool
     */
    public static function dealIp($id)
    {
        $cusCustomerUser = self::findOne($id);
        if (empty($cusCustomerUser)) {
            return false;
        }

        if (empty($cusCustomerUser->ip)) {
            return false;
        }

        $ip = $cusCustomerUser->ip;
        $ipInfo = Ip::getIpInfoByIp138($ip);
        if (empty($ipInfo)) {
            return false;
        }
    }
}
