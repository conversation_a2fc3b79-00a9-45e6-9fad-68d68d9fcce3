<?php

namespace common\services;

use auth\models\customer_recharge\CustomerRechargePay;
use common\components\PayFactory;
use common\enums\CustomerRechargeCardEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\pay\LogPaySourceEnum;
use common\enums\pay\StorePayRecordPaymentTypeEnum;
use common\enums\pay\StorePayRecordPayTypeEnum;
use common\enums\WhetherEnum;
use common\helpers\BcHelper;
use common\models\backend\order\OrderHeader;
use common\models\backend\Store;
use auth\models\customer_recharge\CustomerRechargeCard;
use common\models\log\Pay as PayLog;
use auth\models\OrderPay;
use common\models\StorePayRecord;
use Yii;
use Exception;
use auth\services\customer_recharge\CustomerRechargeCardService;

class PaymentService
{
    public static $modelClass = '';

    /**
     * 扫码枪支付
     * @param int $orderId order.id或CustomerRechargeCard。id
     * @param int $customerRechargeCard 储值卡类型1是0否
     * @param float $amount 金额
     * @param string $code 条形码
     * @return float[]
     * @throws Exception
     */
    public static function unionScanCode(int $orderId, int $customerRechargeCard, float $amount, string $code, int $convenientPay)
    {
        //后续优化
        throw new Exception('扫码枪功能维护中');

        if (!in_array($customerRechargeCard, array(0,1))) {
            throw new Exception('储值卡支付类型错误');
        }
        $order = $customerRechargeCard == 1 ? CustomerRechargeCard::findOne($orderId) : OrderHeader::findOne($orderId);
        if (!$order) {
            throw new Exception('订单不存在');
        }
        if (($customerRechargeCard == 0 && $order->order_status == OrderHeaderStatusEnum::STATUS_COMPLETED) || ($customerRechargeCard == 1 && $order->pay_status == CustomerRechargeCardEnum::HAVE_PAID)) {
            throw new Exception('订单已支付');
        }
        $payAmount = $customerRechargeCard == 1 ? $order->recharge_amount : $order->pay_amount;
        $arrears = $payAmount - $order->received_amount;
        if ($convenientPay == 0 && $arrears < $amount) {
            throw new Exception('订单金额不能大于应付金额');
        }
        if ($order->store_id == 0) {
            throw new Exception('订单数据错误');
        }
        if ($amount <= 0) {
            throw new Exception('订单金额需要大于零');
        }
        $orderNo = $customerRechargeCard == 0 ? $order->order_no : 'E' . date("YmdHis") . sprintf("%05d", $order->store_id) . mt_rand(100000, 999999);
        $params = [
            'amount' => $amount,
            'code' => $code,
            'type' => 'scan',
            'order_no' => $orderNo . 'out' . mt_rand(10000, 99999)
        ];
        $time = time();
        $result = PayFactory::getInstance()->getPayment('NewUnionPay', $order->store_id, $params)->scan();
        $store = Store::findOne($order->store_id);
        $payLog = new PayLog();
        $payLog->pay_time = $time;
        $payLog->amount = $amount;
        $payLog->content = json_encode($result);
        $payLog->request_param = json_encode($params);
        $payLog->source_id = $orderId;
        $payLog->mch_id = $store->mid;
        $payLog->entity_id = $order->entity_id;
        $payLog->source_id = $orderId;
        $payLog->source_type = $customerRechargeCard == 1 ? LogPaySourceEnum::CARD_PAY : LogPaySourceEnum::ORDER_PAY;
        if (!is_array($result)) {
            throw new Exception('系统错误：接口响应格式解析失败' . var_export($result, true));
        }
        if ($result['errCode'] !== '00') {
            $payLog->out_trade_no = isset($result['thirdPartyOrderId']) ? $result['thirdPartyOrderId'] : '';
            $payLog->payment_type = StorePayRecordPaymentTypeEnum::UNION_QRCODE;
            $payLog->save();
            throw new Exception($result['errInfo']);
        }
        $result['pay_time'] = $time;
        // 微信支付、支付宝支付、云闪付支付
        switch ($result['thirdPartyName']) {
            case strstr($result['thirdPartyName'], '微信'):
                $payType = StorePayRecordPayTypeEnum::WXPAY;
                break;
            case strstr($result['thirdPartyName'], '支付宝'):
                $payType = StorePayRecordPayTypeEnum::ALIPAY;
                break;
            case strstr($result['thirdPartyName'], '银联'):
                $payType = StorePayRecordPayTypeEnum::UNION_PAY;
                break;
            default:
                $payType = StorePayRecordPayTypeEnum::UNKNOWN;
        }
        $payLog->out_trade_no = isset($result['orderId']) ? $result['orderId'] : '';
        $payLog->transaction_id = isset($result['thirdPartyOrderId']) ? $result['thirdPartyOrderId'] : '';
        $payLog->pay_type = $payType;
        $payLog->save();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 支付是优惠，银联对应会扣除
            $order->updateCounters(['received_amount' => $amount]);

            $storePayRecord = new StorePayRecord();
            $storePayRecord->store_id = $order->store_id;
            $storePayRecord->out_trade_no = $result['orderId'];
            $storePayRecord->transaction_id = $result['thirdPartyOrderId'];
            $storePayRecord->payment_type = StorePayRecordPaymentTypeEnum::UNION_SCAN;
            $storePayRecord->amount = $amount;
            $storePayRecord->left_amount = $amount;
            $storePayRecord->pay_time = $result['pay_time'];
            $storePayRecord->mch_id = $store->mid ?? 0;
            $storePayRecord->entity_id = $unionPay['entity_id'] ?? 1;
            $storePayRecord->pay_type = $payType;
            $storePayRecord->is_use = 1;
            if (!$storePayRecord->save()) {
                throw new Exception(current($storePayRecord->getFirstErrors()));
            }

            if ($customerRechargeCard == 1) {
                $modelPay = new CustomerRechargePay();
                $modelPay->amount = $amount;
                $modelPay->cus_id = $order->cus_id;
                $modelPay->cus_recharge_id = $order->id;
                $modelPay->store_id = $order->store_id;
                $modelPay->pay_record_id = $storePayRecord->id;
                $modelPay->entity_id = $unionPay['entity_id'] ?? 1;
            } else {
                $modelPay = new OrderPay();
                $modelPay->amount = $amount;
                $modelPay->store_pay_record_id = $storePayRecord->id;
                $modelPay->transaction_id = $result['thirdPartyOrderId'];
                $modelPay->order_id = $order->id;
                $modelPay->store_id = $order->store_id;
            }
            if (!$modelPay->save()) {
                // $err = $modelPay->getErrors(); 
                // file_put_contents("/tmp/pay.txt", print_r($modelPay->getErrors(), true), FILE_APPEND);
                throw new Exception('LOG存储出错，请联系管理员');
            }

            //重新计算订单项目分摊
//            if ($customerRechargeCard == WhetherEnum::DISABLED) {
//                $order->calcOrderProjects();
//            }

            $transaction->commit();
        }catch (Exception $e) {           
            $transaction->rollBack();
        }
        return ['amount' => $amount];
    }

    /**
     * 条形码收款
     *  type：0订单、1储值卡
     * @param array $params
     * @return bool
     * @throws Exception
     */
    public static function barCodePay($params = [])
    {
        if (!isset($params['type'])) {
            throw new Exception('请选择支付类型');
        }

        if ($params['type'] == 1) {
            //储值卡流水号核销
            static::cardBarCodePay($params);
        } else if ($params['type'] == 0){
            //普通订单流水号核销
            static::orderBarCodePay($params);
        }

        return true;
    }

    /**
     * 条形码收款 - 订单
     *
     * @param $params
     * @return bool
     * @throws Exception
     */
    public static function orderBarCodePay($params)
    {
        $order = OrderHeader::find()
            ->andWhere(['id' => $params['id']])
            ->one();

        if (!$order) {
            throw new Exception('订单不存在');
        }

        if ($order->order_status != OrderHeaderStatusEnum::STATUS_SETTLEMENT) {
            throw new Exception('只有待结算状态，才可录入流水');
        }
 
        if ($order->pay_amount == $order->received_amount) {
            throw new Exception('本单金额已收完,请勿重复支付流水');
        }

        $bar_code = trim($params['bar_code']);
        if (preg_match('/[^a-zA-Z0-9]/', $bar_code)) { // 只允许字母和数字
            throw new Exception('流水号不正确,不允许存在特殊符号，只能一笔流水一笔流水录入，不能多笔流水号同时录入');
        }

        $exit = StorePayRecord::find()
            ->select('id')
            ->andWhere(['or', ['out_trade_no' => $bar_code], ['transaction_id' => $bar_code]])
            ->limit('1')
            ->one();

        if ($exit) {
            throw new Exception('该流水号在【关联流水】中存在，请到【关联流水】选择即可，不可手动录入');
        }
        
        $payModel = new OrderPay();
        $payModel->amount = $params['amount'];
        $payModel->cus_id = $order->cus_id;
        $payModel->order_id = $order->id;
        $payModel->store_id = $order->store_id;
        $payModel->store_pay_record_id = 0;
        $payModel->transaction_id = $bar_code;
        if (!$payModel->save()) {
            throw new Exception(current($payModel->getFirstErrors()));
        }

        //客户收款金额
        $order->received_amount += $payModel->amount;
        if (!$order->save()) {
            throw new Exception(current($order->getFirstErrors()));
        }

        //重新计算订单项目分摊
//        $order->calcOrderProjects();

        return true;
    }


    /**
     * 条形码收款 - 储值卡
     *
     * @param $params
     * @return bool
     * @throws Exception
     */
    public static function cardBarCodePay($params)
    {
        $model = CustomerRechargeCardService::getCardPayInfo($params);
        $recharge_amount = BcHelper::sub($model->recharge_amount, $model->received_amount);

        $payModel = new CustomerRechargePay();
        $payModel->amount = $recharge_amount;
        $payModel->cus_id = $model->cus_id;
        $payModel->cus_recharge_id = $model->id;
        $payModel->store_id = $model->store_id;
        $payModel->pay_record_id = 0;
        $payModel->transaction_id = $params['bar_code'];
        if (!$payModel->save()) {
            throw new Exception(current($payModel->getFirstErrors()));
        }

        //客户收款金额
        $model->received_amount += $recharge_amount;
        if (!$model->save()) {
            throw new Exception(current($model->getFirstErrors()));
        }

        return true;
    }

}