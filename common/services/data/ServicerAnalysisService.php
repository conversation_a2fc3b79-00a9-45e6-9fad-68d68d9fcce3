<?php

namespace common\services\data;

use backendapi\models\order\OrderHeader;
use backendapi\services\wxcom\CusTagService;
use common\models\backend\order\OrderPlanDetail;
use common\models\common\DepartmentAssignment;
use common\models\common\Entity;
use common\models\data\ServicerAnalysis;
use common\models\order\CustomerChurnRemark;
use common\models\wechat\FansRecord;
use common\models\wxcom\CusCustomerUser;
use common\services\BaseService;
use Exception;
use Yii;

use function Matrix\trace;

class ServicerAnalysisService extends BaseService
{
    /**
     * @var ServicerAnalysis
     */
    public static $modelClass = ServicerAnalysis::class;

    /**
     * 客服数据分析
     *
     * @param string $dateStr
     * @return void
     */
    public static function cal($dateStr)
    {
        $dateBeginTime = strtotime($dateStr);
        $dateEndTime = $dateBeginTime + 24 * 60 * 60 - 1;

        // 统计加粉、定人数
        ServicerAnalysis::deleteAll(['date_time' => $dateBeginTime]);
        $entityMap = Entity::getAllEntity();
        $computeAddWay = implode(',', CusCustomerUser::getComputeAddWay());
        foreach ($entityMap as $entityId => $entityName) {
            $tmpMap = [];
            $initItem = [
                'entity_id' => $entityId,
                'system' => 'erp',
                'date_time' => $dateBeginTime,
                'add_fans_count' => 0,
                'deposit_count' => 0,
                'plan_cus_count' => 0,
                'new_plan_cus_count' => 0,
                'tomorrow_plan_cus_count' => 0,
                'tomorrow_new_plan_cus_count' => 0,
                'deposit_cus_count' => 0,
                'month_deposit_cus_count' => 0,
                'deposit_sum' => 0,
                'new_store_cus_count' => 0,
                'new_store_cus_count_before' => 0,
                'remote_count' => 0,
                'attrition_count' => 0,
                'created_at' => time(),
                'dept_id' => 0,
                'user_id' => 0,
            ];

            // 加粉
            $wxcomAddFansList = Yii::$app->db->createCommand("
                SELECT
                    COUNT(DISTINCT cu.id) AS add_fans_count,
                    cu.created_by as user_id,
                    cu.project_id
                FROM
                    {{%wxcom_cus_customer_user}} cu
                LEFT JOIN {{%wxcom_user}} wu ON wu.id = cu.user_id
                left join {{%wxcom_cus_customer}} ewcc on ewcc.id = cu.cus_id 
                left join {{%wechat_user}} ewu on ewu.unionid = ewcc.unionid 
                WHERE 
                    cu.channel_id > 0 
                    AND cu.add_way IN($computeAddWay) 
                    AND cu.add_time BETWEEN {$dateBeginTime} AND {$dateEndTime} 
                    and ewu.id is null
                    and cu.entity_id = {$entityId}
                    and cu.created_by > 0
                GROUP BY cu.created_by,cu.project_id
            ")->queryAll();
            foreach ($wxcomAddFansList as $addFansCount) {
                self::initializeTmpMap($tmpMap, $addFansCount['user_id'], $addFansCount['project_id'], $initItem);
                $tmpMap[$addFansCount['user_id']][$addFansCount['project_id']]['add_fans_count'] += $addFansCount['add_fans_count'];
            }
            $fansRecordList = FansRecord::find()
                ->where(['between', 'add_time', $dateBeginTime, $dateEndTime])
                ->andWhere(['entity_id' => $entityId])
                ->andWhere(['>', 'user_id', 0])
                ->select('user_id,project_id,count(1) as add_fans_count')
                ->groupBy('user_id,project_id')
                ->asArray()
                ->all();
            foreach ($fansRecordList as $addFansCount) {
                self::initializeTmpMap($tmpMap, $addFansCount['user_id'], $addFansCount['project_id'], $initItem);
                $tmpMap[$addFansCount['user_id']][$addFansCount['project_id']]['add_fans_count'] += $addFansCount['add_fans_count'];
            }

            //设置偏远数
            $tmpMap = static::setRemoteCount($tmpMap, $initItem, $dateBeginTime, $dateEndTime, $entityId);

            //设置当月定金人数
            // month_deposit_cus_count 以改为实时计算，不再使用
            // $tmpMap = static::setMonthDepositCusCount($tmpMap, $initItem, $dateBeginTime, $dateEndTime, $entityId);
            
            //设置流失人数
            $tmpMap = static::setAttritionCount($tmpMap, $initItem, $dateBeginTime, $dateEndTime, $entityId);

            $orderQuery = OrderPlanDetail::find()
                ->alias('planDetail')
                ->joinWith(['order orderHeader'])
                ->leftJoin('erp_customer cus', 'cus.id = orderHeader.cus_id')
                ->where(['planDetail.entity_id' => $entityId])
                ->andWhere(['between', 'orderHeader.pre_pay_time', $dateBeginTime, $dateEndTime])
                ->groupBy('planDetail.created_by,cus.project_id')
                ->asArray();
            $depositOrderStatusList = OrderHeader::depositOrderStatusList();
            
            // 订人数
            $depositCusCountList = $orderQuery
                ->select('planDetail.created_by as user_id,cus.project_id,count(distinct orderHeader.cus_id) as deposit_cus_count,SUM(planDetail.deposit) AS deposit_sum,COUNT(DISTINCT planDetail.order_id) AS deposit_count')
                ->andWhere(['orderHeader.order_status' => $depositOrderStatusList])
                ->all();
            foreach ($depositCusCountList as $depositCusCount) {
                self::initializeTmpMap($tmpMap, $depositCusCount['user_id'], $depositCusCount['project_id'], $initItem);
                $tmpMap[$depositCusCount['user_id']][$depositCusCount['project_id']]['deposit_cus_count'] += $depositCusCount['deposit_cus_count'];
                $tmpMap[$depositCusCount['user_id']][$depositCusCount['project_id']]['deposit_count'] += $depositCusCount['deposit_count'];
                $tmpMap[$depositCusCount['user_id']][$depositCusCount['project_id']]['deposit_sum'] += $depositCusCount['deposit_sum'];
            }
            self::fillUserDeptId($tmpMap);
            self::batchInsert($tmpMap);
        }

        // 统计 entity <> 1 的预约人数、新客到店人数
        $entityMap = Entity::getAllEntity();
        $todayEndTime = $dateBeginTime + 86400 - 1;
        $endTime = $todayEndTime + 86400;
        foreach ($entityMap as $entityId => $entityName) {
            $tmpMap = [];
            $initItem = [
                'entity_id' => $entityId,
                'system' => 'erp',
                'date_time' => $dateBeginTime,
                'add_fans_count' => 0,
                'deposit_count' => 0,
                'plan_cus_count' => 0,
                'new_plan_cus_count' => 0,
                'tomorrow_plan_cus_count' => 0,
                'tomorrow_new_plan_cus_count' => 0,
                'deposit_cus_count' => 0,
                'month_deposit_cus_count' => 0,
                'deposit_sum' => 0,
                'new_store_cus_count' => 0,
                'new_store_cus_count_before' => 0,
                'remote_count' => 0,
                'attrition_count' => 0,
                'created_at' => time(),
                'dept_id' => 0,
                'user_id' => 0,
            ];

            // 预约人数
            $countData = Yii::$app->db->createCommand("
                select
                    eoh.plan_by as user_id,
                    eda.dept_id,
                    ec.project_id ,
                    count(DISTINCT if(eoh.plan_time <= {$todayEndTime} and ec.first_store_time > 0 and FROM_UNIXTIME(eoh.plan_time, '%Y%m%d') <> FROM_UNIXTIME(ec.first_store_time, '%Y%m%d'), ec.mobile, null)) as plan_count,
                    count(DISTINCT if(eoh.plan_time <= {$todayEndTime} and (ec.first_store_time = 0 or FROM_UNIXTIME(eoh.plan_time, '%Y%m%d') = FROM_UNIXTIME(ec.first_store_time, '%Y%m%d')), ec.mobile, null)) as new_plan_count,
                    count(DISTINCT if(eoh.plan_time > {$todayEndTime} and ec.first_store_time > 0, ec.mobile, null)) as tomorrow_plan_count,
                    count(DISTINCT if(eoh.plan_time > {$todayEndTime} and ec.first_store_time = 0, ec.mobile, null)) as tomorrow_new_plan_count
                from erp_order_header eoh
                left join erp_customer ec on ec.id = eoh.cus_id 
                left join erp_department_assignment eda on eda.user_id = eoh.plan_by 
                where eoh.order_status in (1,3,4,5)
                    and eoh.source_type <> 2
                    and eoh.plan_time BETWEEN {$dateBeginTime} and {$endTime}
                    and eoh.plan_by > 0
                    and eoh.entity_id = {$entityId}
                group by eoh.plan_by
            ")->queryAll();
            foreach ($countData as $countDataItem) {
                $userId = $countDataItem['user_id'];
                $projectId = $countDataItem['project_id'];
                if (!isset($tmpMap[$userId]) || !isset($tmpMap[$userId][$projectId])) {
                    $tmpMap[$userId][$projectId] = $initItem;
                    $tmpMap[$userId][$projectId]['user_id'] = $countDataItem['user_id'];
                    $tmpMap[$userId][$projectId]['dept_id'] = $countDataItem['dept_id'];
                    $tmpMap[$userId][$projectId]['project_id'] = $countDataItem['project_id'];
                }
                $tmpMap[$userId][$projectId]['plan_cus_count'] += $countDataItem['plan_count'];
                $tmpMap[$userId][$projectId]['new_plan_cus_count'] += $countDataItem['new_plan_count'];
                $tmpMap[$userId][$projectId]['tomorrow_plan_cus_count'] += $countDataItem['tomorrow_plan_count'];
                $tmpMap[$userId][$projectId]['tomorrow_new_plan_cus_count'] += $countDataItem['tomorrow_new_plan_count'];
            }
            // 新客到店人数
            $countData = Yii::$app->db->createCommand("
                SELECT  ebm.id as user_id,
                    eda.dept_id,
                    ec.project_id,
                    ebm.id = eoh.created_by as new_cus_store_count_before,
                    ebm.id = eoh.plan_by as new_cus_store_count
                from erp_order_header eoh 
                inner join erp_customer ec on ec.id = eoh.cus_id 
                inner join erp_order_project eop on eop.order_id = eoh.id
                left join erp_backend_member ebm on ebm.id = eoh.created_by or ebm.id = eoh.plan_by 
                left join erp_department_assignment eda on eda.user_id = ebm.id 
                where eoh.plan_time BETWEEN {$dateBeginTime} and {$todayEndTime}
                    and eoh.order_status = 5
                    and eoh.source_type <> 2
                    and FROM_UNIXTIME(eoh.plan_time, '%Y%m%d') = FROM_UNIXTIME(ec.first_store_time , '%Y%m%d')
                    and ebm.id is not null
                    and eoh.entity_id = {$entityId}
                group by ebm.id,ec.mobile 
            ")->queryAll();
            //历史的有剔除不计算新客人数的逻辑，先保留，后续看情况启用
            //     $countData = Yii::$app->db->createCommand("
            //     SELECT  ebm.id as user_id,
            //         eda.dept_id,
            //         ec.project_id,
            //         ebm.id = eoh.created_by as new_cus_store_count_before,
            //         ebm.id = eoh.plan_by as new_cus_store_count
            //     from erp_order_header eoh 
            //     inner join erp_customer ec on ec.id = eoh.cus_id 
            //     inner join erp_promote_channel epc on epc.id = eoh.channel_id and epc.is_calculate_results = 0
            //     inner join erp_order_project eop on eop.order_id = eoh.id
            //     left join erp_backend_member ebm on ebm.id = eoh.created_by or ebm.id = eoh.plan_by 
            //     left join erp_goods_package egp on egp.id = eop.package_id
            //     left join erp_goods_product egp2  on egp2.id = eop.goods_id and eop.package_id = 0
            //     left join erp_department_assignment eda on eda.user_id = ebm.id 
            //     where eoh.plan_time BETWEEN {$dateBeginTime} and {$todayEndTime}
            //         and eoh.order_status = 5
            //         and eoh.source_type <> 2
            //         and FROM_UNIXTIME(eoh.plan_time, '%Y%m%d') = FROM_UNIXTIME(ec.first_store_time , '%Y%m%d')
            //         and COALESCE(egp.is_calculate_results, egp2.is_calculate_results) = 1
            //         and ebm.id is not null
            //         and eoh.entity_id = {$entityId}
            //     group by ebm.id,ec.mobile 
            // ")->queryAll();
            foreach ($countData as $countDataItem) {
                $userId = $countDataItem['user_id'];
                $projectId = $countDataItem['project_id'];
                if (!isset($tmpMap[$userId]) || !isset($tmpMap[$userId][$projectId])) {
                    $tmpMap[$userId][$projectId] = $initItem;
                    $tmpMap[$userId][$projectId]['user_id'] = $countDataItem['user_id'];
                    $tmpMap[$userId][$projectId]['dept_id'] = $countDataItem['dept_id'];
                    $tmpMap[$userId][$projectId]['project_id'] = $countDataItem['project_id'];
                }
                $tmpMap[$userId][$projectId]['new_store_cus_count'] += $countDataItem['new_cus_store_count'];
                $tmpMap[$userId][$projectId]['new_store_cus_count_before'] += $countDataItem['new_cus_store_count_before'];
            }

            self::batchInsert($tmpMap);
        }
    }

    /**
     * 补充成员部门id
     *
     * @param array $batchInsertList
     * @return void
     */
    public static function fillUserDeptId(&$batchInsertList)
    {
        $userIds = array_column($batchInsertList, 'user_id');
        $deptAsigms = DepartmentAssignment::find()
            ->where(['user_id' => $userIds])
            ->indexBy('user_id')
            ->all();
        foreach ($batchInsertList as &$batchInsert) {
            if (!empty($batchInsert['dept_id'])) {
                continue;
            }

            $userId = $batchInsert['user_id'];
            if (empty($deptAsigms[$userId])) {
                continue;
            }

            $batchInsert['dept_id'] = $deptAsigms[$userId]['dept_id'];
        }
    }

    /**
     * 批量插入数据
     *
     * @param array $tmpMap
     * @return int
     */
    public static function batchInsert(array $tmpMap): int
    {
        // 验证数据长度
        if (empty($tmpMap)) {
            return 0;
        }

        // 重新组装数据
        $batchInsertList = [];
        foreach ($tmpMap as $userCountList) {
            if (empty($userCountList)) {
                continue;
            }

            $batchInsertList = array_merge($batchInsertList, array_values($userCountList));
        }

        // 验证数据长度
        if (!count($batchInsertList)) {
            return 0;
        }

        // 保存数据
        return ServicerAnalysis::find()
            ->createCommand()
            ->batchInsert(ServicerAnalysis::tableName(), array_keys($batchInsertList[0]), $batchInsertList)
            ->execute();
    }

    public static function setRemoteCount($tmpMap, $initItem, $startTime, $endTime, $entity_id)
    {
        try {
            $tagId = CusTagService::getRemoteTag($entity_id);
            if (empty($tagId)) {
                return $tmpMap;
            }

            $list =  CusCustomerUser::find()->alias('cu')
                ->select(['created_by as user_id,project_id,COUNT(DISTINCT id) AS remote_count'])
                ->where(['>', 'channel_id', 0])
                ->andWhere(['add_way' => CusCustomerUser::getComputeAddWay()])
                ->andWhere(['entity_id' => $entity_id])
                ->andWhere('FIND_IN_SET(:tag_id, tag_ids)', [':tag_id' => $tagId])
                ->andWhere(['BETWEEN', 'add_time', $startTime, $endTime])
                ->andWhere(['>', 'created_by', 0])
                ->groupBy('created_by,project_id')
                ->asArray()
                ->all();

            if (empty($list)) {
                return $tmpMap;
            }

            foreach ($list as $item) {
                self::initializeTmpMap($tmpMap, $item['user_id'], $item['project_id'], $initItem);
                $tmpMap[$item['user_id']][$item['project_id']]['remote_count'] += $item['remote_count'];
            }

            return $tmpMap;
        } catch (Exception $e) {
            error_log($e->getMessage());
            return $tmpMap;
        }
    }

    public static function setMonthDepositCusCount($tmpMap, $initItem, $startTime, $endTime, $entity_id)
    {
        try {
            $monthStartTime = strtotime(date('Y-m-01', $startTime));
            $monthEndTime = strtotime(date('Y-m-t 23:59:59', $endTime));

            $depositOrderStatusList = OrderHeader::depositOrderStatusList();
            $computeAddWay = CusCustomerUser::getComputeAddWay();
            $orderQuery = OrderPlanDetail::find()
                ->alias('planDetail')
                ->joinWith(['order orderHeader'])
                ->leftJoin('erp_customer cus', 'cus.id = orderHeader.cus_id')
                ->leftJoin('erp_wxcom_cus_customer_user ccu', 'ccu.id = orderHeader.customer_user_id')
                ->where(['planDetail.entity_id' => $entity_id])
                ->andWhere(['>', 'ccu.channel_id', 0])
                ->andWhere(['ccu.add_way' => $computeAddWay])
                ->andWhere(['between', 'orderHeader.pre_pay_time', $startTime, $endTime])
                ->andWhere(['between', 'ccu.add_time', $monthStartTime, $monthEndTime])
                ->groupBy('planDetail.created_by,cus.project_id')
                ->asArray();

            // 订人数
            $list = $orderQuery
                ->select('planDetail.created_by as user_id,cus.project_id,count(distinct orderHeader.cus_id) as month_deposit_cus_count')
                ->andWhere(['orderHeader.order_status' => $depositOrderStatusList])
                ->all();

            if (empty($list)) {
                return $tmpMap;
            }

            foreach ($list as $item) {
                self::initializeTmpMap($tmpMap, $item['user_id'], $item['project_id'], $initItem);
                $tmpMap[$item['user_id']][$item['project_id']]['month_deposit_cus_count'] += $item['month_deposit_cus_count'];
            }

            return $tmpMap;
        } catch (Exception $e) {
            error_log($e->getMessage());
            return $tmpMap;
        }
    }

    public static function setAttritionCount($tmpMap, $initItem, $startTime, $endTime, $entity_id)
    {
        try {
            $list = CustomerChurnRemark::find()
                ->alias('ccr')
                ->leftJoin('{{%order_header}} oh', 'oh.id = ccr.order_id')
                ->leftJoin('{{%customer}} cus', 'cus.id = oh.cus_id')
                ->select('ccr.created_by as user_id,cus.project_id,COUNT(DISTINCT ccr.id) as attrition_count')
                ->where([
                    'ccr.entity_id' => $entity_id,
                    'ccr.reach_status' => 2,
                ])
                ->andWhere(['BETWEEN', 'ccr.plan_time', $startTime, $endTime])
                ->groupBy('ccr.created_by,cus.project_id')
                ->asArray()
                ->all();

            if (empty($list)) {
                return $tmpMap;
            }
            foreach ($list as $item) {
                self::initializeTmpMap($tmpMap, $item['user_id'], $item['project_id'], $initItem);
                $tmpMap[$item['user_id']][$item['project_id']]['attrition_count'] += $item['attrition_count'];
            }
            return $tmpMap;
        } catch (Exception $e) {
            error_log($e->getMessage());
            return $tmpMap;
        }
    }

    private static function initializeTmpMap(&$tmpMap, $userId, $projectId, $initItem)
    {
        if (!isset($tmpMap[$userId]) || !isset($tmpMap[$userId][$projectId])) {
            $tmpMap[$userId][$projectId] = $initItem;
            $tmpMap[$userId][$projectId]['user_id'] = $userId;
            $tmpMap[$userId][$projectId]['project_id'] = $projectId;
        }
    }
}
