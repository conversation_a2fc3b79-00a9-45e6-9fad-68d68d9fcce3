<?php

namespace common\enums\pay;

use common\enums\BaseEnum;

/**
 * 银联支付账单编号标题枚举
 *
 * Class UnionPayBillNoTitleEnum
 * @package common\enums\pay
 */
class UnionPayBillNoTitleEnum extends BaseEnum
{
    const TITLE_14JK = '14JK';
    const TITLE_3F0B = '3F0B';
    const TITLE_3F0A = '3F0A';

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::TITLE_14JK => [
                'name' => '福州超荟妆信息科技有限公司', 
                'app_id' => '8a81c1be831e6288018779ec829e4860', 
                'app_key' => '59de8578498b46989c851521ca4bbe48', 
                'key' => 'SA7nN2XAZd67zTn6JPPACdw6SPr5aYD73M84GD2DDK8bt7mT'
            ],
            self::TITLE_3F0B => [
                'name' => '厦门皓蕴网络科技有限公司', 
                'app_id' => '8a81c1da96cf8e51019780d4d76d06a0', 
                'app_key' => 'ec755443b8e840de8d5d00f29b614e0a', 
                'key' => 'WAWfK7SPQaBJQk6tRJB24KascZ4zzbyWw2nyFtWMHpNxZNb5'
            ],
            self::TITLE_3F0A => [
                'name' => '成都武侯韶禾光医疗美容诊所有限公司', 
                'app_id' => '8a81c1f196cf95bc019780cdcda1064c', 
                'app_key' => '543fcc7ce1f342cdbb6940c2a8dcfd88', 
                'key' => '4c46PFedBBfx8RyFjmS37ahmmsN5zdRyTk74b5RPEhbHwpnt'
            ],
        ];
    }

    public static function getSelectList(): array
    {
        $list = self::getMap();
        $selectList = [];
        foreach ($list as $key => $value) {
            $selectList[] = [
                'id' => $key,
                'name' => $value['name'],
                'app_id' => $value['app_id'],
                'app_key' => $value['app_key'],
                'key' => $value['key'],
            ];
        }
        return $selectList;
    }
}