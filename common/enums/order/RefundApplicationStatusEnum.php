<?php

namespace common\enums\order;

use common\enums\BaseProcessStatusEnum;

/**
 * 退款申请状态枚举
 * 
 * Class RefundApplicationStatusEnum
 */
class RefundApplicationStatusEnum extends BaseProcessStatusEnum
{
    public const DRAFT = 0;

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return parent::getMap() + [
            self::DRAFT => '草稿',
        ];
    }

    /**
     * 获取状态颜色
     * @param int $status
     * @return string
     */
    public static function getStatusColor($status)
    {
        $colors = [
            self::DRAFT => 'blue',
            self::IN_REVIEW => 'green', 
            self::NOT_PASS => 'red',
            self::CANCEL => 'orange',
            self::COMPLETE => 'gray',
        ];
        
        return $colors[$status] ?? 'default';
    }
}