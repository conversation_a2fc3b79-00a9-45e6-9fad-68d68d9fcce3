<?php

namespace common\enums;

/**
 * 用户角色
 *
 * Class MemberTypeEnum
 * @package common\enums
 *
 */
class MemberTypeEnum extends BaseEnum
{
    const GENERAL_ADMIN = 1;
    const SUPER_ADMIN = 10;

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::GENERAL_ADMIN => '普通管理员',
            self::SUPER_ADMIN => '超级管理员',
        ];
    }
}