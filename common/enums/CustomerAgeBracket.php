<?php

namespace common\enums;

/**
 * 客户年龄段枚举
 *
 * Class CustomerAgeBracket
 * @package common\enums
 */
class CustomerAgeBracket extends BaseEnum
{
    const OTHER = 0;           // 其他 
    const UNDER_18 = 1;        // 18岁以下
    const AGE_18_19 = 2;       // 18-19岁
    const AGE_20_23 = 3;       // 20-23岁
    const AGE_24_30 = 4;       // 24-30岁
    const AGE_31_35 = 5;       // 31-35岁
    const AGE_36_40 = 6;       // 36-40岁
    const AGE_41_45 = 7;       // 41-45岁
    const AGE_46_50 = 8;       // 46-50岁
    const AGE_51_55 = 9;       // 51-55岁
    const AGE_56_59 = 10;      // 56-59岁
    const OVER_60 = 11;        // 60岁以上

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::UNDER_18 => '18岁以下',
            self::AGE_18_19 => '18-19岁',
            self::AGE_20_23 => '20-23岁',
            self::AGE_24_30 => '24-30岁',
            self::AGE_31_35 => '31-35岁',
            self::AGE_36_40 => '36-40岁',
            self::AGE_41_45 => '41-45岁',
            self::AGE_46_50 => '46-50岁',
            self::AGE_51_55 => '51-55岁',
            self::AGE_56_59 => '56-59岁',
            self::OVER_60 => '60岁以上',
            self::OTHER => '其他',
        ];
    }
}
