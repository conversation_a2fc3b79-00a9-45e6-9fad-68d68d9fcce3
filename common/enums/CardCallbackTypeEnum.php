<?php

namespace common\enums;

/**
 * 飞书卡片回调类型枚举
 *
 * Class CardCallbackTypeEnum
 * @package common\enums
 */
class CardCallbackTypeEnum extends BaseEnum
{
    const ORDER_REPORT = 'order_report'; // 下订上报卡片

    /**
     * 获取卡片类型映射
     * 
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::ORDER_REPORT => '下订上报卡片',
        ];
    }

    /**
     * 根据卡片类型获取处理类名
     * 
     * @param string $cardType 卡片类型
     * @return string
     */
    public static function getClassName($cardType)
    {
        $list = [
            self::ORDER_REPORT => 'OrderReportCardCallback',
        ];

        return $list[$cardType] ?? '';
    }
} 