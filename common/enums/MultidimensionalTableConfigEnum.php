<?php

namespace common\enums;

/**
 * 飞书多维表格-配置类型
 *
 * Class MultidimensionalTableConfigEnum
 * @package common\enums
 */
class MultidimensionalTableConfigEnum extends BaseEnum
{
    const ArriveStore = 1;
    const Procurement = 2;
    const StoreSchedule = 3;

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::ArriveStore => '已到店多维表格', // 已到店多维表格
            self::Procurement => '采购收发货', //门店收货多维表格
            self::StoreSchedule => '门店排课情况表', //门店排课情况表
        ];
    }

    public static function getClassName($enum)
    {
        $list = [
            self::ArriveStore => 'AdvanceOrder', // 已到店多维表格
            self::Procurement => 'ProcurementReceive', // 门店收货多维表格
            self::StoreSchedule => 'StoreSchedule', // 门店排课情况表
        ];

        return $list[$enum] ?? '';
    }
}
