<?php

namespace common\enums;

/**
 * 性别枚举
 *
 * Class GenderEnum
 * @package common\enums
 *
 */
class GenderEnum extends BaseEnum
{
    const OTHER = 0;
    const MAN = 1;
    const WOMAN = 2;

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::MAN => '男',
            self::WOMAN => '女',
            self::OTHER => '其他',
        ];
    }
}