<?php

namespace common\components;

use common\helpers\Tool;
use Yii;

/**
 * Class Ip
 * @package common\components
 */
class Ip
{
    public static function getIp138Status()
    {
        $token = Yii::$app->params['ip138']['token'];
        $url = "https://api.ip138.com/status/?token={$token}";
        $res = Tool::curlRequest($url, [], false);
        return $res;
    }

    /**
     * 获取ip138的ip信息
     * @param string $ip
     * @return array
     */
    public static function getIpInfoByIp138($ip)
    {
        $token = Yii::$app->params['ip138']['token'];
        $url = "https://api.ip138.com/ipdata/?ip={$ip}&datatype=jsonp&token={$token}";
        $res = Tool::curlRequest($url);
        
        return $res;

        //失败
        $res = [
            'ret' => 'err',
            'msg' => '无效token',
        ];

        //成功返回
        $res = [
            'ret' => 'ok',
            'ip' => '**************',
            'data' => [
                '中国',
                '陕西',
                '安康',
                '汉滨',
                '电信',
                '725000',
                '0915',
                '城域网',
            ],
        ];

        return $res;
    }

    public static function getIpInfoByBaidu($ip)
    {
        $url = "https://api.ip138.com/ipdata/?ip={$ip}&datatype=jsonp&token={self::TOKEN}";
        $res = Tool::curlRequest($url);
        return $res;
    }
}
