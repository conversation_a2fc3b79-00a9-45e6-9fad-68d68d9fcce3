<?php

/**
 * 推广各平台-上报事件
 */

namespace common\components;

use common\enums\ApiLogEnum;
use common\enums\HeadLinesReportTypeEnum;
use common\enums\PlatformEnum;
use common\enums\reportEnum;
use common\enums\ReportEventEnum;
use common\helpers\DateHelper;
use common\helpers\Tool;
use common\models\backendapi\PromoteChannel;
use common\models\common\ApiLog;
use common\models\common\LandPageReport;
use common\models\promote\DataSource;
use common\models\wxcom\Com;
use common\models\wxcom\CusCustomerUser;
use Yii;
use yii\db\Exception;

class PromoteReport
{
    // 上报事件
    const WECHAT_ACTION_TYPE_SCANCODE = 'SCANCODE';
    const WECHAT_ACTION_TYPE_CONFIRM_EFFECTIVE_LEADS = 'CONFIRM_EFFECTIVE_LEADS';
    const WECHAT_ACTION_TYPE_VISIT_STORE = 'VISIT_STORE';
    const WECHAT_ACTION_TYPE_COMPLETE_ORDER = 'COMPLETE_ORDER';

    // 平台账号消息
    const PLATFORM_ACCOUNT_INFO_WECHAT = [
        'accessToken' => 'abde16bb2a19773bdb4bf1d36c375e0c',
        'accountId' => '********',
        'actionSetId' => '**********',
    ];

    const PLATFORM_ACCOUNT_INFO_WECHAT_ADQ = [
        'accessToken' => 'abde16bb2a19773bdb4bf1d36c375e0c',
        'accountId' => '********',
        'actionSetId' => '**********',
    ];

    private $platform = '';
    private $report_event = '';
    private $report_key = '';
    private $cl = '';
    private $event_name = '';
    private $params = '';
    private $report_platform = '';
    public $callback = '';

    /**
     * AdReportService constructor.
     *
     * @param string $cl 渠道参数
     * @param string $event_name 事件类型
     * @param string $params 上报参数
     */
    public function __construct($cl, $event_name, $params, $report_platform)
    {
        $this->cl = $cl;
        $this->event_name = $event_name;
        $this->params = json_decode($params, true);
        $this->report_platform = $report_platform;
    }

    /**
     * 检测上报事件
     *
     * @return bool|mixed
     * @throws Exception
     */
    public function checkReport()
    {
        $model = LandPageReport::find()->select('land_id,report_array,report_platform')->where(['params' => $this->cl])->one();
        $report_array = json_decode($model->report_array, true);
        if (empty($report_array)) return true;
        $this->platform = $model->report_platform;

        foreach ($report_array as $k => $v) {
            //新1-意向用户（微信客服专用），上报回传
            if ($v['report_event'] == HeadLinesReportTypeEnum::WECHATADDWXCOM) {
                $this->params['report_event'] = $v['report_event'];
                return $this->reportThirdPlatform($this->params);
            } else {
                if ($v['report_condition'] == $this->event_name) {
                    $this->report_event = $v['report_event'];
                    $this->report_key = $this->event_name . '-' . $this->cl;
                    $this->params['report_event'] = 'form';
                    return $this->reportThirdPlatform($this->params);
                }
            }
        }

        return true;
    }

    /**
     * 上报第三方平台
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    public function reportThirdPlatform($params)
    {
        if (!in_array($this->platform, [reportEnum::TIKTOL, reportEnum::QUICKLY, reportEnum::PANGOLIN])) return false;
        if ($this->platform == reportEnum::TIKTOL || $this->platform == reportEnum::PANGOLIN) { //新1，新25上报事件
            list($res, $code, $trueCodeNum, $type, $callback_pack) = $this->headlinesReport($params);
        } else { //快手上报事件
            list($res, $code, $trueCodeNum, $type, $callback_pack) = $this->quickly($params);
            $this->quicklyOne($params);
        }

        if ($code != $trueCodeNum) {
            $this->reportNotice('统计上报平台时报错', '上报平台：' . ApiLogEnum::getValue($type), [
                '落地页cl' => $this->cl,
                '平台回调信息' => $res,
                '参数' => $params,
            ]);
            throw new Exception('统计上报平台时报错，cl：' . $this->cl . '上报回掉错误' . json_encode($res, JSON_UNESCAPED_UNICODE));
        } else {
            $LogData = [
                'type' => $type,
                'code' => '200',
                'content' => json_encode($res, JSON_UNESCAPED_UNICODE),
                'desc' => '统计上报平台成功cl:' . $this->cl . ' ,report_key:' . $this->report_key . ',加粉企微：' . $this->report_platform,
                'callback_pack' => json_encode($callback_pack, JSON_UNESCAPED_UNICODE),
                'created_at' => time()
            ];
            Yii::$app->db->createCommand()->insert(ApiLog::tableName(), $LogData)->execute();
        }
        $this->reportNotice('上报成功通知', '上报平台：' . ApiLogEnum::getValue($type), [
            '加粉企微' => $this->report_platform,
            '落地页cl' => $this->cl,
            '上报事件' => ReportEventEnum::getValue($this->event_name),
        ]);
        return $res;
    }

    /**
     * 头条上报-新1、新25
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function headlinesReport($params)
    {
        $data = [
            'event_type' => $params['report_event'],
            'context' => [
                'ad' => [
                    'callback' => $params['clickid']
                ]
            ],
            'timestamp' => time()
        ];
        $url = 'https://analytics.oceanengine.com/api/v2/conversion';
        $res = Tool::curlRequest($url, $data, true);
        $code = $res['code'];
        $trueCodeNum = 0; //成功返回时的code
        $this->callback = $params['clickid'];

        return [$res, $code, $trueCodeNum, ApiLogEnum::landReportH, $data];
    }

    /**
     * 下定上报-新25
     *
     * @param $callback
     * @return array
     * @throws \Exception
     */
    public static function orderReport($callback)
    {
        return static::newReport($callback, 196);
    }

    /**
     * 删粉流失上报-新1新25
     *
     * @param $callback
     * @return array
     * @throws \Exception
     */
    public static function cusDelReport($callback)
    {
        return static::newReport($callback, 474);
    }

    /**
     * 新1新25上报
     *
     * @param $callback
     * @return array
     * @throws \Exception
     */
    public static function newReport($callback, $type)
    {
        $url = "https://ad.oceanengine.com/track/activate/?callback={$callback}&event_type={$type}&conv_time=" . time();
        return Tool::curlRequest($url);
    }

    /**
     * 快手上报-新10
     *
     * @param $params
     * @param bool $is_bis
     * @param int $report_event
     * @return array
     * @throws \Exception
     */
    public function quickly($params, $is_bis = false, $report_event = 9)
    {
        if ($is_bis) $this->report_event = $report_event;

        $data = ['actionName' => 'EXTERNAL_ACTION906'];
        // 转换为 JSON 字符串  
        $jsonString = json_encode($data);
        // 对整个 JSON 字符串进行 URL 编码  
        $event_props = rawurlencode($jsonString);

        $url = "http://ad.partner.gifshow.com/track/activate?event_type=" . $this->report_event . "&event_time=" . $params['event_time'] . '&event_props=' . $event_props . "&callback=" . $params['callback'];
        $res = Tool::curlRequest($url);
        $code = $res['result'];
        $trueCodeNum = 1; //成功返回时的code
        $this->callback = $params['callback'];

        return [$res, $code, $trueCodeNum, ApiLogEnum::landReportQ, $url];
    }

    /**
     * 快手上报-新10 上报  118
     */
    public function quicklyOne($params)
    {
        $report_event = 118;
        $url = "http://ad.partner.gifshow.com/track/activate?event_type=" . $report_event . "&event_time=" . $params['event_time'] . "&callback=" . $params['callback'];
        $res = Tool::curlRequest($url);
        $code = $res['result'];
        $trueCodeNum = 1; //成功返回时的code
        $this->callback = $params['callback'];

        return [$res, $code, $trueCodeNum, ApiLogEnum::landReportQ, $url];
    }

    /**
     * 新16上报
     *
     * @param $unionid
     * @param $comCode
     * @return mixed
     * @throws \Exception
     */
    public function wechat($unionid, $comCode, $wx_traceid = '')
    {
        $wechat_app_id = $this->getWxcomDeveloperId($comCode, $unionid);
        $res = $this->wechatOrderReport($unionid, $wechat_app_id, self::WECHAT_ACTION_TYPE_SCANCODE, self::PLATFORM_ACCOUNT_INFO_WECHAT, $wx_traceid);
        if ($res['code'] != 0) {
            $this->reportNotice('统计上报平台时报错', '上报平台：新16', [
                '加粉企微' => $this->report_platform,
                '平台回调信息' => $res,
            ]);
            throw new Exception('统计上报平台时报错，cl：' . $this->cl . '上报回掉错误' . json_encode($res, JSON_UNESCAPED_UNICODE));
        }

        $this->reportNotice('上报成功通知', '上报平台：新16', [
            '加粉企微' => $this->report_platform,
            '上报事件' => '企微加粉',
        ]);

        $apiLog = new ApiLog();
        $apiLog->type = '新16上报成功';
        $apiLog->code = '200';
        $apiLog->desc = $unionid;
        $apiLog->callback_pack = json_encode($res, JSON_UNESCAPED_UNICODE);
        $apiLog->save();

        return $res;
    }

    /**
     * 新2上报(ADQ)
     *
     * @param $unionid
     * @param $comCode
     * @return mixed
     * @throws \Exception
     */
    public function wechat_adq($unionid, $comCode, $wx_traceid = '')
    {
        $wechat_app_id = $this->getWxcomDeveloperId($comCode, $unionid);
        $res = $this->wechatOrderReport($unionid, $wechat_app_id, self::WECHAT_ACTION_TYPE_SCANCODE, self::PLATFORM_ACCOUNT_INFO_WECHAT_ADQ, $wx_traceid);
        if ($res['code'] != 0) {
            $this->reportNotice('统计上报平台时报错', '上报平台：新2', [
                '加粉企微' => $this->report_platform,
                '平台回调信息' => $res,
            ]);
            throw new Exception('统计上报平台时报错，cl：' . $this->cl . '上报回掉错误' . json_encode($res, JSON_UNESCAPED_UNICODE));
        }

        $this->reportNotice('上报成功通知', '上报平台：新2', [
            '加粉企微' => $this->report_platform,
            '上报事件' => '企微加粉',
        ]);

        $apiLog = new ApiLog();
        $apiLog->type = '新2上报成功';
        $apiLog->code = '200';
        $apiLog->desc = $unionid;
        $apiLog->callback_pack = json_encode($res, JSON_UNESCAPED_UNICODE);
        $apiLog->save();

        return true;
    }

    /**
     * 新16,到店PKAM上报
     *
     * @param $unionid
     * @param $comCode
     * @param $order_no
     * @return mixed
     * @throws \Exception
     */
    public function wechatPKAM($unionid, $comCode, $order_no, $wx_traceid)
    {
        $wechat_app_id = $this->getWxcomDeveloperId($comCode, $unionid, $order_no, 'PKAM上报');
        $res = $this->wechatOrderReport($unionid, $wechat_app_id, self::WECHAT_ACTION_TYPE_VISIT_STORE, self::PLATFORM_ACCOUNT_INFO_WECHAT, $wx_traceid);
        if ($res['code'] != 0) {
            $this->reportNotice('统计上报平台时报错', 'PKAM上报平台时报错', [
                'unionid' => $unionid,
                '订单号' => $order_no,
                '平台回调信息' => $res,
            ]);

            $apiLog = new ApiLog();
            $apiLog->type = 'PKAM';
            $apiLog->code = '422';
            $apiLog->content = $order_no;
            $apiLog->desc = $unionid;
            $apiLog->callback_pack = json_encode($res, JSON_UNESCAPED_UNICODE);
            $apiLog->save();

            return false;
        }

        $this->reportNotice('上报成功通知', 'PKAM上报成功', [
            'unionId' => $unionid,
            '订单号' => $order_no,
        ]);

        $apiLog = new ApiLog();
        $apiLog->type = 'PKAM';
        $apiLog->code = '200';
        $apiLog->content = $order_no;
        $apiLog->desc = $unionid;
        $apiLog->callback_pack = json_encode($res, JSON_UNESCAPED_UNICODE);
        $apiLog->save();

        return true;
    }

    /**
     * 新16,下订PKAM上报
     *
     * @param $unionid
     * @param $wechat_app_id
     * @param $order_no
     * @return mixed
     * @throws \Exception
     */
    public function underOrderPKAM($unionid, $wechat_app_id, $order_no, $wx_traceid)
    {
        if (empty($wechat_app_id)) {
            $this->reportNotice('企微的开发者ID不存在', '新16下订-PKAM上报', [
                'unionId' => $unionid,
                'orderNo' => $order_no,
            ]);
            return ['code' => 422, 'message' => '企微的开发者ID不存在', 'unionid' => $unionid, 'order_no' => $order_no];
        }
        $res = $this->wechatOrderReport($unionid, $wechat_app_id, self::WECHAT_ACTION_TYPE_CONFIRM_EFFECTIVE_LEADS, self::PLATFORM_ACCOUNT_INFO_WECHAT, $wx_traceid);
        if ($res['code'] != 0) {
            $this->reportNotice('新16下订PKAM上报平台时报错', '', [
                'unionid' => $unionid,
                '订单号' => $order_no,
                '平台回调信息' => $res,
            ]);
        }

        return $res;
    }

    /**
     * adq,下订上报
     *
     * @param $unionid
     * @param $wechat_app_id
     * @param $order_no
     * @return mixed
     * @throws \Exception
     */
    public function underOrderADQ($unionid, $wechat_app_id, $order_no)
    {
        if (empty($wechat_app_id)) {
            $this->reportNotice('企微的开发者ID不存在', 'adq下订上报', [
                'unionId' => $unionid,
                'orderNo' => $order_no,
            ]);
            return ['code' => 422, 'message' => '企微的开发者ID不存在', 'unionid' => $unionid, 'order_no' => $order_no];
        }

        $res = $this->wechatOrderReport($unionid, $wechat_app_id, self::WECHAT_ACTION_TYPE_COMPLETE_ORDER, self::PLATFORM_ACCOUNT_INFO_WECHAT_ADQ);
        if ($res['code'] != 0) {
            $this->reportNotice('adq下订上报平台时报错', '', [
                'unionid：' => $unionid,
                '订单号：' => $order_no,
                '平台回调信息：' => $res,
            ]);
        }

        return $res;
    }

    /**
     * 腾讯上报--改成数据源上报
     */
    public function wechatReport($customer_user_id, $actionType)
    {
        $customerUser = CusCustomerUser::find()->where(['id' => $customer_user_id])->limit(1)->one();
        if (!$customerUser) {
            return true;
        }
        
        if ($customerUser->add_way == 202) {
            return true;
        }

        $platform = PromoteChannel::find()->select('platform')->where(['id' => $customerUser->channel_id])->cache(300)->scalar();
        if ($platform != PlatformEnum::WECHAT) {
            return true;
        }

        $wecomCustomer = $customerUser->customer;
        if (!$wecomCustomer) {
            throw new Exception('该客户的cus_id数据获取失败');
        }
        $wechat_app_id = Com::find()->select('developer_id')->where(['id' => $customerUser->com_id])->cache(60 * 5)->scalar();
        if (empty($wechat_app_id)) {
            throw new Exception('上报失败，该成员所以企微公司表数据不存在');
        }

        $dataSource = $this->getDataSource($customerUser);
        $res = $this->wechatOrderReport($wecomCustomer->unionid, $wechat_app_id, $actionType, $dataSource);
        $infoData = [
            'customer_user_id' => $customerUser->id,
            'sourceData' => $dataSource,
            'res' => $res
        ];
        Yii::info($infoData, 'data_source_wechat_report');
        // 保存日志
        $apiLog = new ApiLog();
        $apiLog->type = 'data_source_wechat_report';
        $apiLog->content = json_encode($infoData, JSON_UNESCAPED_UNICODE);
        $apiLog->desc = $wecomCustomer->unionid;
        if (!$apiLog->save()) {
            Yii::error('腾讯上报成功：新增数据失败' . current($apiLog->getFirstErrors()), 'data_source_wechat_report');
        }
    }

    public function getDataSource($customerUser)
    {
        $qrcode = $customerUser->qrcode;
        if ($qrcode && $qrcode->data_source_id) {
            $info = DataSource::find()->select('account_id as accountId,data_source_id as actionSetId,secret_key as accessToken')->where(['id' => $qrcode->data_source_id])->asArray()->one();
            if ($info) return $info;
        }

        $info = self::PLATFORM_ACCOUNT_INFO_WECHAT;
        return $info;
    }

    /**
     * 腾讯下订上报
     *
     * @param string $unionid
     * @param string $wechatAppId
     * @param string $actionType
     * @param string $accessToken
     * @param string $accountId 账户ID
     * @param string $actionSetId 数据源ID
     * @return mixed
     */
    public function wechatOrderReport(string $unionid, string $wechatAppId, string $actionType, $accountInfo = self::PLATFORM_ACCOUNT_INFO_WECHAT, $wx_traceid = '')
    {
        $url = 'https://api.e.qq.com/v1.1/user_actions/add';

        $common_parameters = [
            'access_token' => $accountInfo['accessToken'],
            'timestamp' => time(),
            'nonce' => md5(uniqid('', true))
        ];
        $url .= '?' . http_build_query($common_parameters);

        $actions = [
            'user_id' => [
                'wechat_unionid' => $unionid,
                'wechat_app_id' => $wechatAppId ?: ''
            ],
            'action_time' => time(),
            'action_type' => $actionType,
        ];

        if ($wx_traceid) {
            $actions['trace'] = [
                'click_id' => $wx_traceid
            ];
        }

        $postData = [
            'account_id' => $accountInfo['accountId'], //账户id
            'user_action_set_id' => $accountInfo['actionSetId'], //数据源ID
            'actions' => [
                $actions
            ]
        ];

        $res = Tool::curlRequest($url, $postData, true);

        return $res;
    }

    /**
     * 获取企微开发者ID
     *
     * @param string $comCode
     * @param string $unionId
     * @return integer
     */
    protected function getWxcomDeveloperId($comCode, $unionId, $orderNo = null, $remark = '')
    {
        $developerId = Com::find()->select('developer_id')->where(['code' => $comCode])->cache(60 * 5)->scalar();
        if (empty($developerId)) {
            $this->reportNotice('企微的开发者ID不存在', $remark, [
                'unionId' => $unionId,
                'comCode' => $comCode,
                'orderNo' => $orderNo,
            ]);
        }
        return $developerId ?: 0;
    }

    /**
     * 推广钉钉信息
     */
    protected function reportNotice(string $title, string $subTitle = '', array $params = [])
    {
        return true;
        $message = '';
        if ($subTitle) {
            $message .= $subTitle . "\n\n";
        }

        $params['时间'] = DateHelper::toDate(time());
        foreach ($params as $key => $value) {
            if (!$value) {
                continue;
            }
            if (!is_string($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }
            $message .= "> {$key}： {$value} \n\n";
        }

        Yii::$app->notice->AdReport($title, '', $message);
    }
}
