<?php

/**
 * 投诉建议-客诉工单多维表格
 */

namespace common\components\feishu\multidimensionalTable;

use auth\services\feishu\ToolService;
use common\models\backend\Member;
use common\models\complaint\Complaint as ComplaintComplaint;
use yii\db\Exception;
use Yii;

class Complaint extends Base
{
    /**
     * 新增数据
     * 
     * 数据格式
     */
    public function createRecords(ComplaintComplaint $complaint)
    {
        try {
            $addData = [
                '工单ID' => (string)$complaint->id,
                '投诉时间' => $complaint->created_at * 1000,
                '客户' => $complaint->name,
                '联系方式' => $complaint->mobile,
                '投诉账号' => $complaint->respondent->name,
                '投诉原因' => $complaint->reason->content,
                '投诉内容' => $complaint->content,
            ];

            $storeTokenInfo = [
                'table_file_token' => 'Ocw6ba6hna4edQs4FbAc5dQCnfh',
                'table_id' => 'tblTv2JxQrk7TVW9'
            ];

            $parameters = '';
            if ($complaint->respondent_user_id) {
                $union_ids = [
                    ['id' => $this->getUnionId($complaint->respondent_user_id)],
                ];
                $addData['对应人员'] = $union_ids;
                $parameters = '?user_id_type=union_id';
            }

            $imageUrl = $complaint->imgText;
            if ($imageUrl) {
                $imgDir = Yii::getAlias('@mobileapi/runtime/img');
                $uploadRes = ToolService::uploadImageUrlToMultidimensionalTable($imageUrl, $storeTokenInfo['table_file_token'], $imgDir);
                $fileData = ToolService::realToMultidimensionalTableImage($uploadRes);
                $addData['图片举证'] = $fileData;
            }

            $res = $this->feishuClass->createMoreTableData($storeTokenInfo['table_file_token'], $storeTokenInfo['table_id'], $addData, $parameters);
            unset($this->feishuClass);

            if ($res['code'] != 0) {
                throw new Exception('”投诉建议“新增到多维表格数据失败，原因：' . $res['msg'] . ',请联系信息部门');
            }
        } catch (Exception $e) {
            $error = 'ID：' . $complaint->id . '，' . $e->getMessage();
            Yii::$app->feishuNotice->text($error);
            Yii::info($error, 'Complaint');
        }

        return true;
    }

    public function getUnionId($user_id)
    {
        $info = Member::find()->select('username,current_feishu_app_id,feishu_unionid')->where(['id' => $user_id])->one();
        if ($info->current_feishu_app_id == 1) {
            return $info->feishu_unionid;
        }

        $key = 'service_store_chat_member';
        $chatMember = Yii::$app->cache->get($key);
        if (empty($chatMember)) {
            //后续优化
            //oc_bd2fc434ea037f42db3b14f9f26e7a24 这个是“客服-门店信息对接群”的群id
            $chatMember = $this->feishuClass->getChatListOne('oc_bd2fc434ea037f42db3b14f9f26e7a24', 'union_id');
            if ($chatMember['code'] != 0) {
                throw new Exception('获取飞书群成员失败，原因：' . $chatMember['msg'] . ',请联系信息部门');
            }

            Yii::$app->cache->set($key, $chatMember, 86400);
        }

        foreach ($chatMember as $item) {
            if ($item['name'] == $info->username) {
                return $item['member_id'];
            }
        }

        throw new Exception('没有获取到该成员的union_id');
    }
}
