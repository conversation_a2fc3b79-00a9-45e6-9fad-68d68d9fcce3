<?php

namespace common\components\feishu\multidimensionalTable;

use common\helpers\DateHelper;
use auth\services\data\TeacherAnalysisService;
use common\helpers\BcHelper;
use common\models\backend\Member;
use Yii;

class TeacherMonthAmount extends Base
{
    protected $tableFileToken = 'Z6lLb7nE2aI3qssP7RUcPVO1nuc';
    protected $tableId = 'tblpj3lmodHO9hDE';

    /**
     * 同步老师月度业绩
     */
    public function syncTeacherMonthAmount()
    {
        $data = $this->feishuClass->getMoreTableDataAll($this->tableFileToken, $this->tableId);
        
        // 如果有数据，先删除现有数据
        if (!empty($data)) {
            $record_ids = array_column($data, 'record_id');
            $res = $this->feishuClass->multipleDeleteMoreTableData($this->tableFileToken, $this->tableId, $record_ids);
            if ($res['code'] != 0) {
                $error = [
                    'error_msg' => $res['msg']
                ];
                Yii::error('删除老师月度业绩多维表数据失败：' . json_encode($error), 'TeacherMonthAmountSync');
                return false;
            }
        }

        [$startTime, $endTime] = DateHelper::getPreviousMonthStartToYesterdayRange();
        try {
            $data = $this->getTeacherMonthAmountData(['start_time' => $startTime, 'end_time' => $endTime]);
        } catch (\Exception $e) {
            Yii::error('获取老师月度业绩数据失败：' . $e->getMessage(), 'TeacherMonthAmountSync');
            return false;
        }
        if (empty($data)) {
            return false;
        }

        foreach ($data as $item) {
            $item = $this->formatTeacherMonthAmountData($item);
            $res = $this->feishuClass->createMoreTableData($this->tableFileToken, $this->tableId, $item, '?user_id_type=union_id');
            if ($res['code'] != 0) {
                $error = [
                    'data' => $item,
                    'error_msg' => $res['msg']
                ];
                Yii::error('创建老师月度业绩多维表数据失败：' . json_encode($error), 'TeacherMonthAmountSync');
                return false;
            }
        }

        return true;
    }

    /**
     * 获取老师月度业绩数据
     * @param array $params
     *
     * @return array
     */
    public function getTeacherMonthAmountData($params)
    {
        return TeacherAnalysisService::dataQuery($params, true)->all();
    }

    /**
     * 格式化老师月度业绩数据
     * @param array $data
     *
     * @return array
     */
    protected function formatTeacherMonthAmountData($data)
    {
        $childCusCount = $data['new_cus_count'] + $data['loss_num'];
        $childCusCount = $childCusCount > 0 ? $childCusCount : 1;
        $totalPrice = BcHelper::div($data['total_amount'], $childCusCount);

        $result = [
            '日期' => strtotime('-1 day') * 1000,
            '系统门店名称' => $data['dept_name'],
            '总业绩' => floatval($data['total_amount']),
            '新客人数' => intval($data['new_cus_count']),
            '新客业绩' => floatval($data['new_amount']),
            '新客客单价' => floatval($data['new_price']),
            '老客人数' => intval($data['old_cus_count']),
            '老客业绩' => floatval($data['old_amount']),
            '老客客单价' => floatval($data['old_price']),
            '流失数' => intval($data['loss_num']),
            '客单价' => floatval($totalPrice),
        ];
        
        $unionId = Member::getFeishuUnionId($data['teacher_id']);
        if ($unionId) {
            $union_ids = [
                ['id' => $unionId],
            ];
            $result['老师姓名'] = $union_ids;
        }

        return $result;
    }
}