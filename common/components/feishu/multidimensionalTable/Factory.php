<?php

namespace common\components\feishu\multidimensionalTable;

use common\enums\MultidimensionalTableConfigEnum;
use common\models\feishu\MultidimensionalTableConfig;
use Exception;

/**
 * 审批处理服务工厂
 */
class Factory
{
    /**
     * 获取一个审批服务
     *
     * @param array $message
     * @return Process
     */
    public static function getServiceByMessage($message)
    {
        $className = static::getClassName($message);
        $multidimensionalTableClassName = "\\common\\components\\feishu\\multidimensionalTable\\" . $className;
        if (!class_exists($multidimensionalTableClassName)) {
            throw new Exception("{$message['processCode']} 审批对应的类 [{$multidimensionalTableClassName}] 不存在");
        }
        return new $multidimensionalTableClassName($message);
    }

    public static function getClassName($message)
    {
        $file_token = $message['event']['file_token'];
        $type = MultidimensionalTableConfig::find()->select('type')->where(['table_file_token' => $file_token])->scalar();
        return MultidimensionalTableConfigEnum::getClassName($type);
    }
}
