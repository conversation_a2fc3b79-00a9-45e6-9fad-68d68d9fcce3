<?php

namespace common\components\feishu\multidimensionalTable;

use common\models\backend\Store;
use common\models\feishu\StoreSchedule as FeishuStoreSchedule;
use yii\db\Exception;
use Yii;

class StoreSchedule extends Base
{
    protected $tableNodeToken = 'WMwHwQixSixhatk031pcZnHcnXg';
    protected $tableId = 'tbl3Eloa5nqfTBMO';

    /**
     * 拉取门店排客情况表数据
     * @return bool
     */
    public function pullStoreScheduleData()
    {
        $nodeInfo = $this->feishuClass->getNodeInfo($this->tableNodeToken, 'wiki');
        if ($nodeInfo['code'] != 0 || empty($nodeInfo['data'])) {
            Yii::error('获取知识库节点信息失败：' . $nodeInfo['msg'], 'FeishuStoreSchedule');
            return false;
        }
        
        $data = $this->feishuClass->getMoreTableDataAll($nodeInfo['data']['node']['obj_token'], $this->tableId);
        if (empty($data)) return false;

        $storeNameToId = Store::find()
            ->select('id')
            ->indexBy('store_name')
            ->column();

        foreach ($data as $item) {
            $fields = $item['fields'];
            $sn = $fields['编号'];
            if (empty($sn)) {
                Yii::error('获取数据失败，编号为空', 'FeishuStoreSchedule');
                continue;
            }

            $result = $this->processStoreScheduleRecord($sn, $fields, $storeNameToId);
            if (!$result) Yii::error('处理门店排客情况表数据失败，编号：' . $sn, 'FeishuStoreSchedule');
        }

        return true;
    }

    /**
     * 处理门店排客情况表数据
     * @return bool
     */
    public function run()
    {
        $app_token = $this->message['event']['file_token'];
        $table_id = $this->message['event']['table_id'];
        $record_id = $this->message['event']['action_list'][0]['record_id'];

        $action = $this->message['event']['action_list'][0]['action'];
        if ($action == 'record_deleted') return true;

        $info = $this->feishuClass->getMoreTableSingleData($app_token, $table_id, $record_id);
        if ($info['code'] != 0) throw new Exception('获取数据失败，原因：' . $info['msg']);

        $fields = $info['data']['record']['fields'];
        $sn = $fields['编号'];
        if (empty($sn)) throw new Exception('获取数据失败，编号为空');

        $result = $this->processStoreScheduleRecord($sn, $fields);
        if (!$result) throw new Exception('处理门店排客情况表数据失败，编号：' . $sn);

        return true;
    }

    /**
     * 处理门店排客情况表记录
     * @param string $sn 编号
     * @param array $fields 字段数据
     * @param array|null $storeNameToId 门店名称到ID的映射
     * @return bool
     */
    private function processStoreScheduleRecord($sn, $fields, $storeNameToId = null)
    {
        // 查找或创建记录
        $storeSchedule = FeishuStoreSchedule::findOne(['sn' => $sn]);
        if (empty($storeSchedule)) {
            $storeSchedule = new FeishuStoreSchedule();
            $storeSchedule->sn = $sn;
            
            // 设置门店ID
            $storeName = isset($fields['系统名称'][0]['text']) ? $fields['系统名称'][0]['text'] : '';
            $storeSchedule->store_id = $this->getStoreId($storeName, $storeNameToId);
            
            // 设置日期
            $storeSchedule->date = isset($fields['日期']) && $fields['日期'] 
                ? date('Ymd', $fields['日期'] / 1000) 
                : '';
        }

        // 处理可变字段
        $storeSchedule->teacher_num = $fields['老师人数'] ?? 0;
        $storeSchedule->customer_service_num = $fields['可接待客户数'] ?? 0;
        $storeSchedule->reschedule_num = $fields['改期人数'] ?? 0;
        $storeSchedule->remark = $fields['特殊情况说明'] ?? '';

        // 保存记录
        if (!$storeSchedule->save()) {
            $errorMsg = '保存门店排客情况表数据失败：' . current($storeSchedule->getFirstErrors());
            Yii::error($errorMsg, 'FeishuStoreSchedule');
            return false;
        }

        return true;
    }

    /**
     * 获取门店ID
     * @param string $storeName 门店名称
     * @param array|null $storeNameToId 门店名称到ID的映射
     * @return int
     */
    private function getStoreId($storeName, $storeNameToId = null)
    {
        if (empty($storeName)) {
            return 0;
        }

        if ($storeNameToId !== null) {
            return $storeNameToId[$storeName] ?? 0;
        }

        $store = Store::findOne(['store_name' => $storeName]);
        return $store ? $store->id : 0;
    }
}