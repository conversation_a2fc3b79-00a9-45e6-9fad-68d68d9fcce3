<?php

namespace common\components\feishu\process;

use common\components\Feishu;
use common\enums\order\RefundApplicationStatusEnum;
use common\models\Config;
use common\models\order\RefundApplication;
use common\models\order\RefundApplicationDetail;
use common\models\backend\order\OrderHeader;
use common\enums\AgentAccountTypeEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\models\OrderPay;
use common\models\CustomerProductRecord;
use common\models\CustomerProduct;
use common\models\data\FinancialData;
use Exception;
use Yii;

/**
 * 退款申请审批流程处理
 */
class RefundApprovalProcess extends ProcessBase
{
    public $refundApplication; // 退款申请数据
    public $instanceDetail; // 审批返回数据

    public function __construct($message)
    {
        parent::__construct($message);

        $this->refundApplication = RefundApplication::find()
            ->where(['process_instance_id' => $this->instance_code])
            ->one();

        if (empty($this->refundApplication)) {
            throw new Exception("找不到审批单号【instance_code:{$this->instance_code}】，对应的退款申请");
        }

        if ($this->refundApplication->status != RefundApplicationStatusEnum::IN_REVIEW) {
            throw new Exception("该退款申请：{$this->refundApplication->application_no}, 已审批通过了");
        }

        $feishu = new Feishu($message['ComCode']);
        $instanceDetail = $feishu->getSingleApproval($this->instance_code);
        if ($instanceDetail['code'] != 0) {
            throw new Exception("退款申请ID:" . $this->refundApplication->id . '获取审批详情失败，失败原因:' . $instanceDetail['msg']);
        }
        $this->instanceDetail = $instanceDetail;
    }

    /**
     * 审批通过处理
     */
    public function onAgree()
    {
        $this->saveData(RefundApplicationStatusEnum::COMPLETE);
    }

    /**
     * 审批拒绝处理
     */
    public function onRefuse()
    {
        $this->saveData(RefundApplicationStatusEnum::NOT_PASS);
    }

    /**
     * 审批取消/删除处理
     */
    public function onTerminate()
    {
        $this->saveData(RefundApplicationStatusEnum::CANCEL);
    }

    /**
     * 保存退款申请状态并处理相关订单
     * @param int $status
     */
    private function saveData($status)
    {
        // 更新退款申请状态
        $this->refundApplication->status = $status;
        $this->refundApplication->process_detail = json_encode(['data' => $this->dealData($this->instanceDetail)]);

        // 更新出纳确认
        $form = json_decode($this->instanceDetail['data']['form'], true);
        $cashierConfirm = '';
        foreach ($form as $item) {
            if ($item['name'] == '出纳确认') {
                $cashierConfirm = $item['value'];
                break;
            }
        }
        $this->refundApplication->cashier_confirm = $cashierConfirm;
        
        if (!$this->refundApplication->save()) {
            throw new Exception('退款申请状态更新失败：' . $this->refundApplication->getFirstErrMsg());
        }

        // 处理相关订单
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $this->processRelatedOrders($status);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 处理相关订单状态
     * @param int $status
     */
    private function processRelatedOrders($status)
    {
        $refundDetails = RefundApplicationDetail::findAll(['application_id' => $this->refundApplication->id]);
        if (empty($refundDetails)) {
            return;
        }

        foreach ($refundDetails as $detail) {
            $order = OrderHeader::findOne($detail->order_id);
            if (empty($order)) {
                continue;
            }

            // 根据不同的审批状态处理订单
            switch ($status) {
                case RefundApplicationStatusEnum::COMPLETE:
                    // 审批通过：更新订单退款金额
                    $order->refund_amount = $detail->refund_amount;
                    $errorMsg = '退款申请关联订单退款金额更新失败：';
                    break;
                    
                case RefundApplicationStatusEnum::NOT_PASS:
                case RefundApplicationStatusEnum::CANCEL:
                    // 审批拒绝或取消：恢复订单状态为已完成
                    $order->order_status = OrderHeaderStatusEnum::STATUS_COMPLETED;
                    $errorMsg = '退款申请关联订单状态更新失败：';
                    break;
                    
                default:
                    continue 2; // 跳过当前订单处理
            }

            if (!$order->save()) {
                throw new Exception($errorMsg . $order->getFirstErrMsg());
            }

            if (in_array($status, [RefundApplicationStatusEnum::NOT_PASS, RefundApplicationStatusEnum::CANCEL])) {
                $this->revertCustomerProduct(explode(',', $detail->product_record_ids));
            }
        }
    }

    /**
     * 还原客户商品记录
     * @param array $recordIds
     * @return void
     */
    public function revertCustomerProduct(array $recordIds)
    {
        $records = CustomerProductRecord::findAll(['id' => $recordIds]);
        if (empty($records)) {
            return;
        }

        foreach ($records as $record) {
            $customerProduct = new CustomerProduct();
            $customerProduct->id = $record->cus_product_id;
            $customerProduct->source_type = $record->source_type;
            $customerProduct->source_id = $record->source_id;
            $customerProduct->cus_id = $record->cus_id;
            $customerProduct->goods_id = $record->goods_id;
            $customerProduct->max_times = $record->max_times;
            $customerProduct->once_operation_amount = $record->once_operation_amount;
            $customerProduct->goods_name = $record->goods_name;
            $customerProduct->expire_at = $record->expire_at;
            $customerProduct->left_num = $record->num;
            $customerProduct->entity_id = $record->entity_id;
            if (!$customerProduct->save()) {
                throw new Exception('客户商品表保存失败');
            }
            $record->delete();
        }
    }

    /**
     * 创建退款审批实例
     * @param array $data
     * @param int $entity_id
     * @return array
     */
    public static function create($data, $entity_id)
    {
        $approvalCode = Config::getByName('refundApprovalProcessCode');
        if (empty($approvalCode)) {
            throw new Exception("请联系管理员配置退款审批单:refundApprovalProcessCode");
        }
        
        $fields = static::approvalFields();
        return static::createApproval($approvalCode, $fields, $data, $entity_id);
    }

    public static function approvalFields()
    {
        $fields = [
            '客人姓名' => 'cus_name',
            '客户手机号' => 'cus_phone',
            '退款门店' => 'store_name',
            '总实际消费金额（元）' => 'total_amount',
            '总应退款金额' => 'total_refund_amount',
            '退款原因' => 'refund_reason',
            '客户收款账户' => 'bank_account_info',
            '服务老师' => 'teacher_name',
            '付款截图及退款协议照片' => 'img',
            '明细' => [
                '订单号' => 'order_no',
                '消费项目' => 'item_name',
                '消费时间' => 'consume_time',
                '实际消费金额（元）' => 'actual_amount',
                '应退款金额' => 'refund_amount',
                '付款商户单号' => 'transaction_id',
            ],
        ];
        return $fields;
    }

    public static function formatBankAccountInfo($type, $info)
    {
        $info = json_decode($info, true);
        return '账户类型：' . AgentAccountTypeEnum::getValue($type) . PHP_EOL . '户名：' . $info['account_name'] . PHP_EOL . '账号：' . $info['account_no'] . PHP_EOL . '银行：' . $info['bank_name'] . PHP_EOL . '银行所在地：' . $info['bank_location'] . PHP_EOL . '支行名称：' . $info['bank_branch'];
    }

    public static function getTransactionIds($orderIds)
    {
        $transactionIdsRaw = OrderPay::find()
            ->select(['transaction_id', 'order_id'])
            ->where(['order_id' => $orderIds])
            ->andWhere(['deleted_at' => 0])
            ->asArray()
            ->all();

        $transactionIds = [];
        foreach ($transactionIdsRaw as $item) {
            $orderId = $item['order_id'];
            if (!isset($transactionIds[$orderId])) {
                $transactionIds[$orderId] = [];
            }
            $transactionIds[$orderId][] = $item['transaction_id'];
        }

        foreach ($transactionIds as $orderId => $transIds) {
            $transactionIds[$orderId] = implode(',', $transIds);
        }

        return $transactionIds;
    }
}