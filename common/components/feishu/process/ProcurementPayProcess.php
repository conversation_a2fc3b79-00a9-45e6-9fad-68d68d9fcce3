<?php

namespace common\components\feishu\process;

use common\components\Feishu;
use common\enums\ProcessStatusEnum;
use common\enums\ProcurementStatusEnum;
use common\models\Config;
use common\models\material\ProcurementPay;
use console\models\ProcurementDetails;
use yii\db\Exception;
use Yii;
use yii\db\Expression;

class ProcurementPayProcess extends ProcessBase
{
    public $procurementPay; //付款单数据
    public $instanceDetail; //审批返回数据

    public function __construct($message)
    {
        parent::__construct($message);

        $this->procurementPay = ProcurementPay::find()->where(['process_instance_id' => $this->instance_code])->one();
        if (empty($this->procurementPay)) {
            throw new Exception("找不到付款审批单号【instance_code:{$this->instance_code}】，对应的采购付款审批单");
        }

        if ($this->procurementPay->status != ProcessStatusEnum::IN_REVIEW) {
            throw new Exception("该采购付款审批单：{$this->procurementPay->list_no},已审批通过了");
        }

        $feishu = new Feishu($message['ComCode']);
        $instanceDetail = $feishu->getSingleApproval($this->instance_code);
        if ($instanceDetail['code'] != 0) {
            throw new Exception("采购付款单id:" . $this->procurementPay->id . '获取审批详情失败，失败原因:' . $instanceDetail['msg']);
        }
        $this->instanceDetail = $instanceDetail;
    }

    /**
     * 同意
     *
     * @return void
     */
    public function onAgree()
    {
        $this->saveData(ProcessStatusEnum::COMPLETE, ProcurementStatusEnum::WAIT_DELIVERY);
    }

    /**
     * 拒绝
     *
     * @return void
     */
    public function onRefuse()
    {
        $this->saveData(ProcessStatusEnum::NOT_PASS, ProcurementStatusEnum::PAY_NOT_PASS);
    }

    /**
     * 取消审批
     *
     * @return void
     */
    public function onTerminate()
    {
        $this->saveData(ProcessStatusEnum::CANCEL, ProcurementStatusEnum::PAY_CANCEL);
    }

    /**
     * 保存付款单数据
     */
    private function saveData($paysStatus, $status)
    {
        $this->procurementPay->scenario = 'approval';
        $this->procurementPay->status = $paysStatus;
        $this->procurementPay->approval_time = $this->operate_time;
        $this->procurementPay->process_detail = json_encode(['data' => $this->dealData($this->instanceDetail)]);
        if (!$this->procurementPay->save()) {
            throw new Exception("采购付款审批单保存失败:采购付款单Id: {$this->procurementPay->id} 失败原因:" . current($this->procurementPay->getFirstErrors()));
        }

        $procurementDeatilsList = ProcurementDetails::findAll(['receipt_id' =>  $this->procurementPay->id, 'status' => ProcurementStatusEnum::PAY_REVIEW]);
        if (empty($procurementDeatilsList)) {
            return true;
        }

        $detailStatus = $status;
        foreach ($procurementDeatilsList as $procurementDeatil) {
            if ($status == ProcurementStatusEnum::WAIT_DELIVERY) {
                if ($procurementDeatil->acceptance == 1) {
                    $detailStatus = ProcurementStatusEnum::COMPLETE;
                } elseif ($procurementDeatil->shipments == 1) {
                    $detailStatus = ProcurementStatusEnum::WAIT_ACCEPTANCE;
                }
            }

            if ($procurementDeatil->shipments == 0) {
                $procurementDeatil->hair_num = $procurementDeatil->procurement_num;
            }

            $procurementDeatil->status = $detailStatus;
            if (!$procurementDeatil->save()) {
                throw new Exception("采购付款审批单完成后修改采购详情时报错:采购跟进表Id: {$procurementDeatil->id} 失败原因：" . current($procurementDeatil->getFirstErrors()));
            }
        }
    }

    /**
     * 创建
     */
    public static function create($data, $entity_id)
    {
        //审批模板
        $approval_code = Config::getByName('procurementPayProcessCode');
        if (empty($approval_code)) {
            throw new Exception("请联系信息部配置：采购付款审批单:procurementPayProcessCode");
        }

        $fields = static::approvalFields();
        return static::createApproval($approval_code, $fields, $data, $entity_id);
    }

    /**
     * 审批字段匹配
     */
    public static function approvalFields()
    {
        $fields = [
            '付款单号' => 'list_no',
            '付款事由' => 'reason',
            '付款金额' => 'amount',
            '付款方式' => 'type',
            '付款日期' => 'date',
            '银行账户' => 'account',
            '关联审批' => 'connect',
            '附件' => 'attachment',
            '备注' => 'remark'
        ];
        return $fields;
    }

    public function demo()
    {
        $data = [
            'feishu_userid' => '92b12ec5',
            'list_no' => '采购*****************',
            'reason' => '事由原因',
            'amount' => '222',
            'type' => '银行卡',
            'date' => '2023-10-21',
            'account' => '张三' . PHP_EOL . '银行卡:111111',
            'connect' => ["49FCDB7B-CFB7-4A0B-9E16-7F469FDC1D5F"],
            'attachment' => []
        ];
        self::create($data, 1);
    }
}
