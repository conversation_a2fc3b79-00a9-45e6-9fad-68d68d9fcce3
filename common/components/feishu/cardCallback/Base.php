<?php

namespace common\components\feishu\cardCallback;

use common\components\Feishu;
use Exception;
use Yii;

/**
 * 卡片回调处理基类
 */
abstract class Base
{
    /**
     * @var array 飞书回调消息
     */
    public $message = [];

    /**
     * @var Feishu 飞书API实例
     */
    public $feishuClass = null;

    /**
     * @var string 企业标识
     */
    public $comCode = '';

    /**
     * 构造函数
     * 
     * @param array $message 飞书回调消息
     */
    public function __construct($message)
    {
        $this->message = $message;
        $this->comCode = $message['ComCode'] ?? 'chz';
        $this->feishuClass = new Feishu($this->comCode);
    }

    /**
     * 处理卡片交互回调 - 子类必须实现
     * 
     * @return array 响应数据
     */
    abstract public function handle();

    /**
     * 获取卡片类型 - 子类必须实现
     * 
     * @return string
     */
    abstract public function getCardType();

    /**
     * 获取交互操作类型
     * 
     * @return string
     */
    public function getActionType()
    {
        return $this->message['event']['action']['value']['button'] ?? '';
    }

    /**
     * 获取操作数据
     * 
     * @return array
     */
    public function getActionValue()
    {
        return $this->message['event']['action']['value'] ?? [];
    }

    /**
     * 获取表单数据
     * 
     * @return array
     */
    public function getFormValue()
    {
        return $this->message['event']['action']['form_value'] ?? [];
    }

    /**
     * 获取用户信息
     * 
     * @return array
     */
    public function getUserInfo()
    {
        return $this->message['event']['operator'] ?? [];
    }

    /**
     * 构建模板卡片
     * 
     * @return array
     */
    protected function buildTemplateCard($templateId, $templateVariable)
    {
        return [
            'type' => 'template',
            'data' => [
                'template_id' => $templateId,
                'template_variable' => $templateVariable
            ]
        ];
    }

    /**
     * 构建飞书卡片回调响应
     * 
     * @param array $toast 提示信息
     * @param array $card 卡片数据
     * @return array
     * @throws Exception
     */
    protected function buildCallbackResponse(array $toast = [], array $card = [])
    {
        if (empty($toast) && empty($card)) {
            throw new Exception('toast 和 card 不能同时为空');
        }

        $response = [];
        if (!empty($toast)) {
            $response['toast'] = $toast;
        }
        if (!empty($card)) {
            $response['card'] = $card;
        }
        return $response;
    }

    /**
     * 记录操作日志
     * 
     * @param string $action 操作类型
     * @param array $data 操作数据
     * @param bool $success 是否成功
     * @param string $message 日志消息
     */
    protected function logAction($action, $data = [], $success = true, $message = '')
    {
        $logData = [
            'card_type' => $this->getCardType(),
            'action' => $action,
            'success' => $success,
            'message' => $message,
            'data' => $data,
            'user_info' => $this->getUserInfo(),
            'timestamp' => time()
        ];

        $logCategory = 'CardCallback' . ucfirst($this->getCardType());
        
        if ($success) {
            Yii::info($logData, $logCategory);
        } else {
            Yii::error($logData, $logCategory);
        }
    }
} 