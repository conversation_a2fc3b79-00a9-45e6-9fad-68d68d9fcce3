<?php

namespace common\components\feishu\cardCallback;

use common\enums\CardCallbackTypeEnum;
use common\components\report\ReportFactory;
use common\helpers\ArrayHelper;
use common\models\backend\order\OrderHeader;
use common\models\backend\Member;
use common\models\promote\ReportLog;
use common\enums\log\AdsReportTypeEnum;
use backendapi\services\order\OrderHeaderService;
use common\helpers\DateHelper;
use common\helpers\BcHelper;
use common\models\feishu\App;
use Exception;
use Yii;

/**
 * 下订上报卡片回调处理类
 */
class OrderReportCardCallback extends Base
{
    const ORDER_REPORT_TEMPLATE_IDS = [
        'chz' => [
            'message' => 'AAqd2kgsfPc7C',
            'success' => 'AAqdJJ2jxpp4t',
            'failure' => 'AAqdJEMoMXF6E',
            'cancel' => 'AAqdJJEDULEsq',
        ],
        'haoyun' => [
            'message' => 'AAqd5srDiYRfn',
            'success' => 'AAqd5stFlUKXF',
            'failure' => 'AAqd5usvOHHSh',
            'cancel' => 'AAqd5sLrfLdyq',
        ],
        'ftdz' => [],
    ];

    /**
     * 获取卡片类型
     * 
     * @return string
     */
    public function getCardType()
    {
        return CardCallbackTypeEnum::ORDER_REPORT;
    }

    /**
     * 处理卡片交互回调
     * 
     * @return array 响应数据
     */
    public function handle()
    {
        try {
            $actionType = $this->getActionType();
            
            // 记录操作开始日志
            $this->logAction($actionType, [
                'action_value' => $this->getActionValue(),
                'form_value' => $this->getFormValue()
            ], true, "开始处理{$actionType}操作");

            // 根据操作类型分发处理
            switch ($actionType) {
                case 'confirm':
                    return $this->handleConfirm();
                case 'cancel':
                    return $this->handleCancel();
                default:
                    throw new Exception("不支持的操作类型: {$actionType}");
            }
        } catch (Exception $e) {
            // 记录错误日志
            $this->logAction($this->getActionType(), [], false, "处理失败: " . $e->getMessage());
        }
    }

    /**
     * 处理确认操作
     * 
     * @return array
     */
    protected function handleConfirm()
    {
        $actionValue = $this->getActionValue();
        try {
            // 执行下订上报业务逻辑
            $this->executeOrderReport($actionValue);
            $actionValue['percentage_report'] = $this->calculatePercentageReport($actionValue['sub_advertiser_id']);
            
            // 根据上报结果构建响应
            $this->logAction('confirm', [], true, '确认操作成功，上报完成');
            return $this->buildCallbackResponse([], $this->buildTemplateCard(self::ORDER_REPORT_TEMPLATE_IDS[$this->comCode]['success'], $actionValue));
        } catch (Exception $e) {
            $actionValue['percentage_report'] = $this->calculatePercentageReport($actionValue['sub_advertiser_id']);
            $this->logAction('confirm', [], false, '确认操作异常: ' . $e->getMessage());
            return $this->buildCallbackResponse([], $this->buildTemplateCard(self::ORDER_REPORT_TEMPLATE_IDS[$this->comCode]['failure'], ArrayHelper::merge($actionValue, ['error_message' => $e->getMessage()])));
        }
    }

    /**
     * 处理取消操作
     * 
     * @return array
     */
    protected function handleCancel()
    {
        // 获取操作相关数据
        $actionValue = $this->getActionValue();
        $actionValue['percentage_report'] = $this->calculatePercentageReport($actionValue['sub_advertiser_id']);

        $this->logAction('cancel', [
            'action_value' => $actionValue,
        ], true, '取消操作成功');

        return $this->buildCallbackResponse([], $this->buildTemplateCard(self::ORDER_REPORT_TEMPLATE_IDS[$this->comCode]['cancel'], $actionValue));
    }

    // ========================= 业务逻辑方法 =========================

    /**
     * 计算上报百分比
     * 
     * @param string $subAdvertiserId 子广告主ID
     * @return string 格式化的百分比字符串 (reportNum/orderNum(percentage))
     */
    protected function calculatePercentageReport($subAdvertiserId)
    {
        $reportNum = ReportLog::getReportStats($subAdvertiserId, AdsReportTypeEnum::ORDER_CREATED);
        $orderNum = OrderHeaderService::getSubAccountOrderCount($subAdvertiserId);
        return $reportNum . '/' . $orderNum . '(' . BcHelper::percentage($reportNum, $orderNum) . ')';
    }

    /**
     * 执行下订上报业务逻辑
     * 
     * @param array $actionValue 操作数据
     * @return bool 上报结果
     */
    protected function executeOrderReport($actionValue)
    {
        $order = OrderHeader::find()->select(['id', 'customer_user_id', 'channel_id', 'deposit', 'created_at'])->where(['id' => $actionValue['order_id']])->one();
        if (empty($order)) {
            throw new Exception('订单不存在');
        }

        try {
            $reportService = ReportFactory::getReportServiceByChannelId($order->channel_id);
            $reportService->autoReport = false;
            $reportService->manualOnOrderCreatedReport($order);
        } catch (Exception $e) {
            throw new Exception('手动下订上报失败，订单ID: ' . $actionValue['order_id'] . ', 错误信息: ' . $e->getMessage());
        }

        return true;
    }

    /**
     * 发送下订上报卡片消息
     * 
     * @param array $data 数据
     * @return void
     */
    public function sendReportCardMessage(array $data)
    {
        $feishuInfo = Member::find()->select('current_feishu_app_id, feishu_unionid')->where(['id' => $data['sub_account']->responsible_id])->asArray()->one();
        if (empty($feishuInfo['feishu_unionid'])) {
            throw new Exception('backend_member表ID为' . $data['sub_account']->responsible_id . '的用户飞书union_id不存在');
        }
        $comCode = App::find()->select('code')->where(['id' => $feishuInfo['current_feishu_app_id']])->scalar();
        if (empty($comCode)) {
            throw new Exception('feishu_app表ID为' . $feishuInfo['current_feishu_app_id'] . '的飞书应用不存在');
        }

        $content = $this->buildTemplateCard(self::ORDER_REPORT_TEMPLATE_IDS[$comCode]['message'], [
            'sub_advertiser_name' => $data['sub_account']->sub_advertiser_name,
            'sub_advertiser_id' => $data['sub_account']->sub_advertiser_id,
            'order_id' => $data['order']->id,
            'add_time' => DateHelper::toDate($data['order']->customerUser->add_time),
            'order_time' => DateHelper::toDate($data['order']->created_at),
            'deposit' => $data['order']->deposit,
            'percentage_report' => $this->calculatePercentageReport($data['sub_account']->sub_advertiser_id),
            'adid' => $data['order']->customerUser->adid ?? '',
        ]);

        Yii::info($content, 'sendReportCardMessage');
        Yii::$app->feishuNotice->card($content, $feishuInfo['feishu_unionid'], 'union_id');
    }
} 