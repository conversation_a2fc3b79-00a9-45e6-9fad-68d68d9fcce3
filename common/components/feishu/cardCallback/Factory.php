<?php

namespace common\components\feishu\cardCallback;

use common\enums\CardCallbackTypeEnum;
use Exception;

/**
 * 卡片回调处理服务工厂
 */
class Factory
{
    /**
     * 根据回调消息获取对应的处理服务
     *
     * @param array $message 飞书回调消息
     * @return Base|OrderReportCardCallback
     * @throws Exception
     */
    public static function getServiceByMessage($message)
    {
        $cardType = static::getCardType($message);
        if (empty($cardType)) {
            throw new Exception("无法识别卡片类型，消息内容：" . json_encode($message, JSON_UNESCAPED_UNICODE));
        }

        $className = CardCallbackTypeEnum::getClassName($cardType);
        if (empty($className)) {
            throw new Exception("卡片类型 [{$cardType}] 没有对应的处理类");
        }

        $cardCallbackClassName = "\\common\\components\\feishu\\cardCallback\\" . $className;
        if (!class_exists($cardCallbackClassName)) {
            throw new Exception("卡片回调处理类 [{$cardCallbackClassName}] 不存在");
        }

        return new $cardCallbackClassName($message);
    }

    /**
     * 从回调消息中提取卡片类型
     * 
     * @param array $message 飞书回调消息
     * @return string
     */
    public static function getCardType($message)
    {
        if (isset($message['event']['action']['value']['card_type'])) {
            return $message['event']['action']['value']['card_type'];
        }
        return '';
    }
} 