<?php

namespace common\components\log;

/**
 * 日志基础类
 *
 * Class LogBase
 */

use common\models\backend\Member;
use common\models\OperateLog;
use Yii;

class LogBase
{
    private $type = 0;
    private $created_by = 0;
    private $operate_type = '';
    public $source_id = 0;
    public $created_at;

    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    public function setOperateType($operate_type)
    {
        $this->operate_type = $operate_type;
        return $this;
    }

    public function setSourceID($source_id)
    {
        $this->source_id = $source_id;
        return $this;
    }

    public function setCreatedBy($created_by)
    {
        $this->created_by = $created_by;
        return $this;
    }

    public function setCreatedAt($created_at)
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function record($content)
    {
        if (empty($this->created_at)) {
            $this->created_at = time();
        }
        $logData = [
            'source_id' => $this->source_id,
            'type' => $this->type,
            'operate_type' => $this->operate_type,
            'content' => json_encode($content, 256),
            'entity_id' => $this->getEntityID(),
            'created_by' => $this->created_by,
            'created_by_name' => $this->getCreatedByName(),
            'created_at' => $this->created_at,
        ];

        Yii::$app->db->createCommand()->insert(OperateLog::tableName(), $logData)->execute();
    }

    public function getCreatedByName()
    {
        if (empty($this->created_by)) {
            return '';
        }

        return Member::getMemberName($this->created_by);
    }

    public function getEntityID()
    {
        if (empty($this->created_by)) {
            return 1;
        }

        return Member::find()->select('current_entity_id')->where(['id' => $this->created_by])->scalar();
    }
}
