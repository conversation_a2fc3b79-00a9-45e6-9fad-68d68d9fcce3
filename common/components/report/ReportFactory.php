<?php

namespace common\components\report;

use common\enums\PlatformEnum;
use common\models\backendapi\PromoteChannel;

/**
 * 上报类工厂
 */
class ReportFactory
{
    /**
     * 获取一个上报服务
     *
     * @param int $channelId
     * @return Report|TiktokReport|QuicklyReport|WechatReport
     */
    public static function getReportServiceByChannelId($channelId)
    {
        $channel = PromoteChannel::findOne($channelId);
        if (!$channel) {
            $channel = new PromoteChannel();
        }
        $platformCode = strtolower($channel->platform);

        $reportClassName = 'Report';
        $platformArray = PlatformEnum::getKeys();
        if (in_array($platformCode, $platformArray)) {
            $reportClassName = ucfirst($platformCode) . $reportClassName;
        }

        $reportClassName = "\\common\\components\\report\\" . $reportClassName;
        return new $reportClassName($channel);
    }
}
