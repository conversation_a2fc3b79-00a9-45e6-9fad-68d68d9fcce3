<?php

namespace common\components\report;

use common\enums\log\AdsReportTypeEnum;
use common\helpers\Tool;
use common\models\promote\DataSource;
use common\models\backend\order\OrderHeader;
use Exception;
use Yii;

/**
 * 腾讯平台(新16)
 */
class WechatReport extends Report
{
    const ACTION_TYPE_SCANCODE = 'SCANCODE';
    const ACTION_TYPE_CONFIRM_EFFECTIVE_LEADS = 'CONFIRM_EFFECTIVE_LEADS';
    const ACTION_TYPE_VISIT_STORE = 'VISIT_STORE';
    const ACTION_TYPE_COMPLETE_ORDER = 'COMPLETE_ORDER';

    public $accessToken = 'abde16bb2a19773bdb4bf1d36c375e0c';
    // 账户ID
    public $accountId = '********';
    // 数据源ID
    public $actionSetId = '**********';

    public $orderReportPostData = [];

    /**
     * 下单上报前置事件
     *
     * @return boolean
     */
    protected function beforeOrderCreatedReport()
    {
        try {
            // 订单没定金 || 没支付 跳出
            // if (!$this->order->deposit || !$this->order->pre_pay_time) {
            //     return false;
            // }
            // 取不到客户联合id时，直接跳出
            $this->getCusUnionId();
        } catch (Exception $e) {
            return false;
        }

        return true;
    }

    /**
     * 下订上报
     */
    public function onOrderCreatedReport()
    {
        Yii::info($this->order->id, 'WechatReport::onOrderCreatedReport');

        $unionid = $this->getUnionId();
        $callback = $this->getCallback();
        if (!$this->isCanReport()) {
            return false;
        }
        $wechatAppId = $this->getWxcomDeveloperId();
        $getCusUserId = $this->getCusUserIdByOrder();
        $outer_action_id = $getCusUserId . '_' . static::ACTION_TYPE_COMPLETE_ORDER;
        $res = $this->orderReport($unionid, $callback, $wechatAppId, static::ACTION_TYPE_COMPLETE_ORDER, $outer_action_id);
        $this->saveCustomerUserReport($this->order->customerUser->id);

        $this->saveReportLog(AdsReportTypeEnum::ORDER_CREATED, $res, $this->orderReportPostData);
        return $res;
    }

    /**
     * 手动下订上报
     *
     * @param OrderHeader $order
     * @return bool
     */
    public function manualOnOrderCreatedReport(OrderHeader $order)
    {
        Yii::info($order->id, 'WechatReport::manualOnOrderCreatedReport');
        $this->order = $order;

        $unionid = $this->getUnionId();
        $callback = $this->getCallback();
        $wechatAppId = $this->getWxcomDeveloperId();
        $getCusUserId = $this->getCusUserIdByOrder();
        $outer_action_id = $getCusUserId . '_' . static::ACTION_TYPE_COMPLETE_ORDER;
        if (!in_array(YII_ENV, ['local', 'dev'])) {
            $res = $this->orderReport($unionid, $callback, $wechatAppId, static::ACTION_TYPE_COMPLETE_ORDER, $outer_action_id);
        } else {
            $res = true;
        }
        $this->saveCustomerUserReport($this->order->customerUser->id);

        $this->saveReportLog(AdsReportTypeEnum::ORDER_CREATED, $res, $this->orderReportPostData);
        return true;
    }

    protected function afterOrderCreatedReport($result)
    {
        $this->thirdResult = $result;
        return $result['code'] == 0;
    }

    public function orderReport(string $unionid, $wx_traceid, string $wechatAppId, string $actionType, $outer_action_id = null)
    {
        $dataSource = $this->getDataSource();
        $url = 'https://api.e.qq.com/v1.1/user_actions/add';

        $common_parameters = [
            'access_token' => $dataSource['secret_key'],
            'timestamp' => time(),
            'nonce' => md5(uniqid('', true))
        ];
        $url .= '?' . http_build_query($common_parameters);

        $actions = [
            'user_id' => [
                'wechat_unionid' => $unionid,
                'wechat_app_id' => $wechatAppId ?: ''
            ],
            'action_time' => time(),
            'action_type' => $actionType,
        ];

        if ($wx_traceid) {
            $actions['trace'] = [
                'click_id' => $wx_traceid
            ];
        }

        if ($outer_action_id) {
            $actions['outer_action_id'] = $outer_action_id;
        }

        $postData = [
            'account_id' => $dataSource['account_id'],
            'user_action_set_id' => $dataSource['data_source_id'],
            'actions' => [
                $actions
            ]
        ];

        $this->orderReportPostData = $postData;
        $res = Tool::curlRequest($url, $postData, true);
        return $res;
    }

    public function beforeCusUserCreatedReport()
    {
        try {
            // 取不到客户联合id时，直接跳出
            $this->getCusUserUnionId();
        } catch (Exception $e) {
            return false;
        }

        return true;
    }

    public function onCusUserCreatedReport()
    {
        $unionid = $this->getCusUserUnionId();
        if (!$this->isHeavyPowderCanReport()) {
            return false;
        }
        $callback = $this->getCusUserCallback();
        $wechatAppId = $this->getCusUserWxcomDeveloperId();
        $getCusUserId = $this->getCusUserId();
        $outer_action_id = $getCusUserId . '_' . static::ACTION_TYPE_COMPLETE_ORDER;
        $res = $this->orderReport($unionid, $callback, $wechatAppId, static::ACTION_TYPE_COMPLETE_ORDER, $outer_action_id);
        $this->saveCustUserHeavyPowderReport();
        return $res;
    }
    protected function afterCusUserCreatedReport($result)
    {
        $this->thirdResult = $result;
        return $result['code'] == 0;
    }

    /**
     * 获取 callback
     *
     * @return string
     */
    protected function getCallback()
    {
        if (empty($this->order)) {
            throw new Exception('找不到订单信息');
        }
        if (empty($this->order->customerUser)) {
            throw new Exception('订单上找不到加粉信息');
        }
        if (empty($this->order->customerUser->callback)) {
            return '';
        }

        return $this->order->customerUser->callback;
    }

    protected function getCusUserCallback()
    {
        if (empty($this->cusCustomerUser)) {
            throw new Exception('找不到加粉信息');
        }
        if (empty($this->cusCustomerUser->callback)) {
            return '';
        }

        return $this->cusCustomerUser->callback;
    }

    /**
     * 获取数据源
     */
    public function getDataSource()
    {
        $customerUser = $this->cusCustomerUser;
        if (empty($customerUser)) {
            $customerUser = $this->order->customerUser;
            Yii::info(['order_id' => $this->order->id, 'customerUser_id' => $customerUser->id], 'getDataSource-order-customerUser');
        } else {
            Yii::info($customerUser->id, 'getDataSource-customerUser');
        }

        if ($customerUser) {
            $qrcode = $customerUser->qrcode;
            if ($qrcode && $qrcode->data_source_id) {
                $info = DataSource::find()->select('account_id,data_source_id,secret_key')->where(['id' => $qrcode->data_source_id])->asArray()->one();
                if ($info) return $info;
            }
        }

        Yii::info('查找不到数据源，获取默认数据源', 'getDataSource');
        $info = [
            'account_id' => $this->accountId,
            'data_source_id' => $this->actionSetId,
            'secret_key' => $this->accessToken
        ];

        return $info;
    }
}
