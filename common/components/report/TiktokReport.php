<?php

namespace common\components\report;

use common\enums\HeadLinesReportTypeEnum;
use common\enums\log\AdsReportTypeEnum;
use common\helpers\Tool;
use common\models\backend\order\OrderHeader;
use Exception;
use Yii;

/**
 * 抖音平台(新1)
 */
class TiktokReport extends Report
{
    const EVENT_TYPE_CREATE_ORDER = '196';
    const EVENT_TYPE_INTENTION_ORDER = '388';

    /**
     * 下订上报
     */
    public function onOrderCreatedReport()
    {
        Yii::info($this->order->id, 'TiktokReport::onOrderCreatedReport');

        $callback = $this->getCallback();
        if (!$this->isCanReport()) {
            return false;
        }

        //        $reportArray = json_decode($this->order->customerUser->landPageReport->report_array);
        //        $eventArray = array_column($reportArray, 'report_event');
        //        if (in_array(HeadLinesReportTypeEnum::WECHATADDWXCOM, $eventArray)) {
        //            $landId = $this->order->customerUser->landPageReport->land_id;
        //            $cl = $this->order->customerUser->cl_code;
        //            $res = $this->report([
        //                'link' => "https://erp.radilush.com/landingPage/view/{$landId}?cl={$cl}",
        //                'event_type' => self::EVENT_TYPE_INTENTION_ORDER,
        //            ]);
        //        } else {
        $params = [
            'callback' => $callback,
            'event_type' => self::EVENT_TYPE_CREATE_ORDER,
            'conv_time' => time(),
        ];
        $res = $this->report($params);

        $this->saveCustomerUserReport($this->order->customerUser->id);
        $this->saveReportLog(AdsReportTypeEnum::ORDER_CREATED, $res, $params);
        //        }
        return $res;
    }

    /**
     * 手动下订上报
     *
     * @param OrderHeader $order
     * @return bool
     */
    public function manualOnOrderCreatedReport(OrderHeader $order)
    {
        Yii::info($order->id, 'TiktokReport::manualOnOrderCreatedReport');
        $this->order = $order;

        $callback = $this->getCallback();
        $params = [
            'callback' => $callback,
            'event_type' => self::EVENT_TYPE_CREATE_ORDER,
            'conv_time' => time(),
        ];
        if (!in_array(YII_ENV, ['local', 'dev'])) {
            $res = $this->report($params);
        } else {
            $res = true;
        }
        $this->saveCustomerUserReport($this->order->customerUser->id);

        $this->saveReportLog(AdsReportTypeEnum::ORDER_CREATED, $res, $params);
        return true;
    }

    protected function afterOrderCreatedReport($result)
    {
        $this->thirdResult = $result;
        return $result['code'] == 0;
    }

    /**
     * 上报
     *
     * @param array $params
     * @return mixed
     */
    public function report(array $params)
    {
        $url = "https://ad.oceanengine.com/track/activate/";

        $url .= '?' . http_build_query($params);
        return Tool::curlRequest($url);
    }

    /**
     * 重写获取客户unionid
     *
     * @return string
     */
    public function getCusUnionId()
    {
        return $this->getCallback();
    }

    public function onCusUserCreatedReport()
    {
        $callback = $this->getCusUserCallback();
        if (!$this->isHeavyPowderCanReport()) {
            return false;
        }
        $res = $this->report([
            'callback' => $callback,
            'event_type' => self::EVENT_TYPE_CREATE_ORDER,
            'conv_time' => time(),
        ]);
        $this->saveCustUserHeavyPowderReport();
        return $res;
    }

    protected function afterCusUserCreatedReport($result)
    {
        $this->thirdResult = $result;
        return $result['code'] == 0;
    }

    /**
     * 重写获取客户unionid
     *
     * @return string
     */
    public function getCusUserUnionId()
    {
        return $this->getCusUserCallback();
    }
}
