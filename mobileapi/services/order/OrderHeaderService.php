<?php

namespace mobileapi\services\order;

use auth\models\Store;
use auth\services\UnionPayService;
use common\enums\order\OrderHeaderStatusEnum;
use common\helpers\DateHelper;
use common\helpers\ResultHelper;
use common\models\backend\Member;
use common\models\common\DepartmentAssignment;
use common\models\Customer;
use common\models\wxcom\CusCustomer;
use common\services\order\OrderHeaderService as CommonHeaderService;
use Exception;
use mobileapi\models\order\OrderHeader;
use mobileapi\services\UserService;
use Yii;

class OrderHeaderService extends CommonHeaderService
{
    /**
     * @var OrderHeader
     */
    public static $modelClass = OrderHeader::class;

    /**
     * 订金流水列表
     *
     * @param array $params
     * @return \yii\db\ActiveQuery
     */
    public static function getWriteListQuery($params = [])
    {
        $query = parent::getWriteListQuery($params);
        if (Yii::$app->services->auth->isSuperAdmin()) {
            return $query;
        }

        if (empty($params['cus_id'])) throw new Exception('下单客户ID，不能为空！');

        $customer = Customer::findOne($params['cus_id']);
        if (empty($customer->wxcom_cus_id)) { //如果是同行人，则可使用老客的流水信息
            $customer = Customer::findOne($customer->generation_id);
        }

        $payeeUser = UserService::getCurrentWxcomUser();
        $wxcomCustomer = CusCustomer::find()
            ->where([
                'com_id' => $payeeUser->com_id,
                'unionid' => $customer->unionid
            ])
            ->one();

        $query
            ->andWhere([
                'wxcom_cus_id' => $wxcomCustomer->id,
                'payee_userid' => $payeeUser->wxcom_user_id,
                'entity_id' => Yii::$app->user->identity->current_entity_id,
            ]);
        return $query;
    }

    /**
     * 接待列表-今日接待/近30天的接待
     */
    public static function receptionList($params = [], $isToday = true)
    {
        $plan_user_id = Yii::$app->user->identity->id;

        $arrStoreId = Store::find()->alias('s')
            ->select('s.id')
            ->leftJoin(['da' => DepartmentAssignment::tableName()], 'da.dept_id = s.dept_id')
            ->andWhere(['da.user_id' => $plan_user_id])
            ->column();
        if (empty($arrStoreId)) {
            $arrStoreId = [-1];
        }

        if ($isToday) { //今日
            $today = DateHelper::today();
            $order_status = [
                OrderHeaderStatusEnum::STATUS_ARRIVED_STORE,
                OrderHeaderStatusEnum::STATUS_SETTLEMENT,
                OrderHeaderStatusEnum::STATUS_COMPLETED,
            ];
            $conditions =  [
                'and',
                ['between', 'plan_time', $today['start'], $today['end']],
                ['order_status' => $order_status]
            ];
        } else { //近30天，不包括当天
            $lastThirtyDays = DateHelper::lastDays(30, false);
            $conditions = [
                'and',
                ['between', 'plan_time', $lastThirtyDays['start'], $lastThirtyDays['end']],
                ['order_status' => [OrderHeaderStatusEnum::STATUS_ARRIVED_STORE, OrderHeaderStatusEnum::STATUS_SETTLEMENT]]
            ];
        }

        $query = static::getQuery($params);
        $query = $query
            ->select('id as order_id,cus_id,store_id,plan_time,plan_user_id')
            ->andWhere($conditions)
            ->andWhere(['or', ['plan_user_id' => $plan_user_id], ['store_id' => $arrStoreId]])
            ->andWhere(['>', 'plan_user_id', 0])
            ->groupBy('cus_id');

        $aa = clone $query;
        $totalCount = $aa->offset(0)->limit('')->count();
        $list = $query->asArray()->orderBy('plan_time desc')->all();
        foreach ($list as &$item) {
            $customer = Customer::find()->select('name,mobile,avatar')->where(['id' => $item['cus_id']])->one();
            $item['cus_name'] = $item['mobile'] = $item['avatar'] = '';
            if ($customer) {
                $item['cus_name'] = $customer->name;
                $item['avatar'] = $customer->avatar;
                $item['mobile'] = ResultHelper::mobileTailNumber($customer->mobile);
            }
            $item['plan_time'] = DateHelper::toDate($item['plan_time'], 'Y-m-d H:i');
            $item['store_name'] = Store::find()->select('store_name')->where(['id' => $item['store_id']])->cache(5)->scalar();
            $item['amount'] = UnionPayService::getUserUnionPayAmount($item['cus_id'], $today['start'], $today['end']);
            $member = Member::find()->select('username')->where(['id' => $item['plan_user_id']])->cache(60)->one();
            $item['plan_teacher_name'] = $member ? $member->username : '';
        }

        return [$list, $totalCount];
    }
}
