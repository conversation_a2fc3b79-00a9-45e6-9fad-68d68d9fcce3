<?php

namespace backendapi\controllers\feishu;

use backendapi\services\feishu\MultidimensionalTableConfigService;
use common\controllers\BaseController;
use common\enums\MultidimensionalTableConfigEnum;
use common\helpers\ResultHelper;
use common\components\Feishu;
use common\enums\feishu\MultidimensionalTableConfigSubscribeEnum;
use Exception;
use Yii;

/**
 * 飞书多维表格配置控制器
 *
 * Class MultidimensionalTableConfigController
 * @package backendapi\controllers\feishu
 */
class MultidimensionalTableConfigController extends BaseController
{
    /**
     * @var MultidimensionalTableConfigService
     */
    public $serviceClass = MultidimensionalTableConfigService::class;

    /**
     * 新增配置
     * 
     * @return array
     */
    public function actionCreate()
    {
        try {
            $this->serviceClass::create(Yii::$app->request->post());
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }

        return ResultHelper::json(200, '新增成功');
    }

    /**
     * 更新配置
     * 
     * @return array
     */
    public function actionUpdateData()
    {
        try {
            $id = Yii::$app->request->post('id', 0);
            $this->serviceClass::update($id, Yii::$app->request->post());
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }

        return ResultHelper::json(200, '更新成功');
    }

    /**
     * 获取配置详情
     * 
     * @param int $id
     * @return array
     */
    public function actionView($id)
    {
        try {
            $info = $this->serviceClass::view($id);
            return ['info' => $info];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }
    
    /**
     * 获取配置类型列表
     * 
     * @return array
     */
    public function actionTypes()
    {
        return MultidimensionalTableConfigEnum::getSelectList();
    }

    /**
     * 订阅
     * 
     * @return array
     */
    public function actionSubscribe()
    {
        $feishu = new Feishu();
        $result = $feishu->fileSubscribe(Yii::$app->request->post('file_token', ''));
        if ($result['code'] != 0) {
            return ResultHelper::json(422, $result['msg']);
        }

        $model = $this->serviceClass::getInfoById(Yii::$app->request->post('id', 0));
        $model->subscribe = MultidimensionalTableConfigSubscribeEnum::SUBSCRIBE;
        if (!$model->save()) {
            return ResultHelper::json(422, $model->getFirstErrMsg());
        }

        return ResultHelper::json(200, '订阅成功');
    }

    /**
     * 取消订阅
     * 
     * @return array
     */
    public function actionUnsubscribe()
    {
        $feishu = new Feishu();
        $result = $feishu->fileUnsubscribe(Yii::$app->request->post('file_token', ''));
        if ($result['code'] != 0) {
            return ResultHelper::json(422, $result['msg']);
        }

        $model = $this->serviceClass::getInfoById(Yii::$app->request->post('id', 0));
        $model->subscribe = MultidimensionalTableConfigSubscribeEnum::UNSUBSCRIBE;
        if (!$model->save()) {
            return ResultHelper::json(422, $model->getFirstErrMsg());
        }

        return ResultHelper::json(200, '取消订阅成功');
    }
}
