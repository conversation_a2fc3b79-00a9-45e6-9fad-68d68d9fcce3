<?php

namespace backendapi\controllers\wxcom;

use backendapi\controllers\BaseController;
use common\models\wxcom\CusCustomerUser;
use backendapi\forms\wxcom\CusCustomerUserForm;
use backendapi\services\wxcom\CusCustomerUserService;
use common\enums\WxcomAddTypeEnum;
use common\enums\AdPositionEnum;
use common\helpers\ResultHelper;
use Yii;

class CusCustomerUserController extends BaseController
{
    public $modelClass = CusCustomerUser::class;
    public $formClass = CusCustomerUserForm::class;
    public $serviceClass = CusCustomerUserService::class;

    public function actionGetAddWaySelect()
    {
        return WxcomAddTypeEnum::getSelectList();
    }

    public function actionGetAdPositionSelect()
    {
        return AdPositionEnum::getSelectList();
    }

    /**
     * 待继承列表
     *
     * @return array
     * @throws \Exception
     */
    public function actionIndexForExtend()
    {
        $serviceClass = $this->getServiceClass();
        $params = Yii::$app->request->queryParams;
        $params['is_extend'] = 0;
        if(!$params['user_id']){
            return ResultHelper::json(422, 'user_id不能为空');
        }
        list($list, $totalCount) = (new $serviceClass)->search($params);
        return ['list' => $list, 'totalCount' => $totalCount];
    }
}
