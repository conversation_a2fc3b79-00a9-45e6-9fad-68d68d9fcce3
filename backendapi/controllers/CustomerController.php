<?php

namespace backendapi\controllers;

use backendapi\validator\customer\CustomerValidatorGetInfoByOrder;
use backendapi\services\CustomerService;
use common\enums\CustomerAgeBracket;
use common\helpers\ResultHelper;
use common\models\Customer;
use Exception;
use Yii;

class CustomerController extends \auth\controllers\CustomerController
{
    /** @var CustomerService */
    public $serviceClass = CustomerService::class;

    /**
     * 获取用户详情用于订单新增的
     *
     * @route get /customer/get-info-by-order
     * @return array
     */
    public function actionGetInfoByOrder(): array
    {
        $params = Yii::$app->request->queryParams;

        if (!(new CustomerValidatorGetInfoByOrder())->validate($params, $error)) {
            return ResultHelper::json(400, $error);
        }

        return $this->serviceClass::getInfoByOrder($params);
    }

    /**
     * 获取用户列表用于订单新增的
     *
     * @return mixed
     */
    public function actionGetListByOrder()
    {
        return $this->serviceClass::getListByOrder();
    }

    /**
     * 客户详情详情
     *
     * @param $id
     * @return array|mixed
     */
    public function actionView($id)
    {
        try {
            $info = $this->serviceClass::getView($id);
            return ['info' => $info];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 客户订单信息
     *
     * @return array
     * @throws Exception
     */
    public function actionCustomerOrder()
    {
        $params = Yii::$app->request->get();
        list($list, $totalCount) = $this->serviceClass::getCustomerOrder($params);

        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 查看手机号
     *
     * @return array|mixed
     */
    public function actionMobileCheck()
    {
        try {
            $id = Yii::$app->request->get('id', 0);
            if (empty($id)) {
                return ResultHelper::json(422, 'id不能为空');
            }
            $customer = Customer::findOne($id);
            return ResultHelper::json(200, '获取成功', [
                'mobile' => $customer->mobile
            ]);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    //门店新增客户
    public function actionStoreCreate()
    {
        $trans = Yii::$app->db->beginTransaction();
        try {
            $model = $this->serviceClass::storeCreate(Yii::$app->request->post());
            $trans->commit();
        } catch (Exception $e) {
            $trans->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }
        return ResultHelper::json(200, '新增成功', $model);
    }

    //门店搜索客户列表
    public function actionStoreSearchList()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = (new $this->serviceClass)->storeSearchList($params);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 修改手机号及姓名
     */
    public function actionUpdateMobileName()
    {
        try {
            $params = Yii::$app->request->post();
            $this->serviceClass::updateMobileName($params);
            return '修改成功';
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 修改绑定企微
     */
    public function actionUpdateWxcomCustomer()
    {
        try {
            $params = Yii::$app->request->post();
            $this->serviceClass::updateWxcomCustomer($params);
            return '修改成功';
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 作废客户
     *
     * @return array|mixed
     */
    public function actionVoid()
    {
        try {
            $params = Yii::$app->request->post();
            $this->serviceClass::void($params);

            return ResultHelper::json(200, '作废成功', $params['id']);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    public function actionGetAgeBracket()
    {
        return CustomerAgeBracket::getSelectList();
    }
}
