<?php

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/9/5
 * Time: 16:47
 */

namespace backendapi\controllers;

use backendapi\forms\StoreForm;
use backendapi\services\StoreService;
use common\enums\StoreClassEnum;
use common\helpers\ResultHelper;
use common\models\Area;
use common\models\backend\Member;
use common\models\backend\Store;
use Exception;
use Yii;
use yii\db\Query;

/**
 * Class StoreController
 * @package backendapi\controllers
 */
class StoreController extends OnAuthController
{
    protected $unAuthActionOptional = ['search-stores', 'search-state-stores', 'filter-list'];

    /* @var $model Store */
    public $modelClass = Store::class;

    /**
     * 门店-列表
     *
     * @return array
     * @throws \Exception
     */
    public function actionIndex()
    {
        list($list, $totalCount) = (new StoreService())->search(Yii::$app->request->queryParams);
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 门店-导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionExport()
    {
        list($list, $totalCount) = (new StoreService())->search(Yii::$app->request->queryParams);
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 获取门店列表-下拉
     *
     * @route store/filter-list
     * @return array|\yii\db\ActiveRecord[]
     * @throws \yii\base\InvalidConfigException
     */
    public function actionFilterList()
    {
        return StoreService::getFilterList();
    }

    /**
     * 门店-详情
     *
     * @return array|mixed
     */
    public function actionView($id)
    {
        /**@var $model Store */
        $model = $this->modelClass::find()->where(['id' => $id])->one();
        if (!$model) return ResultHelper::json(404, '请求的数据不存在.');
        $model->created_by = Member::getMemberName($model->created_by);
        $model->updated_by = Member::getMemberName($model->updated_by);
        $res = $model->toArray();
        foreach ($res as $key => $value) {
            $res[$key] = (string)$value;
        }
        $res['area_text'] = Area::getAreaText($model->area_id);
        $res['area_ids'] = Area::getAreaIds($model->area_id);
        return [
            'info' => $res,
            'class' => StoreClassEnum::getMap(),
            'type' => StoreService::getType(),
        ];
    }

    /**
     * 门店-修改
     *
     * @return array|mixed
     * @throws Exception
     */
    public function actionUpdateData()
    {
        /** @var StoreForm $model */
        $model = StoreForm::find()->where(['id' => Yii::$app->request->post('id')])->one();
        if (!$model) return ResultHelper::json(422, '该门店不存在。');

        if (!$model->updateData()) return ResultHelper::json(422, $this->getError($model));
        return ResultHelper::json(200, '修改成功');
    }

    /**编辑地址
     * @return array|mixed
     */
    public function actionUpdateAddress()
    {
        $id = Yii::$app->request->post('id');
        $model = $this->modelClass::find()->andWhere(['id' => $id])->one();
        if (!$model) return ResultHelper::json(422, '该门店不存在。');

        $model->scenario = 'update_address';
        $model->attributes = Yii::$app->request->post();
        if (!$model->save()) return ResultHelper::json(422, $this->getError($model));
        return ResultHelper::json(200, '修改成功');
    }

    /**
     * 批量修改门店区域
     *
     * @return array|mixed
     */
    public function actionUpdateAreaBatch()
    {
        $trans = Yii::$app->db->beginTransaction();
        try {
            StoreForm::updateAreaBatch();

            $trans->commit();
            return ResultHelper::json(200, '修改成功');
        } catch (Exception $e) {
            $trans->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 修改状态
     *
     * @return array|mixed
     * @throws Exception
     */
    public function actionStatus()
    {
        $trans = Yii::$app->db->beginTransaction();
        try {
            StoreForm::setStatus();

            $trans->commit();
            return ResultHelper::json(200, '修改成功');
        } catch (Exception $e) {
            $trans->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 获取门店
     *
     * @param $params
     * @return array
     */
    public function actionSearchStores($params)
    {
        $query = new Query();
        $store_data = $query->select("*,,store_name AS text")
            ->from('erp_store')
            ->where(['like', 'store_name', $params])
            ->orwhere(['id' => $params])
            ->orderBy('id DESC')
            ->all();

        return $store_data;
    }

    /**
     * 获取有采购申请的门店
     *
     * @return array
     */
    public function actionSearchStateStores()
    {
        $post = Yii::$app->request->post();

        $query = new Query();
        $store_data = $query->select("COUNT(*) AS count,erp_material_apply_record.store_id,erp_store.store_name AS text")
            ->from('erp_material_apply_record')
            ->leftJoin('erp_store', 'erp_material_apply_record.store_id=erp_store.id')
            ->where(['erp_material_apply_record.state' => $post['state']])
            ->orderBy('erp_material_apply_record.id DESC')
            ->groupBy('store_id')
            ->all();

        return $store_data;
    }

    /**
     * 门店-选择-列表
     *
     * @return array
     * @throws \Exception
     */
    public function actionChoose()
    {
        list($list, $totalCount) = (new StoreService())->getListForChoose(Yii::$app->request->queryParams);
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 门店-下拉选择-列表
     *
     * @return array
     * @throws \Exception
     */
    public function actionSelect()
    {
        return (new StoreService())->getListForSelect(Yii::$app->request->queryParams);
    }

    /**
     * 获取下单门店列表
     *
     * @return array|\yii\db\ActiveRecord[]
     * @throws Exception
     */
    public function actionPlanStoreList()
    {
        return \common\services\StoreService::getPlanStoreList();
    }

    /**
     * 到店统计
     *
     * @return array
     */
    public function actionOrderData()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = StoreService::getOrderData($params);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 到店统计
     *
     * @return array
     */
    public function actionOrderDataExport()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = StoreService::getOrderData($params);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 切换门店列表
     * @return array
     */
    public function actionSwitchStoreList()
    {
        list($list, $totalCount) = (new StoreService())->switchSearch(Yii::$app->request->queryParams);
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 门店老师预约时间列表
     *
     * @return array
     */
    public function actionTeacherPlanTimeList()
    {
        try {
            $params = Yii::$app->request->queryParams;
            return StoreService::getTeacherPlanTimes($params);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店老师预约老师
     *
     * @return array
     */
    public function actionTeacherPlanList()
    {
        try {
            $params = Yii::$app->request->queryParams;
            return StoreService::getTeacherPlan($params);
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店收款配置列表
     * @return array|mixed
     */
    public function actionUnionConfigList()
    {
        try {
            $params = Yii::$app->request->get();
            list($list, $totalCount) = StoreService::getUnionConfigList($params);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店收款配置详情
     *
     * @param $id
     * @return array|mixed
     */
    public function actionUnionConfigView($id)
    {
        try {
            $info = StoreService::getUnionConfigView($id);

            return ['info' => $info];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 银联配置修改
     * @return array|mixed
     */
    public function actionUnionConfigUpdate()
    {
        $trans = Yii::$app->db->beginTransaction();
        try {
            $params = Yii::$app->request->post();
            StoreService::unionConfigUpdate($params);
            $trans->commit();
            return ResultHelper::json(200, '配置成功');
        } catch (Exception $e) {
            $trans->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 门店银联静态码获取
     * @return array|mixed
     */
    public function actionGetQrcode()
    {
        try {
            $storeId = Yii::$app->request->get('store_id', 0);
            $union_pay_id = Yii::$app->request->get('union_pay_id', 0);
            $data = StoreService::getQrcode($storeId, $union_pay_id);
            return ['data' => $data];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 存量统计
     *
     * @return array
     */
    public function actionInventoryData()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = StoreService::getInventoryData($params, false);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }

    /**
     * 存量统计导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionInventoryDataExport()
    {
        try {
            $params = Yii::$app->request->queryParams;
            list($list, $totalCount) = StoreService::getInventoryData($params, true);
            return ['list' => $list, 'totalCount' => $totalCount];
        } catch (Exception $e) {
            return ResultHelper::json(422, $e->getMessage());
        }
    }
}
