<?php

namespace backendapi\controllers\pay;

use backendapi\controllers\NewBaseController;
use common\helpers\ResultHelper;
use common\services\PaymentService;
use Exception;
use Yii;

class PaymentController extends NewBaseController
{
    public $serviceClass = PaymentService::class;

    public function actionUnionScanPay()
    {
        try {
            $param = $this->request->post();
            if (!isset($param['order_id']) || empty($param['order_id'])) {
                throw new Exception('订单id不能为空');
            }
            if (!isset($param['code']) || empty($param['code'])) {
                throw new Exception('条形码错误');
            }
            if (!isset($param['amount']) || empty($param['amount'])) {
                throw new Exception('订单金额不能为空');
            }
            if (!isset($param['customer_recharge_card'])) {
                throw new Exception('是否储值卡类型支付不能为空');
            }
            $convenientPay = $param['convenient_pay'] ?? 0;
            $data = $this->serviceClass::unionScanCode((int)$param['order_id'], $param['customer_recharge_card'], (float)$param['amount'], (string)$param['code'], (int)$convenientPay);
            return ResultHelper::json(200, '支付成功', $data);
        } catch (\Exception $e) {
            return ResultHelper::json(500, $e->getMessage());
        }
    }

    /**
     * 条形码收款
     *
     * @return array|mixed
     */
    public function actionBarCode()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $param = $this->request->post();
            if (!isset($param['id']) || empty($param['id'])) {
                throw new Exception('订单id不能为空');
            }
            if (!isset($param['bar_code']) || empty($param['bar_code'])) {
                throw new Exception('条形码错误');
            }
            
            if (!isset($param['amount']) || $param['amount'] <= 0) {
                throw new Exception('流水码金额不能小于0');
            }
            
            $data = $this->serviceClass::barCodePay($param);
            $transaction->commit();
            return ResultHelper::json(200, '支付成功', $data);
        } catch (\Exception $e) {
            $transaction->rollBack();
            return ResultHelper::json(422, $e->getMessage());
        }
    }
}
