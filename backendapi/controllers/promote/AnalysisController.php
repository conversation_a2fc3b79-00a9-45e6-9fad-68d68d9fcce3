<?php
/**
 * 广告投放-数据分析块
 *
 * Created by PhpStorm
 * User: ldz
 * Date:2021/9/6
 * Time:19:40
 */

namespace backendapi\controllers\promote;

use backendapi\controllers\OnAuthController;
use backendapi\services\promote\AdsAccountDataService;
use backendapi\services\promote\AnalysisService;
use backendapi\services\promote\ChannelService;
use backendapi\services\promote\LandPageService;
use backendapi\services\promote\PromoteLinkService;
use backendapi\services\promote\PromoteProjectService;
use common\enums\PlatformEnum;
use common\helpers\ArrayHelper;
use common\models\backend\Member;
use services\UserService;
use Yii;

class AnalysisController extends OnAuthController
{

    protected $authOptional = ['start-stop-time', 'public-time'];

    public $modelClass = '';

    /**
     * 城市数据-列表
     *
     * @return array
     * @throws \Exception
     */
    public function actionCityData()
    {
        $params = Yii::$app->request->queryParams;
        $end_time = AnalysisService::getTime('end');

        $params['start_time'] = ArrayHelper::getValue($params, 'start_time', $end_time);
        $params['end_time'] = ArrayHelper::getValue($params, 'end_time', $end_time);
        list($list, $totalCount) = AnalysisService::cityData($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'platformList' => PlatformEnum::getList(),
            'channelList' => ChannelService::dropDownData()
        ];
    }

    /**
     * 城市数据-导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionCityDataExport()
    {
        $params = Yii::$app->request->queryParams;
        $end_time = AnalysisService::getTime('end');

        $params['start_time'] = ArrayHelper::getValue($params, 'start_time', $end_time);
        $params['end_time'] = ArrayHelper::getValue($params, 'end_time', $end_time);
        list($list, $totalCount) = AnalysisService::cityData($params, true);

        if (!empty($params['getTotal'])) {
            return ['totalCount' => $totalCount];
        } else {
            return ['list' => $list];
        }
    }

    /**
     * 城市数据-起止时间
     *
     * @return array
     */
    public function actionStartStopTime()
    {
        return ['start_time' => AnalysisService::getTime(), 'end_time' => AnalysisService::getTime('end')];
    }

    /**
     * 获取城市每日数据-明细
     *
     * @return array
     * @throws \Exception
     */
    public function actionCityDailyData()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getCityDailyData($params);

        if (!empty($params['getTotal'])) {
            return ['totalCount' => $totalCount];
        } else {
            return ['list' => $list];
        }
    }

    ################################### 投放人分析###################################

    /**
     * 数据分析-公共时间区间
     *
     * @return array
     */
    public function actionPublicTime()
    {
        return ['start_time' => AnalysisService::getPublicTime(), 'end_time' => AnalysisService::getPublicTime('end')];
    }

    /**
     * 投放人分析 - 列表
     *
     * @return array
     * @throws \Exception
     */
    public function actionCastPeopleAnalysis()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount, $start_time, $end_time) = AnalysisService::getCastPeopleData($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'is_promote_person' => $this->UserIsPromotePerson(),
            'channelList' => ChannelService::dropDownData(),
            'projectList' => PromoteProjectService::dropDownData(),
            'linkList' => PromoteLinkService::dropDownData(),
        ];
    }

    /**
     * 投放人分析 - 导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionCastPeopleExport()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getCastPeopleExport($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'is_promote_person' => $this->UserIsPromotePerson()
        ];
    }

    /**
     * 投放人分析 - 详情
     *
     * @return array
     * @throws \Exception
     */
    public function actionCastPeopleDetails()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getCastPeopleDetails($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'is_promote_person' => $this->UserIsPromotePerson()
        ];
    }

     /**
     * 投放人分析 - 详情导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionCastPeopleDetailsExport()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getCastPeopleDetails($params,true);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'is_promote_person' => $this->UserIsPromotePerson()
        ];
    }


    ################################### 项目分析#####################################

    /**
     * 项目分析  - 列表
     *
     * @return array
     * @throws \Exception
     */
    public function actionProjectAnalysis()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount, $start_time, $end_time) = AnalysisService::getProjectData($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'is_promote_person' => $this->UserIsPromotePerson(),
//            'deptList' => Department::getChildDept(Department::PROMOTE_DEPT),    //获取广告投放部门
            'channelList' => ChannelService::dropDownData(),
            'projectList' => PromoteProjectService::dropDownData(),
            'linkList' => PromoteLinkService::dropDownData(),
        ];
    }

    /**
     * 项目分析 - 详情
     *
     * @return array
     * @throws \Exception
     */
    public function actionProjectDetails()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getProjectDetails($params);

        return [
            'list' => $list,
            'is_promote_person' => $this->UserIsPromotePerson(),
            'totalCount' => $totalCount,
        ];
    }

    /**
     * 项目分析 - 导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionProjectExport()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getProjectExport($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'is_promote_person' => $this->UserIsPromotePerson()
        ];
    }

    ################################### 平台分析#####################################

    /**
     * 平台分析  - 列表
     * @return array
     * @throws \Exception
     */
    public function actionPromoteAnalysis()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount, $start_time, $end_time) = AnalysisService::getPromoteData($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'is_promote_person' => $this->UserIsPromotePerson(),
            'channelList' => ChannelService::dropDownData(),
            'projectList' => PromoteProjectService::dropDownData(),
            'linkList' => PromoteLinkService::dropDownData(),
        ];
    }

    /**
     * 平台分析 - 详情
     *
     * @return array
     * @throws \Exception
     */
    public function actionPromoteDetails()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount, $start_time, $end_time) = AnalysisService::getPromoteDetails($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'is_promote_person' => $this->UserIsPromotePerson()
        ];
    }

    /**
     * 平台分析 - 导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionPromoteExport()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getPromoteExport($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'is_promote_person' => $this->UserIsPromotePerson()
        ];
    }

    ################################### 素材分析 #####################################

    public function actionMaterialAnalysis()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getMaterialDataList($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'channelList' => ChannelService::dropDownData(),
            'projectList' => PromoteProjectService::dropDownData(),
        ];
    }

    public function actionMaterialAnalysisExport()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getMaterialData($params, true);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
        ];
    }

    ################################### 年龄性别分析 #####################################

    public function actionAgeGenderAnalysis()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getAgeGenderDataList($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
        ];
    }

    public function actionAgeGenderAnalysisExport()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::getAgeGenderData($params, true);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
        ];
    }

    ################################### 获取推广用户统计数据#####################################

    /**
     * 落地页数据分析-列表
     *
     * @return array
     * @throws \Exception
     */
    public function actionLandPageList()
    {
        return ['list' => [], 'totalCount' => 0, 'start_time' => '', 'end_time' => ''];
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount, $start_time, $end_time) = AnalysisService::getLandPageList($params);
        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'start_time' => $start_time,
            'end_time' => $end_time,
        ];
    }

    /**
     * 落地页数据分析 - 导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionLandPageExport()
    {
        $params = Yii::$app->request->post();
        list($list, $totalCount) = AnalysisService::getLandPageExport($params);
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 判断用户是否为推广人员权限（用于前端字段查阅权限）
     *
     * @return bool
     */
    public function UserIsPromotePerson()
    {
        $IsPromotePerson = Member::userIsPromotePerson(Yii::$app->user->identity->id, ['推广专员', '推广主管']);

        return $IsPromotePerson ? true : false;
    }

    /**
     * 上报数据-导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionReportDataExport()
    {
        list($list, $totalCount) = LandPageService::reportData();
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 新16上报数据-导出
     *
     * @return array|\yii\db\ActiveRecord[]
     */
    public function actionWecomReportExport()
    {
        list($list, $totalCount) = LandPageService::wecomReport();
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 新16上报数据回调-导出
     *
     * @return array
     */
    public function actionWecomReportCallbackExport()
    {
        list($list, $totalCount) = LandPageService::wecomReportCallback();
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 企微回调数据-导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionWxcomCallbackExport()
    {
        list($list, $totalCount) = LandPageService::wxcomCallback();
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 新16落地页线索-导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionWecomCluesExport()
    {
        list($list, $totalCount) = LandPageService::wecomClues();
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 下订上报数据
     *
     * @return array
     */
    public function actionOrderReportExport()
    {
        list($list, $totalCount) = AdsAccountDataService::orderReportData();
        return ['list' => $list, 'totalCount' => $totalCount];
    }

    /**
     * 获取城市列表数据
     *
     * @return array
     */
    public function actionCityList()
    {
        $data = AnalysisService::getCityList();

        return [
            'list' => $data,
        ];
    }

    /**
     * 加粉产出分析
     *
     * @return array
     */
    public function actionUserGrowthOutput()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::analyzeUserGrowthOutput($params);
        return ['list' => $list,'totalCount' => $totalCount];
    }

    /**
     *  加粉产出分析-导出
     */
    public function actionUserGrowthOutputExport()
    {
        $params = Yii::$app->request->queryParams;
        list($list, $totalCount) = AnalysisService::analyzeUserGrowthOutput($params,true);
        return ['list' => $list,'totalCount' => $totalCount];
    }

    /**
     * 时段数据-列表
     *
     * @return array
     * @throws \Exception
     */
    public function actionHourData()
    {
        $params = Yii::$app->request->queryParams;
        $end_time = AnalysisService::getTime('end');

        $params['start_time'] = ArrayHelper::getValue($params, 'start_time', $end_time);
        $params['end_time'] = ArrayHelper::getValue($params, 'end_time', $end_time);
        $params['entity_id'] = UserService::getInst()->current_entity_id;

        list($list, $totalCount) = AnalysisService::getHourData($params);

        return [
            'list' => $list,
            'totalCount' => $totalCount,
            'channelList' => ChannelService::dropDownData(),
            'hourOptions' => AnalysisService::getHourOptions(),
            'is_promote_person' => $this->UserIsPromotePerson(),
        ];
    }

    /**
     * 时段数据-导出
     *
     * @return array
     * @throws \Exception
     */
    public function actionHourDataExport()
    {
        $params = Yii::$app->request->queryParams;
        $end_time = AnalysisService::getTime('end');

        $params['start_time'] = ArrayHelper::getValue($params, 'start_time', $end_time);
        $params['end_time'] = ArrayHelper::getValue($params, 'end_time', $end_time);
        $params['entity_id'] = UserService::getInst()->current_entity_id;

        list($list, $totalCount) = AnalysisService::getHourData($params, true);

        if (!empty($params['getTotal'])) {
            return ['totalCount' => $totalCount];
        } else {
            return ['list' => $list];
        }
    }

    /**
     * 时段数据-起止时间
     *
     * @return array
     */
    public function actionHourTime()
    {
        return ['start_time' => AnalysisService::getHourTime(), 'end_time' => AnalysisService::getHourTime('end')];
    }
}