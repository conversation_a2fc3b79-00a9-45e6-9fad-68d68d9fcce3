<?php

/**
 * 上报服务
 */

namespace backendapi\services\wxcom;

use backendapi\models\order\OrderHeader;
use backendapi\services\promote\ChannelService;
use common\cache\WxcomCache;
use common\components\PromoteReport;
use common\enums\CusCustomerUserTypeEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\PlatformEnum;
use common\enums\reportEnum;
use common\enums\ReportEventEnum;
use common\enums\WxcomAddTypeEnum;
use common\helpers\DateHelper;
use common\helpers\Tool;
use common\models\common\AdsAccountProgram;
use common\models\common\AdsAccountSub;
use common\models\common\ApiLog;
use common\models\common\LandPageReport;
use common\models\wxcom\CusCustomer;
use common\models\wxcom\CusCustomerUser;
use common\models\wxcom\CusDynamicQrcode;
use common\models\wxcom\WechatServicer;
use common\models\wxcom\WechatServicerStatistics;
use common\queues\UpdateCustomerReportInfoJob;
use services\common\FeishuExamineService;
use common\components\promoteData\Adq;
use common\models\common\AdsAccount;
use common\models\log\WecomCallback;
use common\enums\log\WecomCallbackTypeEnum;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;

class ReportService
{
    const share = 1; //数据为分享数据类型
    const changeInfoCusCustomerUser = 'ChangeInfoCusCustomerUser:';

    public $message = []; //企微回调数据
    public $customerInfo = []; //客户信息
    public $add_type = 0; //添加类型：0客户主动，1员工主动
    public $serial_number; //api编号
    public $error = '';
    public $cusQrCodeInfo = []; //客户动态活码行为数据

    public function __construct()
    {
        $this->serial_number = DateHelper::toDate(time(), 'YmdHis') . mt_rand(10000, 99999);
    }

    /**
     * @return bool
     * @throws \Exception
     */
    public function onEventCallback()
    {
        // 添加方式如果不是 扫码或者获客助手 则不上报
        if (!in_array($this->add_type, [WxcomAddTypeEnum::QRCODE, WxcomAddTypeEnum::LINK, WxcomAddTypeEnum::UNKNOWN])) {
            return false;
        }

        try {
            //判断State不存在
            if (!isset($this->message['State']) || empty($this->message['State']) || strpos($this->message['State'], 'yiye') === 0) {
                return false;
            }
            $state = $this->message['State'];

            if ($this->add_type == WxcomAddTypeEnum::UNKNOWN && $state != 'wxad_button') {
                return false;
            }

            //动态活码匹配上报
            if (strstr($state, WxcomCache::USER_QRCODE) !== false) {
                $this->dynamicQrcodeReport();
                return true;
            }

            // 获客助手-短链上报 
            if (strpos($state, WxcomCache::LINK) === 0) {
                return $this->linkReport();
            }

            // 获客助手-弹窗组件上报 
            if (strpos($state, WxcomCache::CUS_ACQUISITION) === 0) {
                return $this->cusAcquisitionReport();
            }

            $unionid = $this->customerInfo['external_contact']['unionid'];
            if (empty($unionid)) {
                return false;
            }

            $wxcomCache = new WxcomCache();
            $unionResult = $wxcomCache->get('unionid:' . $unionid);
            if (empty($unionResult)) {
                $unionResult = $wxcomCache->get($unionid);
            }

            if ($this->message['State'] == 'wxad_button') {
                // (new PromoteReport($unionResult['cl'], ReportEventEnum::addfans, $unionResult['params'], $this->message['ComCode']))->wechat($this->customerInfo['external_contact']['unionid'], $this->message['ComCode']);
                $wxcomCache->destroy($unionid);
                $wxcomCache->destroy('unionid:' . $unionid);
                return true;
            }

            if (strstr($this->message['State'], 'qd:') !== false) {
                $state = Tool::getStringToArray($this->message['State'], ':')[1];
            }

            $cusQrCodeInfo = CusQrcodeService::find()->alias('c')->select('c.*,pc.platform')
                ->leftJoin(['{{%promote_channel}} pc'], 'pc.id = c.channel_id')
                ->where(['c.id' => $state])
                ->asArray()
                ->one();

            if (empty($cusQrCodeInfo)) { //走旧的流程
                return $this->oldReport($cusQrCodeInfo);
            }

            if (!in_array($cusQrCodeInfo['platform'], [reportEnum::TIKTOL, reportEnum::QUICKLY, reportEnum::WECHAT, reportEnum::ADQ, reportEnum::PANGOLIN])) { //报错
                $error = '上报时通过state：' . $this->message['state'] . ',获取到的渠道不是：新1、新25、新10、新16，新2，请核对' . "\n\n";
                $error .= '> apiLog:code编号：' . $this->serial_number;
                throw new Exception($error);
            }

            if ($cusQrCodeInfo['platform'] == reportEnum::WECHAT) { //上报新16
                // (new PromoteReport($unionResult['cl'], ReportEventEnum::addfans, $unionResult['params'], $this->message['ComCode']))->wechat($unionid, $this->message['ComCode']);
                $wxcomCache->destroy($unionid);
                $wxcomCache->destroy('unionid:' . $unionid);
                return true;
            }

            if ($cusQrCodeInfo['platform'] == reportEnum::ADQ) {
                //    (new PromoteReport($unionResult['cl'], ReportEventEnum::addfans, $unionResult['params'], $this->message['ComCode']))->wechat_adq($unionid, $this->message['ComCode']);
                //     $wxcomCache->destroy($unionid);
                // $wxcomCache->destroy('unionid:'.$unionid);
                return true;
            }

            $this->oldReport($cusQrCodeInfo);

            return true;
        } catch (Exception $ex) {
            $apiLog = new ApiLog();
            $apiLog->type = 'error';
            $apiLog->code = $this->serial_number;
            $apiLog->content = json_encode($this->message, JSON_UNESCAPED_UNICODE);
            $apiLog->desc = $ex->getMessage();
            $apiLog->save();
            return false;
        }
    }

    /**
     * 旧的上报流程
     *
     * @param $cusQrCodeInfo
     * @return bool
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public function oldReport($cusQrCodeInfo)
    {
        $wxcomCache = new WxcomCache();
        $unionid = $this->customerInfo['external_contact']['unionid'];
        $unionResult = $wxcomCache->get('unionid:' . $unionid);
        if (empty($unionResult)) {
            $unionResult = $wxcomCache->get($unionid);
        }

        if (empty($unionResult) || !isset($unionResult['cl']) || empty($unionResult['cl'])) return true; //判断用户行为记录如果不存在，则不上报

        $report = LandPageReport::find()->select('report_platform')->where(['params' => $unionResult['cl']])->asArray()->one();
        if (empty($report)) {
            $error = 'cl：' . $unionResult['cl'] . ',获取的落地页不存在，请核对' . "\n\n";
            $error .= '> apiLog:code编号：' . $this->serial_number;
            Yii::$app->notice->AdReport('企微加粉回调时,通过cl获取落地页失败', '', $error);
            throw new Exception($error);
        }

        if ($cusQrCodeInfo && $report['report_platform'] != $cusQrCodeInfo['platform']) {
            $error = 'cl：' . $unionResult['cl'] . ',获取的落地页渠道为：' . (reportEnum::getValue($report['report_platform']) ?: '空') . "\n\n";
            $error .= '> state：' . $this->message['State'] . ',获取的渠道为：' . (reportEnum::getValue($cusQrCodeInfo['platform']) ?: '空') . "\n\n";
            $error .= '> apiLog:code编号：' . $this->serial_number;
            Yii::$app->notice->AdReport('上报平台与活码渠道不一致', '', $error);
            throw new Exception($error);
        }

        if ($unionResult['share'] == self::share) {
            $wxcomCache->newHset(WxcomCache::Pre . $unionResult['cl'] . '_' . $unionResult['date'] . ':' . ReportEventEnum::share_addfans, $unionid, $unionResult['params']);
            return true;
        }

        if ($unionResult['event'] == ReportEventEnum::CLICK_CONSULT) {
            $addData = $wxcomCache->newHget(WxcomCache::Pre . $unionResult['cl'] . '_' . $unionResult['date'] . ':' . ReportEventEnum::CLICK_CONSULT, $unionid);
        } else {
            $addData = $wxcomCache->newHget(WxcomCache::Pre . $unionResult['cl'] . '_' . $unionResult['date'] . ':' . ReportEventEnum::qrcode, $unionid);
        }

        $addData['unionid'] = $addData['unionid'] ?: $unionid;
        $addData['event'] = ReportEventEnum::addfans;
        $addData['time'] = DateHelper::toDate(time());

        //判断加粉上报是否成功把第三方返回值回传到redis储存
        $AdReportService = new PromoteReport($unionResult['cl'], ReportEventEnum::addfans, $unionResult['params'], $this->message['ComCode']);
        $result = $AdReportService->checkReport();
        if ($result == false) return false;

        //上报成功后，删除unionid记录
        $wxcomCache->destroy($unionid);
        $wxcomCache->destroy('unionid:' . $unionid);

        $addData['third_callback'] = json_encode($result, JSON_UNESCAPED_UNICODE);
        $wxcomCache->newHset(WxcomCache::Pre . $unionResult['cl'] . '_' . $unionResult['date'] . ':' . ReportEventEnum::addfans, $unionid, json_encode($addData));

        //匹配微信链路加粉数据
        $this->wechatAddfans($addData);

        $apiLog = new ApiLog();
        $apiLog->type = ReportEventEnum::addfans;
        $apiLog->code = $this->serial_number;
        $apiLog->content = '满足落地页条件的数据源记录';
        $apiLog->desc = '当前渠道：' . $unionResult['cl'];
        $apiLog->callback_pack = json_encode($this->message, JSON_UNESCAPED_UNICODE);
        if (!$apiLog->save()) throw new Exception('上报成功：新增数据失败' . current($apiLog->getFirstErrors()));

        $params = json_decode($unionResult['params'], true);
        $cusId = CusCustomer::find()->where(['external_user_id' => $this->customerInfo['external_contact']['external_userid']])->select('id')->scalar();
        /** @var CusCustomerUser $cusUser  */
        $cusUser = CusCustomerUser::find()->where(['cus_id' => $cusId, 'wxcom_user_id' => $this->message['UserID']])->one();

        $adid = '';
        if (isset($params['adid']) && $params['adid']) {
            $adid = $params['adid'];
        }

        if (isset($params['promotionid']) && $params['promotionid']) {
            $adid = $params['promotionid'];
        }

        if ($cusId && $cusUser && !empty($AdReportService->callback)) {
            $cusUser->callback = $AdReportService->callback;
            $cusUser->adid = $adid;
            if (!$cusUser->save(false)) throw new Exception('保存callback报错' . current($cusUser->getFirstErrors()));
        }

        if (empty($cusQrCodeInfo)) {
            return true;
        }

        UpdateCustomerReportInfoJob::addJob([
            'adid' => $adid,
            'mid3' => ArrayHelper::getValue($params, 'mid3', ''),
            'csite' => ArrayHelper::getValue($params, 'csite', 0),
            'platform' => $cusQrCodeInfo['platform'],
            'wxcomCusId' => $this->customerInfo['external_contact']['external_userid'],
            'wxcomUserId' => $this->message['UserID'],
            'callback' => $AdReportService->callback,
            'cl_code' => $unionResult['cl'],
            'ua' => $addData['UA'],
        ]);

        return true;
    }

    /**
     * 修改客户推广信息
     *
     * @param $adid
     * @param $platform
     * @param $wxcomCusId
     * @param $wxcomUserId
     * @param $callback
     * @param $cl_code
     * @param $ua
     * @param $mid3
     * @param $csite
     * @return bool
     * @throws Exception
     */
    public function updateCustomerReportInfo($adid, $platform, $wxcomCusId, $wxcomUserId, $callback, $cl_code, $ua = null, $mid3 = '', $csite = 0)
    {
        $cusId = CusCustomer::find()->where(['external_user_id' => $wxcomCusId])->select('id')->scalar();
        if (empty($cusId)) throw new Exception('找不到对应客户');

        /**@var CusCustomerUser $cusUser */
        $cusUser = CusCustomerUser::find()->where(['cus_id' => $cusId, 'wxcom_user_id' => $wxcomUserId])->one();
        if (!$cusUser) throw new Exception('找不到客户客服对应关系');

        $data = $this->getReportParams($adid, $wxcomUserId, $platform, $cusUser->entity_id);

        if (empty($cusUser->channel_id)) {
            $cusUser->channel_id = ArrayHelper::getValue($data, 'channel_id', 0);
        }
        if (empty($cusUser->project_id) || !$cusUser->project_id) {
            $cusUser->project_id = ArrayHelper::getValue($data, 'project_id', 0);
        }
        if (empty($cusUser->adid)) {
            $cusUser->adid = ArrayHelper::getValue($data, 'adid', '');
        }
        if (empty($cusUser->callback)) {
            $cusUser->callback = $callback ?: '';
        }
        if (empty($cusUser->mid3)) {
            $cusUser->mid3 = $mid3 ?: '';
        }
        if (empty($cusUser->csite)) {
            $cusUser->csite = $csite ?: "0";
        }
        $cusUser->responsible_id = ArrayHelper::getValue($data, 'responsible_id', 0);
        $cusUser->link_id = ArrayHelper::getValue($data, 'link_id', 0);
        $cusUser->direction_id = ArrayHelper::getValue($data, 'direction_id', 0);
        $cusUser->sub_advertiser_name = ArrayHelper::getValue($data, 'sub_advertiser_name', '');
        $cusUser->sub_advertiser_id = ArrayHelper::getValue($data, 'sub_advertiser_id', 0);
        $cusUser->cl_code = $cl_code ?: '';
        $cusUser->fillLocationFromUA($ua);
        if (!$cusUser->save()) throw new Exception('客户客服关系表保存失败：' . current($cusUser->getFirstErrors()));
        //重粉已做、重粉已定上报
        $cusUser->heavyPowderReport();

        return true;
    }

    /**
     * 新16上报回调处理
     *
     * @param string $unionId 微信联合ID
     * @param string $adid 广告ID
     * @param string $trace_id 追踪ID
     * @param int $click_time 点击时间
     * @param string $advertiserId 广告主ID
     * @param string $callback 回调链接
     * @return bool
     * @throws Exception
     */
    public function wxcomReport($unionId, $adid, $trace_id, $click_time = 0, $advertiserId = '', $callback = '')
    {
        if (empty($adid) || !$click_time) return false;

        $current_time = time();
        if ($click_time < $current_time - 60 * 60 * 24) {
            return false;
        }

        $cusIds = CusCustomer::find()->where(['unionid' => $unionId])->select('id')->orderBy('id desc')->column();
        if (empty($cusIds)) throw new Exception('找不到对应客户');
        /** @var CusCustomerUser $cusUser  */
        $cusUser = CusCustomerUser::find()
            ->where(['in', 'cus_id', $cusIds])
            ->andWhere([
                'channel_id' => 2,
                'add_way' => [1, 16, 300],
            ])
            ->andWhere(['>', 'add_time', $click_time - 24 * 60 * 60])
            ->orderBy('add_time desc')
            ->one();
        if (empty($cusUser)) throw new Exception('找不到客户客服对应关系');

        //该客户信息已补全，无需再处理'
        if ($cusUser->responsible_id > 0 && $cusUser->adid != '' && $cusUser->link_id > 0 && $cusUser->mid3 != '') {
            return true;
        }

        $sub = null;
        //广告主ID存在时处理
        if ($advertiserId) {
            $sub = AdsAccountSub::find()->andWhere(['sub_advertiser_id' => $advertiserId])->one();
            if (!$sub) {
                Yii::$app->feishuNotice->error('新16上报成功回调后广告子账户不存在', '', '子广告账户ID：' . $sub['sub_advertiser_id']);
                throw new Exception('上报后广告子账户不存在');
            }
            if (empty($sub['responsible_id']) || empty($sub['promote_id']) || empty($sub['link_id']) || empty($sub['project_id'])) {
                Yii::$app->feishuNotice->error('上报后广告账户信息不全', '', '子广告账户ID：' . $sub['sub_advertiser_id']);
            }
            if ($cusUser->qrcode_created_by != $sub->responsible_id) {
                Yii::error('账号负责人与活码归属不一致，trace_id：' . $trace_id, 'wxcom_report');
                throw new Exception('账号负责人与活码归属不一致');
            }

            $cusUser->responsible_id = $sub->responsible_id;
            $cusUser->link_id = $sub->link_id;
            $cusUser->direction_id = $sub->direction_id;
            $cusUser->adid = $adid;
            $cusUser->sub_advertiser_name = $sub->sub_advertiser_name;
            $cusUser->sub_advertiser_id = $advertiserId;
        } else {
            $data = $this->getReportParams($adid, '', PlatformEnum::WECHAT, $cusUser->entity_id);
            $responsibleId = ArrayHelper::getValue($data, 'responsible_id', 0);

            if ($cusUser->qrcode_created_by != $responsibleId) {
                Yii::error('账号负责人与活码归属不一致，trace_id：' . $trace_id, 'wxcom_report');
                throw new Exception('账号负责人与活码归属不一致');
            }

            $cusUser->responsible_id = $responsibleId;
            $cusUser->link_id = ArrayHelper::getValue($data, 'link_id', 0);
            $cusUser->direction_id = ArrayHelper::getValue($data, 'direction_id', 0);
            $cusUser->adid = ArrayHelper::getValue($data, 'adid', '');
            $cusUser->sub_advertiser_name = ArrayHelper::getValue($data, 'sub_advertiser_name', '');
            $cusUser->sub_advertiser_id = ArrayHelper::getValue($data, 'sub_advertiser_id', 0);
        }
        $cusUser->callback = $trace_id;

        // 处理创意组件信息
        $mid3 = '';
        $firstVideoComponentId = null;
        $creativeComponentsInfo = [];
        if (!$sub || !$callback) {
            Yii::error('新16上报成功回调后广告子账户不存在或回调链接不存在，trace_id：' . $trace_id, 'wxcom_report');
            goto save_and_return;
        }

        $wxcomCache = new WxcomCache();
        $minId = $wxcomCache->get('wecom_callback_id');

        if (empty($minId)) {
            $oneDayAgo = time() - 86400;
            $minId = WecomCallback::find()
                ->select('id')
                ->where(['>=', 'created_at', $oneDayAgo])
                ->orderBy('id ASC')
                ->scalar();

            if ($minId) {
                $wxcomCache->set('wecom_callback_id', $minId, 86400);
            } else {
                $minId = 0;
            }
        }

        $wecomCallback = WecomCallback::find()
            ->select('content')
            ->where(['callback' => $callback])
            ->andWhere(['like', 'content', 'creative_components_info'])
            ->andFilterWhere(['>=', 'id', $minId])
            ->asArray()
            ->one();

        if ($wecomCallback) {
            $creativeComponentsInfo = json_decode($wecomCallback['content'], true)['creative_components_info'] ?? [];
            $creativeComponentsInfo = is_string($creativeComponentsInfo) ? json_decode($creativeComponentsInfo, true) : $creativeComponentsInfo;
        }
        if (empty($creativeComponentsInfo)) {
            Yii::error('新16上报成功回调后创意组件信息不存在，trace_id：' . $trace_id, 'wxcom_report');
            goto save_and_return;
        }

        $videoComponents = array_filter($creativeComponentsInfo, function ($component) {
            return isset($component['type'], $component['id']) && $component['type'] === 'VIDEO';
        });
        if (empty($videoComponents)) {
            Yii::error('新16上报成功回调后视频组件信息不存在，trace_id：' . $trace_id, 'wxcom_report');
            goto save_and_return;
        }

        $firstVideoComponentId = array_values($videoComponents)[0]['id'];
        $accessToken = AdsAccount::find()
            ->where(['id' => $sub->td_id])
            ->select('access_token')
            ->scalar();

        try {
            $component = Adq::getComponent($accessToken ?: '', $advertiserId, $firstVideoComponentId);
            $mid3 = ArrayHelper::getValue(current($component), 'component_value.video.value.video_id', '');
        } catch (\Exception $e) {
            Yii::error('通过接口获取创意组件失败【' . $e->getMessage() . '】，trace_id：' . $trace_id, 'wxcom_report');
        }

        save_and_return:
        $cusUser->mid3 = $mid3;
        if (empty($mid3)) {
            Yii::error('新16上报成功回调后mid3不存在，trace_id：' . $trace_id, 'wxcom_report');
        }
        if (!$cusUser->save()) {
            throw new Exception('客户客服关系表保存失败：' . current($cusUser->getFirstErrors()));
        }
        return true;
    }

    /**
     * 获取上报推广信息
     *
     * @param $adid
     * @param $wxcomUserId
     * @param $platform
     * @param $entity_id
     * @return array
     * @throws Exception
     */
    public function getReportParams($adid, $wxcomUserId, $platform, $entity_id): array
    {
        $program = AdsAccountProgram::find()->alias('ap')
            ->select('ap.*,as.responsible_id,as.promote_id,as.link_id,as.project_id,as.direction_id,pc.platform,as.sub_advertiser_id,as.sub_advertiser_name')
            ->leftJoin(['{{%ads_account_sub}} as'], 'as.id = ap.ads_sub_id')
            ->leftJoin(['{{%promote_channel}} pc'], 'pc.id = as.promote_id')
            ->where(['campaign_id' => $adid])
            ->andFilterWhere(['ap.entity_id' => $entity_id])
            ->asArray()
            ->one();

        $channel_id = ChannelService::find()->select('id')->where(['platform' => $platform, 'entity_id' => $entity_id])->scalar();
        $error = 'adid：' . $adid . ',external_user_id：' . $this->customerInfo['external_contact']['external_userid'] . ',wxcom_user_id：' . $wxcomUserId;
        if (empty($program)) {
            $data = [
                'adid' => $adid,
                'date' => DateHelper::toDate(time()),
                'channel_id' => $channel_id ?: 0,
            ];
            return $data;
        }

        // if($program['responsible_id'] == 34){//易风
        //     $url = 'https://api.day.app/nZGQtVWroA3Mk3U9hKkKaP/'.$program['sub_advertiser_name'].'/恭喜新增一个客户';
        //     Tool::curlRequest($url);
        // }

        $newPlatform = $platform;
        if ($platform == reportEnum::PANGOLIN) $newPlatform = reportEnum::TIKTOL;

        if ($program['platform'] != $newPlatform) {
            Yii::$app->notice->AdReport('adid获取到的渠道与上报渠道不一致', '', 'adid：' . $adid);
            throw new Exception('adid获取到的渠道与上报渠道不一致,' . $error);
        }

        $data = [
            'responsible_id' => $program['responsible_id'],
            'channel_id' => $channel_id ?: 0,
            'link_id' => $program['link_id'],
            'project_id' => $program['project_id'],
            'direction_id' => $program['direction_id'],
            'adid' => $adid,
            'date' => DateHelper::toDate(time()),
            'sub_advertiser_id' => $program['sub_advertiser_id'],
            'sub_advertiser_name' => $program['sub_advertiser_name'],
        ];
        return $data;
    }

    /**
     * 订金核销后-上报：新25、新1渠道
     *
     * @param string $order_no 订单号
     * @param int $customer_user_id 表erp_wxcom_cus_customer_user 的id
     * @param int $channel_id 渠道ID
     * @param string $callback
     * @param string $unionid
     * @param string $developer_id
     *
     * @return array|bool
     * @throws \Exception
     */
    public static function orderReport($order_no, $customer_user_id, $channel_id, $callback, $unionid, $developer_id)
    {
        $platform = ChannelService::find()->select('platform')->where(['id' => $channel_id])->scalar();

        $title = reportEnum::getValue($platform);

        $AdReportService = new PromoteReport('', '', '', '');
        switch ($platform) {
            case reportEnum::QUICKLY: //新10下定上报
                if (empty($callback)) return false;
                list($result) = $AdReportService->quickly(['callback' => $callback], true, 3);
                break;
            case reportEnum::PANGOLIN:
            case reportEnum::TIKTOL: //新1
                if (empty($callback)) return false;
                $result = $AdReportService->orderReport($callback);
                break;
            case reportEnum::ADQ:
                if (empty($unionid)) return false;
                $cusUser = CusCustomerUser::findOne($customer_user_id);
                if ($cusUser && $cusUser->sub_advertiser_id == '26054605') {
                    $result = $AdReportService->underOrderADQ($unionid, $developer_id, $order_no);
                }
                break;
            case reportEnum::WECHAT:
                if (empty($unionid)) return false;
                $result = $AdReportService->underOrderPKAM($unionid, $developer_id, $order_no, $callback);
                $callback = $unionid;
                break;
            default:
                return false;
        }

        $result['order_no'] = $order_no;
        if ($result['code'] == 0) {
            $type = 'orderReport';
            Yii::$app->notice->AdReport('下定上报' . $title . '成功', '', '订单号：' . $order_no);
        } else {
            $type = 'orderReportError';
            Yii::$app->notice->AdReport('下定上报' . $title . '失败', '', '失败原因：' . json_encode($result, JSON_UNESCAPED_UNICODE));
        }

        $apiLog = new ApiLog();
        $apiLog->type = $type;
        $apiLog->code = (string)$customer_user_id;
        $apiLog->content = $title . '上报';
        $apiLog->desc = $callback;
        $apiLog->callback_pack = json_encode($result, JSON_UNESCAPED_UNICODE);
        $apiLog->save();

        return $result;
    }

    /**
     * 匹配微信链路加粉数据
     *
     * @param array $params
     * @return bool
     */
    public function wechatAddfans($params = [])
    {
        if (!isset($params) || !isset($params['open_kfid'])) return false;
        $wechat_servicer = WechatServicer::find()->select('id')->andWhere(['open_kfid' => $params['open_kfid']])->asArray()->one();
        if (empty($wechat_servicer)) return false;

        $wechat_servicer_statistics = WechatServicerStatistics::find()->andWhere(['servicer_id' => $wechat_servicer['id'], 'date' => date('Ymd')])->one();
        if ($wechat_servicer_statistics) {
            $wechat_servicer_statistics->addfans++;
        } else {
            $wechat_servicer_statistics = new WechatServicerStatistics();
            $wechat_servicer_statistics->addfans = 1;
            $wechat_servicer_statistics->servicer_id = $wechat_servicer['id'];
            $wechat_servicer_statistics->date = date('Ymd');
        }
        $wechat_servicer_statistics->save(false);

        return true;
    }

    /**
     * PKAM上报
     *
     * @return bool
     * @throws \Exception
     */
    public function pkam()
    {
        try {
            $order_id = Yii::$app->request->get("order_id", 0);
            /**@var OrderHeader $order */
            $order = OrderHeader::find()->select('id,order_no,customer_user_id')->where(['id' => $order_id])->one();
            if (empty($order)) throw new Exception('订单不存在');

            if (!in_array($order->order_status, [OrderHeaderStatusEnum::STATUS_COMPLETED, OrderHeaderStatusEnum::STATUS_OTHER_SETTLEMENT])) {
                if (empty($order)) throw new Exception('该订单状态不正确');
            }

            /**@var $customer_user_data CusCustomerUser */
            $customer_user_data = CusCustomerUser::find()->alias('ccu')
                ->select('cc.unionid,wc.code,ccu.callback')
                ->leftJoin(['{{%wxcom_cus_customer}} cc'], 'cc.id = ccu.cus_id')
                ->leftJoin(['{{%wxcom_com}} wc'], 'wc.id = cc.com_id')
                ->andWhere(['ccu.id' => $order->customer_user_id])
                ->asArray()
                ->one();

            if (empty($customer_user_data)) throw new Exception('该客户不存在');

            $res = (new PromoteReport('', '', '', ''))->wechatPKAM($customer_user_data['unionid'], $customer_user_data['code'], $order->order_no, $customer_user_data['callback']);

            if (!$res) throw new Exception('上报失败');

            return true;
        } catch (Exception $ex) {
            $this->error = $ex->getMessage();
            return false;
        }
    }

    /**
     * 处理动态活码上报
     *
     * @return bool
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public function dynamicQrcodeReport()
    {
        $cusQrCodeInfo = $this->cusQrCodeInfo;
        $cusQrCodeInfo['platform'] = CusDynamicQrcode::find()->alias('q')->leftJoin('{{%promote_channel}} c', 'c.id = q.channel_id')
            ->select('c.platform')
            ->where(['q.id' => $cusQrCodeInfo['qrcode_id']])->scalar();


        if (empty($cusQrCodeInfo)) {    //推广信息找不到或失效，则不上报
            return true;
        }

        if (!in_array($cusQrCodeInfo['platform'], [reportEnum::TIKTOL, reportEnum::QUICKLY, reportEnum::WECHAT, reportEnum::ADQ, reportEnum::PANGOLIN])) { //报错
            $error = '上报时通过state：' . $this->message['State'] . ',获取到的渠道不是：新1、新25、新10、新16，新2，请核对' . "\n\n";
            $error .= '> apiLog:code编号：' . $this->serial_number;
            throw new Exception($error);
        }

        $wxcomCache = new WxcomCache();
        $unionid = $this->customerInfo['external_contact']['unionid'];
        if ($cusQrCodeInfo['platform'] == reportEnum::WECHAT) { //上报新16
            // (new PromoteReport($cusQrCodeInfo['cl'], ReportEventEnum::addfans, $cusQrCodeInfo['params'], $this->message['ComCode']))->wechat($unionid, $this->message['ComCode']);
            $wxcomCache->destroy($unionid);
            $wxcomCache->destroy('unionid:' . $unionid);
            return true;
        }

        if ($cusQrCodeInfo['platform'] == reportEnum::ADQ) {
            // (new PromoteReport($cusQrCodeInfo['cl'], ReportEventEnum::addfans, $cusQrCodeInfo['params'], $this->message['ComCode']))->wechat_adq($unionid, $this->message['ComCode']);
            $wxcomCache->destroy('unionid:' . $unionid);
            return true;
        }

        $this->dynamicReport($cusQrCodeInfo);

        return true;
    }

    /**
     * 获客助手-短链链接上报
     */
    public function linkReport()
    {
        // 获取缓存数据
        $reqId = str_replace(WxcomCache::LINK, '', $this->message['State']);
        $params = Yii::$app->cache->get(WxcomCache::LINK . $reqId);

        $this->reportBase($reqId, $params);
    }

    /**
     * 获客助手-二维码-弹窗
     */
    public function cusAcquisitionReport()
    {
        // 获取缓存数据
        $reqId = str_replace(WxcomCache::CUS_ACQUISITION, '', $this->message['State']);
        $params = Yii::$app->cache->get(WxcomCache::CUS_ACQUISITION . $reqId);

        Yii::info('获客助手-二维码-弹窗,cl:' . $params['cl'] . ',reqId:' . $reqId . ',数据：' . json_encode($params, 256));
        $this->reportBase($reqId, $params);
    }

    public function landPageInfo($cl)
    {
        $landInfo = LandPageReport::find()->select('m.username,m.feishu_userid,lp.id,lp.name')->alias('lpr')
            ->leftJoin('{{%land_page}} lp', 'lp.id = lpr.land_id')->where(['params' => $cl])
            ->leftJoin('{{%backend_member}} m', 'm.id = lp.owner_id')->where(['params' => $cl])
            ->asArray()
            ->one();

        return $landInfo;
    }

    public function reportBase($reqId, $params)
    {
        // 验证数据及 cl 是否存在
        if (empty($params) || empty($params['cl'])) {
            return true;
        }
        Yii::info('11111----' . $params['cl'] . ',reqId:' . $reqId);
        // 验证 cl 对应的上报平台是否存在
        $reportPlatform = LandPageReport::find()->select('report_platform')->where(['params' => $params['cl']])->scalar();
        if (empty($reportPlatform)) {
            Yii::info('cl:' . $params['cl'] . ',reqId:' . $reqId . '获取落地页的上报平台不存在', 'report');
            $error = 'cl：' . $params['cl'] . ',获取的落地页不存在，请核对' . "\n\n";
            $error .= '> apiLog:code编号：' . $this->serial_number;

            //通知推广
            $group = FeishuExamineService::arrGroup('GGYYGTQ');
            $landInfo = $this->landPageInfo($params['cl']);
            $content =  '<at user_id="' . $landInfo['feishu_userid'] . '">' . $landInfo['username'] . '</at>' .
                '落地页ID:' . $landInfo['id'] . ',落地页名称是:"' . $landInfo['name'] . '"未配置上报设置请尽快设置,reqId:' . $reqId;
            Yii::$app->feishuNotice->text($content, $group['chat_id']);
            throw new Exception($error);
        }
        Yii::info('22222----' . $params['cl'] . ',reqId:' . $reqId);
        // 验证上报平台范围
        if (!in_array($reportPlatform, [reportEnum::TIKTOL, reportEnum::QUICKLY, reportEnum::PANGOLIN, reportEnum::WECHAT])) {
            $error = '上报时通过state：' . $this->message['State'] . ',获取到的渠道不是：新1、新25、新10、新16,，请核对' . "\n\n";
            $error .= '> apiLog:code编号：' . $this->serial_number;
            Yii::$app->feishuNotice->error('reportBase上报错误,cl:' . $params['cl'] . ',reqId:' . $reqId . ',渠道有误,该渠道不在新1、新25、新10、新16,该平台是:' . $reportPlatform);
            throw new Exception($error);
        }
        Yii::info('33333----' . $params['cl'] . ',reqId:' . $reqId);
        // 构造 addData 参数
        $addData = $params;
        $addData['unionid'] = $addData['unionid'] ?: $this->customerInfo['external_contact']['unionid'];
        $addData['event'] = ReportEventEnum::addfans;
        $addData['time'] = DateHelper::toDate(time());

        $AdReportService = new PromoteReport($params['cl'], ReportEventEnum::addfans, json_encode($params), $this->message['ComCode']);
        if (in_array($reportPlatform, [reportEnum::TIKTOL, reportEnum::QUICKLY, reportEnum::PANGOLIN])) {
            Yii::info('4444----' . $params['cl'] . ',reqId:' . $reqId);
            // 判断加粉上报是否成功把第三方返回值回传到redis储存
            $result = $AdReportService->checkReport();
            if ($result == false) {
                return false;
            }
        } else { //新16上报
            Yii::info('5555----' . $params['cl'] . ',reqId:' . $reqId);
            // $result = $AdReportService->wechat($this->customerInfo['external_contact']['unionid'], $this->message['ComCode'], $params['wx_traceid']);
            $AdReportService->callback = $params['wx_traceid'];
        }
        Yii::info('666666----' . $params['cl'] . ',reqId:' . $reqId);
        $addData['third_callback'] = json_encode($result, JSON_UNESCAPED_UNICODE);
        $wxcomCache = new WxcomCache();
        $wxcomCache->newHset(WxcomCache::Pre . $params['cl'] . '_' . $params['date'] . ':' . ReportEventEnum::addfans, $reqId, json_encode($addData));
        Yii::info('77777----' . $params['cl'] . ',reqId:' . $reqId);
        // 保存日志
        $apiLog = new ApiLog();
        $apiLog->type = ReportEventEnum::addfans;
        $apiLog->code = $this->serial_number;
        $apiLog->content = '满足落地页条件的数据源记录';
        $apiLog->desc = '当前渠道：' . $params['cl'];
        $apiLog->callback_pack = json_encode($this->message, JSON_UNESCAPED_UNICODE);
        if (!$apiLog->save()) {
            throw new Exception('上报成功：新增数据失败' . current($apiLog->getFirstErrors()));
        }
        Yii::info('88888----' . $params['cl'] . ',reqId:' . $reqId);
        $adid = '';
        if ($reportPlatform == reportEnum::WECHAT) {
            $adid = $params['wx_aid'];
        } else {
            if (isset($params['adid']) && $params['adid']) {
                $adid = $params['adid'];
            }

            if (isset($params['promotionid']) && $params['promotionid']) {
                $adid = $params['promotionid'];
            }
        }

        // 维护回调数据到客户加粉表
        if (!empty($AdReportService->callback)) {
            $cusId = CusCustomer::find()->where(['external_user_id' => $this->customerInfo['external_contact']['external_userid']])->select('id')->scalar();
            if (!empty($cusId)) {
                /** @var CusCustomerUser $cusUser */
                $cusUser = CusCustomerUser::find()->where(['cus_id' => $cusId, 'wxcom_user_id' => $this->message['UserID']])->one();
                if ($cusUser) {
                    $cusUser->callback = $AdReportService->callback;
                    $cusUser->adid = $adid;
                    if (!$cusUser->save(false)) throw new Exception('保存callback报错' . current($cusUser->getFirstErrors()));
                }
            }
        }

        UpdateCustomerReportInfoJob::addJob([
            'adid' => $adid,
            'mid3' =>  ArrayHelper::getValue($params, 'mid3', ''),
            'csite' =>  ArrayHelper::getValue($params, 'csite', 0),
            'platform' => $reportPlatform,
            'wxcomCusId' => $this->customerInfo['external_contact']['external_userid'],
            'wxcomUserId' => $this->message['UserID'],
            'callback' => $AdReportService->callback,
            'cl_code' => $params['cl'],
            'ua' => $addData['ua'],
        ]);

        return true;
    }

    /**
     * 动态活码的上报流程
     *
     * @param $cusQrCodeInfo
     * @return bool
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public function dynamicReport($cusQrCodeInfo)
    {
        $wxcomCache = new WxcomCache();
        if (empty($cusQrCodeInfo) || !isset($cusQrCodeInfo['cl']) || empty($cusQrCodeInfo['cl'])) return true; //判断用户行为记录如果不存在，则不上报

        $report = LandPageReport::find()->select('report_platform')->where(['params' => $cusQrCodeInfo['cl']])->asArray()->one();
        if (empty($report)) {
            $error = 'cl：' . $cusQrCodeInfo['cl'] . ',获取的落地页不存在，请核对' . "\n\n";
            $error .= '> apiLog:code编号：' . $this->serial_number;
            Yii::$app->notice->AdReport('企微加粉回调时,通过cl获取落地页失败', '', $error);
            throw new Exception($error);
        }

        if ($report['report_platform'] != $cusQrCodeInfo['platform']) {
            $error = 'cl：' . $cusQrCodeInfo['cl'] . ',获取的落地页渠道为：' . (reportEnum::getValue($report['report_platform']) ?: '空') . "\n\n";
            $error .= '> state：' . $this->message['State'] . ',获取的渠道为：' . (reportEnum::getValue($cusQrCodeInfo['platform']) ?: '空') . "\n\n";
            $error .= '> apiLog:code编号：' . $this->serial_number;
            Yii::$app->notice->AdReport('上报平台与活码渠道不一致', '', $error);
            throw new Exception($error);
        }

        if ($cusQrCodeInfo['share'] == self::share) {
            $wxcomCache->newHset(WxcomCache::Pre . $cusQrCodeInfo['cl'] . '_' . $cusQrCodeInfo['date'] . ':' . ReportEventEnum::share_addfans, $cusQrCodeInfo['req_id'], $cusQrCodeInfo['params']);
            return true;
        }

        if ($cusQrCodeInfo['event'] == ReportEventEnum::CLICK_CONSULT) {
            $addData = $wxcomCache->newHget(WxcomCache::Pre . $cusQrCodeInfo['cl'] . '_' . $cusQrCodeInfo['date'] . ':' . ReportEventEnum::CLICK_CONSULT, $cusQrCodeInfo['req_id']);
        } else {
            $addData = $wxcomCache->newHget(WxcomCache::Pre . $cusQrCodeInfo['cl'] . '_' . $cusQrCodeInfo['date'] . ':' . ReportEventEnum::qrcode, $cusQrCodeInfo['req_id']);
        }

        $addData['unionid'] = $addData['unionid'] ?: $this->customerInfo['external_contact']['unionid'];
        $addData['event'] = ReportEventEnum::addfans;
        $addData['time'] = DateHelper::toDate(time());

        //判断加粉上报是否成功把第三方返回值回传到redis储存
        $AdReportService = new PromoteReport($cusQrCodeInfo['cl'], ReportEventEnum::addfans, $cusQrCodeInfo['params'], $this->message['ComCode']);
        $result = $AdReportService->checkReport();
        if ($result == false) return false;

        $addData['third_callback'] = json_encode($result, JSON_UNESCAPED_UNICODE);
        $wxcomCache->newHset(WxcomCache::Pre . $cusQrCodeInfo['cl'] . '_' . $cusQrCodeInfo['date'] . ':' . ReportEventEnum::addfans, $cusQrCodeInfo['req_id'], json_encode($addData));

        //匹配微信链路加粉数据
        $this->wechatAddfans($addData);

        $apiLog = new ApiLog();
        $apiLog->type = ReportEventEnum::addfans;
        $apiLog->code = $this->serial_number;
        $apiLog->content = '满足落地页条件的数据源记录';
        $apiLog->desc = '当前渠道：' . $cusQrCodeInfo['cl'];
        $apiLog->callback_pack = json_encode($this->message, JSON_UNESCAPED_UNICODE);
        if (!$apiLog->save()) throw new Exception('上报成功：新增数据失败' . current($apiLog->getFirstErrors()));

        $params = json_decode($cusQrCodeInfo['params'], true);
        $cusId = CusCustomer::find()->where(['external_user_id' => $this->customerInfo['external_contact']['external_userid']])->select('id')->scalar();
        /** @var CusCustomerUser $cusUser  */
        $cusUser = CusCustomerUser::find()->where(['cus_id' => $cusId, 'wxcom_user_id' => $this->message['UserID'], 'type' => CusCustomerUserTypeEnum::DYNAMIC_CODE])->one();

        $adid = '';
        if (isset($params['adid'])) {
            $adid = $params['adid'] ?: '';
        }

        if (isset($params['promotionid'])) {
            $adid = $params['promotionid'] ?: '';
        }

        if ($cusId && $cusUser && !empty($AdReportService->callback)) {
            $cusUser->callback = $AdReportService->callback;
            $cusUser->adid = $adid;
            if (!$cusUser->save(false)) throw new Exception('保存callback报错' . current($cusUser->getFirstErrors()));
        }

        if (empty($adid)) {
            return false;
        }

        UpdateCustomerReportInfoJob::addJob([
            'adid' => $adid,
            'mid3' => ArrayHelper::getValue($params, 'mid3', ''),
            'csite' => ArrayHelper::getValue($params, 'csite', 0),
            'platform' => $cusQrCodeInfo['platform'],
            'wxcomCusId' => $this->customerInfo['external_contact']['external_userid'],
            'wxcomUserId' => $this->message['UserID'],
            'callback' => $AdReportService->callback,
            'cl_code' => $cusQrCodeInfo['cl'],
            'ua' => $addData['UA'],
        ]);

        return true;
    }
}
