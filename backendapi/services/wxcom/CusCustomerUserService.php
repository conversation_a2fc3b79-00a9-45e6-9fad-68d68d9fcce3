<?php

namespace backendapi\services\wxcom;

use backendapi\services\MemberService;
use backendapi\services\promote\AnalysisService;
use common\enums\CusCustomerUserTypeEnum;
use common\enums\StoreBrand;
use common\helpers\TreeHelper;
use common\models\common\Department;
use common\models\wxcom\CusCustomer;
use common\models\wxcom\CusCustomerUser;
use common\enums\AdPositionEnum;
use common\enums\WxcomAddTypeEnum;
use Yii;
use yii\db\Expression;
use yii\helpers\ArrayHelper;

class CusCustomerUserService extends CusCustomerUser
{
    use \common\traits\ModelFieldExtend;
    public static $fieldExtends = [];

    public function attributeExtends()
    {
        return self::$fieldExtends;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function search($params = [], $is_export = false)
    {
        self::$fieldExtends = [
            'customer',
            'user_name',
            'add_time_text',
            'add_way_text',
            'tag_name_array',
            'channel_name',
            'link_name',
            'project_name',
            'responsible_name',
            'qrcode_created_person_name',
            'com_name',
            'location',
            'csite_text',
            'land_page_report_name',
            'video_img'
        ];
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query = self::find()->alias('ccu');
        $query->where(['ccu.entity_id' => Yii::$app->user->identity->current_entity_id]);
        if ($params['com_id'] || $params['cus_name']) {
            $query->joinWith(['customer']);
            $query->andFilterWhere(['=', CusCustomer::tableName() . '.com_id', $params['com_id']]);
            $query->andFilterWhere([
                'OR',
                ['like', CusCustomer::tableName() . '.name', $params['cus_name']],
                ['like', 'ccu.remark', $params['cus_name']]
            ]);
        }

        $adPositions = AdPositionEnum::getPositions();
        if ($params['csite']) {
            // 根据 csite 参数获取对应的范围数组
            if (isset($adPositions[$params['csite']])) {
                $ranges = $adPositions[$params['csite']];
                
                // 构建范围查询条件
                $rangeConditions = [];
                foreach ($ranges as $range) {
                    $rangeConditions[] = ['between', 'ccu.csite', $range['start'], $range['end']];
                }
                
                // 如果有多个范围，用 OR 连接
                if (count($rangeConditions) > 1) {
                    array_unshift($rangeConditions, 'OR');
                    $query->andWhere($rangeConditions);
                } elseif (count($rangeConditions) == 1) {
                    $query->andWhere($rangeConditions[0]);
                }
            }
        }

        if ($params['promote_account']) {
            $query->andFilterWhere([
                'OR',
                ['=', 'ccu.sub_advertiser_id', $params['promote_account']],
                ['like', 'ccu.sub_advertiser_name', $params['promote_account']]
            ]);
        }

        $query->with(['channel', 'project', 'link', 'responsible', 'user', 'qrcodeCreatedPerson', 'province', 'city', 'district', 'customer' => function ($query) {
            $query->with(['com']);
        }]);

        if ($params['type'] == CusCustomerUserTypeEnum::DYNAMIC_CODE) {
            $query->andFilterWhere(['ccu.dynamic_qrcode_id' => $params['qrcode_id']]);
        } else {
            $query->andFilterWhere(['state' => $params['qrcode_id']]);
        }

          //项目搜索
        $params['project_id'] = AnalysisService::getProjectId($params['project_type'], $params['project_id']);

        //不显示的添加方式
        $notShowAddWay = [WxcomAddTypeEnum::ADMIN];

        $query->andFilterWhere(['ccu.type' => $params['type']])
            ->andFilterWhere(['user_id' => $params['user_id']])
            ->andFilterWhere(['add_way' => $params['add_way']])
            ->andFilterWhere(['not in', 'add_way', $notShowAddWay])
            ->andFilterWhere(['is_extend' => $params['is_extend']])
            ->andFilterWhere(['<>', 'is_deleted', $params['status']])
            ->andFilterWhere(['qrcode_created_by' => $params['qrcode_user_id']])
            ->andFilterWhere(['ccu.channel_id' => $params['channel_id']])
            ->andFilterWhere(['ccu.mid3' => trim($params['mid3'])])
            ->andFilterWhere(['ccu.project_id' => $params['project_id']])
            ->andFilterWhere(['between', 'add_time', $params['start_time'], $params['end_time']]);

        if ($params['qrcode_dept_id']) {
            $deptInfo = Department::getInfoById($params['qrcode_dept_id']);
            $deptIds = Department::find()
                ->select('id')
                ->where(['deleted_at' => 0])
                ->andWhere(['like', 'tree', $deptInfo['tree'] . TreeHelper::prefixTreeKey($params['qrcode_dept_id']) . '%', false])
                ->column();
            $deptIds[] = $params['qrcode_dept_id'];
            $query->andFilterWhere(['in', 'qrcode_dept_id', $deptIds]);
        }
        if ($params['tag_ids']) {
            $tagIds = $params['tag_ids'];
            foreach ($tagIds as $tagId) {
                $query->andWhere(new Expression("FIND_IN_SET(:tagId, tag_ids)", [':tagId' => $tagId]));
            }
        }
        $scope = Yii::$app->services->scopeDataService->getScope();

        #判断是否是销售客服主管和超级管理员
        if (!MemberService::userIsAppointRole(Yii::$app->user->id, '销售客服主管') && !Yii::$app->services->auth->isSuperAdmin()) {
            $query->andFilterWhere(['OR', ['qrcode_created_by' => Yii::$app->user->id], ['qrcode_dept_id' => $scope]]);
        }

        if ($is_export) { //导出
            if (!empty($params['getTotal'])) return [[], $query->count()]; //获取总条数
        } else {
            $query->with(['adsMaterial']);
        }

        $totalCount = $query->count();
        $list = $query->offset($offset)->limit($limit)->orderBy('add_time DESC')->all();

        return [$list, $totalCount];
    }

    public static function getInfoById($id)
    {
        self::$fieldExtends = [
            'customer',
            'channel_name',
            'link_name',
            'project_name',
            'responsible_name',
            'qrcode_created_person_name',
        ];
        $query = self::find();
        $query->with(['channel']);
        $query->andFilterWhere(['=', 'id', $id]);
        $info = $query->one();
        $info = ArrayHelper::toArray($info);

        $cusUserList = CusCustomerUser::find()
            ->With(['user'])
            ->andWhere(['cus_id' => $info['cus_id']])
            ->orderBy('add_time DESC')
            ->all();

        $info['relation_user_list'] = [];
        foreach ($cusUserList as $cusUser) {
            $info['relation_user_list'][] = [
                'add_time' => date('Y-m-d H:i:s', $cusUser->add_time),
                'add_way' => $cusUser->getAddWayText(),
                'state' => $cusUser->state,
                'user_name' => $cusUser->user->name,
                'user_avatar' => $cusUser->user->avatar,
            ];
        }

        $info['system'] = StoreBrand::getBrandEnName($info['system']);

        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = self::find();
        $query->select('id,id as name');
        $query->andFilterWhere(['like', 'id', $params['keyword']]);
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    public static function find()
    {
        $query = parent::find();
        $query->where([self::tableName() . '.entity_id' => Yii::$app->user->identity->current_entity_id]);
        $scope = Yii::$app->services->scopeDataService->getScope();
        if (!empty($scope) && !MemberService::userIsAppointRole(Yii::$app->user->id, '销售客服主管')) {
            $query->andWhere([
                'or',
                ['qrcode_created_by' => Yii::$app->user->identity->id],
                ['in', 'qrcode_dept_id', $scope]
            ]);
        }
        return $query;
    }
}
