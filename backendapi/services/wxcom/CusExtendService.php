<?php

namespace backendapi\services\wxcom;

use auth\services\wxcom\CusExtendService as AuthCusExtendService;
use backendapi\models\wxcom\CusExtend;
use backendapi\models\wxcom\CusExtendDetail;
use common\components\wxcom\CusExtendSDK;
use common\enums\CusExtendDetailStatusEnum;
use common\enums\CusExtendTypeEnum;
use common\enums\WxcomErrorcodeEnum;
use common\helpers\ArrayHelper;
use common\models\wxcom\Com;
use common\models\wxcom\CusCustomerUser;
use common\models\wxcom\CusExtendJob;
use common\queues\CustomerInheritJob;
use Exception;
use Yii;
use yii\db\Expression;

class CusExtendService extends AuthCusExtendService
{
    /**
     * @var CusExtend
     */
    public static $modelClass = CusExtend::class;

    /**
     * 新增
     *
     * @param array $params
     * @return bool|void
     */
    public static function create($params)
    {
        return self::customerInheritData($params);
    }

    //失败信息整理
    public static function failMsgDealwith($num, $failMsg)
    {
        $msg = '';
        $wxcomerrcode = WxcomErrorcodeEnum::getMap();
        foreach ($failMsg as $k => $value) {
            if ($wxcomerrcode[$value['errcode']]) {
                $msg .= ' 错误信息:' . $wxcomerrcode[$value['errcode']];
            } else {
                $msg .= ' 错误数据:' . json_encode($value);
            }
            if ($k != $num - 1) {
                $msg .= '|';
            }
        }
        return $msg;
    }

    /**
     * 处理客户继承
     *
     * @param $params
     * @return array
     */
    public static function customerInheritData($params)
    {
        $notes = [];
        $trans = Yii::$app->db->beginTransaction();
        try {
            $model = new static::$modelClass();
            if (isset($model->scenarios()['create'])) {
                $model->scenario = 'create';
            }
            $model->attributes = $params;
            if (!$model->save()) {
                throw new Exception($model->getFirstErrMsg());
            }
            $isCheckAll = ArrayHelper::getValue($params, 'is_check_all', false);
            $checkCusUserIds = ArrayHelper::getValue($params, 'cus_user_ids', []);
            if (!$isCheckAll && (!is_array($checkCusUserIds) || !count($checkCusUserIds))) {
                throw new Exception('请选择需要继承的客户');
            }

            $cusUserQuery = CusCustomerUser::find()
                ->andWhere(['user_id' => $model->source_user_id])
                ->andWhere(['is_extend' => 0]);
            // TODO zkx 这边要不要考虑是否删除
            if (!$isCheckAll) {
                $cusUserQuery->andWhere(['id' => $checkCusUserIds]);
            }
            /** @var array<CusCustomerUser> $checkCusUserList */
            $checkCusUserList = $cusUserQuery->orderBy([new Expression("FIELD(add_way,202) ASC")])->all();
            if (!$checkCusUserList) {
                throw new Exception('暂无客户可继承');
            }

            $newCusExtend = [];
            foreach ($checkCusUserList as $checkCusUser) {
                //判断是否存在该任务
                $cusExtendJobId = CusExtendJob::find()
                    ->select('id')
                    ->andWhere(['cus_user_id' => $checkCusUser->id])
                    ->scalar();
                if ($cusExtendJobId) {
                    CusExtendJob::deleteAll(['id' => $cusExtendJobId]);
                }

                //检测是否满足转粉时间（在职继承90个工作日内，同一个客户只能被转粉2次）
                $executionAt = time();
                if ($model->type == CusExtendTypeEnum::INHERIT && $checkCusUser->add_way == 202) {
                    $day = $checkCusUser->add_time + (90 * 86400);
//                    $dayText = date('Y-m-d H:i:s', $day);
                    if ($day > time()) {
                        continue;
//                        $executionAt = $day;
//                        $notes[] = "客户“{$checkCusUser->customer->name}”,转移时间“{$dayText}”";
                    }
                }

                $newCusExtend[] = [
                    'extend_id' => $model->id,
                    'source_user_id' => $model->source_user_id,
                    'source_wxcom_user_id' => $model->sourceWxcomUser->wxcom_user_id,
                    'target_user_id' => $model->target_user_id,
                    'target_wxcom_user_id' => $model->targetWxcomUser->wxcom_user_id,
                    'cus_id' => $checkCusUser->cus_id,
                    'cus_user_id' => $checkCusUser->id,
                    'external_user_id' => $checkCusUser->customer->external_user_id,
                    'execution_at' => $executionAt,
                    'type' => $model->type,
                    'com_id' => $model->com_id,
                    'entity_id' => Yii::$app->user->identity->current_entity_id,
                    'created_by' => Yii::$app->user->id,
                    'created_at' => time()
                ];
            }

            //开始加入任务
            if ($newCusExtend) {
                foreach (array_chunk($newCusExtend, 1000) as $cusExtends) {
                    CusExtendJob::find()
                        ->createCommand()
                        ->batchInsert(CusExtendJob::tableName(), array_keys($cusExtends[0]), $cusExtends)
                        ->execute();
                }

                $trans->commit();
            }
        } catch (Exception $e) {
            $trans->rollBack();
            return ['code' => 422, 'message' => $e->getMessage()];
        }

        if ($notes) {
            $notes = '存在存期客户：' . implode(';',$notes);
        } else {
            $notes = '加入继承任务成功！';
        }

        return ['code' => 200, 'message' => $notes];
    }

    /**
     * 处理企微客户继承转粉
     *
     * @return string
     * @throws Exception
     */
    public static function customerInherit()
    {
        $cusExtendJob = CusExtendJob::find()
            ->andWhere([
                'AND',
                ['<', 'execution_at', time()],
                ['<', 'run_at', time() - 60]
            ])
            ->orderBy('created_at ASC')
            ->limit(30)
            ->all();

        if(empty($cusExtendJob)){
            return '';
        }

        //标记处理中数据，防止下个进程执行相同的数据
        $cusExtendJobIds = array_column($cusExtendJob,'id');
        CusExtendJob::updateAll(['run_at' => time()], ['id' => $cusExtendJobIds]);
        $cusExtendJob = ArrayHelper::index($cusExtendJob,null, 'extend_id');

        //失败信息
        $failMsg = [];
        foreach ($cusExtendJob as $extendJob) {
            $type = $extendJob[0]['type'];
            $sourceWxComUserId = explode('@', $extendJob[0]['source_wxcom_user_id']);   //切割数组，离职人员企微id会拼凑字符‘@’
            $sourceWxComUserId = $sourceWxComUserId[0];
            $targetWxComUserId = $extendJob[0]['target_wxcom_user_id'];
            $cusUserList = array_column($extendJob,'external_user_id');
            $transfer_success_msg = CusExtend::find()->select('remind_content')->where(['id' => $extendJob[0]['extend_id']])->cache(30)->scalar();

            //获取CusCustomerUser表加粉数据
            $cusUserIds = array_column($extendJob,'cus_user_id');
            $cusCustomerUserList = CusCustomerUser::find()
                ->andWhere(['id' => $cusUserIds])
                ->all();
            $cusCustomerUserList = ArrayHelper::index($cusCustomerUserList,'customer.external_user_id');

            $com = Com::findOne($extendJob[0]['com_id']);
            $cusSDK = new CusExtendSDK($com['corp_id'], $com['cus_secret']);
            try {
                $ret = $cusSDK->transfer(
                    $sourceWxComUserId,
                    $targetWxComUserId,
                    $cusUserList,
                    $transfer_success_msg,
                    $type == CusExtendTypeEnum::RESIGN_INHERIT
                );
            } catch (Exception $e) {
                $ret = ['errcode' => 422, 'errmsg' => $e->getMessage(), 'request_data' => json_encode(ArrayHelper::toArray($extendJob))];
            }

            if ($ret['errcode']) {
                //失败信息
                $failMsg[] = ['errcode' => $ret['errcode'], 'errmsg' => $ret['errmsg'], 'request_data' => json_encode(ArrayHelper::toArray($extendJob))];
            }

            //重组结果数据
            if ($ret['customer']) {
                $ret['customer'] = ArrayHelper::index($ret['customer'],'external_userid');
            }

            $extendJob = ArrayHelper::index($extendJob, 'external_user_id');
            foreach ($cusUserList as $externalUserid) {
                $cusUser = $cusCustomerUserList[$externalUserid];
                if (isset($ret['customer'][$externalUserid]['errcode'])) {
                    $customerInfo = $ret['customer'][$externalUserid];
                } else {
                    $customerInfo = $ret;
                }

                /*if ($customerInfo['errcode']) {
                    //更新失败记录(客户拒绝)
                    $cusUser->updateToExtendFail();
                    //失败信息
                    $failMsg[] = ['errcode' => $customerInfo['errcode'], 'errmsg' => '', 'request_data' => json_encode(ArrayHelper::toArray($extendJob))];
                    continue;
                }*/

                $extendDetail = new CusExtendDetail();
                $extendDetail->extend_id = $extendJob[$externalUserid]['extend_id'];
                $extendDetail->wxcom_user_id = $extendJob[$externalUserid]['target_wxcom_user_id'];
                $extendDetail->external_user_id = $extendJob[$externalUserid]['external_user_id'];
                $extendDetail->cus_user_id = $extendJob[$externalUserid]['cus_user_id'];
                $extendDetail->cus_id = $extendJob[$externalUserid]['cus_id'];
                $extendDetail->tag_ids = $cusUser->tag_ids;
                $extendDetail->origin_created_by = $cusUser->created_by;
                $extendDetail->errcode = $customerInfo['errcode'];
                if ($customerInfo['errcode'] != 0) {
                    $extendDetail->status = CusExtendDetailStatusEnum::INHERIT_FAILURE;
                }

                if (!$extendDetail->save()) {
                    throw new Exception('继承子表保存失败');
                }
                $cusUser->updateToExtend();
            }

            // 判断离职人员是否存在待分配客户
            $model = static::$modelClass::findOne($extendJob[$externalUserid]['extend_id']);
            $cusUserId = CusCustomerUser::find()
                ->select('id')
                ->andWhere([
                    'is_extend' => 0,
                    'user_id' => $model->source_user_id
                ])
                ->scalar();
            //该客服无未转的粉，删除离职记录
            if (!$cusUserId && $model && $model->quitUser) {
                $model->quitUser->delete();
            }

            $jobIds = ArrayHelper::getColumn($extendJob,'id');
            if ($jobIds) {
                CusExtendJob::deleteAll(['id' => $jobIds]);
            }
        }

        $msg = '';
        $num = count($failMsg);
        if ($num > 0) {
            //失败信息整理
            $msg = self::failMsgDealwith($num, $failMsg);
            $msg = '分配失败,存在' . $num . '条失败信息：' . $msg;
        }

        return $msg;
    }
}
