<?php

namespace backendapi\services;

use auth\models\Store;
use backendapi\models\pay\StoreUnionPay;
use backendapi\models\UnionPay;
use backendapi\services\material\MaterialCheckService;
use common\components\PayFactory;
use common\enums\MaterialBelongWarehouse;
use common\enums\MaterialCheckTypeEnum;
use common\enums\StatusEnum;
use common\enums\StoreBrand;
use common\enums\WhetherEnum;
use common\helpers\ArrayHelper as HelpersArrayHelper;
use common\helpers\BcHelper;
use common\helpers\DateHelper;
use common\models\Area;
use common\models\backend\Member;
use common\models\common\Department;
use common\models\common\DepartmentAssignment;
use common\models\common\Provinces;
use common\models\TeacherJob;
use common\models\wxcom\CusCustomerUser;
use common\models\backend\order\OrderHeader;
use common\enums\order\OrderHeaderStatusEnum;
use common\models\Customer;
use common\enums\order\OrderHeaderPrePayStatusEnum;
use Exception;
use services\UserService;
use Yii;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;


class StoreService extends Store
{
    public $city_name;

    public function rules()
    {
        return [
            [['store_name', 'brand', 'city_name'], 'trim'],
            [['brand', 'store_type', 'class', 'store_name', 'store_intro', 'address', 'telephone', 'mobile', 'city_name'], 'string'],
            [['id', 'created_at', 'updated_at', 'status'], 'integer'],
        ];
    }

    /**
     * 切换门店列表
     * @param array $params
     */
    public function switchSearch($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $query = Store::find();
        $this->load($params, '');
        $this->validate();
        //模糊搜索
        if (isset($params['store_name']) && $params['store_name']) {
            $query->andFilterWhere(['like', 'store_name', $params['store_name']]);
        }
        //管辖范围
        $scope = Yii::$app->services->scopeDataService->getScope();
        $query->andFilterWhere(['in', 'dept_id', $scope]);
        $query->andWhere(['not in', 'store_name', MaterialBelongWarehouse::getKeys()]);
        $query->andWhere(['status' => StatusEnum::ENABLED]);
        if (isset($params['id']) && $params['id']) {
            $query->andFilterWhere(['=', 'id', $params['id']]);
        }
        $list = $query->select('id,store_name,area_id,store_type,brand,status,avatar,address')->offset(($page - 1) * $limit)->limit($limit)
            ->orderBy('created_at DESC,id DESC')->asArray()->all();
        foreach ($list as &$item) {
            $item['area_text'] = Area::getAreaText($item['area_id']);
        }
        //门店id处理
        if (isset($params['id']) && $params['id']) {
            return [$list[0], 1];
        }
        return [$list, $query->count()];
    }

    /**
     * 获取管辖范围和本部门id
     * @param $user_id
     * @return array
     */
    public static function getScopeCurrentDeptIds($user_id)
    {
        //用户管理范围
        $scope_list = Department::getStoreScope($user_id);
        //获取门店对应门店标记的部门id
        $dept_arr = Department::getStoreManageDeptIdsByIds($scope_list);
        // TODO hqs 优化
        //获取本部门
        $dept = DepartmentAssignment::find()->where(['user_id' => $user_id])->select('dept_id')->one();
        if ($dept) {
            array_unshift($dept_arr, $dept['dept_id']);
        }
        return $dept_arr;
    }

    /**
     * 门店关键词搜索列表
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function search($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        $query = Store::find()
            ->alias('s')
            ->leftJoin('erp_department d', 'd.id = s.dept_id')
            ->where(['d.entity_id' => UserService::getInst()->current_entity_id]);
        $this->load($params, '');
        $this->validate();

        if ((isset($params['status'])) && $params['status'] != -1) $query->andWhere(['status' => intval($params['status'])]);

        //模糊搜索与批量精准搜索
        if (isset($params['store_name_type'])) {
            if ($params['store_name_type'] == 1) {
                if (isset($params['store_name']) && $params['store_name']) {
                    $query->andFilterWhere(['like', 'store_name', $params['store_name']]);
                }
            } else if ($params['store_name_type'] == 2) {
                if (isset($params['store_name']) && is_array($params['store_name'])) {
                    $query->andFilterWhere(['in', 'store_name', $params['store_name']]);
                }
            }
        }


        //省市搜索
        if (isset($params['province_id']) && $params['province_id']) {
            $query->andFilterWhere(['province_id' => $params['province_id']]);
        }
        if (isset($params['city_id']) && $params['city_id']) {
            $query->andFilterWhere(['in', 'city_id', $params['city_id']]);
        }
        $query->andFilterWhere(['like', 'brand', trim($this->brand)])
            ->andFilterWhere(['like', 'store_intro', trim($this->store_intro)])
            ->andFilterWhere(['like', 'address', trim($this->address)])
            ->andFilterWhere(['like', 'telephone', trim($this->telephone)]);
        //管辖范围
        $query->andFilterWhere(['in', 'dept_id', Yii::$app->services->scopeDataService->getScope()]);
        //区域
        if (isset($params['area_id']) && $params['area_id'] > 0) {
            //获取子级id
            $area_ids = Area::getChildsId($params['area_id']);
            $query->andFilterWhere(['in', 'area_id', $area_ids]);
        }

        // 排序规则
        $orderField = ArrayHelper::getValue($params, 'order_field', '');
        if ($orderField) {
            $orderType = ArrayHelper::getValue($params, 'order_type', 'desc');
            if (in_array($orderType, ['asc', 'desc'])) {
                $query->orderBy("{$orderField} {$orderType},status desc,id desc");
            }
        } else {
            $query->orderBy('status desc,id desc');
        }

        $list = $query->offset(($page - 1) * $limit)->limit($limit)
            ->asArray()->all();
        $memberInfo = [];
        $cityInfo = [];
        foreach ($list as &$item) {
            if (!in_array($item['created_by'], array_keys($memberInfo))) {
                $memberInfo[$item['created_by']] = Member::getMemberName($item['created_by']);
            }
            if (!in_array($item['updated_by'], array_keys($memberInfo))) {
                $memberInfo[$item['updated_by']] = Member::getMemberName($item['updated_by']);
            }
            $item['created_by'] = $memberInfo[$item['created_by']];
            $item['updated_by'] = $memberInfo[$item['updated_by']];
            if (!in_array($item['city_id'], array_keys($cityInfo))) {
                $cityInfo[$item['city_id']] = Provinces::getNameByID($item['city_id']);
            }
            $item['city_name'] = $cityInfo[$item['city_id']];
            $item['area_text'] = Area::getAreaText($item['area_id']);
        }

        return [$list, $query->count()];
    }

    /**
     * 获取门店列表-下拉
     *
     * @return array|\yii\db\ActiveRecord[]
     * @throws \yii\base\InvalidConfigException
     */
    public static function getFilterList()
    {
        $keyword = trim(Yii::$app->request->get('keyword'));
        $is_show_warehouse = Yii::$app->request->get('is_show_warehouse', '1');

        $scope = Yii::$app->services->scopeDataService->getScope();
        $storeList = Department::find()->alias('d')
            ->select('d.id,d.name,s.brand,s.address,s.id as store_id')
            ->innerJoin('{{%store}} s', 's.dept_id = d.id')
            ->where(['d.entity_id' => Yii::$app->user->identity->current_entity_id, 'd.tag' => Department::TAG_STORE, 'd.deleted_at' => 0])
            ->andWhere(['s.status' => StatusEnum::ENABLED])
            ->andWhere(['<>', 'd.name', MaterialBelongWarehouse::ALL])
            ->andFilterWhere(['like', 'd.name', $keyword])
            ->andFilterWhere(['in', 'd.id', $scope])
            ->limit(20)
            ->asArray()
            ->all();

        foreach ($storeList as &$item) {
            $item['store_name'] = $item['name'];
            $item['brand_en'] = isset($item['brand']) ? StoreBrand::getBrandEn($item['brand']) : '';
            unset($item['name']);
        }

        if ($is_show_warehouse) {
            $all_warehouse_info = Department::getAllWarehouse();
            if ($all_warehouse_info) {
                if (empty($scope) || in_array($all_warehouse_info->id, $scope)) {
                    array_unshift($storeList, ['id' => $all_warehouse_info->id, 'store_name' => $all_warehouse_info->name]);
                }
            }
        }

        return $storeList;
    }

    /**
     * 盘点获取门店列表
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function storeSearch($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        $query = Store::find()->alias('s')
            ->select('s.dept_id as id,s.store_name,s.brand,s.store_type,cp.title AS city_name')
            ->leftJoin('{{%common_provinces}} cp', 'cp.id = s.city_id')
            ->where(['s.entity_id' => Yii::$app->user->identity->current_entity_id]);

        $this->load($params, '');
        $this->validate();

        $query->andFilterWhere(['s.dept_id' => Yii::$app->services->scopeDataService->getScope()]);
        $query->andFilterWhere(['LIKE', 's.store_name', $this->store_name])
            ->andFilterWhere(['s.brand' => $this->brand])
            ->andFilterWhere(['cp.title' => $this->city_name])
            ->andFilterWhere(['s.status' => StatusEnum::ENABLED]);

        $totalCount = $query->count();
        $data = $query->offset(($page - 1) * $limit)->limit($limit)->groupBy('s.id')->orderBy('s.created_at DESC,s.id DESC')->asArray()->all();

        $dept_ids = array_column($data, 'id');
        $material_check = MaterialCheckService::find()->select('dept_id,updated_at')
            ->where(['dept_id' => $dept_ids, 'status' => MaterialCheckTypeEnum::COMPLETE])
            ->orderBy('updated_at desc')
            ->groupBy('dept_id')
            ->indexBy('dept_id')
            ->asArray()->all();

        foreach ($data as &$v) {
            $start_time = $material_check[$v['id']] ? $material_check[$v['id']]['updated_at'] : '';
            $v['overdue_days'] = DateHelper::getOverdueTime(time(), $start_time);
        }

        return [$data, $totalCount];
    }

    /**
     * 门店选择列表
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function getListForChoose($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query = Store::find()
            ->andFilterWhere(['brand' => $params['brand']])
            ->andFilterWhere(['=', 'store_type', $params['store_type']])
            ->andFilterWhere(['like', 'store_name', $params['store_name']]);

        $status = ArrayHelper::getValue($params, 'status', -1);
        if ($status != -1 && $status != '') {
            $query->andWhere(['status' => $status]);
        }
        //区域
        if (isset($params['area_id']) && $params['area_id'] > 0) {
            //获取子级id
            $area_ids = Area::getChildsId($params['area_id']);
            $query->andFilterWhere(['in', 'area_id', $area_ids]);
        }

        $list = $query->offset($offset)
            ->limit($limit)
            ->select('id,store_name,store_type,brand,status,dept_id')
            ->orderBy('created_at DESC,id DESC')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['brand_en'] = (new StoreBrand)::getBrandEn($item['brand']);
        }

        return [$list, $query->count()];
    }

    /**
     * 门店下拉选择列表
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function getListForSelect($params = [])
    {
        $query = Store::find()
            ->andFilterWhere(['like', 'store_name', $params['keyword']]);
        $address = $params['address'] ?? 0;
        $field = $address == 1 ? 'id,store_name,address' : 'id,store_name,address';
        $list = $query->select($field)
            ->orderBy('id DESC')
            ->all();

        return $list;
    }

    public function getListToFeishu($params = [])
    {
        $key = 'ChzGetListToFeishu@ljs5';
        $md5 = md5($key);
        if ($params['token'] != $md5) {
            $data = [
                'code' => 0,
                'msg' => 'token error!',
                'data' => []
            ];

            return json_encode($data, JSON_UNESCAPED_UNICODE);
        }

        $ids = [12, 13, 42];
        $query = Store::find()->andFilterWhere(['like', 'store_name', $params['store_name']]);
        $list = $query->select('id,store_name')
            ->andWhere(['not in', 'id', $ids])
            ->andWhere(['status' => StatusEnum::ENABLED])
            ->orderBy('feishu_sort desc,id DESC')
            ->asArray()
            ->all();

        $other = [
            'id' => 99999,
            'store_name' => '其他，请在备注中说明'
        ];
        $list[] = $other;

        $options = [];
        $texts_zh = [];
        $texts_en = [];

        foreach ($list as $item) {
            $options[] = [
                'id' => 'options_1_id_' . $item['id'],
                'value' => '@i18n@options_1_name_' . $item['id']
            ];
            $texts_zh['@i18n@options_1_name_' . $item['id']] = $item['store_name'];
            $texts_en['@i18n@options_1_name_' . $item['id']] = 'value' . $item['id'];
        }

        $data = [
            'code' => 0,
            'msg' => 'success!',
            'data' => [
                'result' => [
                    'options' => $options,
                    'i18nResources' => [
                        [
                            'locale' => 'zh_cn',
                            'isDefault' => false,
                            'texts' => $texts_zh
                        ],
                        [
                            'locale' => 'en_us',
                            'isDefault' => false,
                            'texts' => $texts_en
                        ]
                    ]
                ]
            ]
        ];

        return json_encode($data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 到店统计
     *
     * @param array $params
     * @return void
     */
    public static function getOrderData($params)
    {
        $where = ['eoh.entity_id = ' . UserService::getInst()->current_entity_id];
        $searchStartTime = $params['plan_time_start'] ?: strtotime(date('Y-m-1'));
        $searchEndTime = $params['plan_time_end'] ?: time();
        $where[] = "eoh.plan_time between {$searchStartTime} and {$searchEndTime}";

        $storeId = $params['store_id'];
        if ($storeId) {
            $where[] = "eoh.store_id = {$storeId}";
        }
        $whereSql = implode(' and ', $where);

        $planOrderSql = 'eoh.order_status in (0,1,3,4,5,6)';
        $finishOrderSql = 'eoh.order_status in (5,6)';
        $newCusSql = "(ec.first_store_time = 0 or ec.first_store_time >= {$searchStartTime})";
        $oldCusSql = "(ec.first_store_time <> 0 and ec.first_store_time < {$searchStartTime})";
        $newList = Yii::$app
            ->db
            ->createCommand("
                select 
                    eoh.store_id
                    ,es.store_name 
                    ,count(distinct IF({$planOrderSql}, cus_id, null)) as plan_count
                    ,count(distinct IF({$planOrderSql} and {$newCusSql}, cus_id, null)) as new_cus_plan_count
                    ,count(distinct IF({$planOrderSql} and {$oldCusSql}, cus_id, null)) as old_cus_plan_count
                    ,count(distinct IF({$finishOrderSql}, cus_id, null)) as finish_count
                    ,count(distinct IF({$finishOrderSql} and {$newCusSql}, cus_id, null)) as new_cus_finish_count
                    ,count(distinct IF({$finishOrderSql} and {$oldCusSql}, cus_id, null)) as old_cus_finish_count
                    ,count(distinct IF({$finishOrderSql} and {$oldCusSql}, cus_id, null), FROM_UNIXTIME(IFNULL(plan_time, 0) , '%Y%m%d')) as old_cus_day_finish_count
                from erp_order_header eoh
                left join erp_store es on es.id = eoh.store_id 
                left join erp_customer ec on ec.id = eoh.cus_id 
                where {$whereSql}
                group by eoh.store_id 
            ")
            ->queryAll();
        $totalCount = count($newList);

        $totalRecord = [
            "store_id" => 0,
            "store_name" => "-",
            "plan_count" => 0,
            "new_cus_plan_count" => 0,
            "old_cus_plan_count" => 0,
            "finish_count" => 0,
            "new_cus_finish_count" => 0,
            "old_cus_finish_count" => 0,
            "old_cus_day_finish_count" => 0,
        ];
        foreach ($newList as $item) {
            $totalRecord['plan_count'] += $item['plan_count'];
            $totalRecord['new_cus_plan_count'] += $item['new_cus_plan_count'];
            $totalRecord['old_cus_plan_count'] += $item['old_cus_plan_count'];
            $totalRecord['finish_count'] += $item['finish_count'];
            $totalRecord['new_cus_finish_count'] += $item['new_cus_finish_count'];
            $totalRecord['old_cus_finish_count'] += $item['old_cus_finish_count'];
            $totalRecord['old_cus_day_finish_count'] += $item['old_cus_day_finish_count'];
        }


        // 排序
        $orderField = $params['order_field'] ?: 'plan_count';
        $orderType = $params['order_type'] ?: 'desc';
        $newList = HelpersArrayHelper::arraySort($newList, $orderField, $orderType);

        // 分页
        $page = $params['page'] - 0 ?: 1;
        $limit = $params['limit'] - 0 ?: 10;
        $offset = ($page - 1) * $limit;
        $newList = array_splice($newList, $offset, $limit);

        array_unshift($newList, $totalRecord);

        return [$newList, $totalCount];
    }

    /**
     * 获取门店老师档期
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getTeacherPlanTimes($params)
    {
        // 判断门店
        if (empty($params['store_id'])) {
            throw new Exception('门店id不能为空');
        }
        $store = Store::findOne($params['store_id']);
        if (empty($store)) {
            throw new Exception('找不到对应门店');
        }

        // 判断门店营业时间
        $openHour = explode(':', $store->open_hour . '0')[0] - 0;
        $closeHour = explode(':', $store->close_hour . '0')[0] - 0;
        if (!$openHour || !$closeHour || $openHour > $closeHour) {
            throw new Exception('门店营业时间配置有异常');
        }

        // 判断预约日期
        if (empty($params['plan_time'])) {
            throw new Exception('预约日期不能为空');
        }
        $dateTime = strtotime(date('Y-m-d', $params['plan_time']));
        $minHour = 0;
        if (date('Y-m-d', $params['plan_time']) == date('Y-m-d')) {
            $minHour = date('H') + (date('i') / 30);
        }

        // 构造时间列表
        $hours = [];
        for ($hour = $openHour; $hour <= $closeHour; $hour += 0.5) {
            $hours[] = $hour;
        }

        // 初始化老师档期
        $newTeacherList = [];
        /** @var array<TeacherJob> */
        $teacherList = TeacherJob::find()
            ->alias('tj')
            ->with([
                'member',
                'orders' => function ($query) use ($dateTime) {
                    $query->andWhere(['between', 'plan_time', $dateTime, $dateTime + 86400])
                        ->andWhere(['order_status' => [1, 3, 4, 5, 8]]);
                }
            ])
            ->leftJoin('{{%teacher_type}} tt', 'tt.id = tj.teacher_type_id')
            ->where(['tj.entity_id' => UserService::getInst()->current_entity_id])
            ->andWhere(['tj.store_id' => $params['store_id']])
            ->andWhere([
                'OR',
                [
                    'AND',
                    ['tj.teacher_type_id' => 0],
                    ['tj.is_virtual' => WhetherEnum::ENABLED],
                ],
                [
                    'AND',
                    ['>', 'tj.teacher_type_id', 0],
                    ['tt.is_open' => WhetherEnum::ENABLED]
                ]
            ])
            ->groupBy(new Expression('if(tj.user_id > 0,tj.user_id,tj.id)'))
            ->orderBy('tj.id ASC')
            ->all();

        foreach ($teacherList as $teacher) {
            //过滤关闭档期或者离职的人员
            if ($teacher->is_open == WhetherEnum::DISABLED || ($teacher->is_virtual == WhetherEnum::DISABLED && $teacher->member->status == WhetherEnum::DISABLED)) {
                continue;
            }

            $newTeacher = [
                'id' => $teacher->id,
                'user_id' => $teacher->user_id,
                'project' => $teacher->project,
                'is_virtual' => $teacher->is_virtual,
                'is_open' => $teacher->is_open,
                'user_name' => $teacher->is_virtual ? '虚拟老师-' . $teacher->id : $teacher->member->realname,
            ];

            $teacherHours = [];
            $lockDateType = $teacher->getScheduleLockTypes($dateTime);
            foreach ($hours as $hour) {
                $dateType = ($hour >= 14) + 1;
                $teacherHours['H_' . $hour] = [
                    'is_lock' => $lockDateType & $dateType,
                    'status' => (!($lockDateType & $dateType) && $hour >= $minHour) ? StatusEnum::ENABLED : StatusEnum::DISABLED,
                    'text' => date('H:i', strtotime("2022-01-01") + $hour * 60 * 60),
                ];
            }
            foreach ($teacher->orders as $order) {
                $beginHour = date('H', $order->plan_time) - 0 + (date('i', $order->plan_time) / 60);
                $endHour = $beginHour + $order->schedule_num / 60 - 0.5;
                for ($hour = $beginHour; $hour <= $endHour && isset($teacherHours['H_' . $hour]); $hour += 0.5) {
                    $teacherHours['H_' . $hour]['status'] = StatusEnum::DISABLED;
                }
            }
            $newTeacher['hours'] = $teacherHours;
            $newTeacherList[] = $newTeacher;
        }
        $teacherList = $newTeacherList;

        return $teacherList;
    }

    /**
     * 获取预约老师
     */
    public static function getTeacherPlan($params)
    {
        if ($params['username']) {
            $params['store_id'] = null;
        }
        $teacherList = TeacherJob::find()->alias('tj')
            ->select('m.id,m.username,m.jobnumber')
            ->leftJoin('{{%backend_member}} m', 'm.id = tj.user_id')
            ->where(['tj.entity_id' => UserService::getInst()->current_entity_id])
            ->andFilterWhere(['tj.store_id' => $params['store_id']])
            ->andWhere(['tj.is_virtual' => WhetherEnum::DISABLED])
            ->andWhere(['tj.is_open' => WhetherEnum::ENABLED])
            ->andWhere(['m.status' => WhetherEnum::ENABLED])
            ->andFilterWhere(['like', 'm.username', trim($params['username'])])
            ->limit(20)
            ->groupBy('tj.user_id')
            ->asArray()
            ->all();

        if (empty($teacherList)) {
            return [];
        }

        $teacherListData = [];
        foreach ($teacherList as $value) {
            $teacher_name = $value['username'];
            if ($value['jobnumber']) {
                $teacher_name .= '-' . $value['jobnumber'];
            }

            $teacherListData[] = [
                'id' => $value['id'],
                'username' => $teacher_name
            ];
        }

        return $teacherListData;
    }

    /**
     * 获取门店银联收款配置
     * @param $params
     * @return array
     * @throws \Exception
     */
    public static function getUnionConfigList($params)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query = Store::find()->alias('s')
            ->select('s.id,s.store_name')
            ->where(['s.entity_id' => UserService::getInst()->current_entity_id])
            ->andFilterWhere(['s.id' => $params['store_id']])
            ->groupBy('s.id');

        if ($params['name']) {
            $query->leftJoin(['su' => StoreUnionPay::tableName()], 's.id=su.store_id')
                ->leftJoin(['up' => UnionPay::tableName()], 'su.union_pay_id=up.id')
                ->andFilterWhere(['like', 'up.name', trim($params['name'])]);
        }

        $totalCount = $query->count();
        $list = $query
            ->offset($offset)
            ->limit($limit)
            ->orderBy('s.status DESC,s.id DESC')
            ->asArray()
            ->all();

        if (empty($params['startTime']) || empty($params['endTime'])) {
            $thisMonth = DateHelper::thisMonth();
            $params['startTime'] = $thisMonth['start'];
            $params['endTime'] = $thisMonth['end'];
        }

        foreach ($list as &$item) {
            $unionPayConfig = UnionPay::find()->alias('up')
                ->select('up.id,up.name,up.licence,su.rate,up.status')
                ->leftJoin(['su' => StoreUnionPay::tableName()], 'su.union_pay_id = up.id')
                ->where(['su.store_id' => $item['id']])
                ->andFilterWhere(['like', 'up.name', $params['name']])
                ->asArray()
                ->all();

            $arrUnionPayConfig = [];
            if ($unionPayConfig) {
                $totalAmount = 0;
                foreach ($unionPayConfig as $payConfig) {
                    $config = $payConfig;
                    $config['amount'] = UnionPayService::getStoreUnionPayAmount($payConfig['id'], $item['id'], $params['startTime'], $params['endTime']);
                    $totalAmount += $config['amount'];
                    $arrUnionPayConfig[] = $config;
                }

                foreach ($arrUnionPayConfig as &$k) {
                    $k['amount_rate'] = BcHelper::sprintf(BcHelper::div($k['amount'], $totalAmount, 4) * 100, 2);
                }
            }
            $item['unionPayConfig'] = $arrUnionPayConfig;
        }

        return [$list, $totalCount];
    }

    public static function getUnionConfigView($id)
    {
        $info = self::find()
            ->select('id as store_id,store_name')
            ->where(['id' => $id])
            ->asArray()
            ->one();

        if (!$info) {
            throw new Exception('请求的数据不存在');
        }

        $unionPayConfig = UnionPay::find()->alias('up')
            ->select('up.id as union_pay_id,up.name,up.licence,su.rate')
            ->leftJoin(['su' => StoreUnionPay::tableName()], 'su.union_pay_id = up.id')
            ->where(['su.store_id' => $id])
            ->asArray()
            ->all();

        $info['unionPayConfig'] = $unionPayConfig;

        return $info;
    }

    public static function getQrcode($storeId, $union_pay_id)
    {
        $callbackUrl = Url::to(['apis/pay-callback/union'], true);
        $storeInfo = Store::find()->alias('s')
            ->select('s.store_name,up.app_id,up.app_key,up.mid,up.tid,up.merchantCode,up.terminalCode')
            ->leftJoin(['su' => StoreUnionPay::tableName()], 's.id = su.store_id')
            ->leftJoin(['up' => UnionPay::tableName()], 'su.union_pay_id = up.id')
            ->where(['s.entity_id' => UserService::getInst()->current_entity_id])
            ->andWhere(['s.id' => $storeId])
            ->andWhere(['su.union_pay_id' => $union_pay_id])
            ->asArray()
            ->one();

        if (empty($storeInfo)) {
            throw new Exception('该门店配置不存在');
        }

        $config = $storeInfo;
        unset($config['store_name']);

        $srcReserve = [
            'store_id' => $storeId
        ];

        $data = ['notify_url' => $callbackUrl, 'billDesc' => $storeInfo['store_name'], 'srcReserve' => json_encode($srcReserve, 256), 'type' => 'getQrcode'];
        $model = PayFactory::getInstance()->getPayment('getQrcode', $config, $data);
        return [$model->getQrcode()];
    }

    public static function unionConfigUpdate($params)
    {
        $store_id = $params['store_id'] ?? 0;
        $model = Store::find()->andWhere(['id' => $store_id])->one();
        if (empty($model)) throw new Exception('该门店不存在');

        // 删除之前配置
        StoreUnionPay::deleteAll(['store_id' => $params['store_id']]);

        if (!static::isMeetsRate($params['unionPayConfig'])) {
            throw new Exception('比例总和必须为100%');
        }

        foreach ($params['unionPayConfig'] as $v) {
            $unionPay = UnionPay::find()->where(['id' => $v['union_pay_id']])->one();
            if (empty($unionPay)) {
                throw new Exception('银联收款配置ID：' . $v['union_pay_id'] . ',不存在');
            }

            $model = new StoreUnionPay();
            $model->store_id = $params['store_id'];
            $model->union_pay_id = $v['union_pay_id'];
            $model->rate = $v['rate'];
            $model->save();
        }

        return true;
    }

    public static function isMeetsRate($unionPayConfig)
    {
        $rete = 0;
        foreach ($unionPayConfig as $v) {
            $rete += $v['rate'];
        }

        if ($rete != 100) {
            return false;
        }

        return true;
    }

    /**
     * 存量统计数据
     * @param array $params 查询参数
     * @param bool $isExport 是否为导出模式
     * @return array
     * @throws Exception
     */
    public static function getInventoryData($params = [], $isExport = false)
    {
        $defaultStartTime = strtotime(date('Y-m-01'));
        $defaultEndTime = strtotime(date('Y-m-t 23:59:59'));
        
        $startTime = isset($params['plan_time_start']) 
            ? (is_numeric($params['plan_time_start']) ? $params['plan_time_start'] : $defaultStartTime)
            : $defaultStartTime;
            
        $endTime = isset($params['plan_time_end']) 
            ? (is_numeric($params['plan_time_end']) ? $params['plan_time_end'] : $defaultEndTime)
            : $defaultEndTime;

        // 构建基础查询条件
        $baseQuery = (new \yii\db\Query())
            ->from(['oh' => OrderHeader::tableName()])
            ->leftJoin(['ccu' => CusCustomerUser::tableName()], 'ccu.id = oh.customer_user_id')
            ->leftJoin(['cus' => Customer::tableName()], 'cus.id = oh.cus_id')
            ->leftJoin(['s' => Store::tableName()], 's.id = oh.store_id')
            ->andWhere(['between', 'oh.plan_time', $startTime, $endTime])
            ->andWhere(['oh.order_status' => OrderHeader::depositOrderStatusList()])
            ->andWhere(['>', 'oh.deposit', 0])
            ->andWhere(['oh.pre_pay_status' => OrderHeaderPrePayStatusEnum::HAVE_PAID])
            ->andWhere(['in', 'ccu.add_way', CusCustomerUser::getComputeAddWay()]);

        if (!empty($params['store_id'])) {
            $baseQuery->andWhere(['oh.store_id' => intval($params['store_id'])]);
        }

        try {
            // 合计数据
            $totalQuery = clone $baseQuery;
            $totalData = $totalQuery->select([
                'total_deposit_count' => 'COUNT(DISTINCT oh.cus_id)',
                'total_new_customer_visit_count' => 'COUNT(DISTINCT IF(oh.order_status = ' . OrderHeaderStatusEnum::STATUS_COMPLETED . ' AND (FROM_UNIXTIME(cus.first_store_time,\'%Y-%m-%d\') = FROM_UNIXTIME(oh.plan_time,\'%Y-%m-%d\')), oh.cus_id, NULL))'
            ])->one();
            $totalRecord = [
                'store_id' => 0, 
                'store_name' => '合计',
                'deposit_count' => intval($totalData['total_deposit_count'] ?? 0),
                'new_customer_visit_count' => intval($totalData['total_new_customer_visit_count'] ?? 0),
                'deposit_to_visit_rate' => BcHelper::percentage(
                    $totalData['total_new_customer_visit_count'],
                    $totalData['total_deposit_count']
                )
            ];

            // 主查询
            $mainQuery = clone $baseQuery;
            $mainQuery->select([
                's.store_name',
                'oh.store_id',
                'deposit_count' => 'COUNT(DISTINCT oh.cus_id)',
                'new_customer_visit_count' => 'COUNT(DISTINCT IF(oh.order_status = ' . OrderHeaderStatusEnum::STATUS_COMPLETED . ' AND (FROM_UNIXTIME(cus.first_store_time,\'%Y-%m-%d\') = FROM_UNIXTIME(oh.plan_time,\'%Y-%m-%d\')), oh.cus_id, NULL))'
            ])
            ->groupBy('oh.store_id')
            ->orderBy(['deposit_count' => SORT_DESC]);

            // 获取总门店数（用于分页计算）
            $totalStoresForPagination = (clone $mainQuery)->count();

            if ($isExport) {
                if (!empty($params['getTotal'])) {
                    return [[], $totalStoresForPagination];
                }
                $pagedStoreData = $mainQuery->all();
            } else {
                $pageSize = isset($params['limit']) ? intval($params['limit']) : 20;
                $currentPage = isset($params['page']) ? intval($params['page']) : 1;
                $offset = ($currentPage - 1) * $pageSize;
                
                $pagedStoreData = $mainQuery->limit($pageSize)->offset($offset)->all();
            }
            
            $processedList = [];
            foreach ($pagedStoreData as $item) {
                $depositCount = intval($item['deposit_count']);
                $newCustomerVisitCount = intval($item['new_customer_visit_count']);
                $depositToVisitRate = BcHelper::percentage($newCustomerVisitCount, $depositCount);

                $processedList[] = [
                    'store_id' => $item['store_id'],
                    'store_name' => $item['store_name'],
                    'deposit_count' => $depositCount,
                    'new_customer_visit_count' => $newCustomerVisitCount,
                    'deposit_to_visit_rate' => $depositToVisitRate
                ];
            }
            
            array_unshift($processedList, $totalRecord);
            return [$processedList, $totalStoresForPagination];
        } catch (Exception $e) {
            Yii::error("查询存量统计数据失败: " . $e->getMessage());
            throw new Exception('查询存量统计数据失败: ' . $e->getMessage());
        }
    }
}
