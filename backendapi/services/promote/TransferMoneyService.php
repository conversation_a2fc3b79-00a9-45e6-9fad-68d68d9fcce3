<?php

namespace backendapi\services\promote;

use backendapi\models\promote\AdsMainBody;
use common\components\promoteData\Adq;
use common\components\promoteData\Oceanengine;
use common\enums\reportEnum;
use common\helpers\BcHelper;
use common\models\common\AdsAccountSub;
use yii\httpclient\Client;
use Exception;
use Yii;

class TransferMoneyService
{
    private $accessToken = '';
    public $advertiser_id = '';
    private $target_advertiser_id = '';
    private $amount = 0;
    private $transaction_seq = '';
    private $user_id;
    private $user_name;
    public $mainBody;
    public $platform = '';
    public $organization_id = ''; //组织ID
    public $insufficientNalance; //备用金剩余金额
    private $tiktok_single_recharge_amount = 1000; //抖音单次充值金额上限
    private $adq_single_recharge_amount = 2000; //adq单次充值金额上限

    private $tiktok_one_hour_max_recharge_amount = 3000; //抖音一小时充值金额上限
    private $adq_one_hour_max_recharge_amount = 20000; //adq一小时充值金额上限

    public $code = 422; //默认技术错误码
    private $success_code = 200; //成功返回码
    public $success_insufficient_balance_code = 201; //成功但余额不足返回码
    private $error_code_it = 422; //技术错误码
    private $error_code_promote = 423; //推广错误码
    private $error_code_insufficient_balance = 424; //余额不足错误码

    private $redisKeys = 'transferMoneyData:';
    private $transferMoneyBalance = 'transferMoneyBalance:';
    private $redisCacheData = [];

    public function run($params)
    {
        $this->timeLimit();
        $data = $this->dealParams($params);
        $this->setFields($data);
        $this->amountlimit();
        $this->transferMoney();
        $this->success();
    }

    public function setAdvertiserId()
    {
        $accountList = $this->accountList();
        if (empty($accountList)) {
            $this->code = $this->error_code_promote;
            throw new Exception('备用金账户不存在，请联系信息部处理');
        }
        $advertiser_id = $accountList[$this->mainBody];
        if (empty($advertiser_id)) {
            $strMainBody = implode('、', array_keys($accountList));
            $this->code = $this->error_code_promote;
            throw new Exception('输入的主体有误，主体只能是：' . $strMainBody);
        }

        $this->advertiser_id = $advertiser_id;
    }

    public function setToken()
    {
        $info = AdsAccountSub::find()->alias('as')
            ->select('a.access_token,a.advertiser_id as organization_id,a.platform')
            ->leftJoin('{{%ads_account}} a', 'a.id = as.td_id')
            ->where(['as.sub_advertiser_id' => $this->advertiser_id])
            ->asArray()
            ->one();

        if (empty($info)) {
            $this->code = $this->error_code_it;
            throw new Exception('该备用金户：' . $this->advertiser_id . '不存在erp系统中，请联系技术部处理');
        }

        if (empty($info['access_token'])) {
            $this->code = $this->error_code_it;
            throw new Exception('主体：' . $this->mainBody . '中，备用金户：' . $this->advertiser_id . '的token不存在erp系统中，请联系技术部处理');
        }

        if (empty($info['organization_id']) && $info['platform'] == reportEnum::TIKTOL) {
            $this->code = $this->error_code_it;
            throw new Exception('该备用金户：' . $this->advertiser_id . '的组织ID不存在erp系统中，请联系技术部处理');
        }

        $this->accessToken = $info['access_token'];
        $this->organization_id = $info['organization_id'];
    }

    public function setPlatform()
    {
        $platform = AdsAccountSub::find()->alias('as')
            ->select('a.platform')
            ->leftJoin('{{%ads_account}} a', 'a.id = as.td_id')
            ->where(['as.sub_advertiser_id' => $this->advertiser_id])
            ->scalar();

        if (empty($platform)) {
            $this->code = $this->error_code_it;
            throw new Exception('主体：' . $this->mainBody . '中，备用金户：' . $this->advertiser_id . '的平台不存在erp系统中，请联系技术部处理');
        }

        $this->platform = $platform;
    }

    public function setFields($data)
    {
        $this->amount = $data['转账金额'];
        if ($this->amount <= 0) {
            $this->code = $this->error_code_promote;
            throw new Exception('单次充值金额必须大于0');
        }

        $this->target_advertiser_id = $data['账户ID'];
        if (empty($this->target_advertiser_id)) {
            $this->code = $this->error_code_promote;
            throw new Exception('账户ID不能为空');
        }

        $this->mainBody = AdsMainBody::find()
            ->alias('mb')->select('mb.name')
            ->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.main_body_id = mb.id')
            ->where(['aas.sub_advertiser_id' => $this->target_advertiser_id])
            ->scalar();

        if (empty($this->mainBody)) {
            $this->code = $this->error_code_promote;
            throw new Exception('账户主体未绑定，请联系管理员绑定！');
        }

        $this->setAdvertiserId();
        $this->setToken();
        $this->setPlatform();

        if ($this->platform == reportEnum::ADQ) {
            $maxAmount = $this->adq_single_recharge_amount;
        } else {
            $maxAmount = $this->tiktok_single_recharge_amount;
        }
        if ($this->amount > $maxAmount) {
            $this->code = $this->error_code_promote;
            throw new Exception('单次充值金额不得超过' . $maxAmount);
        }
    }

    public function timeLimit()
    {
        $currentTime = date('H:i');

        // 定义时间范围
        $startTime = '02:00';
        $endTime = '06:30';

        // 判断当前时间是否在范围内
        if ($currentTime >= $startTime && $currentTime <= $endTime) {
            $this->code = $this->error_code_promote;
            throw new Exception('“凌晨2点到6点30分”时间段不可充值');
        }
    }

    public function amountlimit()
    {
        $key = $this->redisKeys . $this->target_advertiser_id;
        $cacheData = Yii::$app->cache->get($key);
        if (empty($cacheData)) {
            return true;
        }

        $oneHourTime = time() - 60 * 60;
        $totalAmount = 0;
        foreach ($cacheData as $item) {
            if ($item['time'] < $oneHourTime) {
                continue;
            }
            $totalAmount +=  $item['amount'];
            $this->redisCacheData[] = $item;
        }

        if ($this->platform == reportEnum::ADQ) {
            $maxTotalAmount =  $this->adq_one_hour_max_recharge_amount;
        } else {
            $maxTotalAmount =  $this->tiktok_one_hour_max_recharge_amount;
        }

        if (($totalAmount + $this->amount) > $maxTotalAmount) {
            $this->code = $this->error_code_promote;
            throw new Exception('1小时内限制充值金额不能超过' . $maxTotalAmount . '，' . $this->target_advertiser_id . '该户已经充值了' . $totalAmount);
        }
    }

    public function success()
    {
        $key = $this->redisKeys . $this->target_advertiser_id;
        $this->redisCacheData[] = [
            'user_name' => $this->user_name,
            'amount' => $this->amount,
            'time' => time()
        ];

        Yii::$app->cache->set($key, $this->redisCacheData, 60 * 60);
        if ($this->insufficientNalance <= 3000) {
            $this->code = $this->success_insufficient_balance_code;
        } else {
            $this->code = $this->success_code;
        }
    }

    public function dealParams($params = '')
    {
        if (empty($params)) {
            $this->code = $this->error_code_promote;
            throw new Exception('充值数据不能为空');
        }

        $this->user_id = $params['user_id'];
        $this->user_name = $params['user_name'];

        $parts = explode("\n", $params['data']);
        $list = [];
        $keyList = [];
        foreach ($parts as $part) {
            list($key, $value) = explode('：', $part);
            $key = trim($key);
            $value = trim($value);
            $list[$key] = $value;
            $keyList[] = $key;
        }
        $fileds = ['账户ID', '转账金额'];
        $diff = array_diff($fileds, $keyList);
        if (!empty($diff)) {
            $this->code = $this->error_code_promote;
            throw new Exception('数据格式有误，请认真审查');
        }

        return $list;
    }

    /**
     * 转账
     */
    public function transferMoney()
    {
        $balance = $this->getBalance();
        $this->insufficientNalance = $balance - $this->amount;

        if ($this->platform == reportEnum::TIKTOL) { //抖音
            $result = Oceanengine::transferCreate($this->accessToken, $this->organization_id, $this->advertiser_id, $this->target_advertiser_id, $this->amount);
            if (!isset($result['code']) || $result['code'] != 0) {
                $this->code = $this->error_code_promote;
                throw new Exception('创建转账交易号：' . $this->advertiser_id . ' 报错：' . $result['message'] . ',请稍后重新提交');
            }
        } elseif ($this->platform == reportEnum::ADQ) {
            $result = Adq::subcustomerTransfer($this->accessToken, $this->advertiser_id, $this->target_advertiser_id, $this->amount);
            if ($result['code'] != 0) {
                $this->code = $this->error_code_it;
                throw new Exception('充值失败：' . $result['message_cn']);
            }
        } else {
            $this->code = $this->error_code_it;
            throw new Exception('充值失败：充值平台不存在，请联系信息部处理');
        }

        $key = $this->transferMoneyBalance . $this->advertiser_id;
        Yii::$app->cache->set($key, $this->insufficientNalance, 500);
    }

    public function getBalance()
    {
        $key = $this->transferMoneyBalance . $this->advertiser_id;
        $balance = Yii::$app->cache->get($key);
        if (!$balance) {
            if ($this->platform == reportEnum::TIKTOL) { //抖音
                $res = Oceanengine::getFund($this->accessToken, $this->advertiser_id);
                if (!isset($res['code']) || $res['code'] != 0) {
                    $this->code = $this->error_code_it;
                    throw new Exception('查询户' . $this->advertiser_id . '可用余额报错:' . $res['message']);
                }
                $balance = $res['data']['balance'];
            } elseif ($this->platform == reportEnum::ADQ) { //ADQ
                $data = Adq::getBalance($this->accessToken, $this->advertiser_id, '', '');
                foreach ($data as $item) {
                    $balance = BcHelper::add($balance, $item['balance']);
                }
                $balance = BcHelper::div($balance, 100);
            } else {
                $this->code = $this->error_code_it;
                throw new Exception('查询户' . $this->advertiser_id . '，充值平台不存在，请联系信息部处理');
            }
        }

        if ($this->amount > $balance) {
            Yii::$app->cache->delete($key);
            $this->code = $this->error_code_insufficient_balance;
            $errorMsg = '充值失败，主体：' . $this->mainBody . '（' . $this->advertiser_id . '）， ' . '账户余额不足，剩余：' . $balance . '，请联系推广管理人员处理';
            throw new Exception($errorMsg);
        }

        return $balance;
    }

    /**
     * 备用金账户列表
     */
    public function accountList()
    {
        $key = AdsMainBody::$reidsKey;
        $redis = Yii::$app->cache;
        $list = $redis->get($key);
        if ($list) {
            return $list;
        }

        $list = AdsMainBody::find()
            ->select('name,sub_advertiser_id')
            ->where(['<>', 'sub_advertiser_id', ''])
            ->andWhere(['status' => 1])
            ->asArray()->all();

        $list = array_column($list, 'sub_advertiser_id', 'name');
        $redis->set($key, $list, 3600);
        return $list;
    }

    /**
     * 查询余额
     */
    public function getAccountBalance()
    {
        try {
            $list = $this->accountList();
            if (empty($list)) {
                throw new Exception('要查询的账户不能为空');
            }

            $content = '';
            foreach ($list as $name => $account) {
                $this->advertiser_id = $account;
                $this->mainBody = $name;
                $this->setToken();
                $this->setPlatform();
                if ($this->platform == reportEnum::TIKTOL) {
                    $res = Oceanengine::getFund($this->accessToken, $this->advertiser_id);
                    if (!isset($res['code']) || $res['code'] != 0) {
                        throw new Exception('查询户' . $this->advertiser_id . '可用余额报错:' . $res['message']);
                    }
                    $balance = $res['data']['balance'];
                    $content .= $name . '：' . $balance . PHP_EOL;
                } elseif ($this->platform == reportEnum::ADQ) {
                    $data = Adq::getBalance($this->accessToken, $this->advertiser_id, '', '');
                    $balance = 0;
                    foreach ($data as $item) {
                        $balance = BcHelper::add($balance, $item['balance']);
                    }
                    $balance = BcHelper::div($balance, 100);
                    $content .= $name . '：' . $balance . PHP_EOL;
                } else {
                    $this->code = $this->error_code_it;
                    throw new Exception('查询户' . $this->advertiser_id . '，充值平台不存在，请联系信息部处理');
                }
            }

            return $content;
        } catch (Exception $e) {
            throw new Exception('查询余额失败，原因：' . $e->getMessage());
        }
    }

    /**
     * 发送余额通知
     */
    public function sendAccountBalanceNotice()
    {
        try {
            $list = $this->accountList();
            if (empty($list)) return false;

            $content = '';
            foreach ($list as $name => $account) {
                $this->advertiser_id = $account;
                $this->mainBody = $name;
                $this->setToken();
                $this->setPlatform();
                if ($this->platform == reportEnum::TIKTOL) {
                    $res = Oceanengine::getFund($this->accessToken, $this->advertiser_id);
                    if (!isset($res['code']) || $res['code'] != 0) {
                        throw new Exception('查询户' . $this->advertiser_id . '可用余额报错:' . $res['message']);
                    }
                    $balance = $res['data']['balance'];
                    $content .= $name . '：' . $balance . PHP_EOL;
                } elseif ($this->platform == reportEnum::ADQ) {
                    $data = Adq::getBalance($this->accessToken, $this->advertiser_id, '', '');
                    $balance = 0;
                    foreach ($data as $item) {
                        $balance = BcHelper::add($balance, $item['balance']);
                    }
                    $balance = BcHelper::div($balance, 100);
                    $content .= $name . '：' . $balance . PHP_EOL;
                } else {
                    $this->code = $this->error_code_it;
                    throw new Exception('查询户' . $this->advertiser_id . '，充值平台不存在，请联系信息部处理');
                }
            }

            $data['content'] = $content;

            $baseUrl = 'https://www.feishu.cn/flow/api/trigger-webhook/30db93e6f3128907c756db4f3e7ae1f4';
            $client = new Client();
            $client->post($baseUrl, (string)json_encode($data), [
                "Content-Type" => "application/json;charset=utf-8",
            ])->send();
        } catch (Exception $e) {
            Yii::error('广告账户余额通知失败：' . $e->getMessage());
        }

        return true;
    }
}
