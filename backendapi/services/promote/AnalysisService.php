<?php

/**
 * Created by PhpStorm
 * User: ldz
 * Date:2021/9/6
 * Time:19:41
 */

namespace backendapi\services\promote;

use backendapi\services\wxcom\CusTagService;
use auth\models\Store;
use common\models\feishu\StoreSchedule;
use backendapi\models\Customer;
use backendapi\models\promote\Code;
use backendapi\services\ConfigService;
use common\models\promote\AdsMaterialLabel;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\PlatformEnum;
use common\enums\PromotionEventTypeEnum;
use common\enums\AdsMaterialLabelTypeEnum;
use common\helpers\ArrayHelper;
use common\helpers\BcHelper;
use common\helpers\DateHelper;
use common\helpers\NumberHelper;
use common\helpers\Tool;
use common\models\backend\Member;
use common\models\backend\order\OrderHeader;
use common\models\backend\order\OrderPlanDetail;
use common\models\common\AdsAccountData;
use common\models\common\AdsAccountDataCity;
use common\models\common\AdsAccountDataHour;
use common\models\common\AdsAccountSub;
use common\models\common\AdsAgeGenderData;
use common\models\common\AdsMaterialData;
use common\models\common\Department;
use common\models\common\DepartmentAssignment;
use common\models\common\PromotionUserAnalysis;
use common\models\common\PromotionUserAnalysisLog;
use common\models\rbac\AuthAssignment;
use common\models\wechat\FansRecord;
use common\models\wechat\WechatUser;
use common\models\wxcom\CusCustomerUser;
use console\models\WxcomCusCustomerUser;
use services\UserService;
use Yii;
use common\models\promote\AdsCusLabel;
use yii\db\Expression;
use yii\db\Query;

class AnalysisService
{
    /**
     * 获取城市分析数据
     *
     * @param array $params
     * @param bool $export
     * @return array
     * @throws \Exception
     */
    public static function cityData($params = [], $export = false)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        list($scope, $responsibleId) = Tool::getAuthScope();

        $query = AdsAccountDataCity::find()->alias('ad_city')
            ->select('SUM(ad_city.click) as click,SUM(IF(ad_sub.report_event = 1 or ad_city.promote_id != 1,ad_city.`convert`,ad_city.`form`)) as convert,
            SUM(ad_city.show) as show,SUM(ad_city.cost) as cost,ad_city.city_id,ad_city.city_name,SUM(ad_city.actual_consume) as actual_consume,
            SUM(ad_city.deposit_count) as deposit_count,SUM(ad_city.new_store_cus_count) as new_store_cus_count')
            ->leftJoin(['{{%ads_account_sub}} ad_sub'], 'ad_sub.id = ad_city.ads_sub_id')
            ->leftJoin(['{{%ads_account}} ad_ac'], 'ad_ac.id = ad_sub.td_id');

        $start_time = DateHelper::toDate(strtotime($params['start_time']), 'Ymd');
        $end_time = DateHelper::toDate(strtotime($params['end_time']), 'Ymd');

        //项目搜索
        $params['project_id'] = static::getProjectId($params['project_type'], $params['project_id']);
        $query
            ->where(['ad_city.entity_id' => UserService::getInst()->current_entity_id])
            ->andFilterWhere(['between', 'ad_city.date', $start_time, $end_time])
            ->andFilterWhere(['ad_city.province_id' => $params['province_id']])
            ->andFilterWhere(['ad_city.city_id' => $params['city_id']])
            ->andFilterWhere(['ad_city.promote_id' => $params['promote_id']])
            ->andFilterWhere(['ad_city.project_id' => $params['project_id']])
            ->andFilterWhere(['ad_city.responsible_id' => $params['responsible_id']])
            ->andFilterWhere(['ad_city.direction_id' => $params['direction_id']])
            ->andFilterWhere(['ad_sub.sub_advertiser_id' => $params['sub_advertiser_id']])
            ->andFilterWhere(['ad_sub.sub_advertiser_name' => $params['sub_advertiser_name']])
            ->andFilterWhere(['or', ['ad_city.responsible_id' => $responsibleId], ['ad_city.dept_id' => $scope]])
            ->andFilterWhere(['ad_ac.platform' => $params['platform']])
            ->andFilterWhere(['ad_sub.main_body_id' => $params['main_body_id']]);

        if (isset($params['dept_id']) && $params['dept_id']) {
            $query->andWhere(['ad_city.dept_id' => Department::getManageDeptIdsByIds($params['dept_id'])]);
        }

        //排序
        if (!empty($params['column']) && in_array($params['column'], ['cost', 'click', 'convert'])) {
            $orderBy = $params['column'] . ' ' . ArrayHelper::getValue($params, 'order', 'desc');
        } else {
            $orderBy = 'ad_city.cost desc';
        }
        $groupBy = 'ad_city.city_id';

        $sum = $query->asArray()->one();
        if ($sum) $sum['city_name'] = '合计';

        $isLookAuth = self::isLookAuth();

        $cityQuery = static::cityAmountQuery($params);
        $citydata = $cityQuery->groupBy('s.city_id')->asArray()->all();
        $citydata = ArrayHelper::map($citydata, 'city_id', 'amount');

        // 获取门店排班统计数据
        $scheduleStats = StoreSchedule::getCityStoreScheduleStats($start_time, $end_time);
        $storeScheduleData = $scheduleStats['data'];
        $sumStoreScheduleData = $scheduleStats['summary'];

        if ($export) {  //导出数据
            if (!empty($params['getTotal'])) return [[], $query->groupBy($groupBy)->count()];
            $list = $query->offset(($page - 1) * $limit)->limit($limit)->orderBy($orderBy . ',ad_city.id ASC')->groupBy($groupBy)->asArray()->all();
            foreach ($list as &$v) {
                $v['cost_ratio'] = NumberHelper::replenish(NumberHelper::division($v['cost'], $sum['cost'], 4), 100) . '%';
                if (!$isLookAuth) {
                    $v['conversion_cost'] = $v['actual_consume'] = '-';
                } else {
                    $v['conversion_cost'] = BcHelper::div($v['actual_consume'], $v['convert']);
                }
                $v['click_through_rate'] = (BcHelper::div($v['click'], $v['show'], 4) * 100) . '%';
                $v['conversion_rate'] = (BcHelper::div($v['convert'], $v['click'], 4) * 100) . '%';
                $v['book_conversion_cost'] = BcHelper::div($v['cost'], $v['convert']);

                $v['deposit_cost'] = BcHelper::div($v['cost'], $v['deposit_count']);
                $v['real_deposit_cost'] = BcHelper::div($v['actual_consume'], $v['deposit_count']);
                $v['deposit_rate'] = (BcHelper::div($v['deposit_count'], $v['convert'], 4) * 100) . '%';
                $v['store_cost'] = BcHelper::div($v['cost'], $v['new_store_cus_count']);
                $v['real_store_cost'] = BcHelper::div($v['actual_consume'], $v['new_store_cus_count']);
                $v['fans_arrive_store_rate'] = (BcHelper::div($v['new_store_cus_count'], $v['convert'], 4) * 100) . '%';
                $v['amount'] = $citydata[$v['city_id']] ?? 0;
                $v['customer_price'] = BcHelper::div($v['amount'], $v['new_store_cus_count']);
                $v['amount_cost_rate'] = BcHelper::div($v['amount'], $v['actual_consume']);
                $v['book_roi'] = BcHelper::div($v['amount'], $v['cost']);
                $v['teacher_num'] = $storeScheduleData[$v['city_id']]['teacher_num'] ?? 0;
                $v['customer_service_num'] = $storeScheduleData[$v['city_id']]['customer_service_num'] ?? 0;
                $v['reschedule_num'] = $storeScheduleData[$v['city_id']]['reschedule_num'] ?? 0;
                $v['full_load_rate'] = BcHelper::percentage($v['new_store_cus_count'], $v['customer_service_num']);
                if (!$isLookAuth) {
                    $v['deposit_cost'] = $v['deposit_rate'] =  $v['real_deposit_cost'] = $v['store_cost'] = $v['fans_arrive_store_rate'] = $v['real_store_cost'] = '-';
                    $v['amount_cost_rate'] = '-';
                }
            }

            return [$list, 0];
        } else {
            $totalCount = $query->groupBy($groupBy)->count();
            $list = $query->offset(($page - 1) * $limit)->limit($limit)->orderBy($orderBy)->groupBy($groupBy)->asArray()->all();
            foreach ($list as &$v) {
                $v['cost_ratio'] = NumberHelper::replenish(NumberHelper::division($v['cost'], $sum['cost'], 4), 100) . '%';
                if (!$isLookAuth) {
                    $v['conversion_cost'] = $v['actual_consume'] = '-';
                } else {
                    $v['conversion_cost'] = BcHelper::div($v['actual_consume'], $v['convert']);
                }
                $v['click_through_rate'] = (BcHelper::div($v['click'], $v['show'], 4) * 100) . '%';
                $v['conversion_rate'] = (BcHelper::div($v['convert'], $v['click'], 4) * 100) . '%';
                $v['book_conversion_cost'] = BcHelper::div($v['cost'], $v['convert']);

                $v['deposit_cost'] = BcHelper::div($v['cost'], $v['deposit_count']);
                $v['real_deposit_cost'] = BcHelper::div($v['actual_consume'], $v['deposit_count']);
                $v['deposit_rate'] = (BcHelper::div($v['deposit_count'], $v['convert'], 4) * 100) . '%';
                $v['store_cost'] = BcHelper::div($v['cost'], $v['new_store_cus_count']);
                $v['real_store_cost'] = BcHelper::div($v['actual_consume'], $v['new_store_cus_count']);
                $v['fans_arrive_store_rate'] = (BcHelper::div($v['new_store_cus_count'], $v['convert'], 4) * 100) . '%';
                $v['amount'] = $citydata[$v['city_id']] ?? 0;
                $v['customer_price'] = BcHelper::div($v['amount'], $v['new_store_cus_count']);
                $v['amount_cost_rate'] = BcHelper::div($v['amount'], $v['actual_consume']);
                $v['book_roi'] = BcHelper::div($v['amount'], $v['cost']);
                $v['teacher_num'] = $storeScheduleData[$v['city_id']]['teacher_num'] ?? 0;
                $v['customer_service_num'] = $storeScheduleData[$v['city_id']]['customer_service_num'] ?? 0;
                $v['reschedule_num'] = $storeScheduleData[$v['city_id']]['reschedule_num'] ?? 0;
                $v['full_load_rate'] = BcHelper::percentage($v['new_store_cus_count'], $v['customer_service_num']);
                if (!$isLookAuth) {
                    $v['deposit_cost'] = $v['deposit_rate'] =  $v['real_deposit_cost'] = $v['store_cost'] = $v['fans_arrive_store_rate'] = $v['real_store_cost'] = '-';
                    $v['amount_cost_rate'] = '-';
                }
            }
            if ($list) {
                $sumAmount = array_sum($citydata);
                $sum['actual_consume'] = $isLookAuth ? $sum['actual_consume'] : '-';
                $sum['click_through_rate'] = (BcHelper::div($sum['click'], $sum['show'], 4) * 100) . '%';
                $sum['conversion_rate'] =  (BcHelper::div($sum['convert'], $sum['click'], 4) * 100) . '%';
                $sum['book_conversion_cost'] =  BcHelper::div($sum['cost'], $sum['convert']);
                $sum['conversion_cost'] = $isLookAuth ? BcHelper::div($sum['actual_consume'], $sum['convert']) : '-';

                $sum['deposit_cost'] = BcHelper::div($sum['cost'], $sum['deposit_count']);
                $sum['real_deposit_cost'] = BcHelper::div($sum['actual_consume'], $sum['deposit_count']);
                $sum['deposit_rate'] = (BcHelper::div($sum['deposit_count'], $sum['convert'], 4) * 100) . '%';
                $sum['store_cost'] = BcHelper::div($sum['cost'], $sum['new_store_cus_count']);
                $sum['real_store_cost'] = BcHelper::div($sum['actual_consume'], $sum['new_store_cus_count']);
                $sum['fans_arrive_store_rate'] = (BcHelper::div($sum['new_store_cus_count'], $sum['convert'], 4) * 100) . '%';
                $sum['amount'] = $sumAmount;
                $sum['customer_price'] = BcHelper::div($sum['amount'], $sum['new_store_cus_count']);
                $sum['amount_cost_rate'] = BcHelper::div($sum['amount'], $sum['actual_consume']);
                $sum['book_roi'] = BcHelper::div($sum['amount'], $sum['cost']);
                $sum['teacher_num'] = $sumStoreScheduleData['teacher_num'];
                $sum['customer_service_num'] = $sumStoreScheduleData['customer_service_num'];
                $sum['reschedule_num'] = $sumStoreScheduleData['reschedule_num'];
                $sum['full_load_rate'] = BcHelper::percentage($sum['new_store_cus_count'], $sum['customer_service_num']);
                if (!$isLookAuth) {
                    $sum['amount_cost_rate'] = '-';
                    $sum['deposit_cost'] = $sum['deposit_rate'] =  $sum['real_deposit_cost'] = $sum['store_cost'] = $sum['fans_arrive_store_rate'] = $sum['real_store_cost'] = '-';
                }
                array_unshift($list, $sum);
            }

            return [$list, $totalCount];
        }
    }

    /**
     * 获取城市消耗的起始时间
     *
     * @param string $type
     * @return false|string
     */
    public static function getTime($type = 'start')
    {
        $order_by = ($type == 'start') ? 'asc' : 'desc';
        $info = AdsAccountDataCity::find()->select('date')->asArray()->orderBy('date ' . $order_by)->limit(1)->one();
        return empty($info['date']) ? '' : DateHelper::toDate(strtotime($info['date']), 'Y-m-d');
    }

    /**
     * 获取每日城市数据-明细
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public static function getCityDailyData($params)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 1000);   //条数

        list($scope, $responsibleId) = Tool::getAuthScope();

        $query = AdsAccountDataCity::find()->alias('ad_city')
            ->select('ad_city.date,ad_sub.sub_advertiser_name,ad_sub.sub_advertiser_id,ad_city.platform_city_name as city_name,ad_city.cost,IF(ad_sub.report_event = 1 or ad_city.promote_id != 1,ad_city.`convert`,ad_city.`form`) as convert,ad_city.deposit_count,ad_city.new_store_cus_count,ad_ac.platform,ad_city.rebates,l.name as link_name,p.name as project_name,m.realname,ad_city.responsible_id,pd.name as direction_name,ad_city.ctr,ad_city.show,ad_city.click,ad_city.actual_consume,ad_city.city_id')
            ->leftJoin(['{{%ads_account_sub}} ad_sub'], 'ad_sub.id = ad_city.ads_sub_id')
            ->leftJoin(['{{%ads_account}} ad_ac'], 'ad_ac.id = ad_sub.td_id')
            ->leftJoin(['{{%backend_member}} m'], 'm.id = ad_city.responsible_id')
            ->leftJoin(['{{%promote_project}} p'], 'p.id = ad_city.project_id')
            ->leftJoin(['{{%promote_direction}} pd'], 'pd.id = ad_city.direction_id')
            ->leftJoin(['{{%promote_link}} l'], 'l.id = ad_city.link_id');

        $start_time = DateHelper::toDate(strtotime($params['start_time']), 'Ymd');
        $end_time = DateHelper::toDate(strtotime($params['end_time']), 'Ymd');

        //项目搜索
        $params['project_id'] = static::getProjectId($params['project_type'], $params['project_id']);

        $query->andFilterWhere(['between', 'ad_city.date', $start_time, $end_time])
            ->andFilterWhere(['ad_city.province_id' => $params['province_id']])
            ->andFilterWhere(['ad_city.city_id' => $params['city_id']])
            ->andFilterWhere(['ad_sub.sub_advertiser_id' => $params['sub_advertiser_id']])
            ->andFilterWhere(['ad_sub.sub_advertiser_name' => trim($params['sub_advertiser_name'])])
            ->andFilterWhere(['ad_sub.main_body_id' => $params['main_body_id']])
            ->andFilterWhere(['ad_ac.platform' => $params['platform']])
            ->andFilterWhere(['ad_city.promote_id' => $params['promote_id']])
            ->andFilterWhere(['ad_city.project_id' =>  $params['project_id']])
            ->andFilterWhere(['ad_city.responsible_id' => $params['responsible_id']])
            ->andFilterWhere(['ad_city.direction_id' => $params['direction_id']])
            ->andFilterWhere(['or', ['ad_city.responsible_id' => $responsibleId], ['ad_city.dept_id' => $scope]]);

        if (isset($params['dept_id']) && $params['dept_id']) {
            $query->andWhere(['ad_city.dept_id' => Department::getManageDeptIdsByIds($params['dept_id'])]);
        }

        if (!empty($params['getTotal'])) return [[], $query->count()];

        $list = $query->offset(($page - 1) * $limit)->limit($limit)->orderBy('ad_city.date desc,ad_city.cost desc,ad_city.id ASC')->asArray()->all();

        $userIds = ArrayHelper::getColumn($list, 'responsible_id');
        $userIds = array_unique($userIds);

        $arr_user_dept = DepartmentAssignment::find()->select('user_id,dept_id')->where(['user_id' => $userIds])->groupBy('user_id')->asArray()->all();
        $user_dept = ArrayHelper::map($arr_user_dept, 'user_id', 'dept_id');
        
        // 获取是否有查看权限
        $isLookAuth = self::isLookAuth();

        $cityQuery = static::cityAmountQuery($params);
        $dateQueryResults = $cityQuery
            ->select([
                'FROM_UNIXTIME( oh.plan_time ,"%Y%m%d") as date_str',
                's.city_id',
                'ccu.sub_advertiser_id',
                'SUM(IFNULL(oh.received_amount, 0) + IFNULL(oh.card_real_amount, 0) + IFNULL(oh.group_amount, 0)) AS amount'
            ])
            ->groupBy(['date_str', 's.city_id', 'ccu.sub_advertiser_id'])
            ->asArray()
            ->all();
        
        // 使用日期、城市ID和子广告主ID作为键保存amount数据
        $cityData = [];
        foreach ($dateQueryResults as $result) {
            $dateStr = $result['date_str'];
            $cityId = $result['city_id'];
            $subAdvertiserId = $result['sub_advertiser_id'];
            $key = $dateStr . '_' . $cityId . '_' . $subAdvertiserId;
            $cityData[$key] = $result['amount'];
        }
        
        foreach ($list as &$item) {
            $item['dept_name'] = $user_dept[$item['responsible_id']] ? Department::getName($user_dept[$item['responsible_id']]) : '';
            $originalDate = $item['date'];
            // 格式化显示的日期
            $item['date'] = $item['date'] ? DateHelper::toDate(strtotime($item['date']), 'Y-m-d') : '';
            $item['platform'] = PlatformEnum::getValue($item['platform']);
            $item['convert_rate'] = (BcHelper::div($item['convert'], $item['click'], 4) * 100) . '%';
            
            // 使用组合键获取对应的amount
            $key = $originalDate . '_' . $item['city_id'] . '_' . $item['sub_advertiser_id'];

            $item['book_roi'] = BcHelper::div($item['amount'], $item['cost']);
            if (!$isLookAuth) {
                $item['actual_consume'] = '-';
                $item['deposit_rate'] = '-';
                $item['amount_cost_rate'] = '-';
                $item['amount'] = '-';
                $item['customer_price'] = '-';
            } else {
                $item['deposit_rate'] = (BcHelper::div($item['deposit_count'], $item['convert'], 4) * 100) . '%';
                $item['amount'] = isset($cityData[$key]) ? $cityData[$key] : 0;
                $item['customer_price'] = BcHelper::div($item['amount'], $item['new_store_cus_count']);
                $item['amount_cost_rate'] = BcHelper::div($item['amount'], $item['actual_consume']);
            }
        }

        return [$list, 0];
    }

    public static function cityAmountQuery($params)
    {
        list($scope, $responsibleId) = Tool::getAuthScope();

        $startTime = strtotime($params['start_time']);
        $endTime = strtotime($params['end_time']) + 86400 - 1;
        $query = OrderHeader::find()
            ->alias('oh')
            ->select([
                's.city_id',
                'SUM(
                    IFNULL(oh.received_amount, 0) + IFNULL(oh.card_real_amount, 0) + IFNULL(oh.group_amount, 0)
                ) AS amount'
            ])
            ->leftJoin(['s' => Store::tableName()], 's.id = oh.store_id')
            ->leftJoin(['cus' => Customer::tableName()], 'cus.id = oh.cus_id')
            ->leftJoin(['pc' => Code::tableName()], 'pc.code = cus.code')
            ->leftJoin(['ccu' => WxcomCusCustomerUser::tableName()], 'ccu.id = oh.customer_user_id')
            ->leftJoin(['da' => departmentAssignment::tableName()], 'da.user_id = pc.user_id')
            ->where(['oh.order_status' => OrderHeaderStatusEnum::STATUS_COMPLETED])
            ->andWhere(['between', 'oh.plan_time', $startTime, $endTime])
            ->andWhere(['<>', 'cus.code', ''])
            ->andFilterWhere(['pc.user_id' => $params['responsible_id']])
            ->andFilterWhere(['s.province_id' => $params['province_id']])
            ->andFilterWhere(['s.city_id' => $params['city_id']])
            ->andFilterWhere(['ccu.sub_advertiser_name' => trim($params['sub_advertiser_name'])])
            ->andFilterWhere(['ccu.sub_advertiser_id' => trim($params['sub_advertiser_id'])])
            ->andFilterWhere(['ccu.channel_id' => $params['promote_id']])
            ->andFilterWhere(['ccu.direction_id' => $params['direction_id']])
            ->andFilterWhere(['ccu.project_id' => $params['project_id']])
            ->andFilterWhere(['or', ['pc.user_id' => $responsibleId], ['da.dept_id' => $scope]]);

        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $query->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = ccu.sub_advertiser_id')
                ->andWhere(['main_body_id' => $params['main_body_id']]);
        }

        if (isset($params['dept_id']) && $params['dept_id']) {
            $query->andWhere(['da.dept_id' => Department::getManageDeptIdsByIds($params['dept_id'])]);
        }

        return $query;
    }

    /**
     * 投放人分析 - 列表
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getCastPeopleData($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $params['entity_id'] = Yii::$app->user->identity->current_entity_id;

        //项目搜索
        $params['project_id'] = static::getProjectId($params['project_type'], $params['project_id']);

        list($query, $start_time, $end_time) = static::dealCastPeopleDataList($params);
        // 获取加粉人数和订金数
        list($cusCustomerUserArr, $fansRecordArr, $sumDepositArr, $sumNotDepositArr, $fansRecordMoney, $fansNotRecordMoney) = self::getFansAndSumDeposit($params, $start_time, $end_time + 86399);
        $sumWxcomAddFans = empty($cusCustomerUserArr) ? 0 : array_sum(array_column($cusCustomerUserArr, 'wxcom_add_fans'));
        $sumWechatAddFans = empty($fansRecordArr) ? 0 : array_sum(array_column($fansRecordArr, 'wechat_add_fans'));
        $allSumAddFans = $sumWxcomAddFans + $sumWechatAddFans;

        //新客当月到店人数、当月业绩
        $groupByFileld = 'promoter_user_id';
        $monthCusCountList = self::getMonthCusCountList($params, $start_time, $end_time + 86399, $groupByFileld);
        $sumMonthCusCount = empty($monthCusCountList) ? 0 : array_sum(array_column($monthCusCountList, 'cus_count'));
        $sumMonthReceivedAmount = empty($monthCusCountList) ? 0 : array_sum(array_column($monthCusCountList, 'month_received_amount'));

        //当月订金人数
        $monthDepositCountList = self::getMonthDepositCount($params, $start_time, $end_time + 86399, $groupByFileld);
        $sumMonthDepositCount = empty($monthDepositCountList) ? 0 : array_sum(array_column($monthDepositCountList, 'month_deposit_cus_count'));

        //删粉人数
        $delFansList = self::getDelFansList($params, $start_time, $end_time + 86399);
        $sumDelFansCount = empty($delFansList) ? 0 : array_sum(array_column($delFansList, 'del_fans_count'));

        //不回复人数
        $notReplyList = self::getNotReplyList($params, $start_time, $end_time + 86399);
        $sumNotReplyCount = empty($notReplyList) ? 0 : array_sum(array_column($notReplyList, 'not_reply_count'));

        $sumWxcomDeposit = empty($sumDepositArr) ? 0 : array_sum(array_column($sumDepositArr, 'wxcom_deposit'));
        $sumWechatDeposit = empty($fansRecordMoney) ? 0 : array_sum(array_column($fansRecordMoney, 'wechat_deposit'));
        $sumDeposit = $sumWxcomDeposit + $sumWechatDeposit;

        $sumWxcomNotDeposit = empty($sumNotDepositArr) ? 0 : array_sum(array_column($sumNotDepositArr, 'wxcom_not_deposit'));
        $sumWechatNotDeposit = empty($fansNotRecordMoney) ? 0 : array_sum(array_column($fansNotRecordMoney, 'wechat_not_deposit'));
        $sumNotDeposit = $sumWxcomNotDeposit + $sumWechatNotDeposit;

        $completeData = static::getCompleteData($params, $start_time, $end_time + 86399);
        $sumNewStoreCusCount = empty($completeData) ? 0 : array_sum(array_column($completeData, 'new_store_cus_count'));
        $sumNotDepositNewStoreCusCount = empty($completeData) ? 0 : array_sum(array_column($completeData, 'not_deposit_new_store_cus_count'));
        $sumAmount = empty($completeData) ? 0 : array_sum(array_column($completeData, 'received_amount'));

        //获取消耗汇总数据
        $select_field = ['ad.responsible_id,bm.realname AS responsible_name,"" AS `name`,"" AS promote_name,SUM(ad.sum_cost) AS cost,SUM(ad.sum_compensate) AS compensate,round(sum(real_cost),2) as real_cost,SUM(ad.sum_click) AS click,SUM(ad.sum_show) AS `show`,SUM(ad.sum_convert) AS `convert`,SUM(IFNULL(dps.wxcom_add_fans,0)) AS wxcom_add_fans,SUM(IFNULL(dps.wxcom_add2_fans,0)) AS wxcom_add2_fans
        ,SUM(IFNULL(dps.wechat_add_fans,0)) AS wechat_add_fans,SUM(IFNULL(dps.sum_add_fans,0)) AS sum_add_fans,SUM(IFNULL(dps.wxcom_deposit,0)) AS wxcom_deposit,SUM(IFNULL(dps.wechat_deposit,0)) AS wechat_deposit,SUM(IFNULL(dps.sum_deposit,0)) AS sum_deposit,SUM(IFNULL(bis_promote.new_store_cus_count,0)) AS new_store_cus_count,SUM(IFNULL(bis_promote.amount,0)) AS amount,SUM(IFNULL(ad.attrition_count,0)) AS attrition_count
        ,SUM(IFNULL(ad.remote_count,0)) AS remote_count,SUM(IFNULL(ad.repeat_fans_count,0)) AS repeat_fans_count'];
        list($sm_query) = static::dealCastPeopleDataList($params, $select_field);

        //无效数
        $invalidList = self::getInvalidData($params, $start_time, $end_time + 86399, $groupByFileld);
        $invalid_num = empty($invalidList) ? 0 : array_sum(array_column($invalidList, 'invalid_num'));

        $sum = $sm_query->asArray()->one();
        if ($sum) {
            $sum['new_store_cus_count'] = BcHelper::add($sum['new_store_cus_count'], $sumNewStoreCusCount, 0);
            $sum['not_deposit_new_store_cus_count'] = $sumNotDepositNewStoreCusCount;
            $sum['amount'] = BcHelper::add($sum['amount'], $sumAmount);
            $sum['name'] = '';
            $sum['responsible_id'] = '';
            $sum['responsible_name'] = '合计';
            $sum['sub_advertiser_id'] = '';
            $sum['wxcom_add_fans'] = $sumWxcomAddFans;
            $sum['wechat_add_fans'] = $sumWechatAddFans;
            $sum['sum_add_fans'] = $allSumAddFans;
            $sum['wxcom_deposit'] = $sumWxcomDeposit;
            $sum['wechat_deposit'] = $sumWechatDeposit;
            $sum['sum_deposit'] = $sumDeposit;
            $sum['wxcom_not_deposit'] = $sumWxcomNotDeposit;
            $sum['wechat_not_deposit'] = $sumWechatNotDeposit;
            $sum['sum_not_deposit'] = $sumNotDeposit;
            $sum['fans_arrive_store_rate'] = (BcHelper::div($sum['new_store_cus_count'], $allSumAddFans, 4) * 100) . '%';  // 新增加粉转到店率=新客到店人数/加粉数
            $sum['add_fans_cost'] = BcHelper::div($sum['cost'], $allSumAddFans);           //账面加粉成本
            $sum['deposit_cost'] = BcHelper::div($sum['cost'], $sumDeposit);             //账面定金成本
            $sum['real_add_fans_cost'] = BcHelper::div($sum['real_cost'], $allSumAddFans); //实际加粉成本
            $sum['real_deposit_cost'] = BcHelper::div($sum['real_cost'], $sumDeposit);   //实际定金成本
            $sum['conversion_cost'] = BcHelper::div($sum['cost'], $sum['convert']);              //账面转化成本
            $sum['deposit_rate'] = BcHelper::sprintf(BcHelper::div($sumDeposit, $allSumAddFans, 4) * 100) . '%';    //加粉订金转化率
            $sum['store_cost'] = BcHelper::div($sum['cost'], $sum['new_store_cus_count']);    //账面到店成本
            $sum['real_store_cost'] = BcHelper::div($sum['real_cost'], $sum['new_store_cus_count']);    //实际到店成本
            $sum['amount_cost_rate'] = BcHelper::div($sum['amount'], $sum['real_cost']);    //ROI
            $sum['book_roi'] = BcHelper::div($sum['amount'], $sum['cost']);    //账面ROI
            $sum['real_conversion_cost'] = BcHelper::div($sum['real_cost'], $sum['convert']);    //实际转化成本
            $sum['customer_price'] = BcHelper::div($sum['amount'], $sum['new_store_cus_count']); //客单价
            $sum['attrition_count_rate'] = (BcHelper::div($sum['attrition_count'], $sum['new_store_cus_count'], 4) * 100) . '%'; //流失率
            $sum['remote_count_rate'] = (BcHelper::div($sum['remote_count'], $allSumAddFans ?: 1, 4) * 100) . '%'; //偏远率
            $sum['sum_month_cus_count'] = $sumMonthCusCount; //当月到店新客人数
            $sum['sum_month_cus_count_rate'] = (BcHelper::div($sumMonthCusCount, $allSumAddFans ?: 1, 4) * 100) . '%'; //当月加人转到店率
            $sum['sum_month_deposit_count'] = $sumMonthDepositCount; //当月订金人数
            $sum['sum_month_deposit_count_rate'] = (BcHelper::div($sumMonthDepositCount, $allSumAddFans ?: 1, 4) * 100) . '%'; //当月订金转化率
            $sum['sum_month_real_deposit_cost'] = BcHelper::div($sum['real_cost'], $sumMonthDepositCount);   //当月定金成本
            $sum['del_fans_count'] = $sumDelFansCount; //删粉人数
            $sum['del_fans_count_rate'] = (BcHelper::div($sumDelFansCount, $allSumAddFans ?: 1, 4) * 100) . '%';; //删粉率
            $sum['not_reply_count'] = $sumNotReplyCount;
            $sum['not_reply_count_rate'] = (BcHelper::div($sum['not_reply_count'], $allSumAddFans ?: 1, 4) * 100) . '%';; //不回复率
            $sum['sum_month_received_amount'] = BcHelper::sprintf($sumMonthReceivedAmount); //当月业绩
            $sum['sum_month_amount_cost_rate'] = BcHelper::div($sumMonthReceivedAmount, $sum['real_cost']); //当月ROI
            $sum['invalid_num'] = $invalid_num; //无效数=偏远数+删粉数+不回复数
            $sum['invalid_rate'] = (BcHelper::div($sum['invalid_num'], $allSumAddFans ?: 1, 4) * 100) . '%';; //无效率
            $sum['repeat_fans_rate'] = (BcHelper::div($sum['repeat_fans_count'], $allSumAddFans ?: 1, 4) * 100) . '%';//重粉率

            if (!self::isLookAuth()) {
                $sum['real_cost'] = '-';
                $sum['real_add_fans_cost'] = '-';
                $sum['real_deposit_cost'] = '-';
                $sum['real_conversion_cost'] = '-';
                $sum['real_store_cost'] = '-';
                $sum['amount_cost_rate'] = '-';
            }
        }

        $query->groupBy('bm.id')->orderBy('ad.sum_cost DESC,sum_add_fans DESC,sum_deposit DESC,bm.id DESC');
        $totalCount = $query->count();
        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();
        foreach ($list as &$l) {
            $responsibleId = $l['responsible_id'] ?? 0;
            $l['wxcom_add_fans'] = 0;
            $l['wxcom_add2_fans'] = 0;
            $l['wechat_add_fans'] = 0;
            $l['wxcom_deposit'] = 0;
            $l['wechat_deposit'] = 0;
            $l['wxcom_not_deposit'] = 0;
            $l['wechat_not_deposit'] = 0;

            $l['sum_add_fans'] = 0;
            if (isset($cusCustomerUserArr[$responsibleId]) || isset($fansRecordArr[$responsibleId])) {
                $wxcomAddFans = ArrayHelper::getValue($cusCustomerUserArr[$responsibleId], 'wxcom_add_fans', 0);
                $wechatAddFans = ArrayHelper::getValue($fansRecordArr[$responsibleId], 'wechat_add_fans', 0);
                $l['wxcom_add_fans'] = $wxcomAddFans;
                $l['wechat_add_fans'] = $wechatAddFans;
                $l['sum_add_fans'] = $wxcomAddFans + $wechatAddFans;
            }
            $l['sum_deposit'] = 0;
            if (isset($sumDepositArr[$responsibleId]) || isset($fansRecordMoney[$responsibleId])) {
                $wxcomDeposit = ArrayHelper::getValue($sumDepositArr[$responsibleId], 'wxcom_deposit', 0);
                $wechatDeposit = ArrayHelper::getValue($fansRecordMoney[$responsibleId], 'wechat_deposit', 0);
                $l['wxcom_deposit'] = $wxcomDeposit;
                $l['wechat_deposit'] = $wechatDeposit;
                $l['sum_deposit'] = $wxcomDeposit + $wechatDeposit;
            }
            $l['sum_not_deposit'] = 0;
            if (isset($sumNotDepositArr[$responsibleId]) || isset($fansNotRecordMoney[$responsibleId])) {
                $wxcomNotDeposit = ArrayHelper::getValue($sumNotDepositArr[$responsibleId], 'wxcom_not_deposit', 0);
                $wechatNotDeposit = ArrayHelper::getValue($fansNotRecordMoney[$responsibleId], 'wechat_not_deposit', 0);
                $l['wxcom_not_deposit'] = $wxcomNotDeposit;
                $l['wechat_not_deposit'] = $wechatNotDeposit;
                $l['sum_not_deposit'] = $wxcomNotDeposit + $wechatNotDeposit;
            }

            $l['not_deposit_new_store_cus_count'] = 0;
            if (isset($completeData[$responsibleId])) {
                $l['amount'] +=  ArrayHelper::getValue($completeData[$responsibleId], 'received_amount', 0);
                $l['new_store_cus_count'] +=  ArrayHelper::getValue($completeData[$responsibleId], 'new_store_cus_count', 0);
                $l['not_deposit_new_store_cus_count'] +=  ArrayHelper::getValue($completeData[$responsibleId], 'not_deposit_new_store_cus_count', 0);
            }

            $l['sum_month_cus_count'] = $l['sum_month_received_amount'] = 0;
            if (isset($monthCusCountList[$responsibleId])) {
                $l['sum_month_cus_count'] =  ArrayHelper::getValue($monthCusCountList[$responsibleId], 'cus_count', 0);
                $l['sum_month_received_amount'] =  ArrayHelper::getValue($monthCusCountList[$responsibleId], 'month_received_amount', 0);
            }

            $l['sum_month_deposit_count'] = 0;
            if (isset($monthDepositCountList[$responsibleId])) {
                $l['sum_month_deposit_count'] =  ArrayHelper::getValue($monthDepositCountList[$responsibleId], 'month_deposit_cus_count', 0);
            }

            $l['del_fans_count'] = 0;
            if (isset($delFansList[$responsibleId])) {
                $l['del_fans_count'] =  ArrayHelper::getValue($delFansList[$responsibleId], 'del_fans_count', 0);
            }

            $l['not_reply_count'] = 0;
            if (isset($notReplyList[$responsibleId])) {
                $l['not_reply_count'] =  ArrayHelper::getValue($notReplyList[$responsibleId], 'not_reply_count', 0);
            }

            $l['invalid_num'] = 0;
            if (isset($invalidList[$responsibleId])) {
                $l['invalid_num'] =  ArrayHelper::getValue($invalidList[$responsibleId], 'invalid_num', 0);
            }

            $l['sub_advertiser_id'] = $l['sub_advertiser_id'] ?: '-';
            $l['cost'] = $l['cost'] ?: 0;
            $l['real_cost'] = $l['real_cost'] ?: 0;
            $l['rebates'] = $l['rebates'] ?: 0;
            $l['compensate'] = $l['compensate'] ?: 0;
            $l['click'] = $l['click'] ?: 0;
            $l['show'] = $l['show'] ?: 0;
            $l['convert'] = $l['convert'] ?: 0;
            $l['fans_arrive_store_rate'] = (BcHelper::div($l['new_store_cus_count'], $l['sum_add_fans'], 4) * 100) . '%';  // 新增加粉转到店率=新客到店人数/加粉数
            $l['add_fans_cost'] = BcHelper::div($l['cost'], $l['sum_add_fans']);           //账面加粉成本
            $l['deposit_cost'] = BcHelper::div($l['cost'], $l['sum_deposit']);             //账面定金成本
            $l['real_add_fans_cost'] = BcHelper::div($l['real_cost'], $l['sum_add_fans']); //实际加粉成本
            $l['real_deposit_cost'] = BcHelper::div($l['real_cost'], $l['sum_deposit']);   //实际定金成本
            $l['conversion_cost'] = BcHelper::div($l['cost'], $l['convert']);              //账面转化成本
            $l['real_conversion_cost'] = BcHelper::div($l['real_cost'], $l['convert']);    //实际转化成本
            $l['deposit_rate'] = BcHelper::sprintf(BcHelper::div($l['sum_deposit'], $l['sum_add_fans'], 4) * 100) . '%';    //加粉订金转化率
            $l['store_cost'] = BcHelper::div($l['cost'], $l['new_store_cus_count']);    //账面到店成本
            $l['real_store_cost'] = BcHelper::div($l['real_cost'], $l['new_store_cus_count']);    //实际到店成本
            $l['amount_cost_rate'] = BcHelper::div($l['amount'], $l['real_cost']);    //ROI
            $l['book_roi'] = BcHelper::div($l['amount'], $l['cost']);    //账面ROI
            $l['sum_month_amount_cost_rate'] = BcHelper::div($l['sum_month_received_amount'], $l['real_cost']);    //当月ROI
            $l['customer_price'] = BcHelper::div($l['amount'], $l['new_store_cus_count']); //客单价
            $l['attrition_count_rate'] = (BcHelper::div($l['attrition_count'], $l['new_store_cus_count'], 4) * 100) . '%'; //流失率
            $l['remote_count_rate'] = (BcHelper::div($l['remote_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //偏远率
            $l['sum_month_cus_count_rate'] = (BcHelper::div($l['sum_month_cus_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //当月加人转到店率
            $l['sum_month_deposit_count_rate'] = (BcHelper::div($l['sum_month_deposit_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //当月订金转化率
            $l['sum_month_real_deposit_cost'] =  BcHelper::div($l['real_cost'], $l['sum_month_deposit_count']); //当月定金成本
            $l['del_fans_count_rate'] = (BcHelper::div($l['del_fans_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //删粉率
            $l['not_reply_count_rate'] = (BcHelper::div($l['not_reply_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //不回复率
            $l['invalid_rate'] = (BcHelper::div($l['invalid_num'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //无效率
            $l['repeat_fans_rate'] = (BcHelper::div($l['repeat_fans_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //重粉率

            if (!self::isLookAuth()) {
                $l['real_cost'] = '-';
                $l['real_add_fans_cost'] = '-';
                $l['real_deposit_cost'] = '-';
                $l['real_conversion_cost'] = '-';
                $l['real_store_cost'] = '-';
                $l['amount_cost_rate'] = '-';
            }
        }
        //合并汇总数据
        if ($list) array_unshift($list, $sum);

        return [$list, $totalCount, $start_time, $end_time];
    }

    public static function getFansAndSumDeposit($params, $startTime, $endTime)
    {
        // 本人的数据 or 管理范围之内
        list($scope, $responsibleId) = Tool::getAuthScope();

        // 企微加粉人数
        $wechatUser = WechatUser::find()->select('unionid')->all();
        $unionids = array_column($wechatUser, 'unionid');
        $deptIds = Department::getManageDeptIdsByIds($params['dept_id']);

        $cusCustomerUserArrQuery = CusCustomerUser::find()->alias('cu')
            ->select(['cu.qrcode_created_by,COUNT(DISTINCT cu.id) AS wxcom_add_fans'])
            ->leftJoin('{{%wxcom_cus_customer}} wcc', 'cu.cus_id = wcc.id')
            ->leftJoin('{{%department_assignment}} da', 'da.user_id = cu.qrcode_created_by')
            ->andWhere(['add_way' => CusCustomerUser::getComputeAddWay()])
            ->andFilterWhere(['not in', 'wcc.unionid', $unionids])
            ->andFilterWhere(['BETWEEN', 'cu.add_time', $startTime, $endTime])
            ->andFilterWhere(['OR', ['like', 'cu.sub_advertiser_name', $params['sub_advertiser_name']], ['=', 'cu.sub_advertiser_id', $params['sub_advertiser_name']]])
            ->andFilterWhere(['cu.link_id' => $params['link_id']])
            ->andFilterWhere(['cu.channel_id' => $params['promote_id']])
            ->andFilterWhere(['da.dept_id' => $deptIds])
            ->andFilterWhere(['cu.project_id' => $params['project_id']])
            ->andFilterWhere(['cu.direction_id' => $params['direction_id']])
            ->andFilterWhere(['cu.qrcode_created_by' => $params['responsible_id']])
            ->andFilterWhere(['and', ['>', 'cu.channel_id', 0], ['>', 'cu.qrcode_created_by', 0]])
            ->andFilterWhere(['or', ['cu.qrcode_created_by' => $responsibleId], ['da.dept_id' => $scope]]);

        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $cusCustomerUserArrQuery->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = cu.sub_advertiser_id')
                ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
        }

        $cusCustomerUserArr = $cusCustomerUserArrQuery->groupBy('cu.qrcode_created_by')->asArray()->all();
        if ($cusCustomerUserArr) {
            $cusCustomerUserArr = ArrayHelper::index($cusCustomerUserArr, 'qrcode_created_by');
        }
        $promoteDepositOrderStatus = OrderHeader::promoteDepositOrderStatusList();
        $fansRecordMoney = $fansNotRecordMoney = $fansRecordArrNumber = [];
        if (empty($params['sub_advertiser_name'])) {
            // 个微加粉
            $fansRecordArrNumberQuery = FansRecord::find()->alias('f')
                ->select(['f.responsible_id,COUNT(DISTINCT f.id) AS wechat_add_fans'])
                ->leftJoin('{{%department_assignment}} da', 'da.user_id = f.responsible_id')
                ->andWhere(['and', ['>', 'f.channel_id', 0], ['>', 'f.responsible_id', 0]])
                ->andFilterWhere(['BETWEEN', 'f.add_time', $startTime, $endTime])
                ->andFilterWhere(['f.link_id' => $params['link_id']])
                ->andFilterWhere(['f.channel_id' => $params['promote_id']])
                ->andFilterWhere(['da.dept_id' => $deptIds])
                ->andFilterWhere(['f.project_id' => $params['project_id']])
                ->andFilterWhere(['f.direction_id' => $params['direction_id']])
                ->andFilterWhere(['f.responsible_id' => $params['responsible_id']])
                ->andFilterWhere(['or', ['f.responsible_id' => $responsibleId], ['da.dept_id' => $scope]]);

            if (isset($params['main_body_id']) && $params['main_body_id']) {
                $fansRecordArrNumberQuery->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.responsible_id = f.responsible_id')
                    ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
            }

            $fansRecordArrNumber = $fansRecordArrNumberQuery->groupBy('f.responsible_id')->asArray()->all();
            if ($fansRecordArrNumber) {
                $fansRecordArrNumber = ArrayHelper::index($fansRecordArrNumber, 'responsible_id');
            }
            // 个微定金数
            $fansRecordMoneyQuery = OrderHeader::find()->alias('h')
                ->select(['h.promoter_user_id,COUNT(DISTINCT h.id) AS wechat_deposit'])
                ->leftJoin('{{%customer}} c', 'h.cus_id = c.id')
                ->leftJoin('{{%wechat_fans_record}} w', 'c.add_fans_id = w.id')
                ->leftJoin('{{%department_assignment}} da', 'da.user_id = h.promoter_user_id')
                ->andWhere(['h.order_status' => $promoteDepositOrderStatus])
                ->andWhere(['and', ['=', 'h.customer_user_id', 0], ['>', 'h.deposit', 0], ['>', 'h.promoter_user_id', 0]])
                ->andFilterWhere(['BETWEEN', 'h.pre_pay_time', $startTime, $endTime])
                ->andFilterWhere(['w.link_id' => $params['link_id']])
                ->andFilterWhere(['w.channel_id' => $params['promote_id']])
                ->andFilterWhere(['da.dept_id' => $deptIds])
                ->andFilterWhere(['w.project_id' => $params['project_id']])
                ->andFilterWhere(['w.direction_id' => $params['direction_id']])
                ->andFilterWhere(['h.promoter_user_id' => $params['responsible_id']])
                ->andFilterWhere(['or', ['h.promoter_user_id' => $responsibleId], ['da.dept_id' => $scope]]);

            if (isset($params['main_body_id']) && $params['main_body_id']) {
                $fansRecordMoneyQuery->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.responsible_id = w.responsible_id')
                    ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
            }

            $fansRecordMoney = $fansRecordMoneyQuery->groupBy('h.promoter_user_id')->asArray()->all();
            if ($fansRecordMoney) {
                $fansRecordMoney = ArrayHelper::index($fansRecordMoney, 'promoter_user_id');
            }
            // 个微0订金数
            $fansNotRecordMoneyQuery = OrderHeader::find()->alias('h')
                ->select(['h.promoter_user_id,COUNT(DISTINCT h.id) AS wechat_not_deposit'])
                ->leftJoin('{{%customer}} c', 'h.cus_id = c.id')
                ->leftJoin('{{%wechat_fans_record}} w', 'c.add_fans_id = w.id')
                ->leftJoin('{{%department_assignment}} da', 'da.user_id = h.promoter_user_id')
                ->andWhere(['h.order_status' => $promoteDepositOrderStatus])
                ->andWhere(['and', ['=', 'h.customer_user_id', 0], ['=', 'h.deposit', 0], ['>', 'h.promoter_user_id', 0]])
                ->andFilterWhere(['BETWEEN', 'h.created_at', $startTime, $endTime])
                ->andFilterWhere(['w.link_id' => $params['link_id']])
                ->andFilterWhere(['w.channel_id' => $params['promote_id']])
                ->andFilterWhere(['da.dept_id' => $deptIds])
                ->andFilterWhere(['w.project_id' => $params['project_id']])
                ->andFilterWhere(['w.direction_id' => $params['direction_id']])
                ->andFilterWhere(['h.promoter_user_id' => $params['responsible_id']])
                ->andFilterWhere(['or', ['h.promoter_user_id' => $responsibleId], ['da.dept_id' => $scope]]);

            if (isset($params['main_body_id']) && $params['main_body_id']) {
                $fansNotRecordMoneyQuery->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.responsible_id = w.responsible_id')
                    ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
            }

            $fansNotRecordMoney = $fansNotRecordMoneyQuery->groupBy('h.promoter_user_id')->asArray()->all();
            if ($fansNotRecordMoney) {
                $fansNotRecordMoney = ArrayHelper::index($fansNotRecordMoney, 'promoter_user_id');
            }
        }

        // 企微定金数
        $sumDepositArrQuery = OrderHeader::find()->alias('h')
            ->select(['h.promoter_user_id,COUNT(DISTINCT h.id) AS wxcom_deposit'])
            ->leftJoin('{{%wxcom_cus_customer_user}} cu', 'cu.id = h.customer_user_id')
            ->leftJoin('{{%wxcom_cus_customer}} wcc', 'cu.cus_id = wcc.id')
            ->leftJoin('{{%department_assignment}} da', 'da.user_id = h.promoter_user_id')
            ->andWhere(['h.order_status' => $promoteDepositOrderStatus])
            ->andWhere(['and', ['>', 'h.customer_user_id', 0], ['>', 'deposit', 0], ['>', 'h.promoter_user_id', 0]])
            ->andFilterWhere(['BETWEEN', 'h.pre_pay_time', $startTime, $endTime])
            ->andFilterWhere(['not in', 'wcc.unionid', $unionids])
            ->andFilterWhere(['cu.link_id' => $params['link_id']])
            ->andFilterWhere(['h.channel_id' => $params['promote_id']])
            ->andFilterWhere(['da.dept_id' => $deptIds])
            ->andFilterWhere(['cu.project_id' => $params['project_id']])
            ->andFilterWhere(['cu.direction_id' => $params['direction_id']])
            ->andFilterWhere(['h.promoter_user_id' => $params['responsible_id']])
            ->andFilterWhere(['or', ['h.promoter_user_id' => $responsibleId], ['da.dept_id' => $scope]]);

        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $sumDepositArrQuery->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = cu.sub_advertiser_id')
                ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
        }

        $sumDepositArr = $sumDepositArrQuery->groupBy('h.promoter_user_id')->asArray()->all();
        if ($sumDepositArr) {
            $sumDepositArr = ArrayHelper::index($sumDepositArr, 'promoter_user_id');
        }
        // 企微0订金数
        $sumNotDepositArrQuery = OrderHeader::find()->alias('h')
            ->select(['h.promoter_user_id,COUNT(DISTINCT h.id) AS wxcom_not_deposit'])
            ->leftJoin('{{%wxcom_cus_customer_user}} cu', 'cu.id = h.customer_user_id')
            ->leftJoin('{{%wxcom_cus_customer}} wcc', 'cu.cus_id = wcc.id')
            ->leftJoin('{{%department_assignment}} da', 'da.user_id = h.promoter_user_id')
            ->andWhere(['h.order_status' => $promoteDepositOrderStatus])
            ->andWhere(['and', ['>', 'h.customer_user_id', 0], ['=', 'deposit', 0], ['>', 'h.promoter_user_id', 0]])
            ->andFilterWhere(['BETWEEN', 'h.created_at', $startTime, $endTime])
            ->andFilterWhere(['not in', 'wcc.unionid', $unionids])
            ->andFilterWhere(['cu.link_id' => $params['link_id']])
            ->andFilterWhere(['h.channel_id' => $params['promote_id']])
            ->andFilterWhere(['da.dept_id' => $deptIds])
            ->andFilterWhere(['cu.project_id' => $params['project_id']])
            ->andFilterWhere(['cu.direction_id' => $params['direction_id']])
            ->andFilterWhere(['h.promoter_user_id' => $params['responsible_id']])
            ->andFilterWhere(['or', ['h.promoter_user_id' => $responsibleId], ['da.dept_id' => $scope]]);

        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $sumNotDepositArrQuery->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = cu.sub_advertiser_id')
                ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
        }

        $sumNotDepositArr = $sumNotDepositArrQuery->groupBy('h.promoter_user_id')->asArray()->all();
        if ($sumNotDepositArr) {
            $sumNotDepositArr = ArrayHelper::index($sumNotDepositArr, 'promoter_user_id');
        }

        return [$cusCustomerUserArr, $fansRecordArrNumber, $sumDepositArr, $sumNotDepositArr, $fansRecordMoney, $fansNotRecordMoney];
    }

    public static function getCompleteData($params, $startTime, $endTime)
    {
        // 本人的数据 or 管理范围之内
        list($scope, $responsibleId) = Tool::getAuthScope();
        $deptIds = Department::getManageDeptIdsByIds($params['dept_id']);

        $query = OrderHeader::find()
            ->alias('orderHeader')
            ->select(["pc.user_id,COUNT(DISTINCT if(FROM_UNIXTIME(orderHeader.plan_time,'%Y-%m-%d') = FROM_UNIXTIME(cus.first_store_time,'%Y-%m-%d'), cus.id,NULL)) as new_store_cus_count,
                    COUNT(DISTINCT if(FROM_UNIXTIME(orderHeader.plan_time,'%Y-%m-%d') = FROM_UNIXTIME(cus.first_store_time,'%Y-%m-%d') AND IFNULL(orderHeader.deposit, 0) = 0, cus.id,NULL)) as not_deposit_new_store_cus_count,
                    sum(IFNULL( orderHeader.received_amount, 0 ) + IFNULL( orderHeader.card_real_amount, 0 ) + IFNULL(orderHeader.group_amount,0)) as received_amount"])
            ->leftJoin('erp_customer cus', 'cus.id = orderHeader.cus_id')
            ->leftJoin('{{%promote_code}} pc', 'pc.code = cus.code')
            ->leftJoin('{{%department_assignment}} da', 'da.user_id = pc.user_id')
            ->where(['orderHeader.entity_id' => $params['entity_id']])
            ->andWhere(['between', 'orderHeader.plan_time', $startTime, $endTime])
            ->andWhere(['orderHeader.order_status' => 5])
            ->andWhere(['<>', 'cus.code', ''])
            ->andFilterWhere(['cus.link_id' => $params['link_id']])
            ->andFilterWhere(['orderHeader.channel_id' => $params['promote_id']])
            ->andFilterWhere(['da.dept_id' => $deptIds])
            ->andFilterWhere(['cus.project_id' => $params['project_id']])
            ->andFilterWhere(['cus.direction_id' => $params['direction_id']])
            ->andFilterWhere(['pc.user_id' => $params['responsible_id']])
            ->andFilterWhere(['or', ['pc.user_id' => $responsibleId], ['da.dept_id' => $scope]]);

        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $query->leftJoin(['ccu' => WxcomCusCustomerUser::tableName()], 'ccu.id = orderHeader.customer_user_id')
                ->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = ccu.sub_advertiser_id')
                ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
        }

        $list = $query->groupBy('pc.user_id')->asArray()->all();

        if (empty($list)) {
            return [];
        }

        return ArrayHelper::index($list, 'user_id');
    }

    /**
     * 新客当月到店人数
     */
    public static function getMonthCusCountList($params, $startTime, $endTime, $groupByFileld)
    {
        // 本人的数据 or 管理范围之内
        list($scope, $responsibleId) = Tool::getAuthScope();

        $query = OrderHeader::find()
            ->select(["pc.user_id as promoter_user_id,cu.sub_advertiser_id,COUNT(DISTINCT if(FROM_UNIXTIME(h.plan_time,'%Y-%m-%d') = FROM_UNIXTIME(c.first_store_time,'%Y-%m-%d'), c.id,NULL)) as cus_count,sum(IFNULL(h.received_amount, 0 ) + IFNULL(h.card_real_amount, 0 ) + IFNULL(h.group_amount,0)) as month_received_amount"])
            ->alias('h')
            ->leftJoin('{{%customer}} c', 'c.id = h.cus_id')
            ->leftJoin('{{%promote_code}} pc', 'pc.code = c.code')
            ->leftJoin('{{%wxcom_cus_customer_user}} cu', 'cu.id = h.customer_user_id')
            ->leftJoin('{{%department_assignment}} da', 'da.user_id = pc.user_id')
            ->where(['h.order_status' => [5, 6], 'h.source_type' => 1])
            ->andWhere(['BETWEEN', 'h.plan_time', $startTime, $endTime])
            ->andWhere(['BETWEEN', 'cu.add_time', $startTime, $endTime])
            ->andWhere(['<>', 'c.code', ''])
            ->andWhere(['h.entity_id' => Yii::$app->user->identity->current_entity_id])
            ->andFilterWhere(['or', ['like', 'cu.sub_advertiser_name', trim($params['sub_advertiser_name'])], ['like', 'cu.sub_advertiser_id', trim($params['sub_advertiser_name'])]])
            ->andFilterWhere(['cu.link_id' => $params['link_id']])
            ->andFilterWhere(['cu.channel_id' => $params['promote_id']])
            ->andFilterWhere(['pc.user_id' => $params['responsible_id']])
            ->andFilterWhere(['cu.direction_id' => $params['direction_id']])
            ->andFilterWhere(['cu.project_id' => $params['project_id']])
            ->andFilterWhere(['or', ['pc.user_id' => $responsibleId], ['da.dept_id' => $scope]])
            ->andFilterWhere(['da.dept_id' => Department::getManageDeptIdsByIds($params['dept_id'])]);
        
        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $query->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = cu.sub_advertiser_id')
                ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
        }

        if ($groupByFileld != 'promoter_user_id') {
            $monthCusCountList = $query->groupBy('cu.sub_advertiser_id')->asArray()->all();
        } else {
            $monthCusCountList = $query->groupBy('pc.user_id')->asArray()->all();
        }

        if (empty($monthCusCountList)) {
            return [];
        }

        return ArrayHelper::index($monthCusCountList, $groupByFileld);
    }

    /**
     * 当月加粉订金人数
     */
    public static function getMonthDepositCount($params, $startTime, $endTime, $groupByFileld)
    {
        // 本人的数据 or 管理范围之内
        list($scope, $responsibleId) = Tool::getAuthScope();
        $depositOrderStatusList = OrderHeader::depositOrderStatusList();
        $computeAddWay = CusCustomerUser::getComputeAddWay();

        $query = OrderPlanDetail::find()
            ->select('orderHeader.promoter_user_id,ccu.sub_advertiser_id,count(distinct orderHeader.cus_id) as month_deposit_cus_count')
            ->alias('planDetail')
            ->joinWith(['order orderHeader'])
            ->leftJoin('{{%customer}} cus', 'cus.id = orderHeader.cus_id')
            ->leftJoin('{{%wxcom_cus_customer_user}} ccu', 'ccu.id = orderHeader.customer_user_id')
            ->leftJoin('{{%department_assignment}} da', 'da.user_id = orderHeader.promoter_user_id')
            ->where(['planDetail.entity_id' => Yii::$app->user->identity->current_entity_id])
            ->andWhere(['>', 'ccu.channel_id', 0])
            ->andWhere(['ccu.add_way' => $computeAddWay])
            ->andWhere(['between', 'orderHeader.pre_pay_time', $startTime, $endTime])
            ->andWhere(['between', 'ccu.add_time', $startTime, $endTime])
            ->andWhere(['orderHeader.order_status' => $depositOrderStatusList])
            ->andFilterWhere(['ccu.link_id' => $params['link_id']])
            ->andFilterWhere(['ccu.channel_id' => $params['promote_id']])
            ->andFilterWhere(['orderHeader.promoter_user_id' => $params['responsible_id']])
            ->andFilterWhere(['ccu.direction_id' => $params['direction_id']])
            ->andFilterWhere(['ccu.project_id' => $params['project_id']])
            ->andFilterWhere(['or', ['like', 'ccu.sub_advertiser_name', trim($params['sub_advertiser_name'])], ['like', 'ccu.sub_advertiser_id', trim($params['sub_advertiser_name'])]])
            ->andFilterWhere(['or', ['orderHeader.promoter_user_id' => $responsibleId], ['da.dept_id' => $scope]])
            ->andFilterWhere(['da.dept_id' => Department::getManageDeptIdsByIds($params['dept_id'])]);

        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $query->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = ccu.sub_advertiser_id')
                ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
        }

        if ($groupByFileld != 'promoter_user_id') {
            $monthCusCountList = $query->groupBy('ccu.sub_advertiser_id')->asArray()->all();
        } else {
            $monthCusCountList = $query->groupBy('orderHeader.promoter_user_id')->asArray()->all();
        }

        if (empty($monthCusCountList)) {
            return [];
        }

        return ArrayHelper::index($monthCusCountList, $groupByFileld);
    }

    public static function getDelFansList($params, $startTime, $endTime)
    {
        // 本人的数据 or 管理范围之内
        list($scope, $responsibleId) = Tool::getAuthScope();
        $deptIds = Department::getManageDeptIdsByIds($params['dept_id']);

        //12小时内删除的客户记做删粉
        $hour = 60 * 60 * 12;
        $listQuery = CusCustomerUser::find()->alias('cu')
            ->select(['cu.qrcode_created_by,COUNT(DISTINCT cu.id) AS del_fans_count'])
            ->leftJoin('{{%department_assignment}} da', 'da.user_id = cu.qrcode_created_by')
            ->andWhere(['cu.add_way' => CusCustomerUser::getComputeAddWay()])
            ->andWhere(['cu.entity_id' => $params['entity_id'], 'is_deleted' => 1])
            ->andWhere(['BETWEEN', 'cu.add_time', $startTime, $endTime])
            ->andWhere(['<', new \yii\db\Expression('cu.deleted_time - cu.add_time'), $hour])
            ->andFilterWhere(['OR', ['like', 'cu.sub_advertiser_name', $params['sub_advertiser_name']], ['=', 'cu.sub_advertiser_id', $params['sub_advertiser_name']]])
            ->andFilterWhere(['cu.link_id' => $params['link_id']])
            ->andFilterWhere(['cu.channel_id' => $params['promote_id']])
            ->andFilterWhere(['da.dept_id' => $deptIds])
            ->andFilterWhere(['cu.project_id' => $params['project_id']])
            ->andFilterWhere(['cu.direction_id' => $params['direction_id']])
            ->andFilterWhere(['cu.qrcode_created_by' => $params['responsible_id']])
            ->andFilterWhere(['>', 'cu.qrcode_created_by', 0])
            ->andFilterWhere(['or', ['cu.qrcode_created_by' => $responsibleId], ['da.dept_id' => $scope]]);

        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $listQuery->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = cu.sub_advertiser_id')
                ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
        }

        $list = $listQuery->groupBy('cu.qrcode_created_by')->asArray()->all();
        if (empty($list)) {
            return [];
        }

        return ArrayHelper::index($list, 'qrcode_created_by');
    }

    public static function getNotReplyList($params, $startTime, $endTime)
    {
        // 本人的数据 or 管理范围之内
        list($scope, $responsibleId) = Tool::getAuthScope();
        $deptIds = Department::getManageDeptIdsByIds($params['dept_id']);

        $entity_id = Yii::$app->user->identity->current_entity_id;
        $tagId = CusTagService::getNotReplyTag($entity_id);
        if (empty($tagId)) {
            return [];
        }

        $query = CusCustomerUser::find()->alias('cu')
            ->select(['cu.qrcode_created_by,COUNT(DISTINCT cu.id) AS not_reply_count'])
            ->leftJoin('{{%department_assignment}} da', 'da.user_id = cu.qrcode_created_by')
            ->andWhere(['cu.add_way' => CusCustomerUser::getComputeAddWay()])
            ->andWhere(['>', "FIND_IN_SET({$tagId}, cu.tag_ids)", 0])
            ->andWhere(['BETWEEN', 'cu.add_time', $startTime, $endTime])
            ->andFilterWhere(['OR', ['like', 'cu.sub_advertiser_name', $params['sub_advertiser_name']], ['=', 'cu.sub_advertiser_id', $params['sub_advertiser_name']]])
            ->andFilterWhere(['cu.link_id' => $params['link_id']])
            ->andFilterWhere(['cu.channel_id' => $params['promote_id']])
            ->andFilterWhere(['da.dept_id' => $deptIds])
            ->andFilterWhere(['cu.project_id' => $params['project_id']])
            ->andFilterWhere(['cu.direction_id' => $params['direction_id']])
            ->andFilterWhere(['cu.qrcode_created_by' => $params['responsible_id']])
            ->andFilterWhere(['>', 'cu.qrcode_created_by', 0])
            ->andFilterWhere(['or', ['cu.qrcode_created_by' => $responsibleId], ['da.dept_id' => $scope]]);

        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $query->leftJoin(['aas' => AdsAccountSub::tableName()], 'aas.sub_advertiser_id = cu.sub_advertiser_id')
                ->andWhere(['aas.main_body_id' => $params['main_body_id']]);
        }

        $downPaymentTagId = CusTagService::getDownPaymentTag($entity_id);
        if ($downPaymentTagId) {
            $query->andWhere(['=', "FIND_IN_SET({$downPaymentTagId}, cu.tag_ids)", 0]);
        }

        $list = $query->groupBy('cu.qrcode_created_by')
            ->asArray()
            ->all();

        if (empty($list)) {
            return [];
        }

        return ArrayHelper::index($list, 'qrcode_created_by');
    }

    public static function getInvalidData($params, $startTime, $endTime, $groupByFileld)
    {
        // 本人的数据 or 管理范围之内
        list($scope, $responsibleId) = Tool::getAuthScope();
        $deptIds = Department::getManageDeptIdsByIds($params['dept_id']);

        //12小时内删除的客户记做删粉
        $hour = 60 * 60 * 12;
        $remoteTag = CusTagService::getRemoteTag($params['entity_id']);
        $notReplyTag = CusTagService::getNotReplyTag($params['entity_id']);
        $orWhere = '(`is_deleted` = 1 AND deleted_time - add_time < ' . $hour . ' )';
        if ($notReplyTag) {
            $downPaymentTag = CusTagService::getDownPaymentTag($params['entity_id']);
            if ($downPaymentTag) {
                $orWhere .= "or (FIND_IN_SET( {$notReplyTag}, tag_ids ) > 0 AND FIND_IN_SET( {$downPaymentTag}, tag_ids ) = 0 )";
            } else {
                $orWhere .= "or (FIND_IN_SET( {$notReplyTag}, tag_ids ) > 0)";
            }
        }

        if ($remoteTag) {
            $orWhere .= "or (FIND_IN_SET( {$remoteTag}, tag_ids ) > 0)";
        }

        // 子查询1
        $subQuery1 = (new Query())
            ->select(['sub_advertiser_id', 'cu.id AS customer_user_id'])
            ->from('erp_wxcom_cus_customer_user cu')
            ->where(['>', 'channel_id', 0])
            ->andWhere(['add_way' => CusCustomerUser::getComputeAddWay()])
            ->andWhere(['entity_id' => $params['entity_id']])
            ->andWhere(new \yii\db\Expression($orWhere))
            ->andWhere(['between', 'add_time', $startTime, $endTime]);

        // 子查询2
        // $subQuery2 = (new Query())
        //     ->select(['cus.sub_advertiser_id', 'oh.customer_user_id'])
        //     ->from('erp_customer_churn_remark ccr')
        //     ->leftJoin('erp_order_header oh', 'oh.id = ccr.order_id')
        //     ->leftJoin('erp_customer cus', 'cus.id = oh.cus_id')
        //     ->where(['ccr.entity_id' => $params['entity_id'], 'ccr.reach_status' => 2])
        //     ->andWhere(['between', 'ccr.plan_time', $startTime, $endTime])
        //     ->andWhere(['<>', 'cus.sub_advertiser_id', '']);

        // 主查询
        $query = (new Query())
            ->select([
                'cu.qrcode_created_by as promoter_user_id',
                'a.sub_advertiser_id',
                'COUNT(DISTINCT a.customer_user_id) AS invalid_num'
            ])
            ->from(['a' => $subQuery1])
            // ->from(['a' => $subQuery1->union($subQuery2)])
            ->leftJoin('erp_wxcom_cus_customer_user cu', 'cu.id = a.customer_user_id')
            ->leftJoin('erp_ads_account_sub ad_sub', 'ad_sub.sub_advertiser_id = cu.sub_advertiser_id')
            ->leftJoin('erp_department_assignment da', 'da.user_id = cu.qrcode_created_by')
            ->andFilterWhere(['OR', ['like', 'cu.sub_advertiser_name', $params['sub_advertiser_name']], ['=', 'cu.sub_advertiser_id', $params['sub_advertiser_name']]])
            ->andFilterWhere(['cu.link_id' => $params['link_id']])
            ->andFilterWhere(['ad_sub.main_body_id' => $params['main_body_id']])
            ->andFilterWhere(['cu.channel_id' => $params['promote_id']])
            ->andFilterWhere(['cu.project_id' => $params['project_id']])
            ->andFilterWhere(['cu.direction_id' => $params['direction_id']])
            ->andFilterWhere(['cu.qrcode_created_by' => $params['responsible_id']])
            ->andFilterWhere(['da.dept_id' => $deptIds])
            ->andFilterWhere(['or', ['cu.qrcode_created_by' => $responsibleId], ['da.dept_id' => $scope]]);

        if ($groupByFileld == 'promoter_user_id') {
            $list = $query->groupBy('cu.qrcode_created_by')->all();
        } else {
            $list = $query->groupBy('a.sub_advertiser_id')->all();
        }

        return ArrayHelper::index($list, $groupByFileld);
    }

    public static function getProjectId($project_type, $project_id)
    {
        if ($project_type == 1) {
            $ids = PromoteProjectService::find()->select('id')->where(['group_id' => $project_id])->column();
        } else {
            $ids = $project_id ? [$project_id] : [];
        }

        return $ids;
    }

    /**
     * 投放人分析 - 详情
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getCastPeopleDetails($params = [], $isExport = false)
    {
        //获取详情数据时，无责任人id则直接返回空数据
        if (!$isExport && !isset($params['responsible_id'])) {
            return [[], 0];
        }

        $select_field = ['ad_sub.sub_advertiser_id,ad.responsible_id,bm.realname AS responsible_name,ad_sub.sub_advertiser_name AS name,ad_pc.name AS promote_name,
        SUM(ad.cost) AS cost,SUM(ad.compensate) AS compensate,round(sum((ad.cost - ad.compensate) / ad.rebates),2) as real_cost,ad_sub.rebates,
        SUM(ad.click) AS click,SUM(ad.`show`) AS `show`,SUM(ad.`convert`) AS `convert`, ad_pd.name as direction_name,
        SUM(ad.add_fans_count) as wxcom_add_fans,SUM(ad.deposit_count) as wxcom_deposit,SUM(not_deposit_count) as not_deposit_count,SUM(new_store_cus_count) as new_store_cus_count,SUM(not_deposit_new_store_cus_count) as not_deposit_new_store_cus_count,SUM(received_amount) as amount,SUM(attrition_count) as attrition_count,SUM(remote_count) as remote_count,
        SUM(repeat_fans_count) as repeat_fans_count,SUM(ad.del_fans_count) as del_fans_count,SUM(ad.not_reply_count) as not_reply_count'];

        // 导出时需要按日期分组，添加日期字段
        if ($isExport && isset($params['groupByDate']) && $params['groupByDate']) {
            $select_field[0] = 'ad.date,' . $select_field[0];
        }

        //项目搜索
        $params['project_id'] = static::getProjectId($params['project_type'], $params['project_id']);

        list($query, $start_time, $end_time) = static::dealCastPeopleData($params, $select_field);
        
        // 根据是否需要按日期分组来设置分组字段
        if ($isExport && isset($params['groupByDate']) && $params['groupByDate']) {
            $query->groupBy(['ad.ads_sub_id', 'ad.date']); // 按子账户ID和日期分组
        } else {
            $query->groupBy('ad.ads_sub_id'); // 仅按子账户ID分组(原有逻辑)
        }

        $totalCount = 0;
        if ($isExport && isset($params['getTotal']) && $params['getTotal']) {
            return [[], $query->count()];
        }

        $params['entity_id'] = Yii::$app->user->identity->current_entity_id;

        //新客当月到店人数
        $groupByFileld = 'sub_advertiser_id';
        $monthCusCountList = self::getMonthCusCountList($params, $start_time, $end_time + 86399, $groupByFileld);

        //当月订金人数
        $monthDepositCountList = self::getMonthDepositCount($params, $start_time, $end_time + 86399, $groupByFileld);

        //无效数
        $invalidList = self::getInvalidData($params, $start_time, $end_time + 86399, $groupByFileld);

        $list = $query->asArray()->all();
        foreach ($list as &$v) {
            // 按日期分组时添加日期显示
            if ($isExport && isset($params['groupByDate']) && $params['groupByDate'] && isset($v['date'])) {
                $v['date_format'] = date('Y-m-d', strtotime($v['date']));
            }
            
            $v['account_name'] = $v['name'];
            $v['sum_deposit'] = $v['wxcom_deposit'];
            $v['sum_not_deposit'] = $v['not_deposit_count'];
            $v['sum_add_fans'] = $v['wxcom_add_fans'];
            $v['add_fans_cost'] = BcHelper::div($v['cost'], $v['sum_add_fans']);
            $v['real_add_fans_cost'] = BcHelper::div($v['real_cost'], $v['sum_add_fans']);
            $v['deposit_cost'] = BcHelper::div($v['cost'], $v['sum_deposit']);
            $v['real_deposit_cost'] = BcHelper::div($v['real_cost'], $v['sum_deposit']);
            $v['deposit_rate'] = BcHelper::sprintf(BcHelper::div($v['sum_deposit'], $v['sum_add_fans'], 4) * 100) . '%';   //加粉订金转化率
            $v['conversion_cost'] = BcHelper::div($v['cost'], $v['convert']);
            $v['real_conversion_cost'] = BcHelper::div($v['real_cost'], $v['convert']);
            $v['store_cost'] = BcHelper::div($v['cost'], $v['new_store_cus_count']);
            $v['real_store_cost'] = BcHelper::div($v['real_cost'], $v['new_store_cus_count']);
            $v['amount_cost_rate'] = BcHelper::div($v['amount'], $v['real_cost']);
            $v['book_roi'] = BcHelper::div($v['amount'], $v['cost']);
            $v['fans_arrive_store_rate'] = BcHelper::sprintf(BcHelper::div($v['new_store_cus_count'], $v['sum_add_fans'], 4) * 100) . '%';
            $v['customer_price'] = BcHelper::div($v['amount'], $v['new_store_cus_count']); //客单价
            $v['attrition_count_rate'] = (BcHelper::div($v['attrition_count'], $v['new_store_cus_count'], 4) * 100) . '%'; //流失率
            $v['remote_count_rate'] = (BcHelper::div($v['remote_count'], $v['sum_add_fans'] ?: 1, 4) * 100) . '%'; //偏远率
            $v['del_fans_count_rate'] = (BcHelper::div($v['del_fans_count'], $v['sum_add_fans'] ?: 1, 4) * 100) . '%'; //删粉率
            $v['not_reply_count_rate'] = (BcHelper::div($v['not_reply_count'], $v['sum_add_fans'] ?: 1, 4) * 100) . '%'; //不回复率
            $v['repeat_fans_rate'] = (BcHelper::div($v['repeat_fans_count'], $v['sum_add_fans'] ?: 1, 4) * 100) . '%'; //重粉数

            $v['sum_month_cus_count'] = $v['sum_month_received_amount'] = 0;
            if (isset($monthCusCountList[$v['sub_advertiser_id']])) {
                $v['sum_month_cus_count'] =  ArrayHelper::getValue($monthCusCountList[$v['sub_advertiser_id']], 'cus_count', 0);
                $v['sum_month_received_amount'] =  ArrayHelper::getValue($monthCusCountList[$v['sub_advertiser_id']], 'month_received_amount', 0);
            }
            $v['sum_month_cus_count_rate'] = (BcHelper::div($v['sum_month_cus_count'], $v['sum_add_fans'] ?: 1, 4) * 100) . '%'; //当月加人转到店率
            $v['sum_month_amount_cost_rate'] = BcHelper::div($v['sum_month_received_amount'], $v['real_cost']); //当月ROI

            $v['sum_month_deposit_count'] = 0;
            if (isset($monthDepositCountList[$v['sub_advertiser_id']])) {
                $v['sum_month_deposit_count'] =  ArrayHelper::getValue($monthDepositCountList[$v['sub_advertiser_id']], 'month_deposit_cus_count', 0);
            }
            $v['sum_month_deposit_count_rate'] = (BcHelper::div($v['sum_month_deposit_count'], $v['sum_add_fans'] ?: 1, 4) * 100) . '%'; //当月加人转到店率
            $v['sum_month_real_deposit_cost'] = BcHelper::div($v['real_cost'], $v['sum_month_deposit_count']); //当月定金成本

            $v['invalid_num'] = 0;
            if (isset($invalidList[$v['sub_advertiser_id']])) {
                $v['invalid_num'] =  ArrayHelper::getValue($invalidList[$v['sub_advertiser_id']], 'invalid_num', 0);
            }
            $v['invalid_rate'] = (BcHelper::div($v['invalid_num'], $v['sum_add_fans'] ?: 1, 4) * 100) . '%'; //无效率

            if (!self::isLookAuth()) {  //判断该人员角色是否有权限查看
                $v['real_cost'] = '-';
                $v['real_deposit_cost'] = '-';
                $v['real_add_fans_cost'] = '-';
                $v['real_conversion_cost'] = '-';
            }
        }

        return [$list, $totalCount];
    }

    /**
     * 投放人分析 - 处理导出数据
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getCastPeopleExport($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $params['entity_id'] = Yii::$app->user->identity->current_entity_id;

        //项目搜索
        $params['project_id'] = static::getProjectId($params['project_type'], $params['project_id']);

        list($query, $startTime, $endTime) = static::dealCastPeopleDataList($params);
        if (!empty($params['getTotal'])) return [[], $query->count()];

        //新客当月到店人数
        $groupByFileld = 'promoter_user_id';
        $monthCusCountList = self::getMonthCusCountList($params, $startTime, $endTime + 86399, $groupByFileld);

        //当月订金人数
        $monthDepositCountList = self::getMonthDepositCount($params, $startTime, $endTime + 86399, $groupByFileld);

        //删粉人数
        $delFansList = self::getDelFansList($params, $startTime, $endTime + 86399);

        //不回复人数
        $notReplyList = self::getNotReplyList($params, $startTime, $endTime + 86399);

        //无效数
        $invalidList = self::getInvalidData($params, $startTime, $endTime + 86399, $groupByFileld);

        list($cusCustomerUserArr, $fansRecordArr, $sumDepositArr, $sumNotDepositArr, $fansRecordMoney, $fansNotRecordMoney) = self::getFansAndSumDeposit($params, $startTime, $endTime + 86399);
        $completeData = static::getCompleteData($params, $startTime, $endTime + 86399);

        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();
        foreach ($list as &$l) {
            $responsibleId = $l['responsible_id'] ?? 0;
            $l['wxcom_add_fans'] = 0;
            $l['wechat_add_fans'] = 0;
            $l['wxcom_deposit'] = 0;
            $l['wechat_deposit'] = 0;
            $l['wxcom_not_deposit'] = 0;
            $l['wechat_not_deposit'] = 0;
            $l['sum_add_fans'] = 0;
            if (isset($cusCustomerUserArr[$responsibleId]) || isset($fansRecordArr[$responsibleId])) {
                $wxcomAddFans = ArrayHelper::getValue($cusCustomerUserArr[$responsibleId], 'wxcom_add_fans', 0);
                $wechatAddFans = ArrayHelper::getValue($fansRecordArr[$responsibleId], 'wechat_add_fans', 0);
                $l['wxcom_add_fans'] = $wxcomAddFans;
                $l['wechat_add_fans'] = $wechatAddFans;
                $l['sum_add_fans'] = $wxcomAddFans + $wechatAddFans;
            }
            $l['sum_deposit'] = 0;
            if (isset($sumDepositArr[$responsibleId]) || isset($fansRecordMoney[$responsibleId])) {
                $wxcomDeposit = ArrayHelper::getValue($sumDepositArr[$responsibleId], 'wxcom_deposit', 0);
                $wechatDeposit = ArrayHelper::getValue($fansRecordMoney[$responsibleId], 'wechat_deposit', 0);
                $l['wxcom_deposit'] = $wxcomDeposit;
                $l['wechat_deposit'] = $wechatDeposit;
                $l['sum_deposit'] = $wxcomDeposit + $wechatDeposit;
            }
            $l['sum_not_deposit'] = 0;
            if (isset($sumNotDepositArr[$responsibleId]) || isset($fansNotRecordMoney[$responsibleId])) {
                $wxcomNotDeposit = ArrayHelper::getValue($sumNotDepositArr[$responsibleId], 'wxcom_not_deposit', 0);
                $wechatNotDeposit = ArrayHelper::getValue($fansNotRecordMoney[$responsibleId], 'wechat_not_deposit', 0);
                $l['wxcom_not_deposit'] = $wxcomNotDeposit;
                $l['wechat_not_deposit'] = $wechatNotDeposit;
                $l['sum_not_deposit'] = $wxcomNotDeposit + $wechatNotDeposit;
            }

            $l['not_deposit_new_store_cus_count'] = 0;
            if (isset($completeData[$responsibleId])) {
                $l['amount'] +=  ArrayHelper::getValue($completeData[$responsibleId], 'received_amount', 0);
                $l['new_store_cus_count'] +=  ArrayHelper::getValue($completeData[$responsibleId], 'new_store_cus_count', 0);
                $l['not_deposit_new_store_cus_count'] +=  ArrayHelper::getValue($completeData[$responsibleId], 'not_deposit_new_store_cus_count', 0);
            }

            $l['sum_month_cus_count'] = $l['sum_month_received_amount'] = 0;
            if (isset($monthCusCountList[$responsibleId])) {
                $l['sum_month_cus_count'] =  ArrayHelper::getValue($monthCusCountList[$responsibleId], 'cus_count', 0);
                $l['sum_month_received_amount'] =  ArrayHelper::getValue($monthCusCountList[$responsibleId], 'month_received_amount', 0);
            }

            $l['sum_month_deposit_count'] = 0;
            if (isset($monthDepositCountList[$responsibleId])) {
                $l['sum_month_deposit_count'] =  ArrayHelper::getValue($monthDepositCountList[$responsibleId], 'month_deposit_cus_count', 0);
            }

            $l['del_fans_count'] = 0;
            if (isset($delFansList[$responsibleId])) {
                $l['del_fans_count'] =  ArrayHelper::getValue($delFansList[$responsibleId], 'del_fans_count', 0);
            }


            $l['not_reply_count'] = 0;
            if (isset($notReplyList[$responsibleId])) {
                $l['not_reply_count'] =  ArrayHelper::getValue($notReplyList[$responsibleId], 'not_reply_count', 0);
            }

            $l['invalid_num'] = 0;
            if (isset($invalidList[$responsibleId])) {
                $l['invalid_num'] =  ArrayHelper::getValue($invalidList[$responsibleId], 'invalid_num', 0);
            }

            $l['cost'] = $l['cost'] ?: 0;
            $l['real_cost'] = $l['real_cost'] ?: 0;
            $l['compensate'] = $l['compensate'] ?: 0;
            $l['click'] = $l['click'] ?: 0;
            $l['show'] = $l['show'] ?: 0;
            $l['convert'] = $l['convert'] ?: 0;
            $l['sum_deposit'] = $l['sum_deposit'] ?: 0;
            $l['sum_not_deposit'] = $l['sum_not_deposit'] ?: 0;
            $l['fans_arrive_store_rate'] = (BcHelper::div($l['new_store_cus_count'], $l['sum_add_fans'], 4) * 100) . '%';  // 新增加粉转到店率=新客到店人数/加粉数
            $l['add_fans_cost'] = BcHelper::div($l['cost'], $l['sum_add_fans']);           //账面加粉成本
            $l['deposit_cost'] = BcHelper::div($l['cost'], $l['sum_deposit']);             //账面定金成本
            $l['real_add_fans_cost'] = BcHelper::div($l['real_cost'], $l['sum_add_fans']); //实际加粉成本
            $l['real_deposit_cost'] = BcHelper::div($l['real_cost'], $l['sum_deposit']);   //实际定金成本
            $l['deposit_rate'] = BcHelper::div($l['sum_deposit'], $l['sum_add_fans']);    //加粉订金转化率
            $l['store_cost'] = BcHelper::div($l['cost'], $l['new_store_cus_count']);    //账面到店成本
            $l['real_store_cost'] = BcHelper::div($l['real_cost'], $l['new_store_cus_count']);    //实际到店成本
            $l['amount_cost_rate'] = BcHelper::div($l['amount'], $l['real_cost']);    //ROI
            $l['book_roi'] = BcHelper::div($l['amount'], $l['cost']);    //账面ROI
            $l['sum_month_amount_cost_rate'] = BcHelper::div($l['sum_month_received_amount'], $l['real_cost']); //当月ROI
            $l['real_conversion_cost'] = BcHelper::div($l['real_cost'], $l['convert']);    //实际转化成本
            $l['customer_price'] = BcHelper::div($l['amount'], $l['new_store_cus_count']); //客单价
            $l['attrition_count_rate'] = (BcHelper::div($l['attrition_count'], $l['new_store_cus_count'], 4) * 100) . '%'; //流失率
            $l['remote_count_rate'] = (BcHelper::div($l['remote_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //偏远率
            $l['sum_month_cus_count_rate'] = (BcHelper::div($l['sum_month_cus_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //当月加人转到店率
            $l['sum_month_deposit_count_rate'] = (BcHelper::div($l['sum_month_deposit_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //当月订金转化率
            $l['sum_month_real_deposit_cost'] = BcHelper::div($l['real_cost'], $l['sum_month_deposit_count']);   //当月定金成本
            $l['del_fans_count_rate'] = (BcHelper::div($l['del_fans_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //删粉率
            $l['not_reply_count_rate'] = (BcHelper::div($l['not_reply_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //不回复率
            $l['invalid_rate'] = (BcHelper::div($l['invalid_num'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //无效数
            $l['repeat_fans_rate'] = (BcHelper::div($l['repeat_fans_count'], $l['sum_add_fans'] ?: 1, 4) * 100) . '%'; //重粉率

            if (!self::isLookAuth()) {
                $l['real_cost'] = '-';
                $l['real_add_fans_cost'] = '-';
                $l['real_deposit_cost'] = '-';
                $l['real_conversion_cost'] = '-';
                $l['real_store_cost'] = '-';
                $l['amount_cost_rate'] = '-';
            }
        }

        return [$list, 0];
    }

    /**
     * 投放人分析列表数据
     *
     * @param array $params
     * @param string $select_field
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function dealCastPeopleDataList($params = [], $select_field = "")
    {
        list($start_time, $end_time) = DateHelper::AgoDate(strtotime($params['start_time']), strtotime($params['end_time']));
        $query = self::dealCastPeopleQuery($params);

        $bisPromoteAnalysisWhereSql = 'where date_time BETWEEN ' . strtotime($start_time) . ' AND ' . strtotime($end_time);

        $ad_conditions = $dps_conditions = '';
        if ($params['promote_id']) {
            $ad_conditions .= " AND ad.promote_id = {$params['promote_id']}";
            $bisPromoteAnalysisWhereSql .= " AND channel_id = {$params['promote_id']}";
        }
        if ($params['project_id']) {
            $projectStr = implode(',', $params['project_id']);
            $ad_conditions .= " AND ad.project_id in ({$projectStr})";
            $bisPromoteAnalysisWhereSql .= " AND project_id in ({$projectStr})";
        }
        if ($params['direction_id']) {
            $ad_conditions .= " AND ad.direction_id = {$params['direction_id']}";
        }
        if ($params['link_id']) {
            $ad_conditions .= " AND ad.link_id = {$params['link_id']}";
        }
        if ($params['sub_advertiser_name']) {
            $ad_conditions .= " AND (ad_sub.sub_advertiser_name like '%{$params['sub_advertiser_name']}%' OR ad_sub.sub_advertiser_id = '{$params['sub_advertiser_name']}')";
        }

        if (isset($params['main_body_id']) && $params['main_body_id']) {
            $ad_conditions .= " AND ad_sub.main_body_id = {$params['main_body_id']}";
        }

        // 本人的数据 or 管理范围之内
        list($scope, $responsibleId) = Tool::getAuthScope();
        if (count($scope)) {
            $scopes = implode(',', $scope);
            $ad_conditions .= " AND (ad.responsible_id = {$responsibleId} OR ad.dept_id IN ({$scopes}))";
            $dps_conditions .= " AND (responsible_id = {$responsibleId} OR promote_dept_id IN ({$scopes}))";
        }

        if (isset($params['dept_id']) && $params['dept_id']) {
            $dept_ids = Department::getManageDeptIdsByIds($params['dept_id']);
            $dept_ids = $dept_ids ? implode(',', $dept_ids) : -1;
            $ad_conditions .= " AND ad.dept_id IN ({$dept_ids})";
            $dps_conditions .= " AND promote_dept_id IN ({$dept_ids})";
        }

        if (!$select_field) {
            $select_field = ['bm.id AS responsible_id,bm.realname AS responsible_name,"" AS `name`,"" AS promote_name,IFNULL(ad.sum_cost,0) AS cost,IFNULL(ad.sum_compensate,0) AS compensate,IFNULL(ad.real_cost,0) AS real_cost,IFNULL(ad.sum_click,0) AS click,IFNULL(ad.sum_show,0) AS `show`,IFNULL(ad.sum_convert,0) AS `convert`,IFNULL(dps.wxcom_add_fans,0) AS wxcom_add_fans,IFNULL(dps.wechat_add_fans,0) AS wechat_add_fans,IFNULL(dps.sum_add_fans,0) AS sum_add_fans,IFNULL(dps.wxcom_deposit,0) AS wxcom_deposit,IFNULL(dps.wechat_deposit,0) AS wechat_deposit,IFNULL(dps.sum_deposit,0) AS sum_deposit,IFNULL(bis_promote.new_store_cus_count,0) AS new_store_cus_count,IFNULL(bis_promote.amount,0) AS amount,IFNULL(ad.attrition_count,0) AS attrition_count,IFNULL(ad.remote_count,0) AS remote_count,
            IFNULL(ad.repeat_fans_count,0) AS repeat_fans_count,IFNULL(ad.del_fans_count,0) AS del_fans_count,IFNULL(ad.not_reply_count,0) AS not_reply_count'];
        }

        $query->select($select_field);
        $query->leftJoin(["(
                SELECT ad.responsible_id,ad.dept_id,ad.promote_id,ad.project_id,ad.direction_id,ad.link_id,
                    SUM(ad.cost) AS sum_cost,SUM(ad.compensate) AS sum_compensate,SUM(ad.click) AS sum_click,SUM(ad.`show`) AS sum_show,
                    SUM(ad.`convert`) AS sum_convert,ROUND(SUM((ad.cost - ad.compensate) / ad.rebates),2) AS real_cost,SUM(ad.`attrition_count`) AS attrition_count,SUM(ad.`remote_count`) as remote_count,
                    SUM(ad.`repeat_fans_count`) as repeat_fans_count,SUM(ad.`del_fans_count`) as del_fans_count,SUM(ad.`not_reply_count`) as not_reply_count
                FROM {{%ads_account_data}} ad
                LEFT JOIN {{%ads_account_sub}} ad_sub ON ad_sub.id = ad.ads_sub_id
                WHERE ad.`date` BETWEEN '$start_time' AND '$end_time' $ad_conditions 
                GROUP BY ad.responsible_id 
            ) ad"], "ad.responsible_id = bm.id")
            ->leftJoin(['{{%promote_channel}} ad_pc'], 'ad_pc.id = ad.promote_id')
            ->leftJoin(["(select user_id,sum(new_store_cus_count) as new_store_cus_count,sum(amount) as amount from erp_data_bis_promote_analysis {$bisPromoteAnalysisWhereSql} group by user_id) bis_promote"], 'bis_promote.user_id = bm.id')
            ->leftJoin(["(SELECT responsible_id,SUM(wxcom_add_fans) AS wxcom_add_fans,SUM(wechat_add_fans) AS wechat_add_fans,SUM(wxcom_add2_fans) AS wxcom_add2_fans,SUM(wxcom_add_fans + wxcom_add2_fans + wechat_add_fans) AS sum_add_fans,SUM(promote_wxcom_deposit) AS wxcom_deposit,SUM(promote_wechat_deposit) AS wechat_deposit,SUM(promote_wxcom_deposit + promote_wechat_deposit) AS `sum_deposit` FROM {{%data_promote_servicer}} WHERE date_time BETWEEN '$start_time' AND '$end_time' AND responsible_id > 0 $dps_conditions GROUP BY responsible_id) dps"], "dps.responsible_id = bm.id")
            ->andWhere(['bm.current_entity_id' => Yii::$app->user->identity->current_entity_id])
            ->andWhere([
                'and',
                [
                    'or',
                    'IFNULL(ad.sum_convert,0) > 0',
                    'IFNULL(ad.sum_cost,0) > 0',
                    'IFNULL(ad.sum_compensate,0) > 0',
                    'IFNULL(dps.wxcom_add_fans,0) > 0',
                    'IFNULL(dps.wechat_add_fans,0) > 0',
                    'IFNULL(dps.wxcom_deposit,0) > 0',
                    'IFNULL(dps.wechat_deposit,0) > 0'
                ]
            ]);

        if ($params['sub_advertiser_name']) {
            $query->andWhere('ad.responsible_id IS NOT NULL');
        }
        return [$query, strtotime($start_time), strtotime($end_time)];
    }

    /**
     * 处理投放人分析详情数据
     *
     * @param array $params
     * @param string $select_field
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function dealCastPeopleData($params = [], $select_field = "")
    {
        list($start_time, $end_time) = DateHelper::AgoDate(strtotime($params['start_time']), strtotime($params['end_time']));
        $query = self::dealCastPeopleQuery($params);
        $query->select($select_field)
            ->leftJoin(['{{%ads_account_data}} ad'], 'ad.responsible_id = bm.id')
            ->leftJoin(['{{%ads_account_sub}} ad_sub'], 'ad_sub.id = ad.ads_sub_id')
            ->leftJoin(['{{%promote_channel}} ad_pc'], 'ad_pc.id = ad.promote_id')
            ->leftJoin(['{{%promote_direction}} ad_pd'], 'ad_pd.id = ad.direction_id')
            ->andWhere(['ad.entity_id' => Yii::$app->user->identity->current_entity_id])
            // ->andWhere(['and', ['or', 'ad.`convert` > 0', 'ad.`cost` > 0', 'ad.`compensate` > 0']])
            ->andFilterWhere([
                'or',
                ['like', 'ad_sub.sub_advertiser_name', $params['sub_advertiser_name']],
                ['=', 'ad_sub.sub_advertiser_id', $params['sub_advertiser_name']],
            ])
            ->andFilterWhere(['ad.promote_id' => $params['promote_id']])
            ->andFilterWhere(['ad_sub.main_body_id' => $params['main_body_id']])
            ->andFilterWhere(['ad.project_id' => $params['project_id']])
            ->andFilterWhere(['ad.direction_id' => $params['direction_id']])
            ->andFilterWhere(['ad.link_id' => $params['link_id']])
            ->andFilterWhere(['BETWEEN', 'ad.date', date('Ymd', strtotime($start_time)), date('Ymd', strtotime($end_time))])
            ->orderBy('ad.cost DESC');

        // 本人的数据 or 管理范围之内
        list($scope, $responsibleId) = Tool::getAuthScope();
        $query->andFilterWhere(['or', ['ad.responsible_id' => $responsibleId], ['ad.dept_id' => $scope]]);

        if (isset($params['dept_id']) && $params['dept_id']) {
            $query->andWhere(['ad.dept_id' => Department::getManageDeptIdsByIds($params['dept_id'])]);
        }

        return [$query, strtotime($start_time), strtotime($end_time)];
    }

    /**
     * 投放人分析基础sql
     *
     * @param array $params
     * @return \yii\db\ActiveQuery
     * @throws \yii\base\InvalidConfigException
     */
    public static function dealCastPeopleQuery($params = [])
    {
        $query = Member::find()->alias('bm');

        $query->andFilterWhere(['bm.realname' => $params['responsible_name']])
            ->andFilterWhere(['ad.promote_id' => $params['promote_id']])
            ->andFilterWhere(['ad.project_id' => $params['project_id']])
            ->andFilterWhere(['ad.direction_id' => $params['direction_id']])
            ->andFilterWhere(['ad.link_id' => $params['link_id']])
            ->andFilterWhere(['ad.responsible_id' => $params['responsible_id']]);

        return $query;
    }

    /**
     * 项目分析-列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getProjectData($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        $select_field = ['ad.project_id,pp.name as project_name,"" AS responsible_name,"" AS sub_advertiser_id,"" AS `name`,SUM(ad.cost) AS cost,SUM(ad.compensate) AS compensate,"-" as real_cost,ad_sub.rebates,SUM(ad.`convert`) AS `convert`,SUM(ad.`convert_cost`) AS convert_cost'];
        list($query, $start_time, $end_time) = static::dealProjectData($params, $select_field);
        $sum = $query->asArray()->one();
        if ($sum) {
            $sum['project_name'] = '合计';
            $sum['responsible_name'] = '';
            $sum['name'] = '';
        }

        $query->groupBy('ad.project_id');
        $totalCount = $query->count();
        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();
        if ($list) array_unshift($list, $sum);

        return [$list, $totalCount, $start_time, $end_time];
    }

    /**
     * 获取项目分析数据
     *
     * @param array $params
     * @param string $select_field
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function dealProjectData($params = [], $select_field = "")
    {
        list($start_time, $end_time) = DateHelper::AgoDate(strtotime($params['start_time']), strtotime($params['end_time']));

        if (empty($select_field)) {
            $select_field = ['ad.project_id,pp.name as project_name,"" AS responsible_name,"" AS sub_advertiser_id,"" AS `name`,SUM(ad.cost) AS cost,SUM(ad.compensate) AS compensate,ad_sub.rebates,SUM(ad.`convert`) AS `convert`,SUM(ad.`convert_cost`) AS convert_cost,ad_pd.name as direction_name'];
        }

        list($scope, $responsibleId) = Tool::getAuthScope();

        $query = AdsAccountData::find()->select($select_field)->alias('ad')
            ->leftJoin(['{{%backend_member}} bm'], 'bm.id = ad.responsible_id')
            ->leftJoin(['{{%promote_project}} pp'], 'pp.id = ad.project_id')
            ->leftJoin(['{{%ads_account_sub}} ad_sub'], 'ad_sub.id = ad.ads_sub_id')
            ->leftJoin(['{{%promote_channel}} ad_pc'], 'ad_pc.id = ad.promote_id')
            ->leftJoin(['{{%promote_direction}} ad_pd'], 'ad_pd.id = ad.direction_id')
            ->leftJoin(['{{%department_assignment}} da'], 'da.user_id = bm.id')
            ->where(['ad.entity_id' => Yii::$app->user->identity->current_entity_id])
            ->andWhere(['and', ['or', '`convert` !=0', '`cost` != 0', '`compensate` != 0']]);

        $query->andFilterWhere(['BETWEEN', 'ad.date', date('Ymd', strtotime($start_time)), date('Ymd', strtotime($end_time))])
            ->andFilterWhere(['bm.realname' => $params['keywords_realname']])
            ->andFilterWhere(['or', ['ad.responsible_id' => $responsibleId], ['ad.dept_id' => $scope]])
            ->andFilterWhere(['ad_sub.sub_advertiser_name' => $params['keywords_sub_advertiser_name']])
            ->andFilterWhere(['ad_sub.sub_advertiser_id' => $params['keywords_sub_id']])
            ->andFilterWhere(['ad.promote_id' => $params['promote_id']])
            ->andFilterWhere(['ad.link_id' => $params['link_id']])
            ->andFilterWhere(['ad.project_id' => $params['project_id']])
            ->orderBy('ad.cost DESC');

        if (isset($params['dept_id']) && $params['dept_id']) {
            $query->andWhere(['da.dept_id' => Department::getManageDeptIdsByIds($params['dept_id'])]);
        }

        return [$query, strtotime($start_time), strtotime($end_time)];
    }

    /**
     * 项目分析 - 详情
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getProjectDetails($params = [])
    {
        //        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        //        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        //获取详情数据时，无项目id则直接返回空数据
        if (!isset($params['project_id'])) return [0, 0];
        $select_field = ['ad.project_id,pp.name as project_name,bm.realname AS responsible_name,ad_sub.sub_advertiser_id,ad_sub.sub_advertiser_name AS name,SUM(ad.cost) AS cost,SUM(ad.compensate) AS compensate,"-" as real_cost,ad_sub.rebates,SUM(ad.`convert`) AS `convert`,SUM(ad.`convert_cost`) AS convert_cost,ad_pd.name as direction_name'];
        list($query, $start_time, $end_time) = static::dealProjectData($params, $select_field);
        $query->groupBy('ad.ads_sub_id');
        $totalCount = $query->count();
        //        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();
        $list = $query->asArray()->all();

        return [$list, $totalCount, $start_time, $end_time];
    }

    /**
     * 项目分析 - 处理导出数据
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getProjectExport($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        $select_field = ['ad.project_id,pp.name as project_name,bm.realname AS responsible_name,ad_sub.sub_advertiser_id,ad_sub.sub_advertiser_name AS name,SUM(ad.cost) AS cost,SUM(ad.compensate) AS compensate,ad_sub.rebates,SUM(ad.`convert`) AS `convert`,SUM(ad.`convert_cost`) AS convert_cost,ROUND(IFNULL((ad.cost - ad.compensate) / IFNULL(ad_sub.rebates, 0),0),2) AS real_cost,ad_pd.name as direction_name'];

        list($query) = static::dealProjectData($params, $select_field);

        $query->groupBy('ad.id');
        if (!empty($params['getTotal'])) return [[], $query->count()];
        $list = $query->offset(($page - 1) * $limit)->limit($limit)->orderBy('ad.cost')->asArray()->all();

        return [$list, 0];
    }

    /**
     * 流量数据分析 - 可筛选的时间区间（公共）
     *
     * @param string $type
     * @return false|string
     */
    public static function getPublicTime($type = 'start')
    {
        $order_by = ($type == 'start') ? 'ASC' : 'DESC';
        $info = AdsAccountData::find()->select('date')->asArray()->orderBy('date ' . $order_by)->limit(1)->one();
        return empty($info['date']) ? '' : DateHelper::toDate(strtotime($info['date']), 'Y-m-d');
    }

    /**
     * 平台分析-列表
     *
     * @param array $params
     * @param bool $type
     * @return array
     * @throws \Exception
     */
    public static function getPromoteData($params = [], $type = false)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        list($query, $start_time, $end_time) = static::dealPromoteData($params);
        $sum = $query->asArray()->one();
        if ($sum) {
            $sum['promote_name'] = '合计';
            $sum['responsible_name'] = '';
            $sum['name'] = '';
        }

        $query->groupBy('ad.promote_id');
        $totalCount = $query->count();
        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();
        if ($list) array_unshift($list, $sum);

        return [$list, $totalCount, $start_time, $end_time];
    }

    /**
     * 获取平台分析数据
     *
     * @param array $params
     * @param string $select_field
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function dealPromoteData($params = [], $select_field = "")
    {
        list($start_time, $end_time) = DateHelper::AgoDate(strtotime($params['start_time']), strtotime($params['end_time']));

        if (empty($select_field)) {
            $select_field = ['ad.promote_id,ad_pc.name as promote_name,"" AS sub_advertiser_id,"" AS responsible_name,"" AS `name`,SUM(ad.cost) AS cost,SUM(ad.compensate) AS compensate,"-" as real_cost,ad_sub.rebates,SUM(ad.`convert`) AS `convert`,SUM(ad.`convert_cost`) AS convert_cost'];
        }

        list($scope, $responsibleId) = Tool::getAuthScope();

        $query = AdsAccountData::find()->select($select_field)->alias('ad')
            ->leftJoin(['{{%backend_member}} bm'], 'bm.id = ad.responsible_id')
            ->leftJoin(['{{%ads_account_sub}} ad_sub'], 'ad_sub.id = ad.ads_sub_id')
            ->leftJoin(['{{%promote_channel}} ad_pc'], 'ad_pc.id = ad.promote_id')
            ->leftJoin(['{{%promote_direction}} ad_pd'], 'ad_pd.id = ad.direction_id')
            ->leftJoin(['{{%department_assignment}} da'], 'da.user_id = bm.id')
            ->where(['ad.entity_id' => Yii::$app->user->identity->current_entity_id])
            ->andWhere(['and', ['or', '`convert` !=0', '`cost` != 0', '`compensate` != 0']]);

        $query->andFilterWhere(['BETWEEN', 'ad.date', date('Ymd', strtotime($start_time)), date('Ymd', strtotime($end_time))])
            ->andFilterWhere(['bm.realname' => $params['keywords_realname']])
            ->andFilterWhere(['or', ['ad.responsible_id' => $responsibleId], ['ad.dept_id' => $scope]])
            ->andFilterWhere(['ad_sub.sub_advertiser_name' => trim($params['keywords_sub_advertiser_name'])])
            ->andFilterWhere(['ad_sub.sub_advertiser_id' => $params['keywords_sub_id']])
            ->andFilterWhere(['ad.promote_id' => $params['promote_id']])
            ->andFilterWhere(['ad.link_id' => $params['link_id']])
            ->andFilterWhere(['ad.project_id' => $params['project_id']])
            ->orderBy('ad.cost DESC');

        if (isset($params['dept_id']) && $params['dept_id']) {
            $query->andWhere(['da.dept_id' => Department::getManageDeptIdsByIds($params['dept_id'])]);
        }

        return [$query, strtotime($start_time), strtotime($end_time)];
    }

    /**
     * 平台分析 - 详情
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getPromoteDetails($params = [])
    {
        //        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        //        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        //获取详情数据时，无项目id则直接返回空数据
        if (empty($params['promote_id'])) return [[], 0];

        $select_field = ['ad.promote_id,ad_pc.name as promote_name,bm.realname AS responsible_name,ad_sub.sub_advertiser_id,ad_sub.sub_advertiser_name AS name,SUM(ad.cost) AS cost,SUM(ad.compensate) AS compensate,"-" as real_cost,ad_sub.rebates,SUM(ad.`convert`) AS `convert`,SUM(ad.`convert_cost`) AS convert_cost, ad_pd.name as direction_name'];
        list($query, $start_time, $end_time) = static::dealPromoteData($params, $select_field);
        $query->groupBy('ad.ads_sub_id');
        $totalCount = $query->count();
        $list = $query->asArray()->all();
        //        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();

        return [$list, $totalCount, $start_time, $end_time];
    }

    /**
     * 平台分析 - 处理导出数据
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getPromoteExport($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $select_field = ['ad.promote_id,ad_pc.name as promote_name,bm.realname AS responsible_name,ad_sub.sub_advertiser_id,ad_sub.sub_advertiser_name AS name,ad.cost AS cost,ad.compensate AS compensate,ad_sub.rebates,ad.`convert` AS `convert`,ad.`convert_cost` AS convert_cost,ROUND(IFNULL((ad.cost - ad.compensate) / IFNULL(ad_sub.rebates, 0),0),2) AS real_cost, ad_pd.name as direction_name'];

        list($query) = static::dealPromoteData($params, $select_field);

        $query->groupBy('ad.id');
        if (!empty($params['getTotal'])) return [[], $query->count()];
        $list = $query->offset(($page - 1) * $limit)->limit($limit)->orderBy('ad.cost')->asArray()->all();
        return [$list, 0];
    }

    /**
     * 落地页统计-获取落地页统计列表
     *
     * @param $params
     * @return array
     * @throws \Exception
     */
    public static function getLandPageList($params)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10) - 1;   //条数

        list($query, $start_time, $end_time) = static::dealLandPageData($params);
        $query->groupBy('p.landing_id,p.platform_name');
        $totalCount = $query->count();
        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();

        return [$list, $totalCount, $start_time, $end_time];
    }

    /**
     * 获取落地页数据
     *
     * @param $params
     * @param string $select_field 备用，后期如果需要筛选可添加
     * @return array
     */
    public static function dealLandPageData($params, $select_field = '')
    {
        $start_time = $params['start_time'] ? strtotime($params['start_time']) : strtotime(date('Y-m-d') . '00:00:00');
        $end_time = $params['end_time'] ? strtotime($params['end_time']) : strtotime(date('Y-m-d') . '23:59:59');
        $reportEnum = PromotionEventTypeEnum::REPRORT; //状态为上报的
        $query = PromotionUserAnalysis::find()->alias('p')
            ->select("
            	`p`.`id`,
                    count(
                        CASE
                        WHEN p. EVENT = 'visit' THEN
                            1
                        end
                    ) AS visiter,
                    count(
                        CASE
                        WHEN p. EVENT = 'qrcode' THEN
                            1
                end
                    ) AS qrcoder ,
                    count(DISTINCT
                        CASE
                        WHEN (pl.created_at BETWEEN $start_time AND $end_time) and `pl`.`event_type` = $reportEnum THEN
                            pl.id
                        END
                    ) AS wecomfans,
                    `p`.`unionid`,
                    (
                        CASE
                        WHEN p.platform_name = 'headlines' THEN
                            '新1'
                        WHEN p.platform_name='quickly' THEN 
                            '新10'
                        end
                    ) AS platform_name,
                    count(DISTINCT
                        CASE
                        WHEN (CAST(pl.created_at AS signed) - CAST(p.updated_at AS signed))<1800 and `pl`.`event_type` = $reportEnum THEN
                            p.unionid
                    
                        END
                    ) AS 'time',
                    `p`.`landing_id`,
                    `p`.`channel_id`,
                    `p`.`link_id`,
                    `p`.`date`
           ")
            ->leftJoin(['{{%promotion_user_analysis_log}} pl'], 'p.id = pl.analysis_id')
            ->andFilterWhere(['BETWEEN', 'p.updated_at', $start_time, $end_time]);
        return [$query, $start_time, $end_time];
    }

    /**
     * 落地页 - 处理导出数据
     *
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function getLandPageExport($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10) - 1;   //条数
        $start_time = $params['start_time'] ? strtotime($params['start_time'] . '00:00:00') : strtotime(date('Y-m-d') . '00:00:00');
        $end_time = $params['end_time'] ? strtotime($params['end_time'] . '23:59:59') : strtotime(date('Y-m-d') . '23:59:59');
        $query = PromotionUserAnalysisLog::find()->alias('pl')
            ->select("
            `pl`.`id` as plid,
            `p`.`landing_id`,
            `p`.`platform_name`,
            `pl`.`msg`,
            `pl`. `data`,
            `pl`.`created_at`,
            `u`.name as uname,
            `u`.gender,
            `u`.unionid,
            `u`.company_name,
            `u`.follow_user_id,
            `u`.follow_user_oper_userid,
            `u`.type,
            `u`.`callback_time`,
            `u`.`create_at`
            ")
            ->leftJoin(['{{%wxcom_userinfo}} u'], 'u.unionid = pl.unionid and u.callback_time>' . $start_time)
            ->leftJoin(['{{%promotion_user_analysis}} p'], 'p.id = pl.analysis_id')
            ->where(['not', ['u.name' => null]])
            ->andWhere(['pl.event_type' => 1])
            ->andFilterWhere(['BETWEEN', 'pl.created_at', $start_time, $end_time])
            ->andFilterWhere(['p.landing_id' => $params['landing_id']])
            ->groupBy('pl.unionid');
        if (!empty($params['getTotal'])) return [[], $query->count()];
        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();
        foreach ($list as $k => $v) {
            $list[$k]['created_at'] = DateHelper::toDate($v['created_at']);
            $list[$k]['callback_time'] = DateHelper::toDate($v['callback_time']);
            $list[$k]['create_at'] = DateHelper::toDate($v['create_at']);
        }
        return [$list, 0];
    }

    /**
     * 获取城市列表数据
     *
     * @return array
     */
    public static function getCityList()
    {
        //获取省份
        $provinceList = Yii::$app->services->provinces->getCityByLevel(1);
        if ($provinceList) $provinceList = ArrayHelper::index($provinceList, 'id');
        //获取城市
        $cityList = Yii::$app->services->provinces->getCityByLevel(2);
        if ($cityList) $cityList = ArrayHelper::index($cityList, null, 'pid');

        $data = [];
        foreach ($provinceList as $k => $v) {
            $v['child'] = $cityList[$v['id']];
            $data[$k] = $v;
        }
        sort($data);

        return $data;
    }

    public static $isLookAuthResult = null;
    /**
     * 该人员角色是否有权限查看
     *
     * @return bool
     */
    public static function isLookAuth()
    {
        if (empty(self::$isLookAuthResult)) {
            //获取当前用户权限
            $auth_ids = AuthAssignment::find()->select('role_id')->andWhere(['user_id' => Yii::$app->user->id])->column();
            //获取配置可查阅权限角色
            $realCostLookRole = ConfigService::getByName('realCostLookRole') ?: [];

            self::$isLookAuthResult = Yii::$app->services->auth->isSuperAdmin() || array_intersect($auth_ids, $realCostLookRole);
        }

        return self::$isLookAuthResult;
    }

    /**
     * 获取素材分析数据查询
     * @param array $params 查询参数
     * @return \yii\db\ActiveQuery
     */
    public static function getMaterialDataQuery($params)
    {
        $defaultRange = DateHelper::thisMonth();
        $rawStart     = ArrayHelper::getValue($params, 'start_time');
        $rawEnd       = ArrayHelper::getValue($params, 'end_time');
        $start_time   = !empty($rawStart)
            ? DateHelper::toDate($rawStart, 'Ymd')
            : DateHelper::toDate($defaultRange['start'], 'Ymd');
        $end_time     = !empty($rawEnd)
            ? DateHelper::toDate($rawEnd,   'Ymd')
            : DateHelper::toDate($defaultRange['end'],   'Ymd');

        list($scope, $responsibleId) = Tool::getAuthScope();

        $searchLabel = false;
        $searchLabelCreateTime = false;

        if (isset($params['label_create_start_time']) && isset($params['label_create_end_time'])) {
            $searchLabelCreateTime = true;
        }

        if (isset($params['tag_ids'])) {
            $searchLabel = true;
        }

        $labelIds = $params['tag_ids'] ?? [];
        $label_create_start_time = ArrayHelper::getValue($params, 'label_create_start_time', '');
        $label_create_end_time = ArrayHelper::getValue($params, 'label_create_end_time', '');

        //项目搜索
        $params['project_id'] = static::getProjectId($params['project_type'], $params['project_id']);

        $query = AdsMaterialData::find()
            ->from(['{{%ads_material_data}} `amd` FORCE INDEX (`index_date_cost`)'])
            ->select(['am.material_id,am.id as am_id,am.ad_platform_material_name,amd.relate_id,am.video_img,acs.sub_advertiser_id,acs.sub_advertiser_name,amb.name as main_body_name,amb.id as main_body_id,
            sum(amd.stat_cost) as stat_cost,sum(amd.real_cost) as real_cost,sum(amd.show_cnt) as show_cnt,sum(click_cnt) as click_cnt,
            sum(convert_cnt) as convert_cnt,sum(deep_convert_cnt) as deep_convert_cnt,
            sum(amd.add_fans) as add_fans,sum(amd.deposit_count) as deposit_count,sum(amd.arrival_amount) as arrival_amount,
            sum(amd.real_final) as real_final,sum(amd.attrition_count) as attrition_count'])
            ->leftJoin('{{%ads_material_relate}} amr', 'amr.id = amd.relate_id')
            ->leftJoin('{{%ads_material}} am', 'am.id = amr.material_id')
            ->leftJoin('{{%ads_account_sub}} acs', 'acs.id = amr.ads_sub_id')
            ->leftJoin('{{%ads_main_body}} amb', 'amb.id = acs.main_body_id');

        if ($searchLabel || $searchLabelCreateTime) {
            // 根据标签ID关联素材标签表,筛选出符合条件的主体和素材组合
            $query->innerJoin(['aml' => (new Query())
                ->select(['main_body_id', 'material_id'])
                ->from('{{%ads_material_label}}')
                ->filterWhere(['label_id' => $labelIds])
                ->andFilterWhere(['BETWEEN', 'created_at', $label_create_start_time, $label_create_end_time])
                ->groupBy(['main_body_id', 'material_id'])
            ], 'aml.main_body_id = amb.id AND aml.material_id = am.id');
        }

        // 添加其他查询条件
        $query->where(['amd.entity_id' => UserService::getInst()->current_entity_id])
            ->andFilterWhere(['acs.sub_advertiser_id' => trim($params['sub_advertiser_id'])])
            ->andFilterWhere(['acs.promote_id' => trim($params['promote_id'])])
            ->andFilterWhere(['acs.project_id' => $params['project_id']])
            ->andFilterWhere(['acs.responsible_id' => $params['responsible_id']])
            ->andFilterWhere(['am.composer_id' => $params['composer_id']])
            ->andFilterWhere(['am.refurbisher_id' => $params['refurbisher_id']])
            ->andFilterWhere(['>=', 'am.created_at', $params['create_start_time']])
            ->andFilterWhere(['<=', 'am.created_at', $params['create_end_time']])
            ->andFilterWhere(['am.material_id' => $params['material_id']])
            ->andFilterWhere(['like', 'am.ad_platform_material_name', trim($params['material_name'])])
            ->andFilterWhere(['acs.main_body_id' => $params['main_body_id']])
            ->andFilterWhere(['BETWEEN', 'amd.date', $start_time, $end_time])
            ->andFilterWhere(['or', ['acs.responsible_id' => $responsibleId], ['acs.dept_id' => $scope]]);

        return $query;
    }

    /**
     * 素材数据
     */
    public static function getMaterialData($params, $export = false)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        $totalCount = 0;
        $query = static::getMaterialDataQuery($params);
        $query->groupBy(['am.material_id', 'acs.main_body_id']);

        if ($export) {
            if (!empty($params['getTotal'])) {
                return [[], $query->count()];
            }
        } else {
            $totalCount = $query->count();
        }
        //  排序规则
        $orderField = ArrayHelper::getValue($params, 'order_field', 'stat_cost');
        if (in_array($orderField, ['stat_cost', 'real_cost', 'add_fans', 'deposit_count'])) {
            $orderType = ArrayHelper::getValue($params, 'order_type', 'desc');
            if (in_array($orderType, ['asc', 'desc'])) {
                $query->orderBy("{$orderField} {$orderType}");
            }
        }
        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();

        $mainBodyIds = array_unique(ArrayHelper::getColumn($list, 'main_body_id'));
        $materialTableIds = array_unique(ArrayHelper::getColumn($list, 'am_id'));

        $relateLabelList = AdsMaterialLabel::find()
            ->alias('aml')
            ->select('aml.main_body_id,aml.material_id,aml.label_id,acl.name as label_name,acl.type')
            ->leftJoin('{{%ads_cus_label}} acl', 'acl.id = aml.label_id')
            ->where(['main_body_id' => $mainBodyIds])
            ->andWhere(['material_id' => $materialTableIds])
            ->asArray()->all();

        $labelMap = [];
        foreach ($relateLabelList as $item) {
            $mainBodyId = $item['main_body_id'];
            $materialId = $item['material_id'];
            $type = $item['type'];
        
            if (!isset($labelMap[$mainBodyId])) {
                $labelMap[$mainBodyId] = [];
            }
            if (!isset($labelMap[$mainBodyId][$materialId])) {
                $labelMap[$mainBodyId][$materialId] = [];
            }
            if (!isset($labelMap[$mainBodyId][$materialId][$type])) {
                $labelMap[$mainBodyId][$materialId][$type] = [];
            }
        
            $labelMap[$mainBodyId][$materialId][$type][] = $item['label_name'];
        }

        foreach ($list as &$item) {
            $item['cpm_platform'] = round(BcHelper::div($item['stat_cost'], $item['show_cnt'], 5) * 1000, 2);
            $item['ctr'] = round(BcHelper::div($item['click_cnt'], $item['show_cnt'], 4) * 100, 2);
            $item['cpc_platform'] = BcHelper::div($item['stat_cost'], $item['click_cnt']);
            $item['conversion_rate'] = round(BcHelper::div($item['convert_cnt'], $item['click_cnt'], 4) * 100, 2);
            $item['conversion_cost'] = BcHelper::div($item['stat_cost'], $item['convert_cnt']);
            $item['deep_convert_rate'] = round(BcHelper::div($item['deep_convert_cnt'], $item['convert_cnt'], 4) * 100, 2);
            $item['deep_convert_cost'] = BcHelper::div($item['stat_cost'], $item['deep_convert_cnt']);
            $item['add_fans_cost'] = BcHelper::div($item['stat_cost'], $item['add_fans']);
            $item['real_add_fans_cost'] = BcHelper::div($item['real_cost'], $item['add_fans']);
            $item['deposit_count_cost'] = BcHelper::div($item['stat_cost'], $item['deposit_count']);
            $item['real_deposit_count_cost'] = BcHelper::div($item['real_cost'], $item['deposit_count']);
            $item['arrival_amount_cost'] = BcHelper::div($item['stat_cost'], $item['arrival_amount']);
            $item['real_arrival_amount_cost'] = BcHelper::div($item['real_cost'], $item['arrival_amount']);
            $item['roi'] = BcHelper::div($item['real_final'], $item['real_cost']);
            $item['book_roi'] = BcHelper::div($item['real_final'], $item['stat_cost']);
            $item['deposit_rate'] = round(BcHelper::div($item['deposit_count'], $item['add_fans'], 4) * 100, 2);
            $item['fans_arrive_store_rate'] = round(BcHelper::div($item['arrival_amount'], $item['add_fans'], 4) * 100, 2);
            $item['label_platform'] = $labelMap[$item['main_body_id']][$item['am_id']][AdsMaterialLabelTypeEnum::PLATFORM] ? implode(',', $labelMap[$item['main_body_id']][$item['am_id']][AdsMaterialLabelTypeEnum::PLATFORM]) : '';
            $item['label_internal'] = $labelMap[$item['main_body_id']][$item['am_id']][AdsMaterialLabelTypeEnum::INTERNAL] ? implode(',', $labelMap[$item['main_body_id']][$item['am_id']][AdsMaterialLabelTypeEnum::INTERNAL]) : '';
        }
        return [$list, $totalCount];
    }

    /**
     * 素材数据汇总
     */
    public static function totalMaterialData($params)
    {
        $sumQuery = static::getMaterialDataQuery($params);

        $sumData = $sumQuery->groupBy("")->asArray()->one();
        foreach ($sumData as $key => $v) {
            if (empty($v)) {
                $sumData[$key] = 0;
            }

            $sumData['cpm_platform'] = round(BcHelper::div($sumData['stat_cost'], $sumData['show_cnt'], 5) * 1000, 2);
            $sumData['ctr'] = round(BcHelper::div($sumData['click_cnt'], $sumData['show_cnt'], 4) * 100, 2);
            $sumData['cpc_platform'] = BcHelper::div($sumData['stat_cost'], $sumData['click_cnt']);
            $sumData['conversion_rate'] = round(BcHelper::div($sumData['convert_cnt'], $sumData['click_cnt'], 4) * 100, 2);
            $sumData['conversion_cost'] = BcHelper::div($sumData['stat_cost'], $sumData['convert_cnt']);
            $sumData['deep_convert_rate'] = round(BcHelper::div($sumData['deep_convert_cnt'], $sumData['convert_cnt'], 4) * 100, 2);
            $sumData['deep_convert_cost'] = BcHelper::div($sumData['stat_cost'], $sumData['deep_convert_cnt']);
            $sumData['add_fans_cost'] = BcHelper::div($sumData['stat_cost'], $sumData['add_fans']);
            $sumData['real_add_fans_cost'] = BcHelper::div($sumData['real_cost'], $sumData['add_fans']);
            $sumData['deposit_count_cost'] = BcHelper::div($sumData['stat_cost'], $sumData['deposit_count']);
            $sumData['real_deposit_count_cost'] = BcHelper::div($sumData['real_cost'], $sumData['deposit_count']);
            $sumData['arrival_amount_cost'] = BcHelper::div($sumData['stat_cost'], $sumData['arrival_amount']);
            $sumData['real_arrival_amount_cost'] = BcHelper::div($sumData['real_cost'], $sumData['arrival_amount']);
            $sumData['roi'] = BcHelper::div($sumData['real_final'], $sumData['real_cost']);
            $sumData['book_roi'] = BcHelper::div($sumData['real_final'], $sumData['stat_cost']);
            $sumData['deposit_rate'] = round(BcHelper::div($sumData['deposit_count'], $sumData['add_fans'], 4) * 100, 2);
            $sumData['fans_arrive_store_rate'] = round(BcHelper::div($sumData['arrival_amount'], $sumData['add_fans'], 4) * 100, 2);
        }

        $sumData['video_img'] = $sumData['sub_advertiser_id'] = $sumData['sub_advertiser_name'] = 
            $sumData['label_platform'] = $sumData['label_internal'] = $sumData['main_body_name'] = '';
        $sumData['material_id'] = '合计';
        return $sumData;
    }

    /**
     * 获取素材数据分析
     *
     * @param $params
     * @return array|\yii/db/ActiveRecord[]
     */
    public static function getMaterialDataList($params)
    {
        list($list, $totalCount) = static::getMaterialData($params);
        $sumData = static::totalMaterialData($params);

        array_unshift($list, $sumData);
        return [$list, $totalCount];
    }

    /**
     * 获取年龄性别分析数据查询
     */
    public static function getAgeGenderDataQuery($params)
    {
        list($start_time, $end_time) = DateHelper::AgoDate($params['start_time'], $params['end_time']);
        list($scope, $responsibleId) = Tool::getAuthScope();
        //项目搜索
        $params['project_id'] = static::getProjectId($params['project_type'], $params['project_id']);

        return AdsAgeGenderData::find()->alias('amd')
            ->select('amd.age,amd.gender,acs.sub_advertiser_id,acs.sub_advertiser_name,sum(amd.stat_cost) as stat_cost,sum(amd.show_cnt) as show_cnt,sum(click_cnt) as click_cnt,sum(convert_cnt) as convert_cnt,sum(deep_convert_cnt) as deep_convert_cnt,sum(amd.new_store_cus_count) as new_store_cus_count,sum(amd.amount) as amount,sum(amd.add_fans_count) as add_fans_count')
            ->leftJoin('{{%ads_account_sub}} acs', 'acs.id = amd.ads_sub_id')
            ->where(['amd.entity_id' => UserService::getInst()->current_entity_id])
            ->andFilterWhere(['acs.sub_advertiser_id' => trim($params['sub_advertiser_id'])])
            ->andFilterWhere(['amd.age' => trim($params['age'])])
            ->andFilterWhere(['amd.gender' => trim($params['gender'])])
            ->andFilterWhere(['acs.promote_id' => $params['channel_id']])
            ->andFilterWhere(['acs.project_id' => $params['project_id']])
            ->andFilterWhere(['acs.responsible_id' => $params['responsible_id']])
            ->andFilterWhere(['BETWEEN', 'amd.date', $start_time, $end_time])
            ->andFilterWhere(['or', ['acs.responsible_id' => $responsibleId], ['acs.dept_id' => $scope]]);
    }

    /**
     * 年龄性别数据
     */
    public static function getAgeGenderData($params, $export = false)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数

        $totalCount = 0;
        $query = static::getAgeGenderDataQuery($params);
        $query->groupBy('amd.age,amd.gender');
        if ($export) {
            if (!empty($params['getTotal'])) {
                return [[], $query->count()];
            }
        } else {
            $totalCount = $query->count();
        }
        // 排序规则
        $orderField = ArrayHelper::getValue($params, 'order_field', 'stat_cost');
        if (in_array($orderField, ['stat_cost', 'convert_cnt', 'deep_convert_cnt', 'show_cnt', 'click_cnt'])) {
            $orderType = ArrayHelper::getValue($params, 'order_type', 'desc');
            if (in_array($orderType, ['asc', 'desc'])) {
                $query->orderBy("{$orderField} {$orderType}");
            }
        } else {
            $query->orderBy("{$orderField} desc");
        }

        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();

        foreach ($list as &$item) {
            $item['cpm_platform'] = round(BcHelper::div($item['stat_cost'], $item['show_cnt'], 5) * 1000, 2);
            $item['ctr'] = round(BcHelper::div($item['click_cnt'], $item['show_cnt'], 4) * 100, 2);
            $item['cpc_platform'] = BcHelper::div($item['stat_cost'], $item['click_cnt']);
            $item['conversion_rate'] = round(BcHelper::div($item['convert_cnt'], $item['click_cnt'], 4) * 100, 2);
            $item['conversion_cost'] = BcHelper::div($item['stat_cost'], $item['convert_cnt']);
            $item['deep_convert_rate'] = round(BcHelper::div($item['deep_convert_cnt'], $item['convert_cnt'], 4) * 100, 2);
            $item['deep_convert_cost'] = BcHelper::div($item['stat_cost'], $item['deep_convert_cnt']);
            // 客单价 = 门店实收 / 新客到店人数
            $item['customer_price'] = BcHelper::div($item['amount'], $item['new_store_cus_count']);
            // 账面ROI = 门店实收 / 消耗
            $item['book_roi'] = BcHelper::div($item['amount'], $item['stat_cost']);
        }
        return [$list, $totalCount];
    }

    /**
     * 年龄性别数据汇总
     */
    public static function totalAgeGenderData($params)
    {
        $sumQuery = static::getAgeGenderDataQuery($params);

        $sumData = $sumQuery->groupBy("")->asArray()->one();
        foreach ($sumData as $key => $v) {
            if (empty($v)) {
                $sumData[$key] = 0;
            }

            $sumData['cpm_platform'] = round(BcHelper::div($sumData['stat_cost'], $sumData['show_cnt'], 5) * 1000, 2);
            $sumData['ctr'] = round(BcHelper::div($sumData['click_cnt'], $sumData['show_cnt'], 4) * 100, 2);
            $sumData['cpc_platform'] = BcHelper::div($sumData['stat_cost'], $sumData['click_cnt']);
            $sumData['conversion_rate'] = round(BcHelper::div($sumData['convert_cnt'], $sumData['click_cnt'], 4) * 100, 2);
            $sumData['conversion_cost'] = BcHelper::div($sumData['stat_cost'], $sumData['convert_cnt']);
            $sumData['deep_convert_rate'] = round(BcHelper::div($sumData['deep_convert_cnt'], $sumData['convert_cnt'], 4) * 100, 2);
            $sumData['deep_convert_cost'] = BcHelper::div($sumData['stat_cost'], $sumData['deep_convert_cnt']);
            // 客单价 = 门店实收 / 新客到店人数
            $sumData['customer_price'] = BcHelper::div($sumData['amount'], $sumData['new_store_cus_count']);
            // 账面ROI = 门店实收 / 消耗
            $sumData['book_roi'] = BcHelper::div($sumData['amount'], $sumData['stat_cost']);
        }

        $sumData['gender'] = $sumData['sub_advertiser_id'] = $sumData['sub_advertiser_name'] = '';
        $sumData['age'] = '合计';
        return $sumData;
    }

    /**
     * 获取年龄性别数据分析
     *
     * @param $params
     * @return array|\yii/db/ActiveRecord[]
     */
    public static function getAgeGenderDataList($params)
    {
        list($list, $totalCount) = static::getAgeGenderData($params);
        $sumData = static::totalAgeGenderData($params);

        array_unshift($list, $sumData);
        return [$list, $totalCount];
    }

    /**
     * 加粉产出分析
     */
    public static function analyzeUserGrowthOutput($params, $isExport = false)
    {
        // 时间范围
        $month = DateHelper::thisMonth();
        $addStartTime = ArrayHelper::getValue($params, 'add_start_time', $month['start']);
        $addEndTime = ArrayHelper::getValue($params, 'add_end_time', $month['end']);
        $outputStartTime = ArrayHelper::getValue($params, 'output_start_time', $month['start']);
        $outputEndTime = ArrayHelper::getValue($params, 'output_end_time', $month['end']);

        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 20);   //条数

        $params['project_id'] = static::getProjectId($params['project_type'], $params['project_id']);
        // 查询
        $query = CusCustomerUser::find()
            ->alias('ccu')
            ->select([
                '`m`.`username`',
                'COUNT(DISTINCT ccu.id) AS add_fans_num',
                'IFNULL(order_h.total_performance, 0) AS performance',
                'ROUND((IFNULL(order_h.total_performance,0) / IF(COUNT(DISTINCT ccu.id) > 0 ,COUNT(DISTINCT ccu.id),1)),2) AS avg_performance'
            ])
            ->leftJoin(['m' => Member::tableName()], 'm.id = ccu.created_by')
            ->leftJoin(
                [
                    'order_h' => OrderHeader::find()
                        ->alias('oh')
                        ->select(['oh.plan_by', 'SUM(oh.received_amount + oh.group_amount) AS total_performance'])
                        ->leftJoin(['ccu' => CusCustomerUser::tableName()], 'ccu.id = oh.customer_user_id')
                        ->where(['oh.order_status' => 5, 'oh.source_type' => 1])
                        ->andWhere(['>', 'ccu.channel_id', 0])
                        ->andWhere(['between', 'oh.plan_time', $outputStartTime, $outputEndTime])
                        ->andWhere(['between', 'ccu.add_time', $addStartTime, $addEndTime])
                        ->andWhere(['ccu.add_way' => CusCustomerUser::getComputeAddWay()])
                        ->andFilterWhere(['ccu.project_id' => $params['project_id']])
                        ->groupBy('oh.plan_by')
                ],
                'order_h.plan_by = ccu.created_by'
            )
            ->where(['between', 'ccu.add_time', $addStartTime, $addEndTime])
            ->andWhere(['ccu.add_way' => CusCustomerUser::getComputeAddWay()])
            ->andWhere(['>', 'ccu.channel_id', 0])
            ->andFilterWhere(['ccu.project_id' => $params['project_id']])
            ->groupBy('ccu.created_by');

        if ($isExport) {
            if (isset($params['getTotal']) && $params['getTotal']) {
                return [[], $query->count()];
            }
            $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();
            return [$list, 0];
        }

        $totalCount = $query->count();
        $list = $query->offset(($page - 1) * $limit)->limit($limit)->asArray()->all();
        return [$list, $totalCount];
    }

    /**
     * 获取时段分析数据
     *
     * @param array $params
     * @param bool $export
     * @return array
     * @throws \Exception
     */
    public static function getHourData($params = [], $export = false)
    {
        $page = ArrayHelper::getValue($params, 'page', 1);
        $limit = ArrayHelper::getValue($params, 'limit', 10);

        list($scope, $responsibleId) = Tool::getAuthScope();

        $query = AdsAccountDataHour::find()->alias('adh')
            ->select('adh.id,adh.date,adh.hour,SUM(adh.click) as click,SUM(adh.add_fans_count) as add_fans_count,
            SUM(adh.show) as show,SUM(adh.cost) as cost,
            COUNT(*) as record_count,
            SUM(adh.deposit_count) as deposit_count,
            SUM(adh.new_store_cus_count) as new_store_cus_count,
            SUM(adh.amount) as amount,
            SUM(adh.actual_consume) as actual_consume')
            ->leftJoin(['ads_sub' => AdsAccountSub::tableName()], 'ads_sub.id = adh.ads_sub_id');

        $start_time = DateHelper::toDate(strtotime($params['start_time']), 'Ymd');
        $end_time = DateHelper::toDate(strtotime($params['end_time']), 'Ymd');

        $params['project_id'] = static::getProjectId($params['project_type'], $params['project_id']);
        $query
            ->where(['adh.entity_id' => $params['entity_id']])
            ->andFilterWhere(['between', 'adh.date', $start_time, $end_time])
            ->andFilterWhere(['ads_sub.sub_advertiser_id' => $params['sub_advertiser_id']])
            ->andFilterWhere(['ads_sub.sub_advertiser_name' => $params['sub_advertiser_name']])
            ->andFilterWhere(['ads_sub.promote_id' => $params['promote_id']])
            ->andFilterWhere(['ads_sub.direction_id' => $params['direction_id']])
            ->andFilterWhere(['ads_sub.responsible_id' => $params['responsible_id']])
            ->andFilterWhere(['ads_sub.main_body_id' => $params['main_body_id']])
            ->andFilterWhere(['ads_sub.project_id' => $params['project_id']])
            ->andFilterWhere(['or', ['ads_sub.responsible_id' => $responsibleId], ['ads_sub.dept_id' => $scope]]);

        if (isset($params['dept_id']) && $params['dept_id']) {
            $query->andWhere(['ads_sub.dept_id' => Department::getManageDeptIdsByIds($params['dept_id'])]);
        }

        if (isset($params['start_hour']) && isset($params['end_hour'])) {
            $query->andFilterWhere(['between', 'adh.hour', $params['start_hour'], $params['end_hour']]);
        }

        //排序
        if (!empty($params['column']) && in_array($params['column'], ['cost', 'click'])) {
            $orderBy = $params['column'] . ' ' . ArrayHelper::getValue($params, 'order', 'desc');
        } else {
            $orderBy = 'adh.hour';
        }
        $groupBy = 'adh.hour';

        // 获取汇总数据
        $sumQuery = clone $query;
        $sum = $sumQuery->select('SUM(adh.click) as click,SUM(adh.add_fans_count) as add_fans_count,
            SUM(adh.show) as show,SUM(adh.cost) as cost,
            SUM(adh.deposit_count) as deposit_count,
            SUM(adh.new_store_cus_count) as new_store_cus_count,
            SUM(adh.amount) as amount,
            SUM(adh.actual_consume) as actual_consume')->asArray()->one();
        if ($sum) $sum['hour'] = '合计';
        
        // 获取是否有查看权限
        $isLookAuth = self::isLookAuth();

        if ($export) {  //导出数据
            if (!empty($params['getTotal'])) return [[], $query->groupBy($groupBy)->count()];
            $list = $query->offset(($page - 1) * $limit)->limit($limit)->orderBy($orderBy)->groupBy($groupBy)->asArray()->all();
            foreach ($list as &$v) {
                $v['hour_display'] = sprintf('%02d:00-%02d:59', $v['hour'], $v['hour']);
                self::calculateMetrics($v, $sum, $isLookAuth);
            }

            return [$list, 0];
        } else {
            $totalCount = $query->groupBy($groupBy)->count();
            $list = $query->offset(($page - 1) * $limit)->limit($limit)->orderBy($orderBy)->groupBy($groupBy)->asArray()->all();
            foreach ($list as &$v) {
                $v['hour_display'] = sprintf('%02d:00-%02d:59', $v['hour'], $v['hour']);
                self::calculateMetrics($v, $sum, $isLookAuth);
            }
            
            if ($list) {
                // 设置汇总行的显示标识
                $sum['hour_display'] = '合计';
                
                self::calculateMetrics($sum, $sum, $isLookAuth);
                
                array_unshift($list, $sum);
            }

            return [$list, $totalCount];
        }
    }

    /**
     * 计算指标数据
     * @param array $v 当前数据
     * @param array $sum 总和数据
     * @param bool $isLookAuth 是否有查看权限
     * @return void
     */
    private static function calculateMetrics(&$v, $sum, $isLookAuth)
    {
        // 消耗占比（对于汇总行，不计算占比，设为空字符串）
        $v['cost_ratio'] = isset($v['hour_display']) && $v['hour_display'] === '合计'
            ? ''
            : BcHelper::percentage($v['cost'], $sum['cost']);
            
        // 点击率
        $v['click_through_rate'] = BcHelper::percentage($v['click'], $v['show']);
        // 账面加粉成本 = 消耗 / 加粉数
        $v['book_add_fans_cost'] = BcHelper::div($v['cost'], $v['add_fans_count']);
        // 客单价 = 门店实收 / 新客到店人数
        $v['customer_price'] = BcHelper::div($v['amount'], $v['new_store_cus_count']);
        // 账面ROI = 门店实收 / 消耗
        $v['book_roi'] = BcHelper::div($v['amount'], $v['cost']);
        
        // 权限控制：敏感数据需要特定权限才能查看
        if (!$isLookAuth) {
            $hiddenMetrics = [
                'deposit_cost', 'deposit_rate',
                'store_cost', 'fans_arrive_store_rate',
                'actual_consume'
            ];
            foreach ($hiddenMetrics as $metric) {
                $v[$metric] = '-';
            }
        } else {
            // 账面到店成本 = 消耗 / 新客到店人数
            $v['store_cost'] = BcHelper::div($v['cost'], $v['new_store_cus_count']);
            // 加粉转到店率 = 新客到店人数 / 加粉数
            $v['fans_arrive_store_rate'] = BcHelper::percentage($v['new_store_cus_count'], $v['add_fans_count']);
            // 推广加粉订金转化率 = 订金数 / 加粉数
            $v['deposit_rate'] = BcHelper::percentage($v['deposit_count'], $v['add_fans_count']);
            // 账面订金成本 = 消耗 / 订金数
            $v['deposit_cost'] = BcHelper::div($v['cost'], $v['deposit_count']);
            // 实际ROI = 门店实收 / 实际消耗
            // $v['amount_cost_rate'] = BcHelper::div($v['amount'], $v['actual_consume']);
        }
    }

    /**
     * 获取时段选项
     *
     * @return array
     */
    public static function getHourOptions()
    {
        $options = [];
        for ($i = 0; $i < 24; $i++) {
            $options[] = [
                'value' => $i,
                'label' => sprintf('%02d:00-%02d:59', $i, $i)
            ];
        }
        return $options;
    }

    /**
     * 获取时段分析的起始时间
     *
     * @param string $type
     * @return false|string
     */
    public static function getHourTime($type = 'start')
    {
        $order_by = ($type == 'start') ? 'asc' : 'desc';
        $info = AdsAccountDataHour::find()->select('date')->asArray()->orderBy('date ' . $order_by)->limit(1)->one();
        return empty($info['date']) ? '' : DateHelper::toDate(strtotime($info['date']), 'Y-m-d');
    }
}
