<?php

namespace backendapi\services\order;

use auth\services\view\GoodsUnionService;
use backendapi\models\order\OrderHeader;
use common\cache\DepartmentCache;
use common\enums\GoodsTypeEnum;
use common\enums\order\OrderHeaderCheckTimeEnum;
use common\enums\order\OrderHeaderSourceTypeEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\enums\WhetherEnum;
use common\helpers\ArrayHelper;
use common\helpers\BcHelper;
use common\helpers\DateHelper;
use common\helpers\ResultHelper;
use common\models\backend\order\OrderProject;
use common\models\Config;
use common\models\Customer;
use common\models\customer_recharge\CustomerRechargeCard;
use common\models\GroupRecord;
use common\models\OrderTeacher;
use common\models\feishu\StoreSchedule;
use common\models\ProductCategory;
use common\models\view\GoodsUnion;
use common\services\CustomerServiceService;
use common\services\order\OrderHeaderService as CommonHeaderService;
use common\enums\GenderEnum;
use common\enums\CustomerAgeBracket;
use Exception;
use services\UserService;
use yii\db\Expression;
use Yii;

class OrderHeaderService extends CommonHeaderService
{
    /**
     * @var OrderHeader
     */
    public static $modelClass = OrderHeader::class;

    /**
     * 门店订单详情
     *
     * @param int $id
     * @return void
     */
    public static function getViewForEdit($id)
    {
        $order = static::$modelClass::find()
            ->andWhere(['id' => $id])
            ->one();

        if (!$order) {
            throw new Exception('订单不存在');
        }

        // 补充老师信息
        OrderProject::setExtendAttrs([
            'teachers_list',
            'error',
            'is_selected' => 'is_select',
            'goods.price_type' => 'price_type',
            'goods.price_one' => 'price_one',
            'goods.price_two' => 'price_two',
            'goods.price_three' => 'price_three',
        ]);
        $orderTeachers = OrderTeacher::find()
            ->select('order_project_id,type as teacher_type_id,user_id')
            ->andWhere(['order_id' => $order->id])
            ->asArray()
            ->all();

        $arrTeacher = [];
        foreach ($orderTeachers as $item) {
            $arrTeacher[$item['order_project_id']][] = [
                'teacher_type_id' => $item['teacher_type_id'],
                'user_id' => $item['user_id'],
            ];
        }

        foreach ($order->projects as $project) {
            if ($arrTeacher[$project->id]) {
                $project->teachers_list = $arrTeacher[$project->id];
            }
        }

        /** @var OrderProject $project */
        foreach ($order->projects as $project) {
            try {
                $project->fillGoodsAndAmountInfo($order);
                $project->checkUsedNum();
            } catch (Exception $e) {
                $project->error = $e->getMessage();
            }
        }

        $projects = ArrayHelper::index($order->projects, null, ['package_id', 'child_package_id']);
        $newProjects = [];
        foreach ($projects as $index => $projectList) {
            if (!$index) {
                $newProjects = array_merge($newProjects, $projectList[0] ?? []);
            } else {
                $packageDetails = [];
                foreach ($projectList as $index => $projectInnerList) {
                    if (!$index) {
                        foreach ($projectInnerList as &$projectInner) {
                            $projectInner['isSelected'] = !!$projectInner['package_is_selected'];
                        }
                        $packageDetails = array_merge($packageDetails, $projectInnerList);
                    } else {
                        $packageIsSelected = false;
                        foreach ($projectInnerList as &$projectInner) {
                            $projectInner['isSelected'] = !!$projectInner['child_package_is_selected'];
                            $packageIsSelected = $packageIsSelected || $projectInner['child_package_is_selected'];
                        }
                        $packageDetails[] = [
                            'goods_type' => GoodsTypeEnum::PACKAGE,
                            "num" => $projectInnerList[0]['num'],
                            "amount" => round(ArrayHelper::getSum($projectInnerList, 'amount'), 2),
                            "selectable_num" => $projectInnerList[0]['child_package_selectable_num'],
                            "package_selectable_num" => $projectInnerList[0]['package_selectable_num'],
                            "goods_id" => $projectInnerList[0]['child_package_id'],
                            "goods_name" => $projectInnerList[0]['child_package_name'],
                            "goods_price" => $projectInnerList[0]['child_package_price'],
                            "package_id" => $projectInnerList[0]['package_id'],
                            "package_name" => $projectInnerList[0]['package_name'],
                            "package_price" => $projectInnerList[0]['package_price'],
                            "price_type" => $projectInnerList[0]['goods']['price_type'],
                            "price_one" => $projectInnerList[0]['goods']['price_one'],
                            "price_two" => $projectInnerList[0]['goods']['price_two'],
                            "price_three" => $projectInnerList[0]['goods']['price_three'],
                            'is_select' => $packageIsSelected,
                            'goods_list' => $projectInnerList,
                        ];
                    }
                }
                $newProject = [
                    'goods_type' => GoodsTypeEnum::PACKAGE,
                    "num" => $packageDetails[0]['num'],
                    "amount" => round(ArrayHelper::getSum($packageDetails, 'amount'), 2),
                    "selectable_num" => $packageDetails[0]['package_selectable_num'],
                    "goods_id" => $packageDetails[0]['package_id'],
                    "goods_name" => $packageDetails[0]['package_name'],
                    "goods_price" => $packageDetails[0]['package_price'],
                    "price_type" => isset($packageDetails[0]['price_type']) ? $packageDetails[0]['price_type'] : $packageDetails[0]['goods']['price_type'],
                    "price_one" => isset($packageDetails[0]['price_one']) ? $packageDetails[0]['price_one'] : $packageDetails[0]['goods']['price_one'],
                    "price_two" => isset($packageDetails[0]['price_two']) ? $packageDetails[0]['price_two'] : $packageDetails[0]['goods']['price_two'],
                    "price_three" => isset($packageDetails[0]['price_three']) ? $packageDetails[0]['price_three'] : $packageDetails[0]['goods']['price_three'],
                    'goods_list' => $packageDetails,
                ];
                // if ($newProject['selectable_num']) {
                //     $selectList = [];
                //     foreach ($packageDetails as $detail) {
                //         if ($detail['is_selected']) {
                //             $selectList[] = $detail->id;
                //         }
                //     }
                //     if (count($selectList) < $newProject['selectable_num']) {
                //         for ($i = count($selectList); $i < $newProject['selectable_num']; $i++) {
                //             $selectList[$i] = '';
                //         }
                //     }
                //     $newProject['select_list'] = $selectList;
                // }
                $newProjects[] = $newProject;
            }
        }
        OrderHeader::setExtendAttrs([
            'groupRecord.platform_id' => 'group_platform_id',
            'groupRecord.groupPlatform.name' => 'group_platform_name',
            'groupRecord.code' => 'group_code',
            'recharge',
        ]);

        // 补充储值卡信息
        if ($order->cus_recharge_id) {
            $rechargeGoodsList = [];
            foreach ($order->projects as $project) {
                $rechargeGoodsList[] = [
                    'goods_type' => $project->goods_type,
                    'goods_id' => $project->goods_id,
                    'goods_name' => $project->package_id ? $project->package_name : $project->goods_name,
                    'discount' => $project->recharge_discount,
                    'discount_amount' => $project->amount * (10 - $project->recharge_discount) / 10,
                    'after_discount_amount' => $project->amount * $project->recharge_discount / 10,
                    'deduction_amount' => $project->recharge_amount,
                ];
            }
            $recharge = [
                'id' => $order->cus_recharge_id,
                'name' => $order->cusRecharge->rechargeCard->name,
                'deduction_amount' => $order->card_amount,
                'left_amount' => $order->cusRecharge->left_amount,
                'balance' => $order->cusRecharge->left_amount - $order->card_amount,
                'expire_time_text' => $order->cusRecharge->expireTimeText,
                'detail' => $rechargeGoodsList,
            ];
            $order->recharge = $recharge;
        }

        $order = ArrayHelper::toArray($order);
        $order['projects'] = $newProjects;
        return $order;
    }

    /**
     * 订单详情 - 用于修改预约
     *
     * @param array $params
     * @return mixed
     */
    public static function getViewForPlan($params)
    {
        if (empty($params['id'])) {
            throw new Exception('订单id不能为空');
        }

        // 验证订单存在
        /** @var OrderHeader */
        $order = static::$modelClass::find()
            ->andWhere(['id' => $params['id']])
            ->andFilterWhere(['source_type' => $params['source_type']])
            ->one();
        if (!$order) {
            throw new Exception('该订单不允许修改或不存在');
        }

        // 验证订单是否允许修改预约
        if (!$order->canUpdateForPlan()) {
            throw new Exception('该订单不允许修改预约');
        }

        // 订单显示字段
        OrderHeader::setShowAttrs([
            'id',
            'cus_id',
            'store_id',
            'channel_id',
            'is_check_time',
            'schedule_num',
            'plan_time',
            'plan_user_id',
            'plan_teacher_id',
            'plan_remark',
            'deposit',
            'source_type',
        ]);
        OrderHeader::setExtendAttrs([
            'customer.name' => 'cus_name',
            'customer.mobile' => 'cus_mobile',
            'promoteChannel.name' => 'channel_name',
            'store.store_name' => 'store_name',
        ]);

        // 订单项目显示字段
        OrderProject::setShowAttrs([
            'goods_id',
            'goods_type',
            'customer_product_id',
        ]);
        OrderProject::setExtendAttrs([
            'goods.code',
            'goods.deposit',
            'goods.name' => 'goods_name',
            'goods.price_one' => 'goods_price',
            'goods.original_price',
            'goods.price_one',
            'goods.price_two',
            'goods.price_three',
            'goods.price_type',
            [
                'field' => 'schedule_num',
                'value' => function ($orderProject) {
                    return ProductCategory::getScheduleNum($orderProject->goods->cate_id);
                }
            ]
        ]);

        // 组装第一层商品
        $projects = ArrayHelper::index($order->projects, null, ['package_id', 'child_package_id']);
        $newProjects = [];
        foreach ($projects as $index => $projectList) {
            if (!$index) {
                $newProjects = array_merge($newProjects, $projectList[0]);
            } else {
                $newProjects[] = current(current($projectList));
            }
        }
        $order = ArrayHelper::toArray($order);
        $order['real_mobile'] = $order['cus_mobile'];
        $order['cus_mobile'] = ResultHelper::mobileEncryption($order['cus_mobile']);
        $order['goods_list'] = $newProjects;

        return $order;
    }

    /**
     * 修改预约
     *
     * @param array $params
     * @return mixed
     */
    public static function updateForPlan($params)
    {
        if (empty($params['id'])) {
            throw new Exception('订单id不能为空');
        }

        // 验证订单存在
        /** @var $order OrderHeader */
        $order = static::$modelClass::find()
            ->andWhere(['id' => $params['id']])
            ->andFilterWhere(['source_type' => $params['source_type']])
            ->one();

        if (!$order) throw new Exception('订单不存在');

        // 验证订单是否允许修改预约
        if (!$order->canUpdateForPlan()) throw new Exception('该订单不允许修改预约');

        if (!Yii::$app->services->auth->isSuperAdmin()) {
            //            $scopeStoreList = Yii::$app->services->scopeDataService->getMenuStore();
            //            if ($order->created_by != Yii::$app->user->id && !in_array($order->store_id, $scopeStoreList)) {
            //                throw new Exception('你暂无该订单的操作权限');
            //            }

            if ($order->source_type != $params['source_type']) {
                throw new Exception('你暂无该订单的操作权限');
            }
        }

        // 验证是否修改门店
        if (!empty($params['store_id']) && $params['store_id'] != $order->store_id && $params['source_type'] == OrderHeader::SOURCE_TYPE_STORE) {
            throw new Exception('门店端不能修改预约门店');
        }

        if (empty($params['goods_list'])) {
            throw new Exception('商品异常，请刷新后重试');
        }

        // 构造订单子表信息
        $projects = [];
        $scheduleNum = 0;
        $params['cus_id'] = $order->cus_id;
        foreach ($params['goods_list'] as $goodsParams) {
            $goodsUnion = GoodsUnion::find()
                ->andWhere(['id' => $goodsParams['id']])
                ->andWhere(['goods_type' => $goodsParams['goods_type']])
                ->one();
            if (empty($goodsUnion)) {
                throw new Exception('商品不存在');
            }
            //合计定金
            $params['deposit'] += $goodsParams['deposit'];
            $params['other_deposit'] += $goodsParams['other_deposit'];
            //限制商品标签限购
            CustomerServiceService::checkTagLimit($goodsUnion, $params['cus_id']);
            //限制商品限购
            GoodsUnionService::checkoutGoodsLimit($goodsUnion, $params['cus_id'], $goodsUnion->goods_type);
            $productSchedule = ProductCategory::getScheduleNum($goodsUnion->cate_id);    //获取档期
            $scheduleNum = $scheduleNum > $productSchedule ? $scheduleNum : $productSchedule;  //比较档期，以最长时间段档期为主

            // 第一层是单品
            if ($goodsUnion->goods_type != GoodsTypeEnum::PACKAGE) {
                $project = $goodsUnion;
                $projects[] = [
                    'num' => 1,
                    'goods_type' => $project->goods_type,
                    'goods_id' => $project->id,
                    'goods_name' => $project->name,
                    'goods_price' => $project->price_one,
                    'goods_original_price' => $project->original_price,
                    'customer_product_id' => $goodsParams['customer_product_id'] ?? 0,
                ];
            } else {
                // 第一层是套餐
                $package = $goodsUnion;
                foreach ($package->details as $packageDetail) {
                    // 第二层是单品
                    if ($packageDetail->goods->goods_type != GoodsTypeEnum::PACKAGE) {
                        $project = $packageDetail->goods;
                        $projects[] = [
                            'num' => 1,
                            'goods_type' => $project->goods_type,
                            'goods_id' => $project->id,
                            'goods_name' => $project->name,
                            'goods_price' => $project->price_one,
                            'goods_original_price' => $project->original_price,
                            'package_id' => $package->id,
                            'package_name' => $package->name,
                            'package_price' => $package->price_one,
                            'package_original_price' => $package->original_price,
                            'package_selectable_num' => $packageDetail->selectable_num,
                        ];
                    } else {
                        // 第二层是套餐
                        $childPackage = $packageDetail->goods;
                        foreach ($childPackage->details as $childPackageDetail) {
                            // 第三层都是单品
                            $project = $childPackageDetail->goods;
                            $projects[] = [
                                'num' => 1,
                                'goods_type' => $project->goods_type,
                                'goods_id' => $project->id,
                                'goods_name' => $project->name,
                                'goods_price' => $project->price_one,
                                'goods_original_price' => $project->original_price,
                                'package_id' => $package->id,
                                'package_name' => $package->name,
                                'package_price' => $package->price_one,
                                'package_original_price' => $package->original_price,
                                'package_selectable_num' => $packageDetail->selectable_num,
                                'child_package_id' => $childPackage->id,
                                'child_package_name' => $childPackage->name,
                                'child_package_price' => $childPackage->price_one,
                                'child_package_original_price' => $childPackage->original_price,
                                'child_package_selectable_num' => $childPackageDetail,
                            ];
                        }
                    }
                }
            }
        }

        $params['schedule_num'] = $scheduleNum;
        if ($params['deposit'] && $params['other_deposit']) {
            throw new Exception('定金数据异常');
        }

        $order->scenario = 'update_for_plan';
        $order->attributes = $params;
        $order->plan_by = UserService::getInst()->id;
        if ($order->is_check_time == OrderHeaderCheckTimeEnum::CHECK_TIME_YES) {
            $order->order_status = OrderHeaderStatusEnum::STATUS_PLAN;
        } else {
            $order->order_status = OrderHeaderStatusEnum::STATUS_PENDING_APPOINTMENT;
        }

        if (!$order->save()) {
            throw new Exception('预约订单修改失败');
        }

        //        OrderProject::deleteAll(['order_id' => $order->id]);
        //        OrderTeacher::deleteAll(['order_id' => $order->id]);
        //        foreach ($projects as $project) {
        //            $orderProject = new OrderProject();
        //            $orderProject->attributes = $project;
        //            $orderProject->order_id = $order->id;
        //            if (!$orderProject->save()) {
        //                throw new Exception('订单项目保存失败');
        //            }
        //        }
    }

    /**
     * 门店订单详情-用于打印
     *
     * @param int $id
     * @return void
     */
    public static function getViewForPrint($id)
    {
        $orderInfo = [];

        $order = static::$modelClass::find()
            ->andWhere(['id' => $id])
            ->one();

        if (!$order) {
            throw new Exception('订单不存在');
        }
        $orderInfo['order_no'] = $order->order_no;
        $orderInfo['store_name'] = $order->store->store_name;
        $orderInfo['store_logo_url'] = $order->store->print_logo;
        $orderInfo['wechat_url'] = $order->store->wechat_url;
        $orderInfo['pay_amount'] = $order->pay_amount;
        $orderInfo['received_amount'] = $order->received_amount;
        $orderInfo['plan_time_text'] = $order->planTimeText;
        $orderInfo['print_time_text'] = date('Y-m-d H:i:s');
        $orderInfo['cus_id'] = $order->cus_id;
        $orderInfo['cus_name'] = $order->customer->name;
        $orderInfo['cus_mobile'] = ResultHelper::mobileEncryption($order->customer->mobile);
        $orderInfo['sale_tel'] = Config::getByName('saleTel');
        $orderInfo['deposit'] = $order->deposit;
        $orderInfo['group_amount'] = $order->group_amount;

        $projects = ArrayHelper::index($order->projects, null, ['package_id', 'child_package_id']);
        $newProjects = [];
        foreach ($projects as $index => $projectList) {
            if (!$index) {
                foreach ($projectList[0] as $project) {
                    $newProjects[] = [
                        "num" => $project['num'],
                        "amount" => $project['amount'],
                        "goods_name" => $project['goods_name'],
                        "goods_price" => $project['goods_price'],
                    ];
                }
            } else {
                $packageDetails = [];
                foreach ($projectList as $index => $projectInnerList) {
                    if (!$index) {
                        foreach ($projectInnerList as $innerProject) {
                            $packageDetails[] = [
                                "num" => $innerProject['num'],
                                "amount" => $innerProject['amount'],
                                "goods_name" => $innerProject['goods_name'],
                                "goods_price" => $innerProject['goods_price'],
                                "package_name" => $innerProject['package_name'],
                                "package_price" => $innerProject['package_price'],
                            ];
                        }
                    } else {
                        $packageDetails[] = [
                            "num" => $projectInnerList[0]['num'],
                            "amount" => $projectInnerList[0]['amount'],
                            "goods_name" => $projectInnerList[0]['child_package_name'],
                            "goods_price" => $projectInnerList[0]['child_package_price'],
                            'goods_list' => [],
                        ];
                        foreach ($projectInnerList as $innerProject) {
                            $packageDetails['goods_list'][] = [
                                "num" => $innerProject['num'],
                                "amount" => $innerProject['amount'],
                                "goods_name" => $innerProject['goods_name'],
                                "goods_price" => $innerProject['goods_price'],
                            ];
                        }
                    }
                }
                $newProject = [
                    "num" => $packageDetails[0]['num'],
                    "amount" => $packageDetails[0]['amount'],
                    "goods_name" => $packageDetails[0]['package_name'],
                    "goods_price" => $packageDetails[0]['package_price'],
                    'goods_list' => $packageDetails,
                ];
                if ($newProject['selectable_num']) {
                    $selectList = [];
                    foreach ($packageDetails as $detail) {
                        if ($detail['is_selected']) {
                            $selectList[] = $detail->id;
                        }
                    }
                    if (count($selectList) < $newProject['selectable_num']) {
                        for ($i = count($selectList); $i < $newProject['selectable_num']; $i++) {
                            $selectList[$i] = '';
                        }
                    }
                    $newProject['select_list'] = $selectList;
                }
                $newProjects[] = $newProject;
            }
        }
        $orderInfo['projects'] = $newProjects;
        return $orderInfo;
    }

    /**
     * 门店结算
     *
     * @param array $params
     * @return void
     */
    public static function storeSettlement($params)
    {
        if (!isset($params['cus_age_bracket']) || !isset($params['cus_gender'])) {
            throw new Exception('客户年龄段和性别不能为空，请刷新页面重试');
        }

        $tran = Yii::$app->db->beginTransaction();
        try {
            /** @var OrderHeader */
            $order = static::$modelClass::find()
                ->andWhere(['id' => $params['id']])
                ->one();

            // 判断订单有效性
            if (!$order) {
                throw new Exception('订单不存在');
            }
            if (!$order->canSettlement()) {
                throw new Exception('订单不可结算');
            }

            $scopeStoreList = Yii::$app->services->scopeDataService->getMenuStore();
            if ($scopeStoreList && !in_array($order->store_id, $scopeStoreList)) {
                throw new Exception('你暂无该订单的操作权限');
            }

            // 撤销团购券
            if (empty($params['group_code']) && $order->group_amount > 0) {
                $groupRecord = $order->groupRecord;
                $groupRecord->unUsed();
            }
            $order->attributes = $params;

            // 使用优惠券
            $order->useGroupCode($params);

            // 补充购买商品信息
            foreach ($params['projects'] as &$project) {
                if ($project['customer_product_id']) {
                    continue;
                }
                $goods = GoodsUnion::findOne(['id' => $project['goods_id'], 'goods_type' => $project['goods_type']]);

                if (!$goods) {
                    throw new Exception("商品[ {$project['goods_name']} ]不存在");
                }
                $project['goods'] = $goods;
            }
            unset($project);

            // 验证第一层商品有效性
            foreach ($params['projects'] as $project) {
                if ($project['customer_product_id']) {
                    continue;
                }
                $goods = $project['goods'];

                // 验证商品可售门店
                $goods->checkStore($order->store_id);
                // 验证商品可选角色
                $goods->checkRole();
                // 验证商品价格
                $goods->checkSalePrice($project['goods_price']);
            }

            // 储值卡验证并计算
            $order->card_amount = 0;
            $order->card_real_amount = 0;
            if (!empty($params['cus_recharge_id'])) {
                $cusRecharge = CustomerRechargeCard::find()
                    ->andWhere(['id' => $params['cus_recharge_id']])
                    ->andWhere(['cus_id' => $params['cus_id']])
                    ->andWhere(['pay_status' => 1])
                    ->one();

                if (!$cusRecharge) {
                    throw new Exception('储值卡不存在');
                }

                if ($cusRecharge->left_amount == 0) {
                    throw new Exception('储值卡余额不足');
                }

                if ($cusRecharge->expire_time > 0 && $cusRecharge->expire_time < time()) {
                    throw new Exception('储值卡已过期');
                }

                foreach ($params['projects'] as $project) {
                    if ($project['customer_product_id']) {
                        continue;
                    }
                    $goods = $project['goods'];

                    // 验证储值卡
                    $goods->checkRecharge($project['recharge_amount'], $project['recharge_discount'], $cusRecharge);

                    // 计算储值卡金额
                    $order->card_amount += $project['recharge_amount'] ?: 0;
                }

                $order->card_real_amount = \common\helpers\BcHelper::div($order->card_amount * $cusRecharge->rechargeCard->recharge_price, $cusRecharge->rechargeCard->recharge_price + $cusRecharge->rechargeCard->give_price);
            }

            // 计算订单金额
            $order->original_amount = 0;
            $order->amount = 0;
            foreach ($params['projects'] as $project) {
                if ($project['customer_product_id']) {
                    continue;
                }
                $goods = $project['goods'];

                // 计算订单金额
                $order->original_amount += $goods->original_price * $project['num'];
                $order->amount += $project['goods_price'] * $project['num'];
            }
            if (isset($params['payamount_modify']) && $params['payamount_modify'] == 1) {
                $order->pay_amount = $params['pay_amount'];
            } else {
                $order->pay_amount = $order->amount - $order->card_amount - $order->other_deposit - $order->group_amount - $order->coupon_amount - $order->integral_amount - $order->shopping_gold_amount;
            }



            // 订单子表树状转行
            $newProjects = [];
            foreach ($params['projects'] as &$project) {
                if ($project['goods_type'] != 3) {
                    $project['customer_product_id'] = $project['customer_product_id'] ?: 0; //补全字段
                    $project['package_id'] = $project['package_id'] ?: 0;                   //补全字段
                    $newProjects[] = $project;
                } else {
                    foreach ($project['goods_list'] as $packageGoods) {
                        if (!isset($packageGoods['is_select'])) {       //补全字段
                            $packageGoods['is_select'] = 0;
                        }
                        $packageGoods['package_is_selected'] = $packageGoods['is_select'] ? 1 : 0;
                        if ($packageGoods['goods_type'] != 3) {
                            $packageGoods['num'] = $project['num'];
                            $packageGoods['package_id'] = $project['goods_id'];
                            $packageGoods['package_price'] = $project['goods_price'];
                            $packageGoods['customer_product_id'] = 0;   //补全字段
                            $newProjects[] = $packageGoods;
                        } else {
                            foreach ($packageGoods['goods_list'] as $goods) {
                                $goods['child_package_is_selected'] = $goods['is_select'] ? 1 : 0;
                                $goods['num'] = $project['num'];
                                $goods['package_id'] = $project['goods_id'];
                                $goods['package_price'] = $project['goods_price'];
                                $goods['child_package_id'] = $packageGoods['goods_id'];
                                $goods['customer_product_id'] = 0;      //补全字段
                                $newProjects[] = $goods;
                            }
                        }
                    }
                }
            }

            // 订单信息保存
            $order->settlement_time = time();
            $order->order_status = OrderHeaderStatusEnum::STATUS_SETTLEMENT;
            if (!$order->save()) {
                throw new Exception('订单保存失败：' . $order->getFirstErrMsg());
            }
            OrderProject::deleteAll(['order_id' => $order->id]);
            OrderTeacher::deleteAll(['order_id' => $order->id]);

            //重新组装数据
            if ($newProjects) {
                $newProjects = ArrayHelper::index($newProjects, null, 'package_id');
            }

            $projectNum = count($newProjects);
            foreach ($newProjects as $key => $newProjectList) {
                $projectNum--;

                if ($key > 0) {
                    //将项目按选中项目在前重新排序
                    $newProjectList = ArrayHelper::orderBy($newProjectList, 'is_select desc,is_selected desc');
                } else {
                    //将项目按照抵扣项目在前重新排序，用于后续计算分摊金额最后一笔使用
                    $newProjectList = ArrayHelper::orderBy($newProjectList, 'customer_product_id desc');
                }

                foreach ($newProjectList as $newProject) {
                    //判断当前数据是否为最后一条
                    if (($projectNum == 0 && $newProject == end($newProjectList)) || ($newProject['package_id'] > 0 && $newProject == end($newProjectList))) {
                        $endNum = true;
                    } else {
                        $endNum = false;
                    }

                    $orderProject = new OrderProject();
                    $orderProject->attributes = $newProject;
                    $orderProject->order_id = $order->id;
                    $orderProject->fillGoodsAndAmountInfo($order, $projectNum == 0, $endNum);
                    $orderProject->checkUsedNum();
                    if (!$orderProject->save()) {
                        throw new Exception('订单项目保存失败');
                    }
                    $orderProject->saveTeachers($newProject);
                }
            }

            // 更新客户年龄段和性别
            $customer = Customer::findOne($order->cus_id);
            $customer->scenario = 'updateAgeBracketAndGender';
            $customer->age_bracket = $params['cus_age_bracket'] ?? CustomerAgeBracket::OTHER;
            $customer->gender = $params['cus_gender'] ?? GenderEnum::WOMAN;
            if (!$customer->save()) {
                throw new Exception('客户年龄段和性别更新失败：' . $customer->getFirstErrMsg());
            }

            $tran->commit();
        } catch (Exception $e) {
            $tran->rollBack();
            throw $e;
        }
    }

    /**
     * 门店下单
     *
     * @param array $params
     */
    public static function storeCreate($params)
    {
        if (!isset($params['cus_age_bracket']) || !isset($params['cus_gender'])) {
            throw new Exception('客户年龄段和性别不能为空，请刷新页面重试');
        }

        // 创建订单
        $newParams = [];
        $newParams['cus_id'] = $params['cus_id'];
        $newParams['store_id'] = $params['store_id'];
        $newParams['channel_id'] = $params['channel_id'];
        $newParams['plan_time'] = time();
        $newParams['is_pc'] = true;
        $newParams['source_type'] = OrderHeaderSourceTypeEnum::SOURCE_TYPE_STORE;
        $newParams['goods_list'] = [];
        $newParams['group_amount'] = 0;
        foreach ($params['projects'] as $project) {
            $newParams['goods_list'][] = [
                'id' => $project['goods_id'],
                'goods_type' => $project['goods_type'],
                'name' => $project['goods_name'],
                'customer_product_id' => $project['customer_product_id'] ?: 0,
            ];
        }
        $customer = Customer::findOne($params['cus_id']);
        $newParams['name'] = $customer->name;
        $newParams['mobile'] = $customer->mobile;
        $newParams['mobile_code'] = $customer->mobile_code;
        $newParams['channel_id'] = $customer->channel_id;
        $newParams['order_status'] = OrderHeaderStatusEnum::STATUS_ARRIVED_STORE;
        $newOrderInfo = self::newCreate($newParams);

        // 订单改为到店状态
        $model = static::$modelClass::findOne($newOrderInfo['id']);
        if (!$model) {
            throw new Exception('记录不存在');
        }
        $model->order_status = OrderHeaderStatusEnum::STATUS_ARRIVED_STORE;
        $model->reach_time = time();
        $model->save();

        // 订单结算
        $params['id'] = $newOrderInfo['id'];
        $params['group_amount'] = $params['group_amount'] ?: 0;
        self::storeSettlement($params);

        // 更新客户年龄段和性别
        $customer->scenario = 'updateAgeBracketAndGender';
        $customer->age_bracket = $params['cus_age_bracket'] ?? CustomerAgeBracket::OTHER;
        $customer->gender = $params['cus_gender'] ?? GenderEnum::WOMAN;
        if (!$customer->save()) {
            throw new Exception('客户年龄段和性别更新失败：' . $customer->getFirstErrMsg());
        }

        return $newOrderInfo['id'];
    }

    /**
     * 门店订单完成
     */
    public static function storeFinish($params)
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            /** @var OrderHeader */
            $order = static::$modelClass::find()
                ->andWhere(['id' => $params['id']])
                ->one();

            // 判断订单有效性
            if (!$order) {
                throw new Exception('订单不存在');
            }

            $scopeStoreList = Yii::$app->services->scopeDataService->getMenuStore();
            if ($scopeStoreList && !in_array($order->store_id, $scopeStoreList)) {
                throw new Exception('你暂无该订单的操作权限');
            }

            $order->beFinish();

            $tran->commit();
        } catch (Exception $e) {
            $tran->rollBack();
            throw $e;
        }
    }

    /**
     * 创建
     *
     * @param array $params
     * @return array|bool
     * @throws Exception
     */
    public static function create($params = [])
    {
        $params['is_pc'] = true;
        return parent::create($params);
    }

    /**
     * 订金流水列表
     *
     * @param array $params
     * @return \yii\db\ActiveQuery
     */
    public static function getWriteListQuery($params = [])
    {
        $query = parent::getWriteListQuery($params);
        $query->andWhere(['wxcom_cus_id' => 0, 'user_id' => Yii::$app->user->id]);

        return $query;
    }

    /**
     * 到店表数据
     *
     * @param array $params
     * @param bool $is_export
     * @return array
     * @throws \yii\base\InvalidConfigException
     */
    public static function reachStoreData($params = [], $is_export = false)
    {
        static::$modelClass::setExtendAttrs([
            'customer.name' => 'cus_name',
            'planBy.username' => 'plan_name',
            'customer.mobile' => 'cus_mobile',
            'store.store_name' => 'store_name',
            'store.store_intro' => 'store_intro',
            'store.directional_map' => 'directional_map',
            'promoteChannel.name' => 'channel_name',
            'customer.first_store_time' => 'first_store_time',
            'customer.first_store_time_text' => 'first_store_time_text',
            'created_by_text',
            'plan_time_text',
            'project',
            'plan_operate_time_text' => 'order_reservation_operate_time',
            'cus_today_is_pay'
        ]);

        $query = static::$modelClass::find()
            ->select([
                'h.id',
                'h.order_no',
                'h.cus_id',
                'h.plan_by',
                'h.store_id',
                'h.plan_time',
                'h.created_by',
                'h.channel_id',
                'h.plan_remark',
                'h.order_status',
                'h.plan_operate_time',
                'h.deposit',
                '(
                    IFNULL(h.received_amount, 0) + IFNULL(h.card_real_amount, 0) + IFNULL(h.group_amount, 0)
                ) AS pay_amount'
            ])
            ->alias('h')
            ->with(['createdPerson', 'planBy', 'promoteChannel'])
            ->joinWith(['store store', 'customer customer'])
            ->where(['h.entity_id' => UserService::getInst()->currentEntityId])
            ->andWhere(['h.order_status' => static::$modelClass::reachStoreStatusList()]);

        $query->andFilterWhere(['h.store_id' => $params['store_id']])
            ->andFilterWhere(['order_status' => $params['order_status']]);

        //默认只能查看当天的数据
        if (empty($params['plan_time'])) {
            $params['plan_time'] = time();
        }

        if ($params['start_time'] && $params['end_time']) {
            $planStartTime = strtotime(date('Y-m-d', $params['start_time']));
            $planEndTime = strtotime(date('Y-m-d 23:59:59', $params['end_time']));
        } else {    // 默认只能查看当天的数据
            $planStartTime = strtotime(date('Y-m-d'));
            $planEndTime = $planStartTime + 86400 - 1;
        }
        $query->andFilterWhere(['BETWEEN', 'h.plan_time', $planStartTime, $planEndTime]);

        $scopeUserIds = static::scopeUser();

        $query->groupBy('h.store_id,h.id')
            ->orderBy('h.plan_time ASC');
        $totalCount = $query->count();
        $list = $query->all();
        $list = ArrayHelper::toArray($list);

        $storeSchedule = StoreSchedule::find()->where(['date' => date('Ymd', $planStartTime)])->indexBy('store_id')->asArray()->all();

        $newData = [];
        if ($is_export) {
            foreach ($list as &$value) {
                if ($scopeUserIds && !in_array($value['plan_by'], $scopeUserIds)) {
                    $value['pay_amount'] = '-';
                }
                if (!$value['first_store_time'] || ($value['first_store_time'] >= $planStartTime && $value['first_store_time'] <= $planEndTime)) {
                    $value['is_new_customer'] = WhetherEnum::ENABLED;
                } else {
                    $value['is_new_customer'] = WhetherEnum::DISABLED;
                }

                $project = $value['project'];
                //                $value['cus_mobile'] = ResultHelper::mobileEncryption($value['cus_mobile']);
                $goods_name_arr = static::getOrderGoodsName($project);
                $value['goods_name'] = implode(',', $goods_name_arr);
                unset($value['project']);
            }

            $newData = $list;
        } else {
            $newCustomerNum = $oldCustomerNum = [];
            foreach ($list as &$value) {
                if ($scopeUserIds && !in_array($value['plan_by'], $scopeUserIds)) {
                    $value['pay_amount'] = '-';
                }
                if (!$value['first_store_time'] || ($value['first_store_time'] >= $planStartTime && $value['first_store_time'] <= $planEndTime)) {
                    $value['is_new_customer'] = WhetherEnum::ENABLED;
                    //统计新客人数
                    $newCustomerNum[$value['store_id']][] = $value['cus_id'];
                } else {
                    $value['is_new_customer'] = WhetherEnum::DISABLED;
                    //统计老客人数
                    $oldCustomerNum[$value['store_id']][] = $value['cus_id'];
                }

                $project = $value['project'];
                //                $value['cus_mobile'] = ResultHelper::mobileEncryption($value['cus_mobile']);
                $goods_name_arr = static::getOrderGoodsName($project);
                $value['goods_name'] = implode(',', $goods_name_arr);
                unset($value['project']);

                if (!$newData[$value['store_id']]) {
                    $newData[$value['store_id']] = [
                        'store_id' => $value['store_id'],
                        'store_name' => $value['store_name'],
                        'store_intro' => $value['store_intro'],
                        'directional_map' => $value['directional_map'],
                        'sum_customer_num' => 0,    //总客户数
                        'new_customer_num' => 0,    //新客
                        'old_customer_num' => 0,    //老客
                        'complete_order_num' => 0,   //已完成
                        'teacher_num' => $storeSchedule[$value['store_id']]['teacher_num'] ?? 0,
                        'customer_service_num' => $storeSchedule[$value['store_id']]['customer_service_num'] ?? 0,
                        'reschedule_num' => $storeSchedule[$value['store_id']]['reschedule_num'] ?? 0,
                        'full_load_rate' => 0
                    ];
                }

                //统计已完成订单数
                if ($value['order_status'] == OrderHeaderStatusEnum::STATUS_COMPLETED) {
                    $newData[$value['store_id']]['complete_order_num'] += 1;
                }
                $newData[$value['store_id']]['order_list'][] = $value;
            }

            foreach ($newCustomerNum as $newStoreId => $newCustomers) {
                $newData[$newStoreId]['new_customer_num'] = count(array_unique($newCustomers));
                // 满载率（新客到店人数/可接待客户数）
                $newData[$newStoreId]['full_load_rate'] = BcHelper::percentage($newData[$newStoreId]['new_customer_num'], $newData[$newStoreId]['customer_service_num']);
                $newData[$newStoreId]['sum_customer_num'] += $newData[$newStoreId]['new_customer_num'];
            }

            foreach ($oldCustomerNum as $oldStoreId => $oldCustomers) {
                $newData[$oldStoreId]['old_customer_num'] = count(array_unique($oldCustomers));
                $newData[$oldStoreId]['sum_customer_num'] += $newData[$oldStoreId]['old_customer_num'];
            }
            sort($newData);
        }

        return [$newData, $totalCount];
    }

    public static function scopeUser()
    {
        $scope = Yii::$app->services->scopeDataService->getScope();
        if (empty($scope)) {
            return [];
        }
        
        $userIds = [Yii::$app->user->identity->id];
        if (in_array(-1, $scope)) {
            return $userIds;
        }

        foreach ($scope as $deptId) {
            $arrUserIds = DepartmentCache::init()->get(DepartmentCache::dept_person . $deptId);
            $userIds = array_merge($userIds, $arrUserIds);
        }
        return $userIds;
    }

    public function cusServicePerformanceOrder($params)
    {
        $todayTime = DateHelper::today();
        $params['search_start_time'] = ArrayHelper::getValue($params, 'search_start_time', $todayTime['start']);
        $params['search_end_time'] = ArrayHelper::getValue($params, 'search_end_time', $todayTime['end']);
        $params['end_time_2'] = $params['search_end_time'] + 2 * 86400;

        $query = OrderHeader::find()->alias('eoh')
        ->select(['eoh.order_no,ec.mobile,ec.name,s.store_name,eoh.plan_time,(IFNULL(eoh.received_amount, 0) + IFNULL(eoh.card_real_amount, 0) + IFNULL(eoh.group_amount, 0)) AS performance'])
        ->leftJoin('{{%customer}} ec', 'eoh.cus_id = ec.id')
        ->leftJoin('{{%store}} s', 'eoh.store_id = s.id')
        ->innerJoin("
                (SELECT 
                    h.plan_by, h.cus_id 
                FROM 
                    erp_order_header h 
                LEFT JOIN erp_customer ec_sub ON h.cus_id = ec_sub.id
                WHERE 
                    h.order_status = 5 
                    AND h.source_type = 1 
                    AND FROM_UNIXTIME(h.plan_time,'%Y-%m-%d') =  FROM_UNIXTIME(ec_sub.first_store_time,'%Y-%m-%d')
                    AND h.plan_time BETWEEN {$params['search_start_time']} AND {$params['search_end_time']}
                    GROUP BY h.cus_id ) AS new_data
            ", 'new_data.cus_id = eoh.cus_id')
        ->leftJoin('{{%backend_member}} ebm', 'ebm.id = new_data.plan_by')
        ->where(['eoh.order_status' => 5])
            ->andFilterWhere(['between', 'eoh.plan_time', $params['search_start_time'], $params['end_time_2']])
            ->andWhere(new Expression("eoh.plan_time BETWEEN 
                UNIX_TIMESTAMP(DATE(FROM_UNIXTIME(ec.first_store_time))) AND 
                (UNIX_TIMESTAMP(DATE_ADD(DATE(FROM_UNIXTIME(ec.first_store_time)), INTERVAL 2 DAY)) + 86399)"))
            ->andWhere(['ebm.id' => $params['user_id']]);

        $totalCount = $query->count();
        if($totalCount == 0){
            return [[], 0];
        }

        // 分页
        $page = (isset($params['page']) && !empty($params['page'])) ? $params['page'] : 1;
        $limit = (isset($params['limit']) && !empty($params['limit'])) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;

        $list = $query->offset($offset)->limit($limit)->orderBy('eoh.plan_time desc')->asArray()->all();
        foreach ($list as &$value) {
            $value['mobile'] = ResultHelper::mobileEncryption($value['mobile']);
            $value['plan_time_text'] = DateHelper::toDate($value['plan_time']);
        }
       
        return [$list, $totalCount];
    }
}
