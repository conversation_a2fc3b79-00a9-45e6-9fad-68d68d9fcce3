<?php

namespace backendapi\forms\wxcom;

use backendapi\services\MemberService;
use backendapi\services\wxcom\ComService;
use backendapi\services\wxcom\CustomerServiceLogService;
use backendapi\services\wxcom\QrcodeService;
use backendapi\services\wxcom\ReportService;
use common\cache\WxcomCache;
use common\components\PromoteReport;
use common\enums\CusCustomerUserTypeEnum;
use common\enums\WxcomAddTypeEnum;
use common\helpers\Tool;
use common\models\backendapi\PromoteChannel;
use common\models\log\WxcomCallback;
use common\models\wxcom\CusCustomer;
use common\models\wxcom\CusCustomerUser;
use common\models\wxcom\CusCustomerUserDel;
use common\models\wxcom\CusDynamicQrcode;
use common\models\wxcom\CusExtendDetail;
use common\models\wxcom\CusQrcode;
use common\models\wxcom\CusTag;
use common\models\wxcom\DynamicMapping;
use common\models\wxcom\User;
use common\queues\AddFansCallbackJob;
use common\queues\UpdateCustomerInfoJob;
use common\queues\WxcomReportJob;
use EasyWeChat\Factory;
use Exception;
use Yii;
use yii\helpers\ArrayHelper;

class CusCustomerForm extends CusCustomer
{
    // 扩展表单新增修改逻辑
    use \common\traits\ModelFormCUD;

    /**
     * 企微事件-数据处理
     * 注意：企微二维码多次加粉回调中的state返回的值为首次加粉的值
     *
     * @param $message
     * @return bool
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    public static function onEventCallback($message)
    {
        switch ($message['ChangeType']) {
            case 'add_external_contact': //添加企业客户事件
                $state = '';
                $cusQrCodeInfo = [];
                if (strpos($message['State'], WxcomCache::USER_QRCODE) !== false) {
                    $state = Tool::getStringToArray($message['State'], ':')[1];   //动态码ID
                    $wxcomCache = (new WxcomCache());
                    $cusQrCodeInfo = $wxcomCache->get(WxcomCache::USER_QRCODE . $state);

                    if (empty($cusQrCodeInfo)) {    //redis映射表不存在，则扫描本地库映射数据
                        $cusQrCodeInfo = DynamicMapping::find()->select('dynamic_qrcode_id AS qrcode_id')
                            ->andWhere([
                                'wxcom_user_id' => $message['UserID'],
                                'external_user_id' => $message['ExternalUserID'],
                                'qrcode_id' => $state
                            ])->asArray()->one();
                    }
                    $cusQrCodeInfo['state'] = $state;
                    $message['qrcode_id'] = $cusQrCodeInfo['qrcode_id'];

                    //设置2秒后自动删除，防止并发回调导致获取redis数据失败
                    Yii::$app->redis->EXPIRE(WxcomCache::USER_QRCODE . $state, 2);
                }

                AddFansCallbackJob::addJob($message);
                Yii::info('----加粉后成功----');
                $wxcomCustomer = self::getWxcomCustomerFromWxcomByMessage($message);
                $add_type = -1;
                foreach ($wxcomCustomer['follow_user'] as $followUser) {
                    if ($followUser['userid'] == $message['UserID']) {
                        $add_type = $followUser['add_way'];
                        break;
                    }
                }

                Yii::info('----进入上报事件----');
                Yii::info(json_encode(['message' => $message, 'wxcomCustomer' => $wxcomCustomer],256), 'add_external_contact');
                //上报事件
                $reportService = new ReportService;
                $reportService->message = $message;
                $reportService->customerInfo = $wxcomCustomer;
                $reportService->add_type = $add_type;
                $reportService->cusQrCodeInfo = $cusQrCodeInfo;
                $reportService->onEventCallback();

                //记录动态码回调映射信息
                if (strpos($message['State'], WxcomCache::USER_QRCODE) !== false) {
                    $dynamicMapping = DynamicMapping::find()
                        ->andWhere([
                            'wxcom_user_id' => $message['UserID'],
                            'external_user_id' => $message['ExternalUserID'],
                            'com_code' => $message['ComCode']
                        ])->one();

                    if (empty($dynamicMapping)) {
                        $dynamicMapping = new DynamicMapping();
                        $dynamicMapping->wxcom_user_id = $message['UserID'];
                        $dynamicMapping->external_user_id = $message['ExternalUserID'];
                        $dynamicMapping->com_code = $message['ComCode'];
                        $dynamicMapping->add_time = $message['CreateTime'];
                        $dynamicMapping->dynamic_qrcode_id = $cusQrCodeInfo['qrcode_id'];
                        $dynamicMapping->qrcode_id = $state;
                        $dynamicMapping->content = json_encode($cusQrCodeInfo);
                        $dynamicMapping->save();
                    }
                }

                break;
            case 'del_follow_user': //外部联系人删除内部联系人事件
                /** @var CusCustomerUser */
                $customerUser = CusCustomerUser::find()
                    ->andWhere(['cus_id' => self::getCusIdByExternalUserId($message['ExternalUserID'])])
                    ->andWhere(['wxcom_user_id' => $message['UserID']])
                    ->one();
                if ($customerUser) {
                    if ($customerUser->is_deleted == 0) {
                        $customerUser->is_deleted = 1;
                        $customerUser->deleted_type = 0;
                        $customerUser->deleted_time = $message['CreateTime'];
                        $customerUser->save();
                        $customerUser->delReport();
                        $customerUser->addDelFanTag();
                    }
                    $customerUser->deleted_type = 0;
                    static::delRecord($customerUser);
                }
                break;
            case 'del_external_contact': //内部联系人删除外部联系人事件
                /** @var CusCustomerUser */
                $customerUser = CusCustomerUser::find()
                    ->andWhere(['cus_id' => self::getCusIdByExternalUserId($message['ExternalUserID'])])
                    ->andWhere(['wxcom_user_id' => $message['UserID']])
                    ->one();
                if ($customerUser) {
                    if ($customerUser->is_deleted == 0) {
                        $customerUser->is_deleted = 1;
                        $customerUser->deleted_type = 1;
                        $customerUser->deleted_time = $message['CreateTime'];
                        $customerUser->save();
                    }
                    $customerUser->deleted_type = 1;
                    static::delRecord($customerUser);
                }
                break;
            case 'edit_external_contact':
                $customerUser = CusCustomerUser::find()
                    ->andWhere(['cus_id' => self::getCusIdByExternalUserId($message['ExternalUserID'])])
                    ->andWhere(['wxcom_user_id' => $message['UserID']])
                    ->one();
                $wxcomCustomer = self::getWxcomCustomerFromWxcomByMessage($message);

                // 保存回调及客户信息到info中
                Yii::info(json_encode(['message' => $message, 'wxcomCustomer' => $wxcomCustomer], 256), 'edit_external_contact');

                foreach ($wxcomCustomer['follow_user'] as $followUser) {
                    if ($followUser['userid'] == $message['UserID']) {
                        $customerUser->remark = $followUser['remark'];
                        $customerUser->remark_mobiles = implode(',', $followUser['remark_mobiles']);
                        $customerUser->description = $followUser['description'];
                        $tagIds = [];
                        $tagNames = [];
                        foreach ($followUser['tags'] as $tag) {
                            if ($tag['type'] == 1) {
                                $tagNames[] = $tag['tag_name'];
                                $tagIds[] = CusTag::getIdByWxcomId($tag['tag_id']);
                            }
                        }
                        if (count($tagIds)) {
                            $customerUser->tag_ids = implode(',', $tagIds);
                        }

                        if(empty($customerUser->channel_id) && !empty($tagNames)){
                            try{
                                foreach($tagNames as $tagName){
                                    $channel_id = PromoteChannel::find()
                                        ->select('id')
                                        ->where(['name' => $tagName])
                                        ->andWhere(['entity_id' => $customerUser->entity_id])
                                        ->scalar();
                                    if($channel_id){
                                        $customerUser->channel_id = $channel_id;
                                        break;
                                    }
                                }
                            }catch(Exception $e){
                                Yii::$app->feishuNotice->text('用户userid:'.$followUser['userid'].'获取渠道报错：'.$e->getMessage());
                            }
                        }
                        $customerUser->save();
                        break;
                    }
                }
                break;
            case 'transfer_fail':
                $extendDetail = CusExtendDetail::find()
                    ->andWhere([
                        'external_user_id' => $message['ExternalUserID'],
                        'wxcom_user_id' => $message['UserID'],
                    ])
                    ->one();
                if (!$extendDetail) {
                    break;
                }

                /**
                 * 修改继承记录状态为失败
                 * @var CusExtendDetail $extendDetail
                 */
                $extendDetail->updateToFail();
                break;
        }
    }

    public static function dealWithMessage($message)
    {
        $config = ComService::getWxcomConfigByCodeForCus($message['ComCode']);
        $wxcomCustomer = self::getWxcomCustomerFromWxcomByMessage($message);

        //进程唯一
        $redis = Yii::$app->redis;
        $cacheKey = md5(json_encode($wxcomCustomer));
        if (!$redis->setnx($cacheKey, 1)) {
            return true;
        }
        $redis->expire($cacheKey, 10);
        unset($redis);

        Yii::info(['message_add_fans' => json_encode($message,256)], 'wxcomCallback');
        Yii::info(['wxcomCustomer' => json_encode($wxcomCustomer,256)], 'wxcomCallback');
        self::saveCustomerAndCustomerUser($wxcomCustomer, $message['UserID'], $config['com_id'], $config['entity_id'], $message['qrcode_id']);
    }

    public static function delRecord($customerUser)
    {
        $data = [
            'customer_user_id' => $customerUser->id,
            'deleted_type' => $customerUser->deleted_type,
            'entity_id' => $customerUser->entity_id
        ];

        $model = CusCustomerUserDel::find()->where($data)->one();
        if ($model) {
            return true;
        }

        $data['deleted_time'] = $customerUser->deleted_time;
        $model = new CusCustomerUserDel();
        $model->setAttributes($data);
        $model->save();

        return true;
    }

    /**
     * 保存客户信息与客户成员关系
     *
     * @param $wxcomCustomer
     * @param $currWxcomUserId
     * @param $comId
     * @param int $entityId
     * @param $qrcode_id
     * @throws Exception
     */
    public static function saveCustomerAndCustomerUser($wxcomCustomer, $currWxcomUserId, $comId, $entityId = 1, $qrcode_id)
    {
        $contact = $wxcomCustomer['external_contact'];

        /**
         * @var CusCustomerForm
         */
        $customer = self::find()
            ->andWhere(['com_id' => $comId])
            ->andWhere(['external_user_id' => $contact['external_userid']])
            ->one();

        $is_exit_customer = false;
        if (!$customer) {
            $customer = new self();
            $customer->com_id = $comId;
            $customer->entity_id = $entityId;

            $customer->external_user_id = $contact['external_userid'];
            $customer->name = $contact['name'];
            $customer->avatar = $contact['avatar'];
            $customer->type = $contact['type'];
            $customer->gender = $contact['gender'];
            $customer->unionid = $contact['unionid'];
            $customer->corp_name = $contact['corp_name'];
            $customer->corp_full_name = $contact['corp_full_name'];
            if (!$customer->save()) {
                throw new Exception('客户信息保存失败：' . current($customer->getFirstErrors()));
            }
        }else{
            $is_exit_customer = true;
        }

        Yii::info(['customer_id' => $customer->id], 'wxcomCallback');

        foreach ($wxcomCustomer['follow_user'] as $followUser) {
            $followUser['qrcode_id'] = $qrcode_id;
            if ($followUser['userid'] != $currWxcomUserId) {
                continue;
            }

            $user = User::find()
                ->where(['entity_id' => $entityId])
                ->andWhere(['wxcom_user_id' => $currWxcomUserId])
                ->one();
            if (empty($user)) {
                continue;
            }

            $customer->findUserOrCreateFromFollowUser($followUser, $user->id,$is_exit_customer);
        }
    }

    /**
     * 通过回调 message 获取企微客户信息
     *
     * @param $message
     * @return array|\EasyWeChat\Kernel\Support\Collection|object|\Psr\Http\Message\ResponseInterface|string
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    public static function getWxcomCustomerFromWxcomByMessage($message)
    {
        $config = ComService::getWxcomConfigByCodeForCus($message['ComCode']);
        $cusSDK = Factory::work($config)->external_contact;
        $wxcomCustomer = $cusSDK->get($message['ExternalUserID']);

        if (empty($wxcomCustomer) || $wxcomCustomer['errcode']) {
            throw new Exception('加粉回调，获取客户详情失败');
        }

        if ($wxcomCustomer['external_contact']['type'] == 2) {
            $wxcomCustomer['external_contact']['unionid'] = $wxcomCustomer['external_contact']['external_userid'];
        }

        return $wxcomCustomer;
    }

    /**
     * 企微客户成员表
     *
     * @param array $followUser
     * @return array|bool|CusCustomerUser|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public function findUserOrCreateFromFollowUser(array $followUser, $userId = null,$is_exit_customer = false)
    {
        /**@var CusCustomerUser $customerUser*/
        $customerUser = CusCustomerUser::find()
            ->andWhere(['cus_id' => $this->id])
            ->andWhere(['wxcom_user_id' => $followUser['userid']])
            ->andFilterWhere(['user_id' => $userId])
            ->one();

        if (!$customerUser) {
            $customerUser = new CusCustomerUser();
            $customerUser->com_id = $this->com_id;
            $customerUser->cus_id = $this->id;
            $customerUser->wxcom_user_id = $followUser['userid'];
            $customerUser->remark = $followUser['remark'];
            $customerUser->remark_mobiles = implode(',', $followUser['remark_mobiles']);
            $customerUser->description = $followUser['description'];
            $customerUser->add_way = $followUser['add_way'];
            $customerUser->add_time = $followUser['createtime'];
            $customerUser->oper_user_id = $followUser['oper_userid'];
            $customerUser->add_type = $followUser['oper_userid'] == $this->external_user_id ? 0 : 1;
            $customerUser->entity_id = $this->entity_id;
            $qrcode = null;

            $is_cus_service_role = false;//是否是客服
            /** @var UserForm */
            $user = UserForm::find()
                ->where(['entity_id' => $this->entity_id])
                ->andWhere(['wxcom_user_id' => $followUser['userid']])
                ->andWhere(['com_id' => $this->com_id])
                ->one();
            if ($user) {
                $customerUser->user_id = $user->id;
                $customerUser->created_by = $user->user_id;
                if(MemberService::userIsAppointRole($user->user_id,'客服')){
                    $is_cus_service_role = true;
                }
            }

            // 判断企微直加
            $button = null;
            if (isset($followUser['state']) && $followUser['state'] == 'wxad_button') {
                $customerUser->add_way = WxcomAddTypeEnum::BUTTON;
                $customerUser->type = CusCustomerUserTypeEnum::AD_BUTTON;
                if ($user) {
                    $button = $user->getButton();
                    if ($button) {
                        $customerUser->button_id = $button->id;
                        $customerUser->channel_id = $button->channel_id;
                        $customerUser->system = $button->system;
                        $customerUser->code = $button->code;
                        $customerUser->project_id = $button->project_id;
                        $customerUser->qrcode_created_by = $button->created_by;
                        $customerUser->qrcode_dept_id = $button->dept_id;
                    }
                }
            }

            $userQrcodeStatus = strpos($followUser['state'], WxcomCache::USER_QRCODE);
            $isLinkState = strpos($followUser['state'], WxcomCache::LINK) === 0;
            $isCusAcquisitionState = strpos($followUser['state'], WxcomCache::CUS_ACQUISITION) === 0;
            $isLandPageState = strpos($followUser['state'], WxcomCache::LAND_PAGE) === 0;

            if (isset($followUser['state']) && (is_numeric($followUser['state']) || strpos($followUser['state'], 'qd:') !== false || $userQrcodeStatus !== false || $isLinkState || $isCusAcquisitionState || $isLandPageState)) {
                if ($userQrcodeStatus !== false) {    //动态码匹配数据
                    $followUser['state'] = str_replace(WxcomCache::USER_QRCODE, '', $followUser['state']);
                    $customerUser->dynamic_qrcode_id = $followUser['qrcode_id'];
                    $customerUser->type = CusCustomerUserTypeEnum::DYNAMIC_CODE;
                    $qrcode = CusDynamicQrcode::findOne($followUser['qrcode_id']);
                } elseif ($isLinkState) {
                    $reqId = str_replace(WxcomCache::LINK, '', $followUser['state']);
                    $params = Yii::$app->cache->get(WxcomCache::LINK . $reqId);
                    $customerUser->type = CusCustomerUserTypeEnum::STATIC_CODE;
                    $followUser['state'] = $params['qrcode_id'];
                    $qrcode = CusQrcode::findOne($params['qrcode_id']);
                }elseif($isCusAcquisitionState){
                    $reqId = str_replace(WxcomCache::CUS_ACQUISITION, '', $followUser['state']);
                    $params = Yii::$app->cache->get(WxcomCache::CUS_ACQUISITION . $reqId);
                    $customerUser->type = CusCustomerUserTypeEnum::STATIC_CODE;
                    $followUser['state'] = $params['qrcode_id'];
                    $qrcode = CusQrcode::findOne($params['qrcode_id']);
                }elseif($isLandPageState){
                    $qrcode_id = str_replace(WxcomCache::LAND_PAGE, '', $followUser['state']);
                    $customerUser->type = CusCustomerUserTypeEnum::STATIC_CODE;
                    $followUser['state'] = $qrcode_id;
                    $qrcode = CusQrcode::findOne($qrcode_id);
                } else {
                    $followUser['state'] = str_replace('qd:', '', $followUser['state']);
                    $customerUser->type = CusCustomerUserTypeEnum::STATIC_CODE;
                    $qrcode = CusQrcode::findOne($followUser['state']);
                }

                $customerUser->state = $followUser['state'];
                if ($qrcode) {
                    $customerUser->qrcode_created_by = $qrcode->created_by;
                    $customerUser->qrcode_dept_id = $qrcode->dept_id;
                    $customerUser->channel_id = $qrcode->channel_id;
                    $customerUser->system = $qrcode->system;
                    $customerUser->code = $qrcode->code;
                    $customerUser->project_id = $qrcode->project_id;
                }
            }
            $tagIds = [];
            $tagNames = [];
            foreach ($followUser['tags'] as $tag) {
                if ($tag['type'] == 1) {
                    $tagNames[] = $tag['tag_name'];
                    $tagIds[] = CusTag::getIdByWxcomId($tag['tag_id']);
                }
            }

            if (count($tagIds)) {
                $customerUser->tag_ids = implode(',', $tagIds);
            }

            // 管理员分配客户时判断是否来源客户继承
            if ($followUser['add_way'] == 202) {
                /**
                 * @var CusExtendDetail $extendDetail
                 */
                $extendDetail = CusExtendDetail::find()
                    ->andWhere([
                        'cus_id' => $this->id,
                        'external_user_id' => $this->external_user_id,
                        'wxcom_user_id' => $followUser['userid'],
                    ])
                    ->one();
                $oldCustomerUser = CusCustomerUser::find()
                    ->andWhere(['id' => $extendDetail->cus_user_id])
                    ->one();

                if ($extendDetail) {
                    if ($user && $user->user_id) {
                        CustomerServiceLogService::changerResponsible($extendDetail->cus_id, $user->user_id);
                    }
                    $customerUser->cus_id = $extendDetail->cus_id;
                    $customerUser->sub_advertiser_name = $oldCustomerUser->sub_advertiser_name;
                    $customerUser->sub_advertiser_id = $oldCustomerUser->sub_advertiser_id;
                    $customerUser->callback = $oldCustomerUser->callback;
                    $customerUser->cl_code = $oldCustomerUser->cl_code;
                    $customerUser->system = $oldCustomerUser->system;
                    $customerUser->code = $oldCustomerUser->code;
                    $customerUser->channel_id = $oldCustomerUser->channel_id;
                    $customerUser->link_id = $oldCustomerUser->link_id;
                    $customerUser->project_id = $oldCustomerUser->project_id;
                    $customerUser->direction_id = $oldCustomerUser->direction_id;
                    $customerUser->responsible_id = $oldCustomerUser->responsible_id;
                    $customerUser->qrcode_created_by = $oldCustomerUser->qrcode_created_by;
                    $customerUser->qrcode_dept_id = $oldCustomerUser->qrcode_dept_id;
                    $customerUser->adid = $oldCustomerUser->adid;
                    $customerUser->mid3 = $oldCustomerUser->mid3;
                    $customerUser->csite = $oldCustomerUser->csite;
                    $customerUser->state = $oldCustomerUser->state;
                    $customerUser->ip = $oldCustomerUser->ip;
                    $customerUser->province_id = $oldCustomerUser->province_id;
                    $customerUser->city_id = $oldCustomerUser->city_id;
                    $customerUser->tag_ids = $oldCustomerUser->tag_ids;
                    $customerUser->created_by = $extendDetail->origin_created_by;

                    $extendDetail->updateToSuccess();
                }
            }

            if(empty($customerUser->channel_id) && !empty($tagNames)){
                try{
                    foreach($tagNames as $tagName){
                        $channel_id = PromoteChannel::find()
                            ->select('id')
                            ->where(['name' => $tagName])
                            ->andWhere(['entity_id' => $this->entity_id])
                            ->scalar();
                        if($channel_id){
                            $customerUser->channel_id = $channel_id;
                            break;
                        }
                    }
                }catch(Exception $e){
                    Yii::$app->feishuNotice->text('用户userid:'.$followUser['userid'].'获取渠道报错：'.$e->getMessage());
                }
            }

            if (!$customerUser->save()) {
                throw new Exception(current($customerUser->getFirstErrors()));
            }

            if ($userQrcodeStatus === false && $qrcode && $qrcode->has_limit) {
                $qrcodeService = new QrcodeService($qrcode);
                $qrcodeService->checkCusCountAndUpdateWxcom();
            }

            $customerUser->unknownSourceWay();

            if ($qrcode || $button) {
                try {
                    $customerUser->updateRemarkToWxcom();
                    $customerUser->updateTagsToWxcom();
                } catch (Exception $e) {
                    UpdateCustomerInfoJob::addJob($customerUser->id);
                }
            }

            if($is_cus_service_role){
                try {
                    $customerUser->cusAddTag();
                } catch (Exception $e) {}
            }

            //腾讯按数据源上报
            if($customerUser->add_way != 202){
                WxcomReportJob::addJob($customerUser->id, PromoteReport::WECHAT_ACTION_TYPE_SCANCODE);
            }
         
            return $customerUser;
        }else{
            $customerUser->againAddDelTag();
        }
        return $customerUser;
    }

    /**
     * @param $comCode
     * @return \EasyWeChat\Work\ExternalContact\Client
     */
    public function getSDK($comCode)
    {
        $config = ComService::getWxcomConfigByCodeForCus($comCode);
        $cusSDK = Factory::work($config)->external_contact;
        return $cusSDK;
    }

    /**
     * 同步数据
     *
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    public function syncData()
    {
        $entityId = Yii::$app->user->identity->current_entity_id;

        $comList = ComForm::find()
            ->select('id,name,code')
            ->where(['entity_id' => $entityId])
            ->where(['id' => 2])
            ->andWhere(['deleted_at' => 0])
            ->all();

        foreach ($comList as $com) {
            $cusSDK = $this->getSDK($com->code);
            $wxcomUserIdList = $cusSDK->getFollowUsers()['follow_user'];
            foreach ($wxcomUserIdList as $wxcomUserId) {
                $wxcomCustomerIdList = $cusSDK->list($wxcomUserId)['external_userid'];
                foreach ($wxcomCustomerIdList as $wxcomCustomerId) {
                    $wxcomCustomer = $cusSDK->get($wxcomCustomerId);
                    if ($wxcomCustomer['external_contact']['type'] == 2) {
                        $wxcomCustomer['external_contact']['unionid'] = $wxcomCustomer['external_contact']['external_userid'];
                    }
                    $customer = self::find()
                        ->andWhere(['com_id' => $com->id])
                        ->andWhere(['external_user_id' => $wxcomCustomerId])
                        ->one();
                    if (!$customer) {
                        $customer = new self();
                        $customer->com_id = $com->id;
                        $customer->entity_id = $entityId;

                        $contact = $wxcomCustomer['external_contact'];
                        $customer->external_user_id = $contact['external_userid'];
                        $customer->name = $contact['name'];
                        $customer->avatar = $contact['avatar'];
                        $customer->type = $contact['type'];
                        $customer->gender = $contact['gender'];
                        $customer->unionid = $contact['unionid'];
                        $customer->corp_name = $contact['corp_name'];
                        $customer->corp_full_name = $contact['corp_full_name'];
                        $customer->save();
                    }

                    foreach ($wxcomCustomer['follow_user'] as $followUser) {
                        /**
                         * @var CusCustomerForm $customer
                         */
                        $customer->findUserOrCreateFromFollowUser($followUser);
                    }
                }
            }
        }
    }

    /**
     * @param $externalUserId
     * @return false|string|null
     */
    public static function getCusIdByExternalUserId($externalUserId)
    {
        return self::find()->select('id')->where(['external_user_id' => $externalUserId])->scalar();
    }

    /**
     * 同步拉取客户信息
     *
     * @param $params
     * @return bool
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    public static function syncCus($params)
    {
        $userId = ArrayHelper::getValue($params, 'user_id');
        if (!$userId) {
            throw new Exception('成员id不能为空');
        }
        $externalUserId = ArrayHelper::getValue($params, 'external_user_id');
        if (!$externalUserId) {
            throw new Exception('客户企微id不能为空');
        }

        $user = User::findOne($userId);
        $config = [
            'com_id' => $user->com['id'],
            'entity_id' => $user->com['entity_id'],
            'corp_id' => $user->com['corp_id'],
            'secret' => $user->com['cus_secret'],
            'token' => $user->com['token'],
            'aes_key' => $user->com['aes_key'],
        ];
        $app = Factory::work($config);
        $cusSDK = $app->external_contact;
        $wxcomCustomer = $cusSDK->get($externalUserId);
        if ($wxcomCustomer['errcode']) {
            throw new Exception($wxcomCustomer['errmsg']);
        }

        if ($wxcomCustomer['external_contact']['type'] == 2) {
            $wxcomCustomer['external_contact']['unionid'] = $wxcomCustomer['external_contact']['external_userid'];
        }

        $is_exit_customer = false;
        $customer = CusCustomerForm::find()->where(['external_user_id' => $externalUserId, 'com_id' => $user->com_id])->one();
        if (!$customer) {
            $contact = $wxcomCustomer['external_contact'];
            $customer = new CusCustomerForm();
            $customer->com_id = $user->com_id;
            $customer->entity_id = $user->entity_id;
            $customer->external_user_id = $contact['external_userid'];
            $customer->name = $contact['name'];
            $customer->avatar = $contact['avatar'];
            $customer->type = $contact['type'];
            $customer->gender = $contact['gender'];
            $customer->unionid = $contact['unionid'];
            $customer->corp_name = $contact['corp_name'];
            $customer->corp_full_name = $contact['corp_full_name'];
            if (!$customer->save()) {
                throw new Exception(current($customer->getFirstErrors()));
            }
        }else{
            $is_exit_customer = true;
        }

        $cusUserExists = CusCustomerUser::find()->where(['cus_id' => $customer->id, 'user_id' => $userId])->exists();
        if ($cusUserExists) {
            throw new Exception('数据库已经存在，不需要重新导入');
        }

        foreach ($wxcomCustomer['follow_user'] as $followUser) {
            if ($followUser['userid'] == $user->wxcom_user_id) {
                if (strpos($followUser['state'], WxcomCache::USER_QRCODE) !== false) {
                    $state = Tool::getStringToArray($followUser['state'], ':')[1];   //动态码ID
                    $cusQrCodeInfo = DynamicMapping::find()->select('dynamic_qrcode_id AS qrcode_id')
                        ->andWhere([
                            'wxcom_user_id' => $followUser['userid'],
                            'external_user_id' => $externalUserId,
                            'qrcode_id' => $state
                        ])->asArray()->one();

                    $cusQrCodeInfo['state'] = $state;
                    $followUser['qrcode_id'] = $cusQrCodeInfo['qrcode_id'];
                }
                /**
                 * @var CusCustomerForm $customer
                 */
                $customer->findUserOrCreateFromFollowUser($followUser, $userId,$is_exit_customer);
                return true;
            }
        }

        return false;
    }

    public static function syncZl($message, $wxcomCustomer)
    {
        try {
            $data = [
                'message' => $message,
                'wxcomCustomer' => $wxcomCustomer
            ];
            $url = 'https://zlapi.zesta.cn/api/chz-callback';
            Tool::curlRequest($url, $data, true);
        } catch (Exception $e) {
        }
    }
}
