<?php
//公共路由
$common_extraPatterns = [
    'GET index' => 'index',
    'GET view' => 'view',
    'GET select' => 'select',
    'GET choose' => 'choose',
    'GET export' => 'export',
    'POST create' => 'create',
    'POST update' => 'update-data',
    'POST set-status' => 'status',
    'DELETE delete' => 'delete',
];

$baseRoutes = [
    '/<key:\w+>.txt' => 'api/check-text',
    '/' => 'api/error',
    [
        //仪表盘控制器
        'class' => 'yii\rest\UrlRule',
        'controller' => ['dashboard'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET core-data' => 'core-data',
            'GET ads-cost-data' => 'ads-cost-data',
            'GET add-fans-data' => 'add-fans-data',
            'GET servicer-deposit-order' => 'servicer-deposit-order',
            'GET deposit-analysis' => 'deposit-analysis',           //核心数据-客服订金数据
            'GET deposit-ranked' => 'deposit-ranked',               //核心数据-客服订金排名
            'GET branch-analysis' => 'branch-analysis',             //核心数据-客服品牌分析
        ]
    ],
    [   //公共路由
        'class' => 'yii\rest\UrlRule',
        'controller' => [
            'member/auth-role',     //角色信息控制器
            'common/auth-interface',//接口控制器
        ],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns, [
            'POST set-scope' => 'set-scope',
            'POST del-role-user' => 'del-role-user',
            'GET check-dept' => 'check-dept',
            'GET servicer-roles' => 'servicer-roles',
            'GET selectable-roles' => 'selectable-roles',
            'GET list' => 'list'
        ]),
    ],
    [   // 花名册
        'class' => 'yii\rest\UrlRule',
        'controller' => ['member/dingtalk-user'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'GET export' => 'export',
            'GET abnormal' => 'abnormal',
            'POST data-entry' => 'data-entry',
        ],
    ],
    [   //后台用户信息路由
        'class' => 'yii\rest\UrlRule',
        'controller' => ['member/member'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST set-status' => 'set-status',
                'POST set-admin' => 'set-admin',
                'GET get-service' => 'get-service',
                'GET get-promote-person' => 'get-promote-person',
                'GET dept-staff' => 'dept-staff',
                'GET select-for-dept' => 'select-for-dept',
                'GET role-staff' => 'role-staff',
                'GET get-responsible-person' => 'get-responsible-person',
                'GET select-created' => 'select-created',
                'GET servicer-select' => 'servicer-select',
            ]),
    ],
    [
        //site控制器
        'class' => 'yii\rest\UrlRule',
        'controller' => ['site'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET sweep-login' => 'sweep-login',
            'POST logout' => 'logout', //退出登录
            'POST up-pwd' => 'up-pwd', //重置密码
            'GET appid' => 'get-appid',//获取企业钉钉appid
            'POST simulation-login' => 'simulation-login',//模拟登陆
            'GET get-signature' => 'get-signature',//获取
            'POST oss' => 'oss',
            'POST relay-log' => 'relay-log',
            'GET get-redis-info' => 'get-redis-info',
            'GET feishu-appid' => 'get-feishu-appid',//获取飞书appid
            'GET feishu-login' => 'feishu-login', //使用飞书扫码登录
            'POST images' => 'images',
        ]
    ],
    [
        //供应商管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['material/supplier'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET search' => 'search',
                'GET export' => 'export',
            ]),
    ],
    [
        //物料管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['material/goods'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST get-info-all' => 'get-info-all',
                'POST batch-create' => 'batch-create',
                'GET get-update-history' => 'get-update-history',
                'GET export' => 'export',
                'GET get-material-inventory-detail' => 'get-material-inventory-detail',
            ]),
    ],
    [
        //库存盘点管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['material/check'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET store-info' => 'store-info',   //门店信息列表
                'GET inventory-view' => 'inventory-view',  //盘点详情
                'GET update-status' => 'update-status',  //取消盘点
                'GET batch-inventory-export' => 'batch-inventory-export',  //批量导出盘点明细
                'POST batch-create' => 'batch-create',
                'POST check-info-all' => 'check-info-all',  //核验导入的盘点明细
                'POST create-inventory' => 'create-inventory',  //创建盘点清单
                'POST create-inventory-details' => 'create-inventory-details',  //提交盘点详情
            ]),
    ],
    [
        //库存管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['material/inventory'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET record-list' => 'record',
                'POST inbound' => 'inbound',
                'POST outbound' => 'outbound',
                'POST check' => 'check-inventory',
                'GET export' => 'export',
                'GET record-export' => 'record-export',
                'GET get-belong-warehouse' => 'get-belong-warehouse',
                'GET material-inventory-detail-export' => 'material-inventory-detail-export',
            ]),
    ],
    [
        //固定资产
        'class' => 'yii\rest\UrlRule',
        'controller' => ['assets/fixed-assets'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET dept-index' => 'dept-index',
                'GET record-list' => 'record',
                'GET fixed-index' => 'fixed-index',
                'GET record-export' => 'record-export',
                'GET fixed-export' => 'fixed-export',
                'POST allot' => 'allot',
                'GET export' => 'export',
                'POST create' => 'create',
                'POST inbound' => 'inbound',
                'POST inventory' => 'inventory',            //盘点
                'POST check-create' => 'check-create',      //批量导入数据验证
                'POST check-info-all' => 'check-info-all',  //调拨导入数据验证
                'POST department-outbound' => 'department-outbound',
            ]),
    ],
    [
        // 物料审批
        'class' => 'yii\rest\UrlRule',
        'controller' => ['material/approve'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST create' => 'create',
                'POST send' => 'send',
                'POST receive' => 'receive',
                'POST cancel' => 'cancel',
                'GET allot-index' => 'allot-index',
                'GET allot-index-export' => 'allot-index-export',
                'GET detail-list' => 'detail-list',
                'GET export-detail-list' => 'export-detail-list',
                'POST change-detail-status' => 'change-detail-status',
            ]),
    ],
    [
        //报损管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['reported-loss'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET inventory-list' => 'inventory-list', //报损详情
                'GET initiate-approve' => 'initiate-approve', //重新发起钉钉审批
                'GET export-list' => 'export-list', //列表导出
                'GET detail-list' => 'detail-list', //详情列表
                'GET export-detail-list' => 'export-detail-list', //详情列表导出
                'POST change-detail-status' => 'change-detail-status', //明细状态变更
            ]),
    ],
    [
        //采购管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['procurement'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST handle-exception' => 'handle-exception', //采购单处理异常
                'POST change-purchase-num' => 'change-purchase-num', //修改采购数量
                'POST shipment' => 'shipment', //发货
                'POST acceptance' => 'acceptance', //收货
                'POST change-prices' => 'change-prices', //修改采购价格
                'POST cancle' => 'cancle', //取消采购
                'GET export' => 'export', //导出采购单详情
                'GET dept-info' => 'dept-info', //获取部门
                'GET inventory-list' => 'inventory-list', //物料明细
                'GET check-info-all' => 'check-info-all', //校验导入物料
                'GET query-ding-ding' => 'query-ding-ding', //查询钉钉审批
                'GET detail-list-columns' => 'detail-list-columns', //采购详情列表字段
                'GET detail-list' => 'detail-list', //采购详情列表
                'GET detail-view' => 'detail-view', //采购详情
                'GET detail-export' => 'detail-export', //采购详情-导出
            ]),
    ],
    [
        //采购付款管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['material/procurement-pay'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'GET export' => 'export',
            'POST create' => 'create',
            'GET pay-type-select' => 'pay-type-select',
            'POST approve' => 'approve',
        ],
    ],
    [   //角色信息路由
        'class' => 'yii\rest\UrlRule',
        'controller' => ['common/menu'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET left' => 'menu-left',
        ],
    ],
    [   //公共接口路由
        'class' => 'yii\rest\UrlRule',
        'controller' => ['common/common'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET approval-status' => 'approval-status',
        ],
    ],
    [   //菜单权限
        'class' => 'yii\rest\UrlRule',
        'controller' => ['member/auth-item'],
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET apps' => 'apps',
            ]
        ),
    ],
    [   //实体信息
        'class' => 'yii\rest\UrlRule',
        'controller' => ['entity'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST index' => 'index',
                'DELETE delete' => 'soft-delete',
            ]),
    ],
    [   //省市区
        'class' => 'yii\rest\UrlRule',
        'controller' => ['common/provinces'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST index' => 'index',
            ]),
    ],
    [   //门店信息
        'class' => 'yii\rest\UrlRule',
        'controller' => ['store'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST set-status' => 'status',
                'GET filter-list' => 'filter-list',
                'GET export' => 'export',
                'GET choose' => 'choose',
                'GET get-qrcode' => 'get-qrcode',
                'GET order-data' => 'order-data',
                'GET plan-store-list' => 'plan-store-list',
                'GET teacher-plan-time-list' => 'teacher-plan-time-list',
                'GET teacher-plan-list' => 'teacher-plan-list',
                'GET order-data-export' => 'order-data-export',
                'GET inventory-data' => 'inventory-data',
                'GET inventory-data-export' => 'inventory-data-export',
                'GET union-config-list' => 'union-config-list',
                'GET union-config-view' => 'union-config-view',
                'GET switch-store-list' => 'switch-store-list',
                'POST update-address' => 'update-address',
                'POST update-area-batch' => 'update-area-batch',
                'POST union-config-update' => 'union-config-update',
            ]),
    ],
    [   //推广页
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote-page'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [

            ]),
    ],
    [   //推广-推广员
        'class' => 'yii\rest\UrlRule',
        'controller' => 'promote/promoter',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'POST bind-code' => 'bind-code',
            ]
        ),
    ],
    [   //推广-账号管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/account'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET export' => 'export',
                'GET get-list' => 'get-list',
                'GET get-plan-view' => 'get-plan-view',
                'GET get-pull-list' => 'get-pull-list',
                'GET report-event-taget-select' => 'report-event-taget-select',
                'POST set-agent' => 'agent',
                'POST set-rebates' => 'rebates',
                'POST update-direction-batch' => 'update-direction-batch',
            ]),
    ],
    [   //推广-渠道管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/channel'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET export' => 'export',
                'GET get-list' => 'get-list',
                'GET get-tree' => 'get-tree',
                'GET get-all-list' => 'get-all-list',
                'GET store-select' => 'store-select',
            ]),
    ],
    [   //推广-链路链路
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/promote-link'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
            ]),
    ],
    [   //推广-项目管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/promote-project'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
            ]),
    ],
    [   //推广-定向管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/direction'],
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            []
        ),
    ],
    [   //推广-代号管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/code'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET choose' => 'choose',
            'POST create' => 'create',
        ],
    ],
    [   //推广-标签管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/ads-material-label'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET get-all' => 'get-all',
            'POST batch-update' => 'batch-update',
        ],
    ],
    [   //推广-系统标签管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/ads-cus-label'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'POST create' => 'create',
            'POST update-data' => 'update-data',
            'POST status' => 'status',
            'GET get-all' => 'get-all',
        ],
    ],
    [   //推广账号
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote-account'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST set-cost_discount' => 'cost-discount',
            ]),
    ],
    [   // 城市分析
        'class' => 'yii\rest\UrlRule',
        'controller' => ['data/city-analysis'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET export' => 'export',
        ],
    ],
    [   // 门店分析
        'class' => 'yii\rest\UrlRule',
        'controller' => ['data/store-analysis'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET export' => 'export',
        ],
    ],
    [   //推广-数据分析
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/analysis'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET city-list' => 'city-list',
                'GET city-data' => 'city-data',
                'GET city-data-export' => 'city-data-export',
                'GET start-stop-time' => 'start-stop-time',
                'GET city-daily-data' => 'city-daily-data',
                'GET cast-people-analysis' => 'cast-people-analysis',
                'GET cast-people-details' => 'cast-people-details',
                'GET cast-people-export' => 'cast-people-export',
                'GET cast-people-details-export' => 'cast-people-details-export',
                'GET project-analysis' => 'project-analysis',
                'GET public-time' => 'public-time',
                'GET project-export' => 'project-export',
                'GET project-details' => 'project-details',
                'GET promote-analysis' => 'promote-analysis',
                'GET promote-export' => 'promote-export',
                'GET promote-details' => 'promote-details',
                'GET land-page-list' => 'land-page-list',
                'POST land-page-export' => 'land-page-export',
                'POST report-data-export' => 'report-data-export',
                'POST wecom-report-export' => 'wecom-report-export',
                'POST wecom-report-callback-export' => 'wecom-report-callback-export',
                'POST wxcom-callback-export' => 'wxcom-callback-export',
                'POST wecom-clues-export' => 'wecom-clues-export',
                'POST order-report-export' => 'order-report-export',
                'GET material-analysis' => 'material-analysis',
                'GET material-analysis-export' => 'material-analysis-export',
                'GET age-gender-analysis' => 'age-gender-analysis',
                'GET age-gender-analysis-export' => 'age-gender-analysis-export',
                'GET user-growth-output' => 'user-growth-output',
                'GET user-growth-output-export' => 'user-growth-output-export',
                'GET hour-data' => 'hour-data',
                'GET hour-data-export' => 'hour-data-export',
                'GET hour-time' => 'hour-time',
            ]),
    ],
    [   //落地页分组管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'land-page-group',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [

            ]
        ),
    ],
    [   //推广-代理商账户
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/agent'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET export' => 'export',
                'GET bank-view' => 'bank-view',
                'POST set-bank' => 'update-bank',
                'GET filter-list' => 'filter-list',
            ]),
    ],
    [   //推广-消耗管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/ads-account-data'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET export' => 'export',
                'POST confirm' => 'confirm',
                'POST set-agent' => 'agent',
                'POST set-rebates' => 'rebates',
            ]),
    ],
    [   //推广-消耗管理：手动拉取账户消耗计划
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/account-pull-record'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST update-program' => 'update-program',
            ]),
    ],
    [   //推广-素材管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/ads-material'],
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET detail' => 'detail',
            'GET export' => 'export',
            'POST change-composer' => 'change-composer',
            'POST change-refurbisher' => 'change-refurbisher',
            'GET bundle-list' => 'bundle-list',
            'POST bundle' => 'bundle',
            'POST unbundling' => 'unbundling',
            'POST update-note' => 'update-note',
        ],
    ],
    [   //推广-请款单
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/request-payout'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET export' => 'export',
                'GET top-up-list' => 'top-up-list',     //充值明细列表
                'GET top-up-export' => 'top-up-export', //充值明细导出
                'GET advances-list' => 'advances-list',
                'GET advances-detail-list' => 'advances-detail-list',       //垫款单明细列表
                'GET advances-detail-export' => 'advances-detail-export',   //垫款单明细导出
            ]),
    ],
    [   //推广-对账单
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/account-check'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET export' => 'export',
            ]),
    ],
    [   //部门管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['department'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET promote-dept' => 'promote-dept',
                'GET get-sub-dept' => 'get-sub-dept',
                'GET get-dept-chain-info' => 'get-dept-chain-info',
            ]),
    ],
    [   //目标-指标管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['target/field'],
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET condition-types' => 'condition-types',
                'GET selectable-field-list' => 'selectable-field-list',
            ]
        ),
    ],
    [   //目标-明细管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['target/record'],
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET import-template' => 'import-template',
                'POST import' => 'import',
                'GET report' => 'report',
            ]
        ),
    ],
    [   // 数据统计-订金排名
        'class' => 'yii\rest\UrlRule',
        'controller' => ['data/servicer-deposit-order'],
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET index-for-group' => 'index-for-group',
                'GET export-for-group' => 'export-for-group',
            ]
        ),
    ],
    [   // 数据统计-订金项目排名
        'class' => 'yii\rest\UrlRule',
        'controller' => ['data/servicer-deposit-project-order'],
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET index-for-group' => 'index-for-group',
                'GET export-for-group' => 'export-for-group',
            ]
        ),
    ],
    [   // 数据统计-订金排名
        'class' => 'yii\rest\UrlRule',
        'controller' => ['data/servicer-analysis'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET index' => 'index',
                'GET export' => 'export',
                'GET index-for-group' => 'index-for-group',
                'GET export-for-group' => 'export-for-group',
                'GET performance' => 'performance',
                'GET performance-export' => 'performance-export',
            ],
    ],
    [   //商品分类
        'class' => 'yii\rest\UrlRule',
        'controller' => ['product-category'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET goods-enable-top-category-list' => 'goods-enable-top-category-list',
                'GET cate-parent' => 'get-category-parent',
                'GET relay-select' => 'relay-select',
                //库存物料
                'GET inventory-index' => 'inventory-index',
                'GET inventory-view' => 'inventory-view',
                'GET inventory-select' => 'inventory-select',
                'POST inventory-create' => 'inventory-create',
                'POST inventory-update' => 'inventory-update-data',
                'POST inventory-set-status' => 'inventory-status',
                //固定资产
                'GET fixed-index' => 'fixed-index',
                'GET fixed-view' => 'fixed-view',
                'GET fixed-select' => 'fixed-select',
                'POST fixed-create' => 'fixed-create',
                'POST fixed-update' => 'fixed-update-data',
                'POST fixed-set-status' => 'fixed-status',
            ]),
    ],
    [   //区域分类
        'class' => 'yii\rest\UrlRule',
        'controller' => ['area'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET  select-category' => 'select-category',
                'POST edit-status' => 'edit-status',
                'POST update-data' => 'update-data',
                'GET index' => 'index',
                'GET view' => 'view',
                'POST create' => 'create',
                'POST set-status' => 'status',
            ],
    ],
    [   //客服微信号管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['weixin-account'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET get-weixin' => 'get-wei-xin',
            ]),
    ],
    [   //客服微信号管理
        'class' => 'yii\rest\UrlRule',
        'controller' => ['weixin-batch'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET get-service' => 'get-service',
            ]),
    ],
    [   // 落地页接口
        'class' => 'yii\rest\UrlRule',
        'controller' => ['apis/land-page',],
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST create-we-chat-url' => 'create-we-chat-url',
                'POST finish-applet-upload' => 'finish-applet-upload',
                'POST new-create-user-promotion' => 'new-create-user-promotion', //推广用户事件添加-新
                'GET get-applet-share' => 'get-applet-share',
                'POST login-we-chat' => 'login-we-chat',
                'GET feishu'=> 'feishu-callback',
            ]
        ),
    ],
    [   // 支付接口
        'class' => 'yii\rest\UrlRule',
        'controller' => ['apis/pay',],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'POST union-pay-callback' => 'union-pay-callback',
                'POST union-pay-callback-for-user' => 'union-pay-callback-for-user',
            ],
    ],
    [   // 回调
        'class' => 'yii\rest\UrlRule',
        'controller' => ['apis/pay-callback',],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'POST union' => 'union',
                'POST lkl' => 'lkl',
                'GET create-qrcode' => 'create-qrcode',
                'GET deal-data' => 'deal-data',
            ],
    ],
    [   //广告系统回调
        'class' => 'yii\rest\UrlRule',
        'controller' => [
            'api',     //广告系统api回调
        ],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET oceanengine' => 'oceanengine-callback',
                'GET oceanengine-new' => 'oceanengine-new-callback',
                'GET tencent' => 'tencent-callback',
                'GET quickly' => 'quickly-callback',
                'POST new-create-user-promotion' => 'new-create-user-promotion', //推广用户事件添加-新
                'GET get-applet-share' => 'get-applet-share',
                'POST create-we-chat-url' => 'create-we-chat-url',
                'POST login-we-chat' => 'login-we-chat',
                'GET feishu'=> 'feishu-callback',
                'GET wxcom-adjust-store'=> 'wxcom-adjust-store-callback',
                'POST yy-callback'=> 'yy-callback',
                'GET yy-callback'=> 'yy-callback',
            ]),
    ],
    [   //开发工具
        'class' => 'yii\rest\UrlRule',
        'controller' => ['dev-tools',],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'GET l19bbd9bdecbc09c6650e82c245caf8e' => 'get-log-list',
                'GET i90998a917b4033d9cfe3eeea14b53cb' => 'get-log-info',
                'GET d0208d9eaedcbebf5ae2d428df427bc6' => 'del-log',
                'GET a40b2b4c266f2b72dd501e4994946c8a' => 'get-redis-keys',
                'GET c3e37c7a9c2aeab1694fa8dc41643d83' => 'get-redis-key-info',
                'GET 62ee52cc4f681e45c4af3021268c7983' => 'get-redis-info',
                'GET 7cefa2feeb9a900d20eb32c29cd50715' => 'redis-manager',
                'GET 449d6d89ead4926e008e4008e8508f16' => 'log-manager',
                'GET search-user' => 'search-user',
                'GET user-lose-cus-list' => 'user-lose-cus-list',
                'GET sync-cus' => 'sync-cus',
                'POST sync-cus-product' => 'sync-cus-product',
                'POST pull-pay-records' => 'pull-pay-records',
                'POST sync-customer-user' => 'sync-customer-user',
                'POST unserialize' => 'unserialize',
                'POST order-stock' => 'order-stock',
            ]),
    ],

    [   //落地页，落地页图库
        'class' => 'yii\rest\UrlRule',
        'controller' => ['promote/land-page'],
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST update' => 'update-data',
                'POST delete' => 'land-delete',
                'GET image-index' => 'image-index',
                'POST image-create' => 'image-create',
                'POST image-delete' => 'image-delete',
                'GET report-list' => 'report-list',
                'POST report-create' => 'report-create',
                'POST report-update' => 'report-update',
                'POST report-delete' => 'report-delete',
                'POST update-group' => 'update-group',
                'POST update-group-batch' => 'update-group-batch',
                'POST update-owner-batch' => 'update-owner-batch',
                'POST land-submit' => 'land-submit',
                'GET selected-list' => 'selected-list',
                'GET selected-land-list' => 'selected-land-page-list',
                'GET report-account-list' => 'report-account-list',
                'GET report-event-list' => 'report-event-list',
                'POST copy' => 'copy',
                'POST report-platform' => 'report-platform',
            ]),
    ],
    [   //小程序管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'applet',
        'pluralize' => false,
        'extraPatterns' => array_merge($common_extraPatterns,
            [
                'POST set-status' => 'set-status',
                'POST transfer-land-page' => 'transfer-land-page',
                'POST release' => 'release',
                'POST upload' => 'upload',
                'GET export' => 'export',
            ]),
    ],
    [   // 二维码背景图管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'qrcode-bgi',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST change-qrcode' => 'change-qrcode',
                'POST qr-code' => 'qr-code',
            ]
        ),
    ],
    [   // 银联商户号管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'union-pay',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET bill-no-title-select' => 'bill-no-title-select',
            ]
        ),
    ],
    [   //渠道活码分组管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/cus-qrgroup',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [

            ]
        ),
    ],
    [   //渠道活码管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/cus-qrcode',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST update-name' => 'update-name',
                'POST update-group' => 'update-group',
                'POST update-status' => 'update-status',
                'POST update-group-batch' => 'update-group-batch',
                'GET select-link' => 'select-link',
                'GET export' => 'export',
            ]
        ),
    ],
    [   //微信客服链路配置管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/wechat-servicer',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET collect-list-export' => 'collect-list-export',
                'GET collect-list' => 'collect-list',
                'POST update-servicer' => 'update-servicer',
                'POST update-responsible' => 'update-responsible',
                'DELETE delete' => 'soft-delete',
            ]
        ),
    ],
    [   //微信用户授权
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wechat/user',
        'pluralize' => false,
        'extraPatterns' => [
            'POST authorize' => 'authorize',
            'GET info' => 'info',
        ]
    ],
    [   //微信客服链路话术配置管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/wechat-servicer-reply',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
            ]
        ),
    ],
    [   //个微微信号管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wechat/wechat-account',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'GET promote-select' => 'promote-select',
            ]
        ),
    ],
    [   //个微微信号管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wechat/wechat-allot',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
            ]
        ),
    ],
    [   //bis系统接口
        'class' => 'yii\rest\UrlRule',
        'controller' => 'other-system',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET admin-code' => 'admin-code',
            ]
        ),
    ],
    [   //商品标签管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'goods/tag',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'POST set-status' => 'set-status',
                'POST delete-tag' => 'delete-tag',
            ]
        ),
    ],
    [   //商品管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'goods/product',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'GET material-list' => 'material-list',
                'POST material-create' => 'material-create',
                'POST copy-product' => 'copy-product',
                'POST material-export' => 'material-export',
                'GET choose' => 'choose',
                'GET relay-choose' => 'relay-choose',
                'GET plan-product-list' => 'plan-product-list',
            ]
        ),
    ],
    [   //客装商品管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'goods/product-material',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'POST copy-product' => 'copy-product',
            ]
        ),
    ],
    [   //商品套餐管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'goods/package',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'POST set-status' => 'status',
                'POST copy' => 'copy',
                'GET choose' => 'choose',
                'GET relay-choose' => 'relay-choose',
                'GET plan-product-list' => 'plan-product-list',
            ]
        ),
    ],
    [   //商品储值卡管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'goods/stored-value-card',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET goods-list' => 'goods-list',
                'POST set-status' => 'set-status',
                'GET export' => 'export',
                'GET gift-list' => 'gift-list',
            ]
        ),
    ],
    [   //企业微信-部门管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/department',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET sync' => 'sync',
                'GET tree' => 'tree',
            ]
        ),
    ],
    [   //企业微信-成员管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/user',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET sync' => 'sync',
                'GET get-service-users' => 'get-service-users',
                'GET get-com-with-service-users' => 'get-com-with-service-users',
                'GET service-users' => 'service-users',
                'GET com-service-users' => 'com-service-users',
                'GET friend-list' => 'friend-list',
                'POST set-promote-status' => 'set-promote-status',
                'POST set-status' => 'set-status'
            ]
        ),
    ],
    [   //企业微信-成员加粉统计
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/cus-count',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET index-for-date' => 'index-for-date',
                'GET export-for-date' => 'export-for-date',
                'GET index-for-user' => 'index-for-user',
                'GET export-for-user' => 'export-for-user',
                'GET today-count' => 'today-count',
            ]
        ),
    ],
    [   //企业微信-成员加粉统计（活码）
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/cus-qrcode-count',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET index-for-date' => 'index-for-date',
                'GET export-for-date' => 'export-for-date',
                'GET index-for-user' => 'index-for-user',
                'GET export-for-user' => 'export-for-user',
                'GET today-count' => 'today-count',
            ]
        ),
    ],
    [   //企业微信接口
        'class' => 'yii\rest\UrlRule',
        'controller' => 'apis/wxcom',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET callback/<comCode:\w+>' => 'callback',
                'POST callback/<comCode:\w+>' => 'callback',
                'POST callback-test' => 'callback-test',
                'POST wechat-auto-reply' => 'wechat-auto-reply',
                'GET wecom-callback' => 'wecom-callback',
                'GET wecom-click-callback' => 'wecom-click-callback',
                'GET tiktok-callback' => 'tiktok-callback',
                'GET chat-record/<comCode:\w+>' => 'chat-record',
                'GET login/<comCode:\w+>' => 'login',
                'POST set-wxcom-qrcode' => 'set-wxcom-qrcode',
            ]
        ),
    ],
    [   // BIS接口
        'class' => 'yii\rest\UrlRule',
        'controller' => 'apis/bis',
        'pluralize' => false,
        'extraPatterns' => [
            'POST sync-promote-code' => 'sync-promote-code',
            'POST sync-order-header' => 'sync-order-header',
        ],
    ],
    [   // 营销通接口
        'class' => 'yii\rest\UrlRule',
        'controller' => 'apis/yxt',
        'pluralize' => false,
        'extraPatterns' => [
            'POST land-page-check' => 'land-page-check',
        ],
    ],
    [   //钉钉接口
        'class' => 'yii\rest\UrlRule',
        'controller' => 'apis/dingtalk',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST callback/<comCode:\w+>' => 'callback',
                'GET invite-info' => 'invite-info',
                'POST bind-dept' => 'bind-dept',
                'GET test-sync' => 'test-sync',
                'POST user-sync' => 'user-sync',
            ]
        ),
    ],
    [   //飞书接口
        'class' => 'yii\rest\UrlRule',
        'controller' => 'apis/feishu',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST callback/<comCode:\w+>' => 'callback',
                'POST callback-config/<comCode:\w+>' => 'callback-config',
                'GET invite-info' => 'invite-info',
                'POST bind-dept' => 'bind-dept',
                'GET test-sync' => 'test-sync',
                'POST user-sync' => 'user-sync',
                'POST talbe-callback' => 'talbe-callback',
                'GET get-table-data' => 'get-table-data',
                'POST transfer-money' => 'transfer-money',
                'POST transfer-money-batch' => 'transfer-money-batch',
                'POST order-operate' => 'order-operate',
                'POST check-balance' => 'check-balance',
                'POST msg-callback' => 'msg-callback',
                'GET adjust-store' => 'adjust-store',
                'POST store-list' => 'store-list',
            ]
        ),
    ],
    [   //采购api接口
        'class' => 'yii\rest\UrlRule',
        'controller' => 'apis/procurement',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST acceptance' => 'acceptance',
            ]
        ),
    ],
    [   //企业微信-外部联系人明细
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/cus-customer-user',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'GET index-for-extend' => 'index-for-extend',
                'GET export-for-qrcode' => 'export',
                'GET get-add-way-select' => 'get-add-way-select',
                'GET get-ad-position-select' => 'get-ad-position-select',
            ]
        ),
    ],
    [   //企业微信-外部联系人
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/cus-customer',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET sync' => 'sync',
            ]
        ),
    ],
    [   //企业微信-客户标签管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/cus-tag',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET sync' => 'sync',
                'DELETE delete' => 'soft-delete',
            ]
        ),
    ],
    [   //配置管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'config',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET get-group' => 'get-group',
                'GET get-brand' => 'get-brand', //门店品牌列表
            ]
        ),
    ],
    [   //品牌管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'brand',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [

            ]
        ),
    ],
    [   //企业微信-企微公司管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wxcom/com',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'GET select' => 'select',
            'GET choose' => 'choose',
            'POST create' => 'create',
            'POST update' => 'update-data',
            'POST update-status' => 'update-status',
        ]
    ],
    [   //客服加粉统计
        'class' => 'yii\rest\UrlRule',
        'controller' => 'customer-service-count',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'GET index-detail' => 'index-detail',
            ]
        ),
    ],
    [   //客服账号管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'customer-service',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'POST update-wxcom-user-customer-service' => 'update-wxcom-user-customer-service',
                'GET third-system-users' => 'third-system-users',
            ]
        ),
    ],
    [   //个微加粉管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'wechat/fans-record',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST batch-create' => 'batch-create',
            ]
        ),
    ],
    [   //订单管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'order/order-header',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST cancel' => 'cancel',
                'POST store-cancel' => 'store-cancel',
                'POST set-status' => 'set-status',
                'POST order-prepay' => 'order-prepay',
                'POST cancellation' => 'cancellation',
                'POST refresh-write-list' => 'refresh-write-list',
                'GET export' => 'export',
                'GET write-list' => 'write-list',
                'GET reach-store' => 'reach-store',
                'GET reach-store-export' => 'reach-store-export',
                'GET mobile-check' => 'mobile-check',
                'GET store-list' => 'store-list',
                'POST update-order-status' => 'update-order-status',
                'GET store-view' => 'store-view',
                'POST store-settlement' => 'store-settlement',
                'POST store-create' => 'store-create',
                'POST store-finish' => 'store-finish',
                'GET store-view-for-edit' => 'store-view-for-edit',
                'GET store-view-for-plan' => 'store-view-for-plan',
                'POST store-update-for-plan' => 'store-update-for-plan',
                'GET store-view-for-print' => 'store-view-for-print',
                'GET store-cus-list' => 'store-cus-list',
                'GET servicer-view-for-plan' => 'servicer-view-for-plan',
                'POST servicer-update-for-plan' => 'servicer-update-for-plan',
                'POST revert-settlement' => 'revert-settlement',
                'GET lkl-list' => 'lkl-list',
                'POST change-plan-store' => 'change-plan-store',
                'GET store-settlement-count' => 'store-settlement-count',
            ]
        ),
    ],
    [   //定金明细
        'class' => 'yii\rest\UrlRule',
        'controller' => 'order/deposit-details',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns
        ),
    ],
    [   //客户管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'customer',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST void' => 'void',
                'POST set-status' => 'set-status',
                'POST store-create' => 'store-create',
                'POST update-mobile-name' => 'update-mobile-name',
                'POST update-wxcom-customer' => 'update-wxcom-customer',
                'GET customer-order' => 'customer-order',
                'GET get-info-by-order' => 'get-info-by-order',
                'GET get-list-by-order' => 'get-list-by-order',
                'GET mobile-check' => 'mobile-check',
                'GET export' => 'export',
                'GET store-search-list' => 'store-search-list',
                'GET customer-info' => 'customer-info',
                'GET customer-remaining-projects' => 'customer-remaining-projects',
                'GET bis-customer-first-store-time' => 'bis-customer-first-store-time',
                'GET get-age-bracket' => 'get-age-bracket',
            ]
        ),
    ],
    [   //退款原因管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'refund/reason',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [

            ]
        ),
    ],
    [   //退款管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'refund/refund',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET export' => 'export',
                'GET status-list' => 'status-list',
                'POST cancel' => 'cancel',
                'POST fail' => 'fail',
            ]
        ),
    ],
    [   //任务管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'task',
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET index' => 'index',
                'POST retry' => 'retry',
            ]
    ],
    [   //支付管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'pay/pay-record',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST set-status' => 'set-status',
                'GET pull-record' => 'pull-record',
            ]
        ),
    ],
    [   // 流水管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'pay-record',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST freeze' => 'freeze',                  //冻结流水
                'POST restore' => 'restore',                //恢复流水
                'POST release-water' => 'release-water',    //释放流水
                'POST update-servicer-customer' => 'update-servicer-customer',   //修改客服归属人
                'POST wxcom-transfer-wechat' => 'wxcom-transfer-wechat',         //流水企微转个微
            ]
        ),
    ],
    [   //客户管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'customer',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'POST set-status' => 'set-status',
                'GET get-list-by-order' => 'get-list-by-order',
            ]
        ),
    ],
    [   //客户管理
        'class' => 'yii\rest\UrlRule',
        'controller' => 'apis/order',
        'pluralize' => false,
        'extraPatterns' => array_merge(
            $common_extraPatterns,
            [
                'GET pkam-report' => 'pkam-report',
            ]
        ),
    ],
    [   //罚款分类
        'class' => 'yii\rest\UrlRule',
        'controller' => ['penalty/category'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET index' => 'index',
                'GET select' => 'select',
                'POST create' => 'create',
                'POST update' => 'update-data',
                'POST set-status' => 'status',
            ],
    ],
    [   //罚款记录
        'class' => 'yii\rest\UrlRule',
        'controller' => ['penalty/record'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET index' => 'index',
                'GET view' => 'view',
                'GET export' => 'export',
                'POST create' => 'create',
                'POST update' => 'update-data',
                'POST set-status' => 'status',
            ],
    ],
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => ['complaint'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET list' => 'list',
                'GET info' => 'info',
                'GET mobile' => 'mobile',
                'GET export' => 'export',
                'POST deal-with' => 'deal-with',
                'GET reason-all' => 'reason-all',
                'GET reason-list' => 'reason-list',
                'POST reason-create' => 'reason-create',
                'POST reason-update' => 'reason-update',
                'GET source-type-all' => 'source-type-all',
                'POST reason-status-update' => 'reason-status-update',
            ],
    ],
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => ['lkl-config'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET index' => 'index',
                'GET view' => 'view',
                'POST create' => 'create',
                'POST status' => 'status',
                'POST update-data' => 'update-data',
            ],
    ],
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => ['sms'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'POST send' => 'send',
            ],
    ],
    [   // 特殊操作-订单
        'class' => 'yii\rest\UrlRule',
        'controller' => ['approval-operation'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'POST update-order-plan-time' => 'update-order-plan-time',
                'POST update-order-price' => 'update-order-price',
                'POST update-order-plan-by' => 'update-order-plan-by',
                // 'POST update-user-channel' => 'update-user-channel',
            ],
    ],
    [   // 操作日志
        'class' => 'yii\rest\UrlRule',
        'controller' => ['operate-log'],
        'pluralize' => false,
        'extraPatterns' =>
            [
                'GET order' => 'order'
            ],
    ],
];

$routeFiles = scandir(__DIR__ . "/routes");
$ignoreRouteFiles = ['.', '..'];
foreach ($routeFiles as $fileName) {
    if (in_array($fileName, $ignoreRouteFiles)) {
        continue;
    }
    $routes = include_once __DIR__ . "/routes/{$fileName}";
    $baseRoutes = array_merge($baseRoutes, $routes);
}
return $baseRoutes;